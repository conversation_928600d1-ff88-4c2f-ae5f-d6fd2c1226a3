using Contracts.Common.Permission.DTOs.Role;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Permission.Api.Services.Validator.Role
{
    public class SetRoleInputValidator : AbstractValidator<SetRoleInput>
    {
        public SetRoleInputValidator()
        {
            RuleFor(s => s.UserId)
                .NotEqual(0);

            RuleFor(s => s.RoleId)
                .NotEqual(0);
        }
    }
}
