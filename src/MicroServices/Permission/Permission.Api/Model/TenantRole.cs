using EfCoreExtensions.EntityBase;

namespace Permission.Api.Model
{
    /// <summary>
    /// 租户角色
    /// </summary>
    public class TenantRole : TenantBase
    {
        public string Name { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;

        public bool IsAdmin { get; set; } = false;
    }
}
