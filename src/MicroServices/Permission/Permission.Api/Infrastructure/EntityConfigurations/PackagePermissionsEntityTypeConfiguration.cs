using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Permission.Api.Infrastructure.EntityConfigurations
{
    public class PackagePermissionsEntityTypeConfiguration : KeyBaseConfiguration<Model.PackagePermissions>, IEntityTypeConfiguration<Model.PackagePermissions>
    {
        public void Configure(EntityTypeBuilder<Model.PackagePermissions> builder)
        {
            base.ConfigureBase(builder);
            
            builder.Property(s => s.PackageId)
                .HasColumnType("bigint");

            builder.Property(s => s.PermissionsId)
                .HasColumnType("bigint");
        }
    }
}