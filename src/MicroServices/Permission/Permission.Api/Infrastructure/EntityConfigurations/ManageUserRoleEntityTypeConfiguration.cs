using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Permission.Api.Infrastructure.EntityConfigurations
{
    public class ManageUserRoleEntityTypeConfiguration : KeyBaseConfiguration<ManageUserRole>, IEntityTypeConfiguration<ManageUserRole>
    {
        public void Configure(EntityTypeBuilder<ManageUserRole> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");

            builder.Property(s => s.ManageRoleId)
                .HasColumnType("bigint");


            builder.HasIndex(s => new { s.UserId, s.ManageRoleId }).IsUnique();

            builder.HasData(new ManageUserRole { Id = 901019344460099999, ManageRoleId = 901019344460088888, UserId = 901019344460090000 });
        }
    }
}
