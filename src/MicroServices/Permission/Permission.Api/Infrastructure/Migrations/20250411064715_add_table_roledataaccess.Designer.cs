// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Permission.Api.Infrastructure;

#nullable disable

namespace Permission.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250411064715_add_table_roledataaccess")]
    partial class add_table_roledataaccess
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Permission.Api.Model.AgencyACL", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AclKey")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ApiPattern")
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("DisplayPattern")
                        .HasColumnType("varchar(1000)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("AclKey")
                        .IsUnique();

                    b.ToTable("AgencyACL", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.AgencyUserACL", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AclKey")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("AgencyUserACL", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.ManageRole", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.ToTable("ManageRole", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 901019344460088888L,
                            Enabled = true,
                            IsAdmin = true,
                            Name = "超级管理员",
                            Remark = "最高权限角色"
                        });
                });

            modelBuilder.Entity("Permission.Api.Model.ManageRolePermissions", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ManageRoleId")
                        .HasColumnType("bigint");

                    b.Property<long>("PermissionsId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ManageRoleId", "PermissionsId")
                        .IsUnique();

                    b.ToTable("ManageRolePermissions", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.ManageUserRole", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ManageRoleId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "ManageRoleId")
                        .IsUnique();

                    b.ToTable("ManageUserRole", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 901019344460099999L,
                            ManageRoleId = 901019344460088888L,
                            UserId = 901019344460090000L
                        });
                });

            modelBuilder.Entity("Permission.Api.Model.Package", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(16)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UpdatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("UpdatorName")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Package", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.PackagePermissions", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("PackageId")
                        .HasColumnType("bigint");

                    b.Property<long>("PermissionsId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("PackagePermissions", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.Permissions", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Code")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("EnglishName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Icon")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ParentId")
                        .HasColumnType("bigint");

                    b.Property<string>("Path")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<int>("SysType")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("Permissions", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 898030447106981111L,
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "产品中心",
                            Name = "产品中心",
                            ParentId = 0L,
                            Sort = 0,
                            SysType = 1,
                            Type = 1,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 898030447106981001L,
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "系统管理",
                            Icon = "el-icon-menu",
                            Name = "系统管理",
                            ParentId = 898030447106981111L,
                            Path = "/systemAdministration",
                            Sort = 0,
                            SysType = 1,
                            Type = 1,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 898030447106981002L,
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "菜单管理",
                            Name = "菜单管理",
                            ParentId = 898030447106981001L,
                            Path = "menuAdministration",
                            Sort = 0,
                            SysType = 1,
                            Type = 2,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 898030447106981003L,
                            Code = "add",
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "添加",
                            Name = "添加",
                            ParentId = 898030447106981002L,
                            Sort = 0,
                            SysType = 1,
                            Type = 3,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 898030447106981004L,
                            Code = "edit",
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "编辑",
                            Name = "编辑",
                            ParentId = 898030447106981002L,
                            Sort = 0,
                            SysType = 1,
                            Type = 3,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 898030447106981005L,
                            Code = "remove",
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "删除",
                            Name = "删除",
                            ParentId = 898030447106981002L,
                            Sort = 0,
                            SysType = 1,
                            Type = 3,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 898030447106981006L,
                            Code = "search",
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "搜索",
                            Name = "搜索",
                            ParentId = 898030447106981002L,
                            Sort = 0,
                            SysType = 1,
                            Type = 3,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = 898030447106981007L,
                            Code = "setting",
                            CreateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EnglishName = "设置",
                            Name = "设置",
                            ParentId = 898030447106981002L,
                            Sort = 0,
                            SysType = 1,
                            Type = 3,
                            UpdateTime = new DateTime(2021, 10, 28, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("Permission.Api.Model.TenantPackage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("PackageId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantPackage", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.TenantPermissions", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("PermissionsId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantPermissions", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.TenantRole", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantRole", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.TenantRoleDataAccess", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("RoleDataPermissions")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantRoleId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantRoleDataAccess", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.TenantRolePermissions", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("PermissionsId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantRoleId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("PermissionsId", "TenantRoleId")
                        .IsUnique();

                    b.ToTable("TenantRolePermissions", (string)null);
                });

            modelBuilder.Entity("Permission.Api.Model.TenantUserRole", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantRoleId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId", "TenantRoleId")
                        .IsUnique();

                    b.ToTable("TenantUserRole", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
