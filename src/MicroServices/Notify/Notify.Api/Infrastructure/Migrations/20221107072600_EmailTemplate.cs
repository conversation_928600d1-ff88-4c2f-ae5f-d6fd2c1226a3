using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    public partial class EmailTemplate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "EmailNotifyIsOpen",
                table: "CustomerNotifyTemplateSwitch",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "EmailTemplateContent",
                table: "CustomerNotifyTemplateLibrary",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "EmailTemplateTitle",
                table: "CustomerNotifyTemplateLibrary",
                type: "varchar(200)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EmailNotifyIsOpen",
                table: "CustomerNotifyTemplateSwitch");

            migrationBuilder.DropColumn(
                name: "EmailTemplateContent",
                table: "CustomerNotifyTemplateLibrary");

            migrationBuilder.DropColumn(
                name: "EmailTemplateTitle",
                table: "CustomerNotifyTemplateLibrary");
        }
    }
}
