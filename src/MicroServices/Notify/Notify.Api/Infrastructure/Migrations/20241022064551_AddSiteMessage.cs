using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    public partial class AddSiteMessage : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SiteMessage",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    AgencyId = table.Column<long>(type: "bigint", nullable: false),
                    ObjectType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Title = table.Column<string>(type: "varchar(256)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Content = table.Column<string>(type: "varchar(512)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LinkType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    LinkId = table.Column<long>(type: "bigint", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SiteMessage", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "SiteMessageReceiver",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    SiteMessageId = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    IsRead = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SiteMessageReceiver", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "SiteMessageTemplateLibrary",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    ObjectType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Title = table.Column<string>(type: "varchar(256)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Content = table.Column<string>(type: "varchar(512)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LinkType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SiteMessageTemplateLibrary", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_SiteMessage_TenantId",
                table: "SiteMessage",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_SiteMessageReceiver_TenantId",
                table: "SiteMessageReceiver",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SiteMessage");

            migrationBuilder.DropTable(
                name: "SiteMessageReceiver");

            migrationBuilder.DropTable(
                name: "SiteMessageTemplateLibrary");
        }
    }
}
