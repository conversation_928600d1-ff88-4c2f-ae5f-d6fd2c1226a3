using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    public partial class add_table_ManagerNotifyTemplateDingtalkRobot : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ManagerNotifyTemplateDingtalkRobot",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    GroupName = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WebHook = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Salt = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ManagerNotifyTemplateDingtalkRobot", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_ManagerNotifyTemplateDingtalkRobot_NotifyEventSubType",
                table: "ManagerNotifyTemplateDingtalkRobot",
                column: "NotifyEventSubType",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ManagerNotifyTemplateDingtalkRobot");
        }
    }
}
