using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    public partial class SiteMessageEnTitleContent : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "EnContent",
                table: "SiteMessageTemplateLibrary",
                type: "varchar(512)",
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "EnTitile",
                table: "SiteMessageTemplateLibrary",
                type: "varchar(256)",
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "EnContent",
                table: "SiteMessage",
                type: "varchar(512)",
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "EnTitle",
                table: "SiteMessage",
                type: "varchar(256)",
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EnContent",
                table: "SiteMessageTemplateLibrary");

            migrationBuilder.DropColumn(
                name: "EnTitile",
                table: "SiteMessageTemplateLibrary");

            migrationBuilder.DropColumn(
                name: "EnContent",
                table: "SiteMessage");

            migrationBuilder.DropColumn(
                name: "EnTitle",
                table: "SiteMessage");
        }
    }
}
