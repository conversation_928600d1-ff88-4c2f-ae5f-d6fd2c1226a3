using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    public partial class AddNotifyEventSubTypeToPullInfo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CustomerNotifyTemplatePullInfo_WechatTemplateNo_TenantId",
                table: "CustomerNotifyTemplatePullInfo");

            migrationBuilder.AddColumn<int>(
                name: "NotifyEventSubType",
                table: "CustomerNotifyTemplatePullInfo",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNotifyTemplatePullInfo_WechatTemplateNo_TenantId_Not~",
                table: "CustomerNotifyTemplatePullInfo",
                columns: new[] { "WechatTemplateNo", "TenantId", "NotifyEventSubType" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CustomerNotifyTemplatePullInfo_WechatTemplateNo_TenantId_Not~",
                table: "CustomerNotifyTemplatePullInfo");

            migrationBuilder.DropColumn(
                name: "NotifyEventSubType",
                table: "CustomerNotifyTemplatePullInfo");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNotifyTemplatePullInfo_WechatTemplateNo_TenantId",
                table: "CustomerNotifyTemplatePullInfo",
                columns: new[] { "WechatTemplateNo", "TenantId" },
                unique: true);
        }
    }
}
