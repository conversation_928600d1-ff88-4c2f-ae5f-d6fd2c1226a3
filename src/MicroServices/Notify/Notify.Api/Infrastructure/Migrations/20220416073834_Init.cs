using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    public partial class Init : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CustomerNotifyTemplateLibrary",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    WechatTemplateNo = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WechatContentExample = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SmsTemplateNo = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SmsContentExample = table.Column<string>(type: "varchar(200)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerNotifyTemplateLibrary", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CustomerNotifyTemplatePullInfo",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    WechatTemplateNo = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WechatTemplateCode = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerNotifyTemplatePullInfo", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CustomerNotifyTemplateSwitch",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    WechatNotifyIsOpen = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    SmsNotifyIsOpen = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerNotifyTemplateSwitch", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "StaffNotifyTemplateLibrary",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    WechatTemplateNo = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WechatTemplateCode = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WechatContentExample = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaffNotifyTemplateLibrary", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "StaffNotifyTemplateSetting",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    SupplierId = table.Column<long>(type: "bigint", nullable: false),
                    StaffId = table.Column<long>(type: "bigint", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaffNotifyTemplateSetting", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "StaffNotifyTemplateSwitch",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    IsOpen = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaffNotifyTemplateSwitch", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "SupplierNotifyTemplateLibrary",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    WechatTemplateNo = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WechatTemplateCode = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WechatContentExample = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupplierNotifyTemplateLibrary", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "SupplierNotifyTemplateSwitch",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    IsOpen = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupplierNotifyTemplateSwitch", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "TenantSmsSign",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Sign = table.Column<string>(type: "varchar(16)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SignId = table.Column<string>(type: "varchar(32)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ApplyStatus = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Description = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TenantSmsSign", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNotifyTemplatePullInfo_TenantId",
                table: "CustomerNotifyTemplatePullInfo",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNotifyTemplatePullInfo_WechatTemplateNo_TenantId",
                table: "CustomerNotifyTemplatePullInfo",
                columns: new[] { "WechatTemplateNo", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNotifyTemplateSwitch_NotifyEventSubType_TenantId",
                table: "CustomerNotifyTemplateSwitch",
                columns: new[] { "NotifyEventSubType", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNotifyTemplateSwitch_TenantId",
                table: "CustomerNotifyTemplateSwitch",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffNotifyTemplateSetting_TenantId",
                table: "StaffNotifyTemplateSetting",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffNotifyTemplateSwitch_NotifyEventSubType_TenantId",
                table: "StaffNotifyTemplateSwitch",
                columns: new[] { "NotifyEventSubType", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffNotifyTemplateSwitch_TenantId",
                table: "StaffNotifyTemplateSwitch",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_SupplierNotifyTemplateSwitch_NotifyEventSubType_TenantId",
                table: "SupplierNotifyTemplateSwitch",
                columns: new[] { "NotifyEventSubType", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SupplierNotifyTemplateSwitch_TenantId",
                table: "SupplierNotifyTemplateSwitch",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_TenantSmsSign_Sign",
                table: "TenantSmsSign",
                column: "Sign",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TenantSmsSign_TenantId",
                table: "TenantSmsSign",
                column: "TenantId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CustomerNotifyTemplateLibrary");

            migrationBuilder.DropTable(
                name: "CustomerNotifyTemplatePullInfo");

            migrationBuilder.DropTable(
                name: "CustomerNotifyTemplateSwitch");

            migrationBuilder.DropTable(
                name: "StaffNotifyTemplateLibrary");

            migrationBuilder.DropTable(
                name: "StaffNotifyTemplateSetting");

            migrationBuilder.DropTable(
                name: "StaffNotifyTemplateSwitch");

            migrationBuilder.DropTable(
                name: "SupplierNotifyTemplateLibrary");

            migrationBuilder.DropTable(
                name: "SupplierNotifyTemplateSwitch");

            migrationBuilder.DropTable(
                name: "TenantSmsSign");
        }
    }
}
