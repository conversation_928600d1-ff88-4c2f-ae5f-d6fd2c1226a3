using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Notify.Api.Infrastructure;

public class CustomDbContext : DbContextBase
{
    public CustomDbContext(DbContextOptions dbContextOptions, ITenantIdentify tenantIdentify) : base(dbContextOptions, tenantIdentify)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }

    public DbSet<TenantSmsSign> TenantSmsSigns { get; set; }
    public DbSet<CustomerNotifyTemplateLibrary> CustomerNotifyTemplateLibrary { get; set; }
    public DbSet<CustomerNotifyTemplatePullInfo> CustomerNotifyTemplatePullInfo { get; set; }
    public DbSet<CustomerNotifyTemplateChannelPullInfo> CustomerNotifyTemplateChannelPullInfo { get; set; }
    
    public DbSet<CustomerNotifyTemplateSwitch> CustomerNotifyTemplateSwitch { get; set; }
    public DbSet<StaffNotifyTemplateLibrary> StaffNotifyTemplateLibrary { get; set; }
    public DbSet<StaffNotifyTemplateSetting> StaffNotifyTemplateSetting { get; set; }
    public DbSet<StaffNotifyTemplateDingtalk> StaffNotifyTemplateDingtalks { get; set; }
    public DbSet<StaffNotifyTemplateDingtalkRobotSwitche> StaffNotifyTemplateDingtalkRobotSwitches { get; set; }
    public DbSet<StaffNotifyTemplateDingtalkRobot> StaffNotifyTemplateDingtalkRobots { get; set; }
    public DbSet<AgencyNotifyTemplateSwitch> AgencyNotifyTemplateSwitchs { get; set; }
    public DbSet<AgencyNotifyTemplateLibrary> AgencyNotifyTemplateLibrarys { get; set; }
    public DbSet<StaffNotifyTemplateSwitch> StaffNotifyTemplateSwitch { get; set; }
    public DbSet<SupplierNotifyTemplateLibrary> SupplierNotifyTemplateLibrary { get; set; }
    public DbSet<SupplierNotifyTemplateSwitch> SupplierNotifyTemplateSwitch { get; set; }
    public DbSet<TenantEmailBox> TenantEmailBoxs { get; set; }

    public DbSet<ManagerNotifyTemplateLibrary> ManagerNotifyTemplateLibraries { get; set; }
    public DbSet<ManagerNotifyTemplateSetting> ManagerNotifyTemplateSettings { get; set; }
    public DbSet<ManagerNotifyTemplateSwitch> ManagerNotifyTemplateSwitchs { get; set; }
    public DbSet<ManagerNotifyTemplateDingtalkRobot> ManagerNotifyTemplateDingtalkRobots { get; set; }
    public DbSet<ManagerNotifyTemplateDingtalk> ManagerNotifyTemplateDingtalks { get; set; }
    public DbSet<ManagerNotifyTemplateDingtalkRobotSwitche> ManagerNotifyTemplateDingtalkRobotSwitches { get; set; }

    public DbSet<SiteMessage> SiteMessages { get; set; }
    public DbSet<SiteMessageReceiver> SiteMessageReceivers { get; set; }
    public DbSet<SiteMessageTemplateLibrary> SiteMessageTemplateLibraries { get; set; }
}
