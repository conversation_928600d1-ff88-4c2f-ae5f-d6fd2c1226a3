using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Notify.Api.Infrastructure.EntityConfigurations;

public class AgencyNotifyTemplateLibraryEntityTypeConfiguration : KeyBaseConfiguration<AgencyNotifyTemplateLibrary>, IEntityTypeConfiguration<AgencyNotifyTemplateLibrary>
{
    public void Configure(EntityTypeBuilder<AgencyNotifyTemplateLibrary> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.NotifyEventType)
            .HasColumnType("tinyint");

        builder.Property(s => s.NotifyEventSubType)
            .HasColumnType("int");

        builder.Property(s => s.SmsTemplateNo)
                .HasColumnType("varchar(64)");

        builder.Property(s => s.SmsContentExample)
            .HasColumnType("varchar(200)");

        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime");
    }
}
