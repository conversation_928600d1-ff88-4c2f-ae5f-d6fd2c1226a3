using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Notify.Api.Infrastructure.EntityConfigurations;

public class ManagerNotifyTemplateLibraryEntityTypeConfiguration : KeyBaseConfiguration<ManagerNotifyTemplateLibrary>, IEntityTypeConfiguration<ManagerNotifyTemplateLibrary>
{
    public void Configure(EntityTypeBuilder<ManagerNotifyTemplateLibrary> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.NotifyEventType)
            .HasColumnType("tinyint");

        builder.Property(s => s.NotifyEventSubType)
            .HasColumnType("int");

        builder.Property(s => s.WechatTemplateNo)
            .HasColumnType("varchar(64)")
            .IsRequired();

        builder.Property(s => s.WechatTemplateCode)
            .HasColumnType("varchar(64)");

        builder.Property(s => s.WechatContentExample)
            .HasColumnType("varchar(500)");

        builder.Property(s => s.Keywords)
            .HasColumnType("text");

        builder.Property(s => s.WechatCategoryTemplateNo)
            .HasColumnType("varchar(64)");

        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime");
    }
}
