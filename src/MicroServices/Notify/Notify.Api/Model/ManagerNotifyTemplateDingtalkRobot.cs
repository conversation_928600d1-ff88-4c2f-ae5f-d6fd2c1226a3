using Contracts.Common.Notify.Enums;
using EfCoreExtensions.EntityBase;

namespace Notify.Api.Model;

public class ManagerNotifyTemplateDingtalkRobot : KeyBase
{
    /// <summary>
    /// 钉钉模版id
    /// </summary>
    public long TemplateDingtalkId { get; set; }

    /// <summary>
    /// 接受群组名称
    /// </summary>
    public string? GroupName { get; set; }

    /// <summary>
    /// WebHook
    /// </summary>
    public string? WebHook { get; set; }

    /// <summary>
    /// 加签内容
    /// </summary>
    public string? Salt { get; set; }

    /// <summary>
    /// 国家编码(可多选)
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    public int[] GetCountryCodes()
    {
        return string.IsNullOrEmpty(CountryCode) ? Array.Empty<int>() : Array.ConvertAll(CountryCode.Split(','), s =>
        {
            return int.TryParse(s, out int temp) ? temp : 0;
        }); 
    }
}
