using Contracts.Common.Notify.Enums;
using EfCoreExtensions.EntityBase;

namespace Notify.Api.Model
{
    /// <summary>
    /// C端用户通知模板 管理员导入数据
    /// </summary>
    public class CustomerNotifyTemplateLibrary : KeyBase
    {
        public NotifyChannel NotifyChannel { get; set; }

        /// <summary>
        /// 通知事件类型
        /// </summary>
        public NotifyEventType NotifyEventType { get; set; }

        /// <summary>
        /// 通知事件子类型
        /// </summary>
        public NotifyEventSubType NotifyEventSubType { get; set; }

        /// <summary>
        /// 微信公共库模板编号
        /// </summary>
        public string WechatTemplateNo { get; set; }

        /// <summary>
        /// 微信模板内容示例
        /// <para>标题：订单取消通知 OPENTM202184281</para>
        /// <para>详细内容：</para>
        /// <para>尊敬的 张三，您有新的【商品退款】退款申请单需要处理</para>
        /// <para>商品名称：显示器</para>
        /// <para>金额：￥5800</para>
        /// <para>退款情况：退款成功</para>
        /// </summary>
        public string? WechatContentExample { get; set; }

        /// <summary>
        /// 短信模板编号
        /// </summary>
        public string? SmsTemplateNo { get; set; }

        /// <summary>
        /// 短信模板内容示例
        /// <para> 526852 (手机动态验证码，10分钟内有效，请完成验证)。</para>
        /// </summary>
        public string? SmsContentExample { get; set; }

        /// <summary>
        /// 邮件模板
        /// </summary>
        public string? EmailTemplateTitle { get; set; }

        /// <summary>
        /// 邮件模板
        /// </summary>
        public string? EmailTemplateContent { get; set; }

        /// <summary>
        /// 关键词
        /// </summary>
        public string Keywords { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
