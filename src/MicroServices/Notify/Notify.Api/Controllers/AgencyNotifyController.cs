using Contracts.Common.Notify.DTOs.AgencyNotify;
using Microsoft.AspNetCore.Mvc;
using Notify.Api.Services.Interfaces;

namespace Notify.Api.Controllers;

/// <summary>
/// 分销商通知
/// </summary>
[Route("[controller]/[action]")]
[ApiController]
public class AgencyNotifyController : ControllerBase
{
    private readonly IAgencyNotifyService _agencyNotifyService;

    public AgencyNotifyController(IAgencyNotifyService agencyNotifyService)
    {
        _agencyNotifyService = agencyNotifyService;
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<GetAgencyNotifySwitchesOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSwitches()
    {
        var result = await _agencyNotifyService.GetSwitches();
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateSwitch(UpdateAgencyNotifySwitchInput input)
    {
        await _agencyNotifyService.UpdateSwitch(input);
        return Ok();
    }


    /// <summary>
    /// 新增模板
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AddLibrary(AddAgencyNotifyLibraryInput input)
    {
        var result = await _agencyNotifyService.AddLibrary(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取模板
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(GetAgencyNotifyLibraryOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetLibrary(long id)
    {
        var result = await _agencyNotifyService.GetLibrary(id);
        return Ok(result);
    }

    /// <summary>
    /// 更新模板
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> UpdateLibrary(UpdateAgencyNotifyLibraryInput input)
    {
        await _agencyNotifyService.UpdateLibrary(input);
        return Ok();
    }

    /// <summary>
    /// 获取模板列表
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(List<GetAgencyNotifyLibrariesOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetLibraries()
    {
        var result = await _agencyNotifyService.GetLibraries();
        return Ok(result);
    }
}
