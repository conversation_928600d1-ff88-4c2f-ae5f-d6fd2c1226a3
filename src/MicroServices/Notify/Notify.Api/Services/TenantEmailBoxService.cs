using AutoMapper;
using AutoMapper.QueryableExtensions;
using Common.GlobalException;
using Contracts.Common.Notify.DTOs;
using Microsoft.EntityFrameworkCore;
using Notify.Api.Services.Interfaces;
using Notify.Api.Services.Sender;

namespace Notify.Api.Services;

public class TenantEmailBoxService : ITenantEmailBoxService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;

    public TenantEmailBoxService(CustomDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<List<TenantEmailBoxDto>> GetByTenant(long tenantId)
    {
        var result = await _dbContext.TenantEmailBoxs
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Where(s => s.TenantId == tenantId)
            .ProjectTo<TenantEmailBoxDto>(_mapper.ConfigurationProvider)
            .ToListAsync();
        return result ?? new();
    }

    public async Task<bool> AddOrUpdate(TenantEmailBoxDto dto)
    {
        var entity = await _dbContext.TenantEmailBoxs
            .Where(x => x.TenantEmailBoxType.Equals(dto.TenantEmailBoxType))
            .FirstOrDefaultAsync();
        bool needCheck = true;

        if (entity is null)
        {
            entity = _mapper.Map<TenantEmailBox>(dto);
            _dbContext.Add(entity);
        }
        else
        {
            if (dto.ServerHost == entity.ServerHost &&
                dto.ServerPort == entity.ServerPort &&
                dto.Address == entity.Address &&
                dto.Password == entity.Password)
                needCheck = false;

            _ = _mapper.Map(dto, entity);
        }

        //确保邮件服务是否可用
        if (needCheck)
        {
            var serverConfig = _mapper.Map<ConfigModel.EmailServerConfig>(entity);
            try
            {
                await MailKitHelper.CheckServerAvailable(serverConfig);
            }
            catch (MailKit.Security.AuthenticationException)
            {
                throw new BusinessException(ErrorTypes.Notify.MailBoxAuthenticationError);
            }
            catch
            {
                throw new BusinessException(ErrorTypes.Notify.MailBoxConnectError);
            }
        }

        var rows = await _dbContext.SaveChangesAsync();
        return rows > 0;
    }
}
