using AutoMapper;
using Contracts.Common.Notify.DTOs;
using Contracts.Common.Notify.DTOs.ManagerNotify.Dingtalks;
using Contracts.Common.Notify.DTOs.ManagerNotify.DingtalkSwitche;
using Contracts.Common.Notify.Enums;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Notify.Api.Services.Interfaces;

namespace Notify.Api.Services;

public class ManagerNotifyTemplateDingtalkService : IManagerNotifyTemplateDingtalkService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;

    public ManagerNotifyTemplateDingtalkService(
        CustomDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    /// <summary>
    /// 查询钉钉服务模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public Task<PagingModel<ManagerNotifyTemplateDingtalkListDto>> SearchAsync(GetManagerNotifyTemplateDingtalkInput input)
    {
        return _dbContext.ManagerNotifyTemplateDingtalks
                .AsNoTracking()
                .Select(x => new ManagerNotifyTemplateDingtalkListDto
                {
                    Id = x.Id,
                    TemplateName = x.TemplateName,
                    CreateTime = x.CreateTime
                })
                .OrderByDescending(x => x.Id)
                .PagingAsync(input.PageIndex, input.PageSize);
    }

    /// <summary>
    /// 获取钉钉模版下拉选择
    /// </summary>
    /// <returns></returns>
    public Task<List<ManagerNotifyTemplateDingtalkDto>> ListAsync()
    {
        return _dbContext.ManagerNotifyTemplateDingtalks
                .AsNoTracking()
                .Select(x => new ManagerNotifyTemplateDingtalkDto
                {
                    Id = x.Id,
                    TemplateName = x.TemplateName
                })
                .ToListAsync();
    }

    /// <summary>
    /// 删除钉钉服务模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task DeleteAsync(ManagerNotifyTemplateDingtalkInput input)
    {
        var templateDingtalk = await _dbContext.ManagerNotifyTemplateDingtalks.FindAsync(input.Id);

        if (templateDingtalk is null)
            return;

        _dbContext.ManagerNotifyTemplateDingtalks.Remove(templateDingtalk);

        //删除机器人
        await DeleteDingtalkRobotAsync(input.Id);

        // 删除关联的钉钉消息模版
        var templateDingtalkSwitche = await _dbContext.ManagerNotifyTemplateDingtalkRobotSwitches
            .Where(x => x.TemplateDingtalkId == input.Id)
            .ToListAsync();

        if (templateDingtalkSwitche?.Count is > 0)
            _dbContext.ManagerNotifyTemplateDingtalkRobotSwitches.RemoveRange(templateDingtalkSwitche);

        await _dbContext.SaveChangesAsync();

    }

    /// <summary>
    /// 删除额外的模版机器人配置
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    private async Task DeleteDingtalkRobotAsync(long Id)
    {
        var dingtalkRobots = await _dbContext.ManagerNotifyTemplateDingtalkRobots
                .Where(x => x.TemplateDingtalkId == Id)
                .ToListAsync();

        if (dingtalkRobots?.Any() is true)
            _dbContext.ManagerNotifyTemplateDingtalkRobots.RemoveRange(dingtalkRobots);
    }

    /// <summary>
    /// 获取模版信息与机器人列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<ManagerNotifyTemplateDingtalkDetailDto> DetailAsync(long templateDingtalkId)
    {
        var result = new ManagerNotifyTemplateDingtalkDetailDto();

        var templateDingtalk = await _dbContext.ManagerNotifyTemplateDingtalks.FindAsync(templateDingtalkId);
        if (templateDingtalk is null)
            return result;

        _mapper.Map(templateDingtalk, result);

        var dingtalkRobots = await _dbContext.ManagerNotifyTemplateDingtalkRobots.AsNoTracking()
            .Where(x => x.TemplateDingtalkId == templateDingtalkId)
            .ToListAsync();

        result.DingtalkRobot = _mapper.Map<List<ManagerNotifyTemplateDingtalkRobot>, List<SaveNotifyDingtalkRobotItem>>(dingtalkRobots);

        return result;
    }

    /// <summary>
    /// 创建钉钉模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task AddAsync(ManagerNotifyTemplateDingtalkDetailDto input)
    {
        var templateDingtalk = new ManagerNotifyTemplateDingtalk
        {
            TemplateName = input.TemplateName,
            CreateTime = DateTime.Now
        };

        await _dbContext.ManagerNotifyTemplateDingtalks.AddAsync(templateDingtalk);
        // 添加钉钉机器人配置
        if (input.DingtalkRobot is not null && input.DingtalkRobot.Any())
        {
            var addDingtalkRobots = input.DingtalkRobot
               .Select(x => new ManagerNotifyTemplateDingtalkRobot()
               {
                   TemplateDingtalkId = templateDingtalk.Id,
                   GroupName = x.GroupName,
                   WebHook = x.WebHook,
                   Salt = x.Salt,
                   CountryCode = x.GetCountryCodeStr(),
               });
            await _dbContext.ManagerNotifyTemplateDingtalkRobots.AddRangeAsync(addDingtalkRobots);
        }
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 修改钉钉模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task UpdateAsync(ManagerNotifyTemplateDingtalkDetailDto input)
    {
        var templateDingtalk = await _dbContext.ManagerNotifyTemplateDingtalks.FindAsync(input.Id);
        if (templateDingtalk is null)
            return;

        _mapper.Map(input, templateDingtalk);

        _dbContext.ManagerNotifyTemplateDingtalks.Update(templateDingtalk);
        // 添加钉钉机器人配置
        if (input.DingtalkRobot is not null && input.DingtalkRobot.Any())
        {
            //删除机器人
            await DeleteDingtalkRobotAsync(input.Id);

            // 添加新的机器人配置
            var addDingtalkRobots = input.DingtalkRobot
               .Select(x => new ManagerNotifyTemplateDingtalkRobot()
               {
                   TemplateDingtalkId = templateDingtalk.Id,
                   GroupName = x.GroupName,
                   WebHook = x.WebHook,
                   Salt = x.Salt,
                   CountryCode = x.GetCountryCodeStr(),
               });
            await _dbContext.ManagerNotifyTemplateDingtalkRobots.AddRangeAsync(addDingtalkRobots);

        }
        await _dbContext.SaveChangesAsync();
    }

    public Task<ManagerNotifyTemplateDingtalkRobotSwitcheDto?> GetSwitche(NotifyEventSubType notifyEventSubType)
    {
        return _dbContext.ManagerNotifyTemplateDingtalkRobotSwitches.AsNoTracking()
            .Where(x => x.NotifyEventSubType == notifyEventSubType)
            .Select(x => new ManagerNotifyTemplateDingtalkRobotSwitcheDto
            {
                NotifyEventType = x.NotifyEventType,
                NotifyEventSubType = x.NotifyEventSubType,
                TemplateDingtalkId = x.TemplateDingtalkId
            })
            .FirstOrDefaultAsync();
    }

}
