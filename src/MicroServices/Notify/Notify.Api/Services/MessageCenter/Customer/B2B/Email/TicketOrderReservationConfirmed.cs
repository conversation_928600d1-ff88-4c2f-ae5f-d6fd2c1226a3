using Contracts.Common.Notify.Enums;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.B2B.Email;

//券类 - 预约确认
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Ticket_ReservationConfirm, NotifyMode.Email, NotifyChannel.B2b)]
public class TicketOrderReservationConfirmed : EmailMessageModelBase
{
    #region Values

    private struct TicketOrderReservationConfirmedValues
    {
        public string Addressee { get; set; }
        public long OrderId { get; set; }
        public string ProductName { get; set; }
        public string SkuName { get; set; }
        public DateTime TravelDateBegin { get; set; }
        public DateTime TravelDateEnd { get; set; }
        public int Nights { get; set; }
        public int Quantity { get; set; }
        public string[] Travelers { get; set; }
        public decimal Amount { get; set; }
    }

    #endregion

    public TicketOrderReservationConfirmed(
        JsonElement variables,
        IParamsSupportService paramsSupportService,
        long tenantId) : base(tenantId)
    {
        var item = JsonSerializer.Deserialize<TicketOrderReservationConfirmedValues>(variables, JsonSerializerHelper.serializerOptions);
        Addressee = item.Addressee;
        OrderId = item.OrderId.ToString();
        ProductName = item.ProductName;
        SkuName = item.SkuName;
        TravelDateBegin = item.TravelDateBegin.ToString("yyyy-MM-dd");
        TravelDateEnd = item.TravelDateEnd.ToString("yyyy-MM-dd");
        Nights = item.Nights.ToString();
        Quantity = item.Quantity.ToString();
        Travelers = string.Join("; ", item.Travelers);
        Amount = item.Amount.ToString("0.00");

        var tenantConfig = paramsSupportService.GetTenantSysConfig(tenantId)
        .GetAwaiter()
        .GetResult();

        string currencyCode = tenantConfig.CurrencyCode;
        // ￥ 500 CNY
        Amount = $"{GetCurrencySymbol(currencyCode)} {Amount} {currencyCode}";

        var subDomain = tenantConfig.Subdomain;
        var agencyWebDomain = paramsSupportService.GetSysSetting().AgencyWeb;
        OrderUrl = $"http://{string.Format(agencyWebDomain, subDomain)}";///ticket/{OrderId}";
    }

    public override string Addressee { get; protected set; }

    public string OrderUrl { get; private set; }

    public string OrderId { get; private set; }

    public string ProductName { get; private set; }

    public string SkuName { get; private set; }

    public string TravelDateBegin { get; set; }

    public string TravelDateEnd { get; set; }

    public string Nights { get; set; }

    public string Quantity { get; private set; }

    public string Travelers { get; private set; }

    public string Amount { get; private set; }
}