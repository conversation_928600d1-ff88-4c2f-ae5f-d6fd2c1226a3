using Contracts.Common.Notify.Enums;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.B2B.Sms;

//日历酒店 - 下单成功
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Hotel_CreateOrder, NotifyMode.Sms, NotifyChannel.B2b)]
public class HotelOrderCreated : SmsMessageModelBase
{
    #region 必传参数列表

    public override string PhoneNumber { get; set; }
    public string HotelName { get; set; }
    public string RoomName { get; set; }
    public DateTime CheckInDate { get; set; }
    public DateTime CheckOutDate { get; set; }
    public int Quantity { get; set; }

    #endregion

    //for deserialize
    public HotelOrderCreated()
    {
    }

    public HotelOrderCreated(JsonElement variables, IParamsSupportService paramsSupportService, long tenantId)
    {
        var item = variables.Deserialize<HotelOrderCreated>(JsonSerializerHelper.serializerOptions);
        //var subDomain = paramsSupportService.GetTenantSysConfig(tenantId).Result.Subdomain;
        //var agencyWebDomain = paramsSupportService.GetSysSetting().AgencyWeb;
        //var originalUrl = $"http://{string.Format(agencyWebDomain, subDomain)}/#/pages/order/mallProductOrderDetail/mallProductOrderDetail?baseOrderId={item.BaseOrderId}";
        //var shortUrl = paramsSupportService.CreateShortUrl(originalUrl).Result;
        var temp = new string[]
        {
            CutTheStr(item.HotelName),
            CutTheStr(item.RoomName),
            item.CheckInDate.ToString("yyyy-MM-dd"),
            $"{item.Quantity}间 {(item.CheckOutDate-item.CheckInDate).Days}晚"
        };
        BuidContentFormat(temp);
        PhoneNumber = item.PhoneNumber;
    }

}
