using Contracts.Common.Notify.Enums;
using Contracts.Common.WeChat.DTOs;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.B2B.Email;

//账号通知 - 分销商账号变更通知
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Account_AgencyUserChange, NotifyMode.Email, NotifyChannel.B2b)]
public class AccountAgencyUserChange : EmailMessageModelBase
{
    #region 必传参数列表

    public override string Addressee { get; protected set; }

    /// <summary>
    /// 品牌名称
    /// </summary>
    public string BrandName { get; set; }

    /// <summary>
    /// 品牌英文名称
    /// </summary>
    public string BrandENName { get; set; }

    /// <summary>
    /// 账号
    /// </summary>
    public string UserName { get; set; }
    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public long AgencyId { get; set; }

    public string? AgencyName { get; set; }

    public string WebLink { get; set; }

    public string WechatBaseLink { get; set; }

    public string SalespersonZHText { get; set; }

    public string SalespersonENText { get; set; }

    #endregion


    public AccountAgencyUserChange(
       JsonElement variables,
       IParamsSupportService paramsSupportService,
       long tenantId) : base(tenantId)
    {

        var item = JsonSerializer.Deserialize<AccountAgencyUserChangeValue>(variables, JsonSerializerHelper.serializerOptions);
        Addressee = item.Addressee;
        BrandName = item.BrandName;
        BrandENName = item.BrandENName;
        UserName = item.UserName;
        Password = item.Password;
        AgencyId = item.AgencyId;

        var tenantSysConfig = paramsSupportService.GetTenantSysConfig(tenantId).GetAwaiter().GetResult();
        var sysConfig = paramsSupportService.GetSysSetting();

        WebLink = tenantSysConfig.AgencyWebHost;

        var agency = paramsSupportService.GetAgencyDetails(tenantId, item.AgencyId).GetAwaiter().GetResult();
        if (agency is not null)
        {
            AgencyName = agency.FullName;
            if (agency.SalespersonId.HasValue)
            {
                var salespersonUsers = paramsSupportService.GetTenantUserInfo(new long[] { agency.SalespersonId.Value! })
                                                           .GetAwaiter()
                                                           .GetResult();

                var salespersonUser = salespersonUsers?.FirstOrDefault(x => x.Id == agency.SalespersonId.Value!);
                if (salespersonUser is not null && string.IsNullOrEmpty(salespersonUser.PhoneNumber) is false)
                {
                    SalespersonZHText = $"如遇问题，可联系销售经理：{salespersonUser.PhoneNumber}";
                    SalespersonENText = $"In case of any issues, please contact the sales manager:{salespersonUser.PhoneNumber}";
                }
            }

        }
        if (string.IsNullOrEmpty(SalespersonZHText))
        {
            SalespersonZHText = $"请妥善保管";
        }
        if (string.IsNullOrEmpty(SalespersonENText))
        {
            SalespersonENText = "Please keep it properly.";
        }


        // 获取小程序短链接
        string wechatqrcode = paramsSupportService.GetWxacodeUnlimit(tenantId, new GetWxacodeUnlimitInput
        {
            PagePath = "pages/index/index",
            Scene = "0"
        })
        .GetAwaiter()
        .GetResult();

        WechatBaseLink = "data:image/png;base64," + wechatqrcode;
        
    }

    public class AccountAgencyUserChangeValue
    {
        public string Addressee { get; set; }

        /// <summary>
        /// 品牌名称
        /// </summary>
        public string BrandName { get; set; }

        /// <summary>
        /// 品牌英文名称
        /// </summary>
        public string BrandENName { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 分销商Id
        /// </summary>
        public long AgencyId { get; set; }
    }
}
