using Contracts.Common.Notify.Enums;
using Contracts.Common.Order.DTOs.GroupBookingOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.WeChat.DTOs;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.B2B.Sms;


//日历酒店 - 团房入住人提醒
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Hotel_GroupRoom_Guests_Reminder, NotifyMode.Sms, NotifyChannel.B2b)]
public class HotelGroupRoomGuestsReminder : SmsMessageModelBase
{
    public override string PhoneNumber { get; set; }
    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }
    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime CheckInDate { get; set; }
    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime CheckOutDate { get; set; }

    /// <summary>
    /// 品牌名称
    /// </summary>
    public string BrandName { get; set; }

    /// <summary>
    /// 添加入住人提醒 入住日前几天
    /// </summary>
    public DateTime? RemindAdvanceTime { get; set; }

    /// <summary>
    /// 账号类型
    /// </summary>
    public UserType UserType { get; set; }

    public long GroupBookingApplicationFormId { get; set; }

    public HotelGroupRoomGuestsReminder()
    {

    }

    public HotelGroupRoomGuestsReminder(JsonElement variables, IParamsSupportService paramsSupportService, long tenantId)
    {
        //入住人维护提醒：您好，您申请的[{1}]出行日期：{2}的 团房单，请及时于{3}前登录平台在[订单→团房订单]填写入住人信息，若填写请忽略 --{4}
        var item = JsonSerializer.Deserialize<HotelGroupRoomGuestsReminder>(variables, JsonSerializerHelper.serializerOptions);

        //string version = paramsSupportService.IsTesting() ? "trial" : "release";

        //string link;

        //if (paramsSupportService.IsTesting())
        //{
        //    // 测试环境请求使用agency链接 替代小程序链接
        //    link = paramsSupportService.GetTenantSysConfig(tenantId).GetAwaiter().GetResult().AgencyWebHost;
        //}
        //else
        //{
        //    // 生产环境请求获取小程序短链接
        //    link = paramsSupportService.GenerateURLLink(tenantId, new GenerateURLLinkInput
        //    {
        //        Path = "pages/group/apply-detail/index",
        //        Query = $"id={item.GroupBookingApplicationFormId}&tab=quotation",
        //        EnvVersion = version
        //    })
        //    .GetAwaiter()
        //    .GetResult();
        //}

        //link = paramsSupportService.CreateShortUrl(link).GetAwaiter().GetResult();

        string hotelName = string.IsNullOrEmpty(item.HotelName) ? "无指定酒店" : item.HotelName;

        var temp = new string[]
        {
            CutTheStr(hotelName),
            ForCheckInOutTime(item.CheckInDate,item.CheckOutDate),
            ForDate(item.RemindAdvanceTime.GetValueOrDefault()),
            //link[..20],
            //link[20..],
            item.GroupBookingApplicationFormId.ToString() ?? string.Empty,
            CutTheStr(item.BrandName)
        };
        BuidContentFormat(temp);
        PhoneNumber = item.PhoneNumber;
    }
}
