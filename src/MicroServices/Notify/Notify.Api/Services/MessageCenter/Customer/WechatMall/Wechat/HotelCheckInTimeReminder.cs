using Contracts.Common.Notify.Enums;
using Contracts.Common.WeChat.DTOs;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.WechatMall.Wechat;

//日历酒店 - 入住提醒
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Hotel_CheckIn_TimeReminder, NotifyMode.Wechat, NotifyChannel.WechatMall)]
public class HotelCheckInTimeReminder : CustomerWechatMessageModelBase
{
    #region 必传参数列表

    public override long UserId { get; set; }
    public long BaseOrderId { get; set; }
    public string HotelName { get; set; }
    public string RoomName { get; set; }
    public string SkuName { get; set; }
    public DateTime CheckInDate { get; set; }
    public DateTime CheckOutDate { get; set; }

    #endregion

    //for deserialize
    public HotelCheckInTimeReminder()
    {
    }

    public HotelCheckInTimeReminder(JsonElement variables)
    {
        var item = JsonSerializer.Deserialize<HotelOrderConfirmed>(variables, JsonSerializerHelper.serializerOptions);
        UserId = item.UserId;
        Url = $"/#/pages/order/hotelOrderDetail/hotelOrderDetail?baseOrderId={item.BaseOrderId}";
        Data = new List<WechatMessageTempalteSendData>
        {
            new WechatMessageTempalteSendData
            {
                Key = "thing5",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = "【入住提醒】"+item.RoomName + $"({item.SkuName})"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "thing1",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = item.HotelName
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "time3",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{item.CheckInDate:yyyy-MM-dd}~{item.CheckOutDate:yyyy-MM-dd}"
                }
            }
        };
    }
}
