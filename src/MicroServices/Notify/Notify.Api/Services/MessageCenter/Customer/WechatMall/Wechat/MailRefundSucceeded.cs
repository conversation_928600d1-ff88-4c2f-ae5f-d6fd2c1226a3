using Contracts.Common.Notify.Enums;
using Contracts.Common.WeChat.DTOs;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.WechatMall.Wechat;

//邮寄订单 - 退款成功
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Mail_RefundSucceeded, NotifyMode.Wechat, NotifyChannel.WechatMall)]
public class MailRefundSucceeded : CustomerWechatMessageModelBase
{
    #region 必传参数列表

    public override long UserId { get; set; }
    public long BaseOrderId { get; set; }
    public long RefundOrderId { get; set; }
    public string ProductName { get; set; }
    public string SkuName { get; set; }
    public decimal RefundAmount { get; set; }

    #endregion

    //for deserialize
    public MailRefundSucceeded()
    {
    }

    public MailRefundSucceeded(JsonElement variables)
    {
        var item = JsonSerializer.Deserialize<MailRefundSucceeded>(variables, JsonSerializerHelper.serializerOptions);

        UserId = item.UserId;
        Url = $"/#/pages/order/refundProgress/refundProgress?refundOrderId={item.RefundOrderId}";
        Data = new List<WechatMessageTempalteSendData>
        {
            new WechatMessageTempalteSendData
            {
                Key = "thing2",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = item.ProductName+$"({item.SkuName})"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "amount3",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{item.RefundAmount:0.00}"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "character_string1",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = item.BaseOrderId.ToString()
                }
            }
        };
    }
}
