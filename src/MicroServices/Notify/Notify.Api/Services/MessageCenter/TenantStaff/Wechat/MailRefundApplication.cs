using Contracts.Common.Notify.Enums;
using Contracts.Common.WeChat.DTOs;
using Notify.Api.Services.MessageCenter.MessageModelBase;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.TenantStaff.Wechat;

//邮寄订单 - 退款申请
[MessageModelMatching(SendToTheRole.TenantStaff, NotifyEventSubType.Mail_RefundApplication, NotifyMode.Wechat)]
public class MailRefundApplication : StaffWechatMessageModelBase
{
    #region 必传参数列表

    public override long SupplierId { get; set; }
    public override long AgencyId { get; set; }
    public string ProductName { get; set; }
    public string SkuName { get; set; }
    public decimal RefundAmount { get; set; }
    public long BaseOrderId { get; set; }
    #endregion

    //for deserialize
    public MailRefundApplication()
    {
    }

    public MailRefundApplication(JsonElement variables)
    {
        var item = JsonSerializer.Deserialize<MailRefundApplication>(variables, JsonSerializerHelper.serializerOptions);

        SupplierId = item.SupplierId;
        AgencyId = item.AgencyId;
        Data = new List<WechatMessageTempalteSendData>
        {
            new WechatMessageTempalteSendData
            {
                Key = "thing8",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = item.ProductName + $"({item.SkuName})"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "amount4",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{item.RefundAmount}"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "time3",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "number2",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{item.BaseOrderId}"
                }
            },
        };
    }
}