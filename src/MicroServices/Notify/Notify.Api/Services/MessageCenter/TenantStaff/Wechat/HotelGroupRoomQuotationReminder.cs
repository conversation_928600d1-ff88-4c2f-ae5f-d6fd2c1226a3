using Contracts.Common.Notify.Enums;
using Contracts.Common.WeChat.DTOs;
using Notify.Api.Services.MessageCenter.MessageModelBase;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.TenantStaff.Wechat;

//团房酒店-报价通知
[MessageModelMatching(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_Quoted, NotifyMode.Wechat)]
[MessageModelMatching(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_UnableQuote, NotifyMode.Wechat)]
[MessageModelMatching(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_Expired, NotifyMode.Wechat)]
public class HotelGroupRoomQuotationReminder : StaffWechatMessageModelBase
{
    #region 必传参数

    public override long SupplierId { get; set; }
    public override long AgencyId { get; set; }

    /// <summary>
    /// 分销商名称
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    /// <summary>
    /// 申请单Id
    /// </summary>
    public string OrderId { get; set; }

    public NotifyEventSubType NotifyEventSubType { get; set; }

    /// <summary>
    /// 服务地点
    /// </summary>
    public const string ServiceTarget = "SAAS管理后台";

    #endregion

    public HotelGroupRoomQuotationReminder()
    {

    }

    public HotelGroupRoomQuotationReminder(JsonElement variables)
    {
        var item = JsonSerializer.Deserialize<HotelGroupRoomQuotationReminder>(variables, JsonSerializerHelper.SerializerOptionsExtend);
        //SupplierId = item.SupplierId;
        //AgencyId = item.AgencyId;
        Users = item.Users;
        OrderId = item.OrderId;
        AgencyName = item.AgencyName;
        HotelName = item.HotelName;
        NotifyEventSubType = item.NotifyEventSubType;

        string OrderChannel = string.Empty;

        switch (item.NotifyEventSubType)
        {
            case NotifyEventSubType.Hotel_GroupRoom_Quoted:
                OrderChannel = "酒店发单已回复报价，请尽快跟进";
                break;
            case NotifyEventSubType.Hotel_GroupRoom_UnableQuote:
                OrderChannel = "酒店发单无法报价，请尽快跟进";
                break;
            case NotifyEventSubType.Hotel_GroupRoom_Expired:
                OrderChannel = "酒店发单已过期，请尽快跟进";
                break;

            default:
                break;
        }


        Data = new List<WechatMessageTempalteSendData>
        {
            new() {
                Key = "thing12",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = OrderChannel
                }
            },
            new() {
                Key = "thing18",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = AgencyName
                }
            },
            new() {
                Key = "thing13",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = HotelName
                }
            },
            new() {
                Key = "thing21",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = ServiceTarget
                }
            },
            new() {
                Key = "character_string2",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = OrderId
                }
            },
        };
    }
}

