using Common.ServicesHttpClient;
using Contracts.Common.Notify.DTOs.SiteMessage;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.User.DTOs.AgencyUser;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Notify.Api.Extensions;
using Notify.Api.Services.Interfaces;
using Notify.Api.Services.Sender;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace Notify.Api.Services.MessageCenter;

public interface ISiteMessageBuilderService
{
    Task<SiteMessageMessageModel> Build(NotifyMessageProcessMessage dto);
}

public class SiteMessageBuilderService : ISiteMessageBuilderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IParamsSupportService _paramsSupportService;
    private readonly ISiteMessageService _notificationService;
    private readonly IHostEnvironment _hostEnvironment;
    private readonly ServicesAddress _servicesAddress;

    public SiteMessageBuilderService(CustomDbContext dbContext,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress,
        IParamsSupportService paramsSupportService,
        ISiteMessageService notificationService,
        IHostEnvironment hostEnvironment)
    {
        _dbContext = dbContext;
        _httpClientFactory = httpClientFactory;
        _paramsSupportService = paramsSupportService;
        _notificationService = notificationService;
        _hostEnvironment = hostEnvironment;
        _servicesAddress = servicesAddress.Value;
    }
    public async Task<SiteMessageMessageModel> Build(NotifyMessageProcessMessage dto)
    {
        if (!TryGetTemplate(dto.NotifyEventSubType, out var template))
            return null;

        Func<MessageModelMatchingAttribute, bool> predicate = s => s.SendToTheRole == dto.SendToTheRole
               && s.NotifyEventSubType == dto.NotifyEventSubType
               && s.NotifyMode == NotifyMode.SiteMessage;
        object? messageModel;
        // 判断 Variables 类型 是否是 JsonElement 不是则需要转换
        if (dto.Variables is not JsonElement)
        {
            var json = JsonConvert.SerializeObject(dto.Variables);
            JsonElement jsonElement = JsonDocument.Parse(json).RootElement;
            messageModel = MessageModelFinder.CreateMessageModel(predicate, jsonElement, template);
        }
        else
        {
            messageModel = MessageModelFinder.CreateMessageModel(predicate, dto.Variables, template);
        }
        SiteMessageModelBase? notificationMessageModel = messageModel as SiteMessageModelBase;
        List<long> userIds = new();
        if (notificationMessageModel.UserId is > 0)
            userIds.Add(notificationMessageModel.UserId.Value);
        if (template.ObjectType == SiteMessageObjectType.All)
        {
            var users = await _paramsSupportService.GetAgencyUsers(new GetDetailsInput
            {
                AgencyId = notificationMessageModel.AgencyId,//分销商id
                Enabled = true,
            });
            userIds = users.Select(x => x.AgencyUserId).ToList();
        }
        if (userIds.Any() is not true)//无需要通知用户的消息
            return null;

        var siteMessageId = await _notificationService.Add(new Contracts.Common.Notify.DTOs.SiteMessage.AddInput
        {
            UserIds = userIds,
            AgencyId = notificationMessageModel.AgencyId,
            Title = notificationMessageModel.Title,
            Content = notificationMessageModel.Content,
            EnTitle = notificationMessageModel.EnTitle,
            EnContent = notificationMessageModel.EnContent,
            LinkId = notificationMessageModel.LinkId,
            LinkType = notificationMessageModel.LinkType,
            ObjectType = template.ObjectType,
            NotifyEventType = template.NotifyEventType,
            NotifyEventSubType = template.NotifyEventSubType,
            TenantId = dto.TenantId,
        });
        SiteMessageMessageModel siteMessageMessage = new()
        {
            Message = new { SiteMessagesId = $"{siteMessageId}" }
        };
        switch (template.ObjectType)
        {
            case SiteMessageObjectType.All:
                siteMessageMessage.GroupId = notificationMessageModel.AgencyId.ToString();
                break;
            case SiteMessageObjectType.Specified:
                siteMessageMessage.UserIds = userIds.Select(x => x.ToString()).ToList();
                break;
        }
        return siteMessageMessage;
    }

    private bool TryGetTemplate(NotifyEventSubType notifyEventSubType, out NotificationTemplate template)
    {
        template = _dbContext.SiteMessageTemplateLibraries
            .Where(x => x.NotifyEventSubType == notifyEventSubType)
            .Select(x => new NotificationTemplate(x.NotifyEventType, x.NotifyEventSubType, x.ObjectType, x.LinkType,x.Title,x.Content,x.EnTitile,x.EnContent))
            .FirstOrDefault();
        return template != null;
    }

}
