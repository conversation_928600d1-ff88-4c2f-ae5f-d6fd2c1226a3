using AutoMapper;
using AutoMapper.QueryableExtensions;
using Common.Message;
using Common.ServicesHttpClient;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.User.DTOs.ManageUser;
using Contracts.Common.User.DTOs.TenantUser;
using Fluid;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MimeKit.Text;
using Newtonsoft.Json;
using Notify.Api.Extensions;
using Notify.Api.Services.Sender;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter;

public interface IEmailBuilderService
{
    /// <summary>
    /// 消息内容构建
    /// </summary>
    /// <param name="dto"></param>
    /// <returns>
    /// 可空
    /// <para>- TenantEmailBoxs 未配置</para>
    /// <para>- 模板库未匹配到当前通知事件模板</para>
    /// </returns>
    Task<EmailMessageModel> Build(NotifyMessageProcessMessage dto);
}

public class EmailBuilderService : IEmailBuilderService
{
    private static readonly FluidParser _parser = new();
    /// <summary>
    /// https://github.com/sebastienros/fluid#allow-listing-object-members
    /// </summary>
    private static readonly TemplateOptions _allowAnyProperty = new() { MemberAccessStrategy = new UnsafeMemberAccessStrategy() };

    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IParamsSupportService _paramsSupportService;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IHostEnvironment _hostEnvironment;

    public EmailBuilderService(
        CustomDbContext dbContext,
        IMapper mapper,
        IParamsSupportService paramsSupportService,
        IOptions<ServicesAddress> options,
        IHttpClientFactory httpClientFactory,
        IHostEnvironment hostEnvironment)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _paramsSupportService = paramsSupportService;
        _servicesAddress = options.Value;
        _httpClientFactory = httpClientFactory;
        _hostEnvironment = hostEnvironment;
    }

    public async Task<EmailMessageModel> Build(NotifyMessageProcessMessage dto)
    {
        //邮箱Server
        EmailServerConfig? emailServer = null;
        //财务通知用专属邮箱
        if (dto.NotifyEventSubType is NotifyEventSubType.Financial_Charge
            || dto.NotifyEventSubType is NotifyEventSubType.Financial_CreditNotEnough
            || dto.NotifyEventSubType is NotifyEventSubType.Financial_OrderStatements)
        {
            emailServer = await _dbContext.TenantEmailBoxs
                .AsNoTracking()
                .IgnoreQueryFilters()
                .Where(s => s.TenantId == dto.TenantId && s.TenantEmailBoxType.Equals(TenantEmailBoxType.FinancialNotify))
                .ProjectTo<EmailServerConfig>(_mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }
        else
        {
            emailServer = await _dbContext.TenantEmailBoxs
                .AsNoTracking()
                .IgnoreQueryFilters()
                .Where(s => s.TenantId == dto.TenantId)
                .ProjectTo<EmailServerConfig>(_mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }


        if (emailServer is null)
            return null;

        //模板内容
        if (TryGetTemplate(dto.SendToTheRole,
                dto.NotifyEventSubType,
                dto.NotifyChannel,
                out string subjectTemplate,
                out string contentTemplate) == false)
            return null;

        //构建、渲染
        Func<MessageModelMatchingAttribute, bool> predicate = s => s.SendToTheRole == dto.SendToTheRole
                && s.NotifyEventSubType == dto.NotifyEventSubType
                && s.NotifyMode == NotifyMode.Email
                && s.NotifyChannel == dto.NotifyChannel;

        object? messageModel;
        // 判断 Variables 类型 是否是 JsonElement 不是则需要转换
        if (dto.Variables is not JsonElement)
        {
            var json = JsonConvert.SerializeObject(dto.Variables);
            JsonElement jsonElement = JsonDocument.Parse(json).RootElement;
            messageModel = MessageModelFinder
            .CreateMessageModel(predicate, jsonElement, _paramsSupportService, dto.TenantId);
        }
        else
        {
            messageModel = MessageModelFinder
              .CreateMessageModel(predicate, dto.Variables, _paramsSupportService, dto.TenantId);
        }


        var subject = RenderTemplate(messageModel, subjectTemplate);
        var content = RenderTemplate(messageModel, contentTemplate);

        var emailMessage = messageModel as EmailMessageModelBase;

        var toAddressee = new string[] { emailMessage.Addressee };

        var userId = await GetUserEmails(dto);

        if (userId?.Any() is true)
        {
            var address = await GetUserEmailAddress(dto, userId.ToArray());
            if (address?.Any() is true)
            {
                toAddressee = address
                    .Append(emailMessage.Addressee)
                    .Where(x => !string.IsNullOrEmpty(x))
                    .ToArray();
            }
        }

        var toCcAddressee = emailMessage.CcAddressee;

        var attachmentFilePaths = messageModel.GetType().GetProperty("AttachmentFilePaths")?.GetValue(messageModel);
        var attachmentInfos = new List<AttachmentInfo>();
        if (attachmentFilePaths is not null)
        {
            foreach (var item in attachmentFilePaths as List<AttachmentFilePath>)
            {
                var client = _httpClientFactory.CreateClient();
                var response =
                    await client.GetAsync(_servicesAddress.Resource_FileDownloadOssObject(item.FilePath));
                var data = await response.Content.ReadAsByteArrayAsync();
                attachmentInfos.Add(new AttachmentInfo
                {
                    ContentType = item.ContentType,
                    FileName = item.FileName,
                    Data = data,
                });
            }
        }

        //旧附件发送方式
        var attachments = messageModel.GetType().GetProperty("Attachments")?.GetValue(messageModel);
        if (attachments is not null)
        {
            foreach (var item in attachments as List<Attachment>)
            {
                attachmentInfos.Add(new AttachmentInfo
                {
                    ContentType = item.ContentType,
                    FileName = item.FileName,
                    Data = item.Data,
                });
            }
        }

        return new EmailMessageModel
        {
            EmailServer = emailServer,
            Subject = subject,
            Content = content,
            ToAddress = toAddressee,
            ToCcAddress = toCcAddressee,
            TextFormat = TextFormat.Html,
            Attachments = attachmentInfos
        };
    }

    private bool TryGetTemplate(
        SendToTheRole sendToTheRole,
        NotifyEventSubType notifyEventSubType,
        NotifyChannel notifyChannel,
        out string subjectTemplate,
        out string contentTemplate)
    {
        subjectTemplate = contentTemplate = null;
        switch (sendToTheRole)
        {
            case SendToTheRole.Customer:
                {
                    var template = _dbContext.CustomerNotifyTemplateLibrary
                        .AsNoTracking()
                        .IgnoreQueryFilters()
                        .FirstOrDefault(s => s.NotifyEventSubType == notifyEventSubType
                        && s.NotifyChannel == notifyChannel
                        && !string.IsNullOrWhiteSpace(s.EmailTemplateTitle)
                        && !string.IsNullOrWhiteSpace(s.EmailTemplateContent));
                    if (template is null)
                        return false;
                    subjectTemplate = template.EmailTemplateTitle;
                    contentTemplate = template.EmailTemplateContent;
                }
                break;
            case SendToTheRole.TenantStaff:
                {
                    var template = _dbContext.StaffNotifyTemplateLibrary
                        .AsNoTracking()
                        .IgnoreQueryFilters()
                        .FirstOrDefault(s => s.NotifyEventSubType == notifyEventSubType
                        && !string.IsNullOrWhiteSpace(s.EmailTemplateTitle)
                        && !string.IsNullOrWhiteSpace(s.EmailTemplateContent));
                    if (template is null)
                        return false;
                    subjectTemplate = template.EmailTemplateTitle;
                    contentTemplate = template.EmailTemplateContent;
                }
                break;
            default:
                subjectTemplate = string.Empty;
                contentTemplate = string.Empty;
                return false;
        }
        return true;
    }

    private static string RenderTemplate(object model, string templateContent)
    {
        var template = _parser.Parse(templateContent);
        var templateContext = new TemplateContext(model, _allowAnyProperty);
        var result = template.Render(templateContext);
        return result;
    }


    private async Task<IEnumerable<long>> GetUserEmails(NotifyMessageProcessMessage dto)
    {
        switch (dto.SendToTheRole)
        {
            case SendToTheRole.TenantStaff:
                var staffId = await _dbContext.StaffNotifyTemplateSetting
                    .AsNoTracking()
                    .IgnoreQueryFilters()
                    .Where(s => s.TenantId == dto.TenantId && s.StaffRole == StaffRole.SalesPerson && s.NotifyEventSubType == dto.NotifyEventSubType)
                    .Select(s => s.StaffId)
                    .ToListAsync();
                return staffId;

            default:
                return Enumerable.Empty<long>();
        }
    }

    private async Task<IEnumerable<string>> GetUserEmailAddress(NotifyMessageProcessMessage message, long[]? userIds)
    {
        if (userIds?.Any() is false)
            return Enumerable.Empty<string>();

        IEnumerable<string> result = new List<string>();

        switch (message.SendToTheRole)
        {
            case SendToTheRole.Customer:
            case SendToTheRole.SupplierStaff:
            case SendToTheRole.AgencyStaff:
            default:

                break;
            case SendToTheRole.TenantStaff:
                result = await GetTenantUserEmail(userIds);
                break;
            case SendToTheRole.ManagerStaff:
                result = await GetManageUserEmail(userIds);
                break;
        }

        return result;
    }

    private async Task<IEnumerable<string>> GetTenantUserEmail(long[]? userIds)
    {
        if (userIds?.Any() is false)
            return Enumerable.Empty<string>();

        var url = _servicesAddress.User_SearchTenantUser();

        var request = new SearchTenantUsersInput()
        {
            Ids = userIds,
        };

        var httpContent = new StringContent(
            JsonConvert.SerializeObject(request),
            Encoding.UTF8,
            "application/json");
        var tenantUsers = await _httpClientFactory
            .InternalPostAsync<IEnumerable<Contracts.Common.User.DTOs.UserSearchOuput>>(url, httpContent: httpContent);
        if (tenantUsers is not null && tenantUsers.Any())
        {
            return tenantUsers.Where(x => !string.IsNullOrWhiteSpace(x.Email)).Select(x => x.Email!);
        }
        else
        {
            return Enumerable.Empty<string>();
        }
    }

    private async Task<IEnumerable<string>> GetManageUserEmail(long[]? userIds)
    {
        if (userIds?.Any() is false)
            return Enumerable.Empty<string>();

        var url = _servicesAddress.User_GetListByIds();
        var httpContent = new StringContent(
            JsonConvert.SerializeObject(userIds),
            Encoding.UTF8,
            "application/json");
        var managerUsers = await _httpClientFactory.InternalPostAsync<IEnumerable<ManageUserDTO>>(url, httpContent: httpContent);
        if (managerUsers is not null && managerUsers.Any())
        {
            return managerUsers.Where(x => !string.IsNullOrWhiteSpace(x.Email)).Select(x => x.Email!);
        }
        else
        {
            return Enumerable.Empty<string>();
        }
    }

}
