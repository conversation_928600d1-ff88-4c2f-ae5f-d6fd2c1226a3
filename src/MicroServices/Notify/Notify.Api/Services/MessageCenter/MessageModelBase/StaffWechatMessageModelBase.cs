using Contracts.Common.Notify.DTOs.StaffNotify;

namespace Notify.Api.Services.MessageCenter.MessageModelBase;

public abstract class StaffWechatMessageModelBase : UsersWechatMessageModelBase
{
    public abstract long SupplierId { get; set; }

    public abstract long AgencyId { get; set; }

    /// <summary>
    /// 国家与区域代码
    /// </summary>
    public List<RegionCode>? RegionCodes { get; set; }

}


public abstract class UsersWechatMessageModelBase : WechatMessageModelBase
{
    /// <summary>
    /// 发送到的员工列表
    /// </summary>
    public StaffUserDto[] Users { get; set; } = Array.Empty<StaffUserDto>();
}

