using Contracts.Common.Notify.Enums;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Agency.SiteMessage;

[MessageModelMatching(SendToTheRole.AgencyStaff, NotifyEventSubType.PrePayment_Charged, NotifyMode.SiteMessage)]
public class PrePaymentCharged : SiteMessageModelBase
{
    public PrePaymentCharged()
    {
    }

    public PrePaymentCharged(JsonElement variables, NotificationTemplate notificationTemplate) : base(variables, notificationTemplate)
    {
        var item = JsonSerializer.Deserialize<PrePaymentCharged>(variables, JsonSerializerHelper.serializerOptions);
        Amount = item.Amount;
        CurrencyCode = item.CurrencyCode;
        AgencyId = item.AgencyId;

        base.RenderMessage(item, notificationTemplate);
    }
    public decimal Amount { get; set; }
    public string CurrencyCode { get; set; }
    public override long AgencyId { get; set; }
    public override long? UserId { get; set; }
}
