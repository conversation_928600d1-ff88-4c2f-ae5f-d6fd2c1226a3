using Contracts.Common.Notify.Enums;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Agency.SiteMessage;

[MessageModelMatching(SendToTheRole.AgencyStaff, NotifyEventSubType.CarProduct_OrderConfirmed, NotifyMode.SiteMessage)]
public class CarProductOrderConfirmed : SiteMessageModelBase
{
    public CarProductOrderConfirmed()
    {
    }

    public CarProductOrderConfirmed(JsonElement variables, NotificationTemplate notificationTemplate) : base(variables, notificationTemplate)
    {
        var item = JsonSerializer.Deserialize<CarProductOrderConfirmed>(variables, JsonSerializerHelper.serializerOptions);
        BaseOrderId = item.BaseOrderId;
        AgencyId = item.AgencyId;
        UserId = item.UserId;

        notificationTemplate.SetLinkId(item.BaseOrderId);
        base.RenderMessage(item, notificationTemplate);
    }

    public long BaseOrderId { get; set; }

    public override long AgencyId { get; set; }
    public override long? UserId { get; set; }
    /// <summary>
    /// 资源名称
    /// </summary>
    public string? ResourceName { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// 规格名称
    /// </summary>
    public string? ProductSkuName { get; set; }
}
