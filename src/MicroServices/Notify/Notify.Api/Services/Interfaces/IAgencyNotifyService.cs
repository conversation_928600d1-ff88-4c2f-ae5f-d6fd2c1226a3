using Contracts.Common.Notify.DTOs.AgencyNotify;

namespace Notify.Api.Services.Interfaces;

public interface IAgencyNotifyService
{
    /// <summary>
    /// 获取开关设置列表
    /// </summary>
    Task<List<GetAgencyNotifySwitchesOutput>> GetSwitches();
    /// <summary>
    /// 修改开关
    /// </summary>
    Task UpdateSwitch(UpdateAgencyNotifySwitchInput input);


    /// <summary>
    /// 新增模板
    /// </summary>
    Task<long> AddLibrary(AddAgencyNotifyLibraryInput input);
    /// <summary>
    /// 获取模板
    /// </summary>
    Task<GetAgencyNotifyLibraryOutput> GetLibrary(long id);
    /// <summary>
    /// 更新模板
    /// </summary>
    Task UpdateLibrary(UpdateAgencyNotifyLibraryInput input);
    /// <summary>
    /// 获取模板列表
    /// </summary>
    Task<List<GetAgencyNotifyLibrariesOutput>> GetLibraries();
}
