using Common.GlobalException;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Microsoft.EntityFrameworkCore;
using Notify.Api.Services.Interfaces;
using Notify.Api.Services.MessageCenter;
using Notify.Api.Services.Sender;

namespace Notify.Api.Services
{
    /// <summary>
    /// 消息通知事件处理
    /// </summary>
    public class MessageNotifyEventService : IMessageNotifyEventService
    {
        private readonly CustomDbContext _dbContext;
        private readonly IEnumerable<ISenderService> _senderServices;
        private readonly IEmailBuilderService _emailBuilderService;
        private readonly IWechatBuilderService _wechatBuilderService;
        private readonly ISmsBuilderService _smsBuilderService;
        private readonly ISiteMessageBuilderService _notificationBuilderService;
        private readonly IDingTalkBuilderService _dingTalkBuilderService;

        public MessageNotifyEventService(CustomDbContext dbContext,
            IEnumerable<ISenderService> senderServices,
            IEmailBuilderService emailBuilderService,
            IWechatBuilderService wechatBuilderService,
            ISmsBuilderService smsBuilderService,
            ISiteMessageBuilderService notificationBuilderService,
            IDingTalkBuilderService dingTalkBuilderService)
        {
            _dbContext = dbContext;
            _senderServices = senderServices;
            _emailBuilderService = emailBuilderService;
            _wechatBuilderService = wechatBuilderService;
            _smsBuilderService = smsBuilderService;
            _notificationBuilderService = notificationBuilderService;
            _dingTalkBuilderService = dingTalkBuilderService;
        }

        #region 订阅-通知中心

        public async Task<NotifyMessageProcessCallbackMessage> NotifyMessageProcess(NotifyMessageProcessMessage receive)
        {
            NotifyMessageProcessCallbackMessage callbackMessage = new() { MessageId = receive.MessageId ?? 0, IsSuccessed = true };

            if (receive.SendToTheRole != SendToTheRole.Customer)
                receive.NotifyChannel = NotifyChannel.None;

            //酒店渠道特殊处理，不做Switch 开关配置  HotelQuotation
            if (receive.NotifyChannel != NotifyChannel.Hotel)
            {
                if (receive.NotifyMode is not NotifyMode.DingTalkRobot)
                {
                    //是否开启
                    var isOpen = await CheckSwitch(receive);
                    if (isOpen == false)
                        return callbackMessage;
                }

            }

            ISenderService senderService = _senderServices.Single(s => s.Mode == receive.NotifyMode);
            switch (receive.NotifyMode)
            {
                case NotifyMode.Wechat:
                    {
                        var messages = await _wechatBuilderService.Build(receive);
                        foreach (var msg in messages)
                        {
                            await senderService.Send(msg);
                        }
                    }
                    break;
                case NotifyMode.Sms:
                    {
                        var message = await _smsBuilderService.Build(receive);
                        if (message is null)
                            break;
                        await senderService.Send(message);
                    }
                    break;
                case NotifyMode.Email:
                    {
                        var message = await _emailBuilderService.Build(receive);
                        if (message is null)
                            break;
                        var result = await senderService.Send(message);
                        callbackMessage.IsSuccessed = result.IsSuccessed;
                        if (result.EmailSmtpStatusCode.HasValue)
                        {
                            callbackMessage.EmailSmtpStatusCode = result.EmailSmtpStatusCode.Value;
                        }
                        if (!callbackMessage.IsSuccessed && !receive.MessageId.HasValue)
                        {
                            switch (result.EmailSmtpStatusCode)
                            {
                                case EmailSmtpStatusCode.Success:
                                    break;
                                case EmailSmtpStatusCode.RecipientNotAccepted:
                                    //对邮箱不正确进行拦截
                                    break;
                                default:
                                    throw new Exception($"{result.Message}({result.EmailSmtpStatusCode})");
                                    break;
                            }
                        }
                    }
                    break;
                case NotifyMode.SiteMessage:
                    {
                        var message = await _notificationBuilderService.Build(receive);
                        if (message is not null)
                        {
                            await senderService.Send(message);
                        }
                    }
                    break;
                case NotifyMode.DingTalkRobot:
                    {
                        var messages = await _dingTalkBuilderService.Build(receive);
                        foreach (var msg in messages)
                        {
                            try { await senderService.Send(msg); }
                            catch (BusinessException) { }
                            catch { throw; }
                        }
                    }
                    break;
                default:
                    throw new NotImplementedException();

            }
            return callbackMessage;
        }

        private async Task<bool> CheckSwitch(NotifyMessageProcessMessage receive)
        {
            bool isOpen = false;
            if (receive.NotifyMode == NotifyMode.SiteMessage)
            {
                isOpen = true;
                return isOpen;
            }
            switch (receive.SendToTheRole)
            {
                case SendToTheRole.Customer:
                    {
                        var sw = await _dbContext.CustomerNotifyTemplateSwitch
                            .AsNoTracking()
                            .IgnoreQueryFilters()
                            .FirstOrDefaultAsync(s => s.TenantId == receive.TenantId
                                && s.NotifyEventSubType == receive.NotifyEventSubType
                                && s.NotifyChannel == receive.NotifyChannel);
                        if (sw is null)
                            return isOpen;
                        isOpen = receive.NotifyMode switch
                        {
                            NotifyMode.Wechat => sw.WechatNotifyIsOpen,
                            NotifyMode.Sms => sw.SmsNotifyIsOpen,
                            NotifyMode.Email => sw.EmailNotifyIsOpen,
                            _ => false
                        };
                    }
                    break;
                case SendToTheRole.SupplierStaff:
                case SendToTheRole.TenantStaff:
                    {
                        var sw = await _dbContext.StaffNotifyTemplateSwitch
                            .IgnoreQueryFilters()
                            .Where(s => s.TenantId == receive.TenantId
                                && s.NotifyEventSubType == receive.NotifyEventSubType)
                            .FirstOrDefaultAsync();

                        if (sw is null)
                            return isOpen;

                        isOpen = receive.NotifyMode switch
                        {
                            NotifyMode.Wechat => sw.IsOpen,
                            NotifyMode.Email => sw.EmailNotifyIsOpen,
                            _ => false
                        };
                    }
                    break;
                case SendToTheRole.AgencyStaff:
                    {
                        var sw = await _dbContext.AgencyNotifyTemplateSwitchs
                                .IgnoreQueryFilters()
                                .Where(x => x.TenantId == receive.TenantId && x.NotifyEventSubType == receive.NotifyEventSubType)
                                .FirstOrDefaultAsync();
                        if (sw is null)
                            return isOpen;

                        isOpen = sw.IsOpen;
                    }
                    break;
                case SendToTheRole.ManagerStaff:
                    {
                        if (receive.NotifyMode == NotifyMode.DingTalkRobot)
                        {
                            //TODO: 需求未设计开关，打乱了原有规则
                            isOpen = true;
                        }
                        else
                        {
                            var sw = await _dbContext.ManagerNotifyTemplateSwitchs
                                    .IgnoreQueryFilters()
                                    .Where(x => x.NotifyEventSubType == receive.NotifyEventSubType)
                                    .FirstOrDefaultAsync();

                            if (sw is null)
                                return isOpen;

                            isOpen = sw.IsOpen;
                        }
                    }
                    break;
                default:
                    break;
            }


            return isOpen;
        }

        #endregion
    }
}
