using Common.GlobalException;
using Contracts.Common.Notify.Enums;

namespace Notify.Api.Services.Sender;

public interface ISenderService
{
    public NotifyMode Mode { get; }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="TMessage"></typeparam>
    /// <param name="model"></param>
    /// <exception cref="ArgumentException"></exception>
    /// <exception cref="BusinessException"></exception>
    /// <returns></returns>
    Task<MessageSendResult> Send<TMessage>(TMessage model);
}