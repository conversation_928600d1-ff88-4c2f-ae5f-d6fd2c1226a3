using Contracts.Common.Notify.Enums;
using MessageHub;
using MessageHub.Hubs;
using Microsoft.AspNetCore.SignalR;

namespace Notify.Api.Services.Sender;

public class SiteMessageSenderService : ISenderService
{
    private readonly IHubContext<CommonHub> _hubContext;

    public NotifyMode Mode => NotifyMode.SiteMessage;

    public SiteMessageSenderService(IHubContext<CommonHub> hubContext)
    {
        _hubContext = hubContext;
    }

    public async Task<MessageSendResult> Send<TMessage>(TMessage model)
    {
        if (model is not SiteMessageMessageModel msg)
            throw new ArgumentException($"must be typeof {nameof(SiteMessageMessageModel)}");

        if (!string.IsNullOrWhiteSpace(msg.GroupId))
            await _hubContext.Clients.Group(msg.GroupId).SendAsync(CommonHubMethod.SiteMessages, msg.Message);
        if (msg.UserIds?.Count is > 0)
            await _hubContext.Clients.Users(msg.UserIds).SendAsync(CommonHubMethod.SiteMessages, msg.Message);
        return new MessageSendResult() { IsSuccessed = true };
    }
}

public class SiteMessageMessageModel
{
    public string? GroupId { get; set; }

    public List<string>? UserIds { get; set; }

    public object Message { get; set; }
}