using AutoMapper;
using Common.ServicesHttpClient;
using Contracts.Common.Marketing.DTOs.CouponActivity;
using Contracts.Common.Marketing.Enums;
using DotNetCore.CAP;
using Marketing.Api.Infrastructure;
using Marketing.Api.Model;
using Marketing.Api.Services;
using Marketing.Api.Services.Interfaces;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Marketing.UnitTest
{
    public class CouponActivityServiceTest : TestBase<CustomDbContext>
    {
        private readonly static long _tenantId = 1;
        public CouponActivityService CreateService(
            CustomDbContext dbContext = null,
            IMapper mapper = null,
            IUserCouponRangeService userCouponRangeService = null,
            IHttpClientFactory httpClientFactory = null,
            ICapPublisher capPublisher = null,
            IOptions<ServicesAddress> options = null) 
        {

            if (options is null)
                options = Options.Create(new ServicesAddress());

            return new CouponActivityService(
                dbContext,
                mapper,
                userCouponRangeService,
                httpClientFactory,
                options,
                capPublisher);
        }

        [Fact(DisplayName = "新增")]
        public async Task Add() 
        {
            var dbContext = GetNewDbContext(_tenantId);
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<CouponActivity, AddCouponActivityInput>().ReverseMap();
            }).CreateMapper();
            var service = CreateService(
                dbContext: dbContext,
                mapper: mapper);

            var couponId = await AddCoupon(dbContext);

            var id = await service.Add(new AddCouponActivityInput()
            {
                ActivityName = "",
                BeginDate = new DateTime(2022, 01, 01),
                EndDate = new DateTime(2022, 12, 31),
                CouponActivityType = CouponActivityType.ConsumptionGift,
                LimitMinConsume = 200,
                LimitProductTypes = new List<int>() { 1, 2 },
                Remark = "",
                CouponItems = new List<AddActivityCouponItem>() 
                {
                    new AddActivityCouponItem()
                    {
                        CouponId = couponId,
                        Count = 10
                    }
                },
            });

            var result = await dbContext.CouponActivities.FindAsync(id);
            Assert.NotNull(result);
        }

        [Fact(DisplayName = "获取")]
        public async Task Get()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<CouponActivity, GetCouponActivityOutput>().ReverseMap();
            }).CreateMapper();
            var service = CreateService(
                dbContext: dbContext,
                mapper: mapper);

            var id = await AddCouponActivity(dbContext);

            var result = await service.Get(id);
            Assert.NotNull(result);
        }

        [Fact(DisplayName = "修改")]
        public async Task Update()
        {
            var dbContext = GetNewDbContext(_tenantId);

            var service = CreateService(dbContext: dbContext);

            var id = await AddCouponActivity(dbContext);

            var input = new UpdateCouponActivityInput()
            {
                Id = id,
                BeginDate = new DateTime(2022, 02, 01),
                EndDate = new DateTime(2022, 02, 28)
            };
            await service.Update(input);

            var result = await dbContext.CouponActivities.FindAsync(id);
            Assert.Equal(input.BeginDate, result.BeginDate);
        }

        [Fact(DisplayName = "搜索")]
        public async Task Search()
        {
            var dbContext = GetNewDbContext(_tenantId);

            var service = CreateService(dbContext: dbContext);

            await AddCouponActivities(dbContext);

            var result = await service.Search(new SearchCouponActivityInput()
            {
                PageIndex = 1,
                PageSize = 10
            });

            Assert.True(result.Total == 2);
        }

        [Fact(DisplayName = "通过活动类型获取优惠券")]
        public async Task GetCouponByActivityType()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var userCouponRangeService = new UserCouponRangeService(dbContext);

            var service = CreateService(
                dbContext: dbContext,
                userCouponRangeService: userCouponRangeService);

            #region fake data

            //新增券
            var coupon = new Coupon()
            {
                CouponName = "满200减20",
                CouponType = CouponType.FullReduction,
                Amount = 20,
                LimitMinAmt = 200,
                ValidDateType = CouponValidDateType.ValidDate,
                ValidStartDate = new DateTime(2022, 01, 01),
                ValidEndDate = new DateTime(2022, 12, 31),
                CouponQuantity = 100
            };
            await dbContext.Coupons.AddAsync(coupon);

            //新增券适用范围
            var couponRanges = new List<CouponRange>()
            {
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Ticket, RangeType = CouponRangeType.Part },
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Mail, RangeType = CouponRangeType.All },
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Hotel, RangeType = CouponRangeType.Part }
            };
            var couponRangeValues = new List<CouponRangeValue>()
            {
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[0].Id, GroupId = 1001 },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[0].Id, GroupId = 1002 },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[2].Id, HotelId = 2001, PriceStrategyIds = "200101,200102" },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[2].Id, HotelId = 2002, PriceStrategyIds = "" },
            };
            await dbContext.CouponRanges.AddRangeAsync(couponRanges);
            await dbContext.CouponRangeValues.AddRangeAsync(couponRangeValues);

            //新增营销活动
            var couponActivity = new CouponActivity()
            {
                ActivityName = "新人有礼",
                CouponActivityType = CouponActivityType.RegistrationGift,
                BeginDate = new DateTime(2022, 01, 01),
                EndDate = new DateTime(2023, 01, 01)
            };
            await dbContext.CouponActivities.AddAsync(couponActivity);

            //新增营销活动包含券
            var couponActivityItem = new CouponActivityItem()
            {
                CouponActivityId = couponActivity.Id,
                CouponId = coupon.Id,
                Count = 1
            };
            await dbContext.CouponActivityItems.AddAsync(couponActivityItem);
            await dbContext.SaveChangesAsync();

            #endregion

            var result = await service.GetCouponByActivityType(new GetCouponByActivityTypeInput()
            {
                TenantId = _tenantId,
                CouponActivityType = CouponActivityType.RegistrationGift
            });
            Assert.NotNull(result);
        }

        [Fact(DisplayName = "通过产品类型获取详情页优惠券")]
        public async Task GetDetailsPageCouponTag()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var userCouponRangeService = new UserCouponRangeService(dbContext);

            var service = CreateService(
                dbContext: dbContext,
                userCouponRangeService: userCouponRangeService);

            #region fake data

            //新增券
            var coupon = new Coupon()
            {
                CouponName = "满200减20",
                CouponType = CouponType.FullReduction,
                Amount = 20,
                LimitMinAmt = 200,
                ValidDateType = CouponValidDateType.ValidDate,
                ValidStartDate = new DateTime(2022, 01, 01),
                ValidEndDate = new DateTime(2022, 12, 31),
                CouponQuantity = 100
            };
            await dbContext.Coupons.AddAsync(coupon);

            //新增券适用范围
            var couponRanges = new List<CouponRange>()
            {
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Ticket, RangeType = CouponRangeType.Part },
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Mail, RangeType = CouponRangeType.All },
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Hotel, RangeType = CouponRangeType.Part }
            };
            var couponRangeValues = new List<CouponRangeValue>()
            {
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[0].Id, GroupId = 1001 },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[0].Id, GroupId = 1002 },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[2].Id, HotelId = 2001, PriceStrategyIds = "200101,200102" },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[2].Id, HotelId = 2002, PriceStrategyIds = "" },
            };
            await dbContext.CouponRanges.AddRangeAsync(couponRanges);
            await dbContext.CouponRangeValues.AddRangeAsync(couponRangeValues);

            //新增营销活动
            var couponActivity = new CouponActivity()
            {
                ActivityName = "新品特惠",
                CouponActivityType = CouponActivityType.DetailsPageDistribution,
                BeginDate = new DateTime(2022, 01, 01),
                EndDate = new DateTime(2023, 01, 01)
            };
            await dbContext.CouponActivities.AddAsync(couponActivity);

            //新增营销活动包含券
            var couponActivityItem = new CouponActivityItem()
            {
                CouponActivityId = couponActivity.Id,
                CouponId = coupon.Id,
                Count = 1
            };
            await dbContext.CouponActivityItems.AddAsync(couponActivityItem);
            await dbContext.SaveChangesAsync();

            #endregion

            var result = await service.GetDetailsPageCouponTag(new GetDetailsPageCouponTagInput()
            {
                LimitProductType = LimitProductType.Hotel                
            });
            Assert.NotNull(result);
        }

        [Fact(DisplayName = "通过产品Id获取详情页发放优惠券")]
        public async Task GetDetailsPageCoupon()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var userCouponRangeService = new UserCouponRangeService(dbContext);

            var httpResponseMessage = new HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = new StringContent(JsonConvert.SerializeObject(new []
                {
                    new 
                    {
                        ProductId = 800001,
                        GroupItems = new [] { new { Id = 1001 }, new { Id = 1003 } }
                    }
                }))
            };
            var httpClientFactory = GetHttpClientFactoryMock(httpResponseMessage);

            var service = CreateService(
                dbContext: dbContext,
                userCouponRangeService: userCouponRangeService,
                httpClientFactory: httpClientFactory,
                options: Options.Create(new ServicesAddress() { Product = "http://127.0.0.1/" }));

            #region fake data

            //新增券
            var coupon = new Coupon()
            {
                CouponName = "满200减20",
                CouponType = CouponType.FullReduction,
                Amount = 20,
                LimitMinAmt = 200,
                ValidDateType = CouponValidDateType.ValidDate,
                ValidStartDate = new DateTime(2022, 01, 01),
                ValidEndDate = new DateTime(2022, 12, 31),
                CouponQuantity = 100
            };
            await dbContext.Coupons.AddAsync(coupon);

            //新增券适用范围
            var couponRanges = new List<CouponRange>()
            {
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Ticket, RangeType = CouponRangeType.Part },
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Mail, RangeType = CouponRangeType.All },
                new CouponRange(){ CouponId = coupon.Id, ProductType = LimitProductType.Hotel, RangeType = CouponRangeType.Part }
            };
            var couponRangeValues = new List<CouponRangeValue>()
            {
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[0].Id, GroupId = 1001 },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[0].Id, GroupId = 1002 },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[2].Id, HotelId = 2001, PriceStrategyIds = "200101,200102" },
                new CouponRangeValue(){ CouponId = coupon.Id, CouponRangeId = couponRanges[2].Id, HotelId = 2002, PriceStrategyIds = "" },
            };
            await dbContext.CouponRanges.AddRangeAsync(couponRanges);
            await dbContext.CouponRangeValues.AddRangeAsync(couponRangeValues);

            //新增营销活动
            var couponActivity = new CouponActivity()
            {
                ActivityName = "新品特惠",
                CouponActivityType = CouponActivityType.DetailsPageDistribution,
                BeginDate = new DateTime(2022, 01, 01),
                EndDate = new DateTime(2023, 01, 01)
            };
            await dbContext.CouponActivities.AddAsync(couponActivity);

            //新增营销活动包含券
            var couponActivityItem = new CouponActivityItem()
            {
                CouponActivityId = couponActivity.Id,
                CouponId = coupon.Id,
                Count = 1
            };
            await dbContext.CouponActivityItems.AddAsync(couponActivityItem);
            await dbContext.SaveChangesAsync();

            #endregion

            var result = await service.GetDetailsPageCoupon(new GetDetailsPageCouponInput()
            {
                TenantId=_tenantId,
                LimitProductType = LimitProductType.Ticket,
                Id = 800001
            });
            Assert.NotNull(result);
        }

        #region fake data

        private static async Task<long> AddCoupon(CustomDbContext dbContext) 
        {
            var coupon = new Coupon()
            {
                CouponName = ""
            };
            await dbContext.Coupons.AddAsync(coupon);
            await dbContext.SaveChangesAsync();

            return coupon.Id;
        }

        private static async Task<long> AddCouponActivity(CustomDbContext dbContext) 
        {
            var couponActivity = new CouponActivity()
            {
                ActivityName = "新人有礼",
                CouponActivityType = CouponActivityType.RegistrationGift,
                BeginDate = new DateTime(2022, 01, 01),
                EndDate = new DateTime(2022, 12, 31),
                Remark  = ""
            };
            await dbContext.CouponActivities.AddAsync(couponActivity);

            var coupon = new Coupon()
            {
                CouponName = ""   ,
                ValidDateType = CouponValidDateType.ValidDate,
                ValidStartDate = new DateTime(2022, 01, 01),
                ValidEndDate = new DateTime(2022, 12, 31),
                CouponQuantity = 100
            };
            await dbContext.Coupons.AddAsync(coupon);

            var couponActivityItems = new List<CouponActivityItem>()
            {
                new CouponActivityItem()
                {
                    CouponActivityId = couponActivity.Id,
                    CouponId = coupon.Id,
                    Count = 100
                }
            };
            await dbContext.CouponActivityItems.AddRangeAsync(couponActivityItems);
            await dbContext.SaveChangesAsync();
            return couponActivity.Id;
        }

        private static async Task AddCouponActivities(CustomDbContext dbContext)
        {
            var couponActivity1 = new CouponActivity()
            {
                ActivityName = "扫码赠送",
                CouponActivityType = CouponActivityType.ScanningGift,
                BeginDate = new DateTime(2022, 01, 01),
                EndDate = new DateTime(2022, 12, 31),
                Remark = ""
            };
            await dbContext.CouponActivities.AddAsync(couponActivity1);

            var couponActivity2 = new CouponActivity()
            {
                ActivityName = "产品优惠",
                CouponActivityType = CouponActivityType.DetailsPageDistribution,
                BeginDate = new DateTime(2022, 01, 01),
                EndDate = new DateTime(2022, 12, 31),
                Remark = ""
            };
            await dbContext.CouponActivities.AddAsync(couponActivity2);

            var userCouponReceives = new List<UserCouponReceive>()
            {
                new UserCouponReceive()
                {
                    CouponActivityId = couponActivity1.Id,
                    UserId = 1001,
                    CouponActivityType = CouponActivityType.ScanningGift,
                    ReceiveBaseOrderId = 0,
                    CreateTime = new DateTime(2022, 02, 28)
                },
                new UserCouponReceive()
                {
                    CouponActivityId = couponActivity1.Id,
                    UserId = 1001,
                    CouponActivityType = CouponActivityType.ScanningGift,
                    ReceiveBaseOrderId = 0,
                    CreateTime = new DateTime(2022, 02, 28)
                },
                new UserCouponReceive()
                {
                    CouponActivityId = couponActivity2.Id,
                    UserId = 1001,
                    CouponActivityType = CouponActivityType.ScanningGift,
                    ReceiveBaseOrderId = 0,
                    CreateTime = new DateTime(2022, 02, 28)
                },
                new UserCouponReceive()
                {
                    CouponActivityId = couponActivity1.Id,
                    UserId = 1002,
                    CouponActivityType = CouponActivityType.ScanningGift,
                    ReceiveBaseOrderId = 0,
                    CreateTime = new DateTime(2022, 02, 28)
                }
            };
            await dbContext.UserCouponReceives.AddRangeAsync(userCouponReceives);
            await dbContext.SaveChangesAsync();
        }

        #endregion
    }
}
