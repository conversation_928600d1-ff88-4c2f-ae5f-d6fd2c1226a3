using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Marketing.Api.Infrastructure.EntityConfigurations
{
    public class PromotionCustomerRemarkEntityTypeConfiguration : TenantBaseConfiguration<Model.PromotionCustomerRemark>, IEntityTypeConfiguration<Model.PromotionCustomerRemark>
    {
        public void Configure(EntityTypeBuilder<Model.PromotionCustomerRemark> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.CustomerId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.CreatorType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.CreatorId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.CreatorName)
                .HasColumnType("varchar(50)")
                .IsRequired();
            
            builder.Property(s => s.Remark)
                .HasColumnType("varchar(500)")
                .IsRequired();
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.HasIndex(x => x.CustomerId);
        }
    }
}
