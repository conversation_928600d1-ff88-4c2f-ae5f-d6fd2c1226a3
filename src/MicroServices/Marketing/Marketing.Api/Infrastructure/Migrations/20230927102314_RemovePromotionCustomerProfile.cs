using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketing.Api.Infrastructure.Migrations
{
    public partial class RemovePromotionCustomerProfile : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PromotionCustomerProfile");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PromotionCustomerProfile",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Avatar = table.Column<string>(type: "varchar(512)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CustomerId = table.Column<long>(type: "bigint", nullable: false),
                    LastOfTraceTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    NickName = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OtherIdentifier = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PhoneNumber = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PromotionTraceId = table.Column<long>(type: "bigint", nullable: false),
                    SourceOfTraceTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PromotionCustomerProfile", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionCustomerProfile_PromotionTraceId",
                table: "PromotionCustomerProfile",
                column: "PromotionTraceId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionCustomerProfile_SourceOfTraceTime",
                table: "PromotionCustomerProfile",
                column: "SourceOfTraceTime");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionCustomerProfile_TenantId",
                table: "PromotionCustomerProfile",
                column: "TenantId");
        }
    }
}
