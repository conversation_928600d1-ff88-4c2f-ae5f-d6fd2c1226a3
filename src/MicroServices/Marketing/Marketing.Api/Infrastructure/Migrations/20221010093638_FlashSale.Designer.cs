// <auto-generated />
using System;
using Marketing.Api.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Marketing.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20221010093638_FlashSale")]
    partial class FlashSale
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Marketing.Api.Model.BackstageGiftRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("GiftType")
                        .HasColumnType("tinyint");

                    b.Property<string>("OperatorName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("BackstageGiftRecord", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.BackstageGiftRecordItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BackstageGiftRecordId")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<long>("GiftItemId")
                        .HasColumnType("bigint");

                    b.Property<string>("GiftItemName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BackstageGiftRecordId");

                    b.HasIndex("TenantId");

                    b.ToTable("BackstageGiftRecordItem", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.BackstageGiftUserRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BackstageGiftRecordId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("NickName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BackstageGiftRecordId");

                    b.HasIndex("TenantId");

                    b.ToTable("BackstageGiftUserRecord", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.Coupon", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(8,2)");

                    b.Property<string>("CouponName")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("CouponQuantity")
                        .HasColumnType("int");

                    b.Property<sbyte>("CouponType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Instruction")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("LimitMinAmt")
                        .HasColumnType("decimal(8,2)");

                    b.Property<decimal>("MaxDiscountAmt")
                        .HasColumnType("decimal(8,2)");

                    b.Property<int>("Nights")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRoom")
                        .HasColumnType("int");

                    b.Property<int>("ReceivedQuantity")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("ValidDateType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ValidDays")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ValidEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidStartDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.ToTable("Coupon", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.CouponActivity", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActivityName")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("BeginDate")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("CouponActivityType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Instruction")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("LimitMinConsume")
                        .HasColumnType("decimal(8,2)");

                    b.Property<int>("LimitProductType")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CouponActivity", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.CouponActivityItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<long>("CouponActivityId")
                        .HasColumnType("bigint");

                    b.Property<long>("CouponId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CouponActivityItem", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.CouponRange", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CouponId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("RangeType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CouponRange", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.CouponRangeValue", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CouponId")
                        .HasColumnType("bigint");

                    b.Property<long>("CouponRangeId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("PriceStrategyIds")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketsIds")
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CouponRangeValue", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.FlashSale", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("BeginTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Limit")
                        .HasColumnType("int");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<string>("SubTitle")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("FlashSale", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.FlashSaleItems", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AvailableInventory")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<sbyte>("CostPriceType")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("CostPriceValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("FlashSaleId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPriceType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("SellingPriceValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("SkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("SkuSubClass")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalInventory")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("FlashSaleItems", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.FlashSaleRecords", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("FlashSaleItemsId")
                        .HasColumnType("bigint");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("FlashSaleRecords", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.StoredValueCard", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CardName")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("CardType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Cover")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Instruction")
                        .HasColumnType("varchar(1000)");

                    b.Property<int>("ProductBusinessType")
                        .HasColumnType("int");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCard", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.StoredValueCardGear", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("GiftValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("StoredValueCardId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardGear", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.StoredValueCardGearGift", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("GiftType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("IsGift")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("StoredValueCardGearId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardGearGift", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.StoredValueCardGearGiftItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<long>("GiftItemId")
                        .HasColumnType("bigint");

                    b.Property<long>("StoredValueCardGearGiftId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardGearGiftItem", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.StoredValueCardRedundantData", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("HasCommission")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("StoredValueCardId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("StoredValueCardRedundantData", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.UserCoupon", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UsedTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserCouponReceiveId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("UserCouponStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ValidBeginDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidEndDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("UserCoupon", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.UserCouponReceive", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CouponActivityId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CouponActivityType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ReceiveBaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("UserCouponReceive", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.UserStoredValueCard", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ActivateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("GiftValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NickName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("StoredValueCardOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Timestamp")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp(6)")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)");

                    b.Property<decimal>("TotalValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("UserStoredValueCard", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.UserStoredValueCardRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Channel")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("OrderRefundId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserStoredValueCardId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("UserStoredValueCardRecord", (string)null);
                });

            modelBuilder.Entity("Marketing.Api.Model.UserCoupon", b =>
                {
                    b.OwnsOne("Marketing.Api.Model.CouponInfo", "CouponInfo", b1 =>
                        {
                            b1.Property<long>("UserCouponId")
                                .HasColumnType("bigint");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(8,2)");

                            b1.Property<string>("CouponName")
                                .HasColumnType("varchar(200)");

                            b1.Property<sbyte>("CouponType")
                                .HasColumnType("tinyint");

                            b1.Property<long>("Id")
                                .HasColumnType("bigint");

                            b1.Property<string>("Instruction")
                                .HasColumnType("varchar(500)");

                            b1.Property<decimal>("LimitMinAmt")
                                .HasColumnType("decimal(8,2)");

                            b1.Property<decimal>("MaxDiscountAmt")
                                .HasColumnType("decimal(8,2)");

                            b1.Property<int?>("Nights")
                                .HasColumnType("int");

                            b1.Property<int?>("NumberOfRoom")
                                .HasColumnType("int");

                            b1.Property<sbyte>("ValidDateType")
                                .HasColumnType("tinyint");

                            b1.Property<int>("ValidDays")
                                .HasColumnType("int");

                            b1.Property<DateTime?>("ValidEndDate")
                                .HasColumnType("datetime");

                            b1.Property<DateTime?>("ValidStartDate")
                                .HasColumnType("datetime");

                            b1.HasKey("UserCouponId");

                            b1.ToTable("UserCoupon");

                            b1.WithOwner()
                                .HasForeignKey("UserCouponId");
                        });

                    b.Navigation("CouponInfo");
                });

            modelBuilder.Entity("Marketing.Api.Model.UserStoredValueCard", b =>
                {
                    b.OwnsOne("Marketing.Api.Model.StoredValueCardInfo", "StoredValueCardInfo", b1 =>
                        {
                            b1.Property<long>("UserStoredValueCardId")
                                .HasColumnType("bigint");

                            b1.Property<string>("CardName")
                                .HasColumnType("varchar(200)");

                            b1.Property<sbyte>("CardType")
                                .HasColumnType("tinyint");

                            b1.Property<string>("Cover")
                                .HasColumnType("varchar(500)");

                            b1.Property<long>("Id")
                                .HasColumnType("bigint");

                            b1.Property<string>("Instruction")
                                .HasColumnType("varchar(1000)");

                            b1.Property<int>("ProductBusinessType")
                                .HasColumnType("int");

                            b1.HasKey("UserStoredValueCardId");

                            b1.ToTable("UserStoredValueCard");

                            b1.WithOwner()
                                .HasForeignKey("UserStoredValueCardId");
                        });

                    b.Navigation("StoredValueCardInfo");
                });
#pragma warning restore 612, 618
        }
    }
}
