using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketing.Api.Infrastructure.Migrations
{
    public partial class AddCouponRedemptionCode : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<sbyte>(
                name: "UseChannel",
                table: "CouponActivity",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)1);

            migrationBuilder.CreateTable(
                name: "CouponRedemptionCode",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    CouponActivityId = table.Column<long>(type: "bigint", nullable: false),
                    AgencyId = table.Column<long>(type: "bigint", nullable: true),
                    Code = table.Column<string>(type: "varchar(32)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RedemptionTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    RedemptionStatus = table.Column<sbyte>(type: "tinyint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CouponRedemptionCode", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_CouponRedemptionCode_TenantId",
                table: "CouponRedemptionCode",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CouponRedemptionCode");

            migrationBuilder.DropColumn(
                name: "UseChannel",
                table: "CouponActivity");
        }
    }
}
