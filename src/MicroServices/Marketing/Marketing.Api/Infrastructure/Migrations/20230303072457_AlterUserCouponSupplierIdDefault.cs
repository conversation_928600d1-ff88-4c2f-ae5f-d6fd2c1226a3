using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketing.Api.Infrastructure.Migrations
{
    public partial class AlterUserCouponSupplierIdDefault : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<long>(
                name: "CouponInfo_SupplierId",
                table: "UserCoupon",
                type: "bigint",
                nullable: true,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<long>(
                name: "CouponInfo_SupplierId",
                table: "UserCoupon",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true,
                oldDefaultValue: 0L);
        }
    }
}
