using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Marketing.DTOs.FlashSale;
using Contracts.Common.Marketing.Messages;
using DotNetCore.CAP;
using Marketing.Api.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Marketing.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class FlashSaleOrderRecordController : ControllerBase
{
    private readonly IFlashSaleOrderRecordService _flashSaleOrderRecordService;

    public FlashSaleOrderRecordController(IFlashSaleOrderRecordService flashSaleOrderRecordService)
    {
        _flashSaleOrderRecordService = flashSaleOrderRecordService;
    }

    /// <summary>
    /// 获取限时抢购预下单所需信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(FlashSalePreOrderOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default,ErrorTypes.Marketing.FlashSaleNotInProcess)]
    public async Task<IActionResult> GetPreOrder(FlashSalePreOrderInput input)
    {
        var userId = HttpContext.GetCurrentUser().userid;
        var result = await _flashSaleOrderRecordService.GetFlashSalePreOrder(input, userId);
        return Ok(result);
    }

    /// <summary>
    /// 记录抢购项订单购买信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default,ErrorTypes.Marketing.FlashSaleInventoryNotEnough,
        ErrorTypes.Marketing.FlashSaleOverProductLimit,
        ErrorTypes.Marketing.FlashSaleNotInProcess)]
    public async Task<IActionResult> Record(FlashSaleRecordInput input)
    {
        var userId = HttpContext.GetCurrentUser().userid;
        await _flashSaleOrderRecordService.Record(input, userId);
        return Ok();
    }

    #region 

    [NonAction]
    [CapSubscribe(CapTopics.Marketing.FlashSaleOrderCancel)]
    public async Task Cancel(FlashSaleOrderCancelMessage receive)
    {
        await _flashSaleOrderRecordService.Cancel(receive);
    }

    #endregion
}
