using Contracts.Common.Marketing.DTOs.PromotionTrace;
using EfCoreExtensions.Abstract;
using Marketing.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Marketing.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class PromotionTraceController : ControllerBase
{
    private readonly IPromotionTraceService _promotionTraceService;

    public PromotionTraceController(IPromotionTraceService promotionTraceService)
    {
        _promotionTraceService = promotionTraceService;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchPromotionTraceOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchPromotionTraceInput input)
    {
        var result = await _promotionTraceService.Search(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(AddPromotionTraceDto dto)
    {
        await _promotionTraceService.Add(dto);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(UpdatePromotionTraceInput input)
    {
        await _promotionTraceService.Update(input);
        return Ok();
    }

    /// <summary>
    /// 检查是否存在对应活码
    /// </summary>
    /// <param name="targetId"></param>
    /// <param name="traceType"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<CheckRefOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CheckRef(List<CheckRefInput> input)
    {
        var result = await _promotionTraceService.CheckRef(input);
        return Ok(result);
    }

    [HttpGet]
    [ProducesResponseType(typeof(PromotionTraceOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get(long Id)
    {
        var result = await _promotionTraceService.Get(Id);
        return Ok(result);
    }

    /// <summary>
    /// 获取推广记录列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<PromotionTraceListDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> List(ListPromotionTraceInput input)
    {
        var result = await _promotionTraceService.List(input);
        return Ok(result);
    }
}
