using Contracts.Common.Marketing.DTOs.PromotionCustomerRemark;
using EfCoreExtensions.Abstract;
using Marketing.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Marketing.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class PromotionCustomerRemarkController : ControllerBase
{
    private readonly IPromotionCustomerRemarkService _promotionCustomerRemarkService;
    public PromotionCustomerRemarkController(
        IPromotionCustomerRemarkService promotionCustomerRemarkService)
    {
        _promotionCustomerRemarkService = promotionCustomerRemarkService;
    }

    /// <summary>
    /// 新增跟进记录
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Add(AddPromotionCustomerRemarkInput input)
    {
        await _promotionCustomerRemarkService.Add(input);
        return Ok();
    }

    /// <summary>
    /// 跟进记录查询
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchPromotionCustomerRemarkOutput>),(int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchPromotionCustomerRemarkInput input)
    {
        var result = await _promotionCustomerRemarkService.Search(input);
        return Ok(result);
    }
}