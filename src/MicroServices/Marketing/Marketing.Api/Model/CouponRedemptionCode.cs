using Contracts.Common.Marketing.Enums;
using EfCoreExtensions.EntityBase;

namespace Marketing.Api.Model;

public class CouponRedemptionCode : TenantBase
{
    public long CouponActivityId { get; set; }
    public long? AgencyId { get; set; }
    public string Code { get; set; }
    /// <summary>
    /// 兑换时间
    /// </summary>
    public DateTime? RedemptionTime { get; set; }
    /// <summary>
    /// 兑换状态
    /// </summary>
    public UserCouponStatus RedemptionStatus { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
