using Contracts.Common.Marketing.Enums;
using EfCoreExtensions.EntityBase;
using System.ComponentModel.DataAnnotations.Schema;

namespace Marketing.Api.Model
{
    /// <summary>
    /// 用户储值卡
    /// </summary>
    public class UserStoredValueCard : TenantBase
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 主单id
        /// </summary>
        public long BaseOrderId { get; set; }

        /// <summary>
        /// 储值卡订单id
        /// </summary>
        public long StoredValueCardOrderId { get; set; }

        /// <summary>
        /// 储值卡信息
        /// </summary>
        public StoredValueCardInfo StoredValueCardInfo { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// 总价值
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 赠送金额
        /// </summary>
        public decimal GiftValue { get; set; }

        public UserStoredValueCardStatus Status { get; set; }

        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime ActivateTime { get; set; }

        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string? NickName { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public string? PhoneNumber { get; set; }
    }

    [NotMapped]
    public class StoredValueCardInfo
    {
        public long Id { get; set; }

        /// <summary>
        /// 卡类型
        /// </summary>
        public CardType CardType { get; set; }

        /// <summary>
        /// 储值卡名称
        /// </summary>
        public string CardName { get; set; }

        /// <summary>
        /// 储值卡面
        /// </summary>
        public string? Cover { get; set; }

        /// <summary>
        /// 储值卡说明
        /// </summary>
        public string? Instruction { get; set; }

        /// <summary>
        /// 线上消费支持品类 位运算
        /// </summary>
        public int ProductBusinessType { get; set; }
    }
}
