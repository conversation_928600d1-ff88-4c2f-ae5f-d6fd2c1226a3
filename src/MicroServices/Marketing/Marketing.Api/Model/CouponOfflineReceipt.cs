using EfCoreExtensions.EntityBase;

namespace Marketing.Api.Model;

/// <summary>
/// 优惠券关联的线下收款码
/// </summary>
public class CouponOfflineReceipt : TenantBase
{
    /// <summary>
    /// 优惠券id
    /// </summary>
    public long CouponId { get; set; }

    /// <summary>
    /// 供应商线下收款码id
    /// </summary>
    public long OfflineReceiptId { get; set; }

    /// <summary>
    /// 收款码名称
    /// </summary>
    public string? Title { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}
