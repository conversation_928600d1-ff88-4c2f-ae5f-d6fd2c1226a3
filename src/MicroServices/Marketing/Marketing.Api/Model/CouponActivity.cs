using Contracts.Common.Marketing.Enums;
using EfCoreExtensions.EntityBase;

namespace Marketing.Api.Model
{
    /// <summary>
    /// 优惠券营销活动
    /// </summary>
    public class CouponActivity : TenantBase
    {
        /// <summary>
        /// 活动名称
        /// </summary>
        public string? ActivityName { get; set; }

        /// <summary>
        /// 活动类型
        /// </summary>
        public CouponActivityType CouponActivityType { get; set; }

        /// <summary>
        /// 使用渠道
        /// </summary>
        public CouponUseChannel UseChannel { get; set; }

        /// <summary>
        /// 活动开始日期
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 活动结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 消费限制 满多少赠送【消费赠送】
        /// </summary>
        public decimal LimitMinConsume { get; set; }

        /// <summary>
        /// 品类限制【消费赠送】 ProductType位运算
        /// </summary>
        public int LimitProductType { get; set; }

        #region B2B指定用户

        /// <summary>
        /// 指定用户类型 1-分销商 2-分销商用户
        /// </summary>
        public CouponActivitySpecifiedUserType? SpecifiedUserType { get; set; }

        /// <summary>
        /// 是否全部指定用户
        /// </summary>
        public bool? IsAllSpecifiedUser { get; set; }

        #endregion

        /// <summary>
        /// 活动说明
        /// </summary>
        public string? Instruction { get; set; }

        /// <summary>
        /// 活动备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 活动状态 1-启用 0-停用
        /// </summary>
        public bool Status { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }
}
