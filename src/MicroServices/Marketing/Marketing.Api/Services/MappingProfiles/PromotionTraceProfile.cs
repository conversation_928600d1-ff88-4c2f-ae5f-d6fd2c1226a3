using AutoMapper;
using Contracts.Common.Marketing.DTOs.PromotionTrace;
using Contracts.Common.Marketing.DTOs.PromotionTraceRecord;
using Contracts.Common.Tenant.DTOs.Page;
using EfCoreExtensions.Abstract;
using Marketing.Api.EsDocument;
using Newtonsoft.Json;

namespace Marketing.Api.Services.MappingProfiles;

public class PromotionTraceProfile : Profile
{
    public PromotionTraceProfile()
    {
        CreateMap<AddPromotionTraceDto, PromotionTrace>()
            .ForMember(dest => dest.Content, opt => opt.MapFrom(x => x.Content != null
            ? JsonConvert.SerializeObject(x.Content).Replace(" ", "") : string.Empty));
        CreateMap<PromotionTrace, SearchPromotionTraceOutput>()
            .ForMember(dest => dest.Content, opt => opt.MapFrom(x => JsonConvert.DeserializeObject<LinkChoose>(x.Content!)));
        CreateMap<PagingModel<PromotionTrace>, PagingModel<SearchPromotionTraceOutput>>();
        CreateMap<UpdatePromotionTraceInput, PromotionTrace>()
            .ForMember(dest => dest.Content, opt => opt.MapFrom(x => x.Content != null
            ? JsonConvert.SerializeObject(x.Content).Replace(" ", "") : string.Empty));
        CreateMap<PromotionTrace, CheckRefOutput>()
            .ForMember(dest => dest.Content, opt => opt.MapFrom(x => JsonConvert.DeserializeObject<LinkChoose>(x.Content!)));

        CreateMap<PromotionTrace, PromotionTraceOutput>()
            .ForMember(dest => dest.Content, opt => opt.MapFrom(x => JsonConvert.DeserializeObject<LinkChoose>(x.Content!)));

        CreateMap<PromotionTraceRecordDocument, GetCustomerTraceListOutput>();

        CreateMap<PromotionTrace, PromotionTraceListDto>().ReverseMap();
    }
}
