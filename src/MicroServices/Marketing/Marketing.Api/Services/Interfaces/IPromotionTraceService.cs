using Contracts.Common.Marketing.DTOs.PromotionTrace;
using EfCoreExtensions.Abstract;

namespace Marketing.Api.Services.Interfaces;

public interface IPromotionTraceService
{
    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchPromotionTraceOutput>> Search(SearchPromotionTraceInput input);

    Task Add(AddPromotionTraceDto dto);

    Task Update(UpdatePromotionTraceInput dto);

    /// <summary>
    /// 检查是否存在对应活码
    /// </summary>
    /// <param name="targetId"></param>
    /// <param name="traceType"></param>
    /// <returns></returns>
    Task<List<CheckRefOutput>> CheckRef(List<CheckRefInput> input);

    Task<PromotionTraceOutput> Get(long id);

    Task<List<PromotionTraceListDto>> List(ListPromotionTraceInput input);
}
