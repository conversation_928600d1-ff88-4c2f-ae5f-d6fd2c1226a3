using Contracts.Common.Marketing.DTOs.Coupon;
using Contracts.Common.Marketing.Enums;
using EfCoreExtensions.Abstract;

namespace Marketing.Api.Services.Interfaces
{
    public interface ICouponService
    {
        /// <exception cref="ErrorTypes.Common.ResourceInvalid"></exception>
        /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
        Task<long> Add(AddCouponInput input);
        Task<GetCouponOutput> Get(long id);

        /// <exception cref="ErrorTypes.Marketing.B2BNotSupportedProductType"></exception>
        /// <exception cref="ErrorTypes.Marketing.B2BNotSupportedCouponType"></exception>
        /// <exception cref="ErrorTypes.Common.ResourceInvalid"></exception>
        /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
        Task Update(UpdateCouponInput input);
        Task<PagingModel<SearchCouponOutput>> Search(SearchCouponInput input);

        /// <summary>
        /// 根据优惠券获取关联的有效活动数量
        /// </summary>
        /// <returns></returns>
        Task<Dictionary<long, int>> GetCouponForActivityCountAsync(IEnumerable<long> couponIds);

        /// <summary>
        /// 根据优惠券获取适用范围(产品类型)
        /// </summary>
        /// <param name="couponIds"></param>
        /// <returns></returns>
        Task<Dictionary<long, IEnumerable<LimitProductType>>> GetCouponForCouponRangesAsync(IEnumerable<long> couponIds);

        /// <summary>
        /// 根据活动Id获取优惠券详情信息
        /// </summary>
        /// <param name="couponActivityId">活动Id</param>
        /// <returns></returns>
        Task<List<GetCouponInfoOutput>> GetCouponInfoByActivityIdAsync(long couponActivityId);

        /// <summary>
        /// 优惠券搜索
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<SearchCouponOutput>> SearchAsync(SearchCouponInput input);

        Task<List<GetCouponBaseInfoOutput>> GetCouponBaseInfos(List<long> couponIds);

        /// <summary>
        /// 根据优惠券ids获取优惠券信息
        /// </summary>
        /// <param name="couponIds"></param>
        /// <returns></returns>
        Task<List<CouponInfoOutput>> GetCouponInfos(List<long> couponIds);

        /// <summary>
        /// 获取所有有效优惠券
        /// </summary>
        Task<List<GetAllCouponOutput>> GetAll();

        /// <summary>
        /// 详情页根据酒店id获取可领取的优惠券
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<GetByHotelIdsOutput>> GetByHotelIds(GetByHotelIdsInput input);
    }
}
