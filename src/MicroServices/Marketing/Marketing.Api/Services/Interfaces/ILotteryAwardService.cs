using Contracts.Common.Marketing.DTOs.Lottery;

namespace Marketing.Api.Services.Interfaces;

public interface ILotteryAwardService
{
    /// <summary>
    /// 创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ErrorTypes.Marketing.LotteryHasStartedOrFinished"></exception>
    /// <exception cref="ErrorTypes.Marketing.LotteryAwardReachingLimit"></exception>
    Task<long> Add(AddLotteryAwardInput input);

    /// <summary>
    /// 获取
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<LotteryAwardDto> Get(long id);

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ErrorTypes.Marketing.LotteryHasStartedOrFinished"></exception>
    Task Update(UpdateLotteryAwardInput input);

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ErrorTypes.Marketing.LotteryHasStartedOrFinished"></exception>
    Task Delete(long id);
}
