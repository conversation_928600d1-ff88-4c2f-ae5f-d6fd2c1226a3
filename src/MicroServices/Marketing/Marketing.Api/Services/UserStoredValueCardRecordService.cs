using Contracts.Common.Marketing.DTOs.StoredValueCard;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Marketing.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Marketing.Api.Services
{
    public class UserStoredValueCardRecordService : IUserStoredValueCardRecordService
    {
        private readonly CustomDbContext _dbContext;

        public UserStoredValueCardRecordService(CustomDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<IEnumerable<GetUserStoredValueCardRecordOutput>> GetRecordsByUserStoredValueCardId(long userStoredValueCardId, long userId)
        {
            var result = await _dbContext.UserStoredValueCards
                .Join(_dbContext.UserStoredValueCardRecords, c => c.Id, r => r.UserStoredValueCardId,
                (c, r) => new
                {
                    c.Id,
                    c.UserId,
                    UserStoredValueCardRecord = r
                })
                .Where(x => x.Id == userStoredValueCardId && x.UserId == userId)
                .OrderByDescending(x=> x.UserStoredValueCardRecord.CreateTime)
                .Select(x => new UserStoredValueCardRecordInfo
                {
                    Balance = x.UserStoredValueCardRecord.Balance,
                    Content = x.UserStoredValueCardRecord.Content,
                    CreateTime = x.UserStoredValueCardRecord.CreateTime,
                    BaseOrderId = x.UserStoredValueCardRecord.BaseOrderId,
                    Price = x.UserStoredValueCardRecord.Price
                })
                .ToListAsync();
            var data = result.GroupBy(x => x.Date)
                .Select(x => new GetUserStoredValueCardRecordOutput
                {
                    Date = x.Key,
                    Records = x.ToList()
                });
            return data;
        }

        public async Task<PagingModel<UserStoredValueCardRecordOutput>> GetRecords(GetUserStoredValueCardRecordInput input, long? userId)
        {
            var result = await _dbContext.UserStoredValueCards
                .Join(_dbContext.UserStoredValueCardRecords, c => c.Id, r => r.UserStoredValueCardId,
                (c, r) => new
                {
                    c.Id,
                    c.UserId,
                    UserStoredValueCardRecord = r
                })
                .Where(x => x.Id == input.UserStoredValueCardId)
                .WhereIF(userId.HasValue, x => x.UserId == userId.Value)
                .OrderByDescending(x=> x.UserStoredValueCardRecord.CreateTime)
                .PagingAsync(input.PageIndex, input.PageSize, x => new UserStoredValueCardRecordOutput
                {
                    UserStoredValueCardId = x.UserStoredValueCardRecord.UserStoredValueCardId,
                    Balance = x.UserStoredValueCardRecord.Balance,
                    Channel = x.UserStoredValueCardRecord.Channel,
                    Content = x.UserStoredValueCardRecord.Content,
                    CreateTime = x.UserStoredValueCardRecord.CreateTime,
                    BaseOrderId = x.UserStoredValueCardRecord.BaseOrderId,
                    Price = x.UserStoredValueCardRecord.Price,
                    RecordType = x.UserStoredValueCardRecord.RecordType
                });
            return result;
        }
    }
}
