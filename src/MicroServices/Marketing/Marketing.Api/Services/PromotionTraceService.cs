using AutoMapper;
using Contracts.Common.Marketing.DTOs.PromotionTrace;
using Contracts.Common.Marketing.Enums;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Marketing.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Marketing.Api.Services;

public class PromotionTraceService : IPromotionTraceService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    public PromotionTraceService(CustomDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<PagingModel<SearchPromotionTraceOutput>> Search(SearchPromotionTraceInput input)
    {
        var promotionTraces = await _dbContext.PromotionTraces.AsNoTracking()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Title), x => x.Title.Contains(input.Title!))
            .WhereIF(input.TraceType is not null, x => x.TraceType.Equals(input.TraceType))
            .WhereIF(input.ProductType is not null, x => x.ProductType.Equals(input.ProductType))
            .WhereIF(input.PromoterType is not null, x => x.PromoterType.Equals(input.PromoterType))
            .WhereIF(input.ClienteleType is not null, x => x.ClienteleType.Equals(input.ClienteleType))
            .OrderByDescending(x => x.CreateTime)
            .PagingAsync(input.PageIndex, input.PageSize, x => x);

        return _mapper.Map<PagingModel<SearchPromotionTraceOutput>>(promotionTraces);
    }

    public async Task<List<PromotionTraceListDto>> List(ListPromotionTraceInput input)
    {
        var promotionTraces = await _dbContext.PromotionTraces.AsNoTracking()
            .WhereIF(input.PromotionTraceTypes?.Any() is true, x => input.PromotionTraceTypes.Contains(x.TraceType))
            .WhereIF(input.ClienteleType.HasValue, x => input.ClienteleType!.Value == x.ClienteleType)
            .OrderByDescending(x => x.Id)
            .ToListAsync();

        return _mapper.Map<List<PromotionTraceListDto>>(promotionTraces);
    }

    public async Task Add(AddPromotionTraceDto dto)
    {
        var promotionTrace = new PromotionTrace();
        _mapper.Map(dto, promotionTrace);

        await _dbContext.AddAsync(promotionTrace);
        await _dbContext.SaveChangesAsync();
    }

    public async Task Update(UpdatePromotionTraceInput dto)
    {
        var promotionTrace = await _dbContext.PromotionTraces.FindAsync(dto.Id);
        if (promotionTrace is null) return;

        if (dto.ClienteleType == PromotionTraceClienteleType.WechatMall)
        {
            //不允许TargetId，PromotionPositionId，TraceType重复
            var oldPromotionTrace = await _dbContext.PromotionTraces
                .Where(x => x.Id != dto.Id)
                .Where(x => x.TargetId == dto.TargetId
                            && x.PromotionPositionId == dto.PromotionPositionId
                            && x.TraceType == dto.TraceType)
                .FirstOrDefaultAsync();
            if (oldPromotionTrace is not null) return;
        }

        _mapper.Map(dto, promotionTrace);
        promotionTrace.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    public async Task<List<CheckRefOutput>> CheckRef(List<CheckRefInput> input)
    {
        var targetIds = input.Select(x => x.TargetId).ToList();
        var traceTypes = input.Select(x => x.TraceType).ToList();
        var promoterIds = input.Where(x => x.PromoterId is not null)
            .Select(x => x.PromoterId).ToList();
        var promotionPositionIds = input.Where(x => x.PromotionPositionId is not null)
            .Select(x => x.PromotionPositionId).ToList();

        var promotionTraces = await _dbContext.PromotionTraces.AsNoTracking()
            .Where(x => traceTypes.Contains(x.TraceType) && targetIds.Contains(x.TargetId))
            .WhereIF(promoterIds.Any(), x => promoterIds.Contains(x.PromoterId))
            .WhereIF(promotionPositionIds.Any(), x => promotionPositionIds.Contains(x.PromotionPositionId))
            .ToListAsync();

        var result = new List<CheckRefOutput>();
        foreach (var checkRefInput in input)
        {
            var promotionTrace = promotionTraces
                .Where(x => x.TargetId.Equals(checkRefInput.TargetId) && x.PromoterType.Equals(checkRefInput.PromoterType))
                .FirstOrDefault();
            if (promotionTrace is not null)
            {
                var checkRefOutput = _mapper.Map<CheckRefOutput>(promotionTrace);
                checkRefOutput.IsExist = true;
                result.Add(checkRefOutput);
            }
            else
            {
                result.Add(new CheckRefOutput
                {
                    TargetId = checkRefInput.TargetId,
                    IsExist = false
                });
            }
        }
        ;
        return result;
    }

    public async Task<PromotionTraceOutput> Get(long id)
    {
        var trace = await _dbContext.PromotionTraces.AsNoTracking()
                .Where(x => x.Id == id)
                .FirstOrDefaultAsync();
        var result = _mapper.Map<PromotionTraceOutput>(trace);
        if (trace is not null)
        {
            var promotionPosition = await _dbContext.PromotionPositions.AsNoTracking()
                .Where(x => x.Id.Equals(trace.PromotionPositionId))
                .Select(x => new { x.Tags })
                .FirstOrDefaultAsync();

            result.PromotionPositionTags = promotionPosition?.Tags;
        }
        return result;
    }
}
