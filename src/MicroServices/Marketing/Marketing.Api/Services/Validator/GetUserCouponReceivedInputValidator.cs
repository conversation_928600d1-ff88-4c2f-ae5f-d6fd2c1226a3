using Contracts.Common.Marketing.DTOs;
using FluentValidation;

namespace Marketing.Api.Services.Validator
{
    public class GetUserCouponReceivedInputValidator : AbstractValidator<GetUserCouponReceivedInput>
    {
        public GetUserCouponReceivedInputValidator()
        {
            RuleFor(s => s.PageIndex).GreaterThan(0);
            RuleFor(s => s.PageSize).GreaterThan(0).LessThanOrEqualTo(100);
            RuleFor(s => s.CouponActivityType).IsInEnum();
        }
    }
}
