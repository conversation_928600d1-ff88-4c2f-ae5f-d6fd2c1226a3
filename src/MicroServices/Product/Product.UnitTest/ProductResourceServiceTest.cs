using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Product.DTOs.ProductResource;
using Contracts.Common.Product.Enums;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Product.Api.Infrastructure;
using Product.Api.Model;
using Product.Api.Services;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Product.UnitTest
{
    public class ProductResourceServiceTest : TestBase<CustomDbContext>
    {
        private readonly static long _tenantId = 1;
        public ProductResourceService CreateService(
            CustomDbContext dbContext = null,
            IHttpClientFactory httpClientFactory = null,
            IOptions<ServicesAddress> options = null) 
        {
            if (options is null)
                options = Options.Create(new ServicesAddress());

            return new ProductResourceService(
                dbContext,
                httpClientFactory,
                options);
        }

        [Fact(DisplayName = "获取距离最近的资源")]
        public async Task GetFirstResource() 
        {
            var dbContext = GetNewDbContext(_tenantId);
            var response = new HttpResponseMessage()
            {
                Content = new StringContent(JsonConvert.SerializeObject(new List<CheckIsEnabledOutput>()
                {
                    new CheckIsEnabledOutput()
                    {
                        Id = 100001,
                        Name = "酒店1",
                        Enabled = true,
                        Longitude = 120,
                        Latitude = 20
                    },
                    new CheckIsEnabledOutput()
                    {
                        Id = 100002,
                        Name = "酒店2",
                        Enabled = false,
                        Longitude = 118,
                        Latitude = 20
                    }
                }), Encoding.UTF8)
            };
            var httpClientFactory = GetHttpClientFactoryMock(response);
            var options = Options.Create(new ServicesAddress { Hotel = "http://127.0.0.1/" });

            var service = CreateService(
                dbContext: dbContext,
                httpClientFactory: httpClientFactory,
                options: options);

            long productId = 10000;
            await AddProductResources(productId, dbContext);

            var result = await service.GetFirstResource(new GetFirstResourceInput()
            {
                ProductId = productId,
                Longitude = 118,
                Latitude = 20
            });

            //Assert.True(result.ResourceInfo.Name == "酒店2");
        }

        [Fact(DisplayName = "获取产品的资源")]
        public async Task GetResources()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var response = new HttpResponseMessage()
            {
                Content = new StringContent(JsonConvert.SerializeObject(new List<CheckIsEnabledOutput>()
                {
                    new CheckIsEnabledOutput()
                    {
                        Id = 100001,
                        Name = "酒店1",
                        Enabled = true,
                        CityName = "深圳",
                        Longitude = 120,
                        Latitude = 20
                    },
                    new CheckIsEnabledOutput()
                    {
                        Id = 100002,
                        Name = "酒店2",
                        Enabled = false,
                        CityName = "广州",
                        Longitude = 118,
                        Latitude = 20
                    }
                }), Encoding.UTF8)
            };
            var httpClientFactory = GetHttpClientFactoryMock(response);
            var options = Options.Create(new ServicesAddress { Hotel = "http://127.0.0.1/" });

            var service = CreateService(
                dbContext: dbContext,
                httpClientFactory: httpClientFactory,
                options: options);

            long productId = 10000;
            await AddProductResources(productId, dbContext);

            var result = await service.GetResources(new GetResourcesInput()
            {
                ProductId = productId
            });

            Assert.True(result.Count() == 3);
        }

        [Fact(DisplayName = "获取供应商的资源")]
        public async Task GetResourcesBySupplier()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var response = new HttpResponseMessage()
            {
                Content = new StringContent(JsonConvert.SerializeObject(new List<CheckIsEnabledOutput>()
                {
                    new CheckIsEnabledOutput()
                    {
                        Id = 200001,
                        Name = "酒店1",
                        Enabled = true,
                        OperatingModel = OperatingModel.Agency
                    },
                    new CheckIsEnabledOutput()
                    {
                        Id = 200002,
                        Name = "酒店2",
                        Enabled = false,
                        OperatingModel = OperatingModel.Agency
                    },
                    new CheckIsEnabledOutput()
                    {
                        Id = 200003,
                        Name = "酒店3",
                        Enabled = true,
                        OperatingModel = OperatingModel.SelfSupport
                    },
                    new CheckIsEnabledOutput()
                    {
                        Id = 200004,
                        Name = "酒店4",
                        Enabled = true,
                        OperatingModel = OperatingModel.Agency
                    },
                    new CheckIsEnabledOutput()
                    {
                        Id = 200005,
                        Name = "酒店5",
                        Enabled = true,
                        OperatingModel = OperatingModel.Agency
                    }
                }), Encoding.UTF8)
            };
            var httpClientFactory = GetHttpClientFactoryMock(response);
            var options = Options.Create(new ServicesAddress { Hotel = "http://127.0.0.1/" });


            var ticketProduct1 = new TicketProduct()
            {
                Title = "餐券1",
                SupplierId = 1001,
            };
            await dbContext.TicketProducts.AddAsync(ticketProduct1);
            var productResources1 = new List<ProductResource>()
            {
                new ProductResource(){ ProductId = ticketProduct1.Id, ResourceId = 200001, ResourceType = ProductResourceType.Hotel },
                new ProductResource(){ ProductId = ticketProduct1.Id, ResourceId = 200002, ResourceType = ProductResourceType.Hotel },
                new ProductResource(){ ProductId = ticketProduct1.Id, ResourceId = 200003, ResourceType = ProductResourceType.Hotel },
            };
            await dbContext.ProductResources.AddRangeAsync(productResources1);

            var ticketProduct2 = new TicketProduct()
            {
                Title = "餐券2",
                SupplierId = 1001,
            };
            await dbContext.TicketProducts.AddAsync(ticketProduct2);
            var productResources2 = new List<ProductResource>()
            {
                new ProductResource(){ ProductId = ticketProduct2.Id, ResourceId = 200001, ResourceType = ProductResourceType.Hotel },
                new ProductResource(){ ProductId = ticketProduct2.Id, ResourceId = 200004, ResourceType = ProductResourceType.Hotel },
                new ProductResource(){ ProductId = ticketProduct2.Id, ResourceId = 200005, ResourceType = ProductResourceType.Hotel },
            };
            await dbContext.ProductResources.AddRangeAsync(productResources2);
            await dbContext.SaveChangesAsync();

            var service = CreateService(
                dbContext: dbContext,
                httpClientFactory: httpClientFactory,
                options: options);

            var result = await service.GetBySupplier(ticketProduct1.SupplierId);
            Assert.True(result.Count == 4);
        }


        #region fake data

        private async Task AddProductResources(long productId, CustomDbContext dbContext) 
        {
            var restaurant = new Restaurant()
            {
                Name = "",
                OperatingModel = OperatingModel.SelfSupport,
                ServiceTime = "",
                Telephone = "",
                CountryCode = 10,
                CountryName = "中国",
                ProvinceCode = 0,
                ProvinceName = "",
                CityCode = 0,
                CityName = "",
                DistrictCode = 0,
                DistrictName = "",
                Address = ""
            };          
            restaurant.SetLocation(122, 22);
            await dbContext.Restaurants.AddAsync(restaurant);

            var list = new List<ProductResource>()
            {
                new ProductResource()
                {
                    TicketBusinessType = TicketBusinessType.RoomVoucher,
                    ProductId = productId,
                    CountryCode = 10,
                    CountryName = "中国",
                    ProvinceCode = 44000,
                    ProvinceName = "",
                    CityCode = 44100,
                    CityName = "",
                    ResourceType = ProductResourceType.Hotel,
                    ResourceId = 100001
                },
                new ProductResource()
                {
                    TicketBusinessType = TicketBusinessType.RoomVoucher,
                    ProductId = productId,
                    CountryCode = 10,
                    CountryName = "中国",
                    ProvinceCode = 44000,
                    ProvinceName = "",
                    CityCode = 44200,
                    CityName = "",
                    ResourceType = ProductResourceType.Hotel,
                    ResourceId = 100002
                },
                new ProductResource()
                {
                    TicketBusinessType = TicketBusinessType.RoomVoucher,
                    ProductId = productId,
                    CountryCode = 10,
                    CountryName = "中国",
                    ProvinceCode = 45000,
                    ProvinceName = "",
                    CityCode = 45100,
                    CityName = "长沙",
                    ResourceType = ProductResourceType.Restaurant,
                    ResourceId = restaurant.Id
                }
            };

            await dbContext.ProductResources.AddRangeAsync(list);
            await dbContext.SaveChangesAsync();
        }

        #endregion
    }
}
