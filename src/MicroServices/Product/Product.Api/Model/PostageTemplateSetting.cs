using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Product.Api.Model
{
    /// <summary>
    /// 邮费模板设置
    /// </summary>
    public class PostageTemplateSetting : TenantBase
    {
        /// <summary>
        /// 模板Id
        /// </summary>
        public long PostageTemplateId { get; set; }

        /// <summary>
        /// 设置类型
        /// </summary>
        public PostageTemplateSettingType Type { get; set; }

        /// <summary>
        /// 起步件数
        /// </summary>
        public int StartPieces { get; set; }

        /// <summary>
        /// 起步费
        /// </summary>
        public decimal StartFee { get; set; }

        /// <summary>
        /// 每增加多少件
        /// </summary>
        public int AdditionalPieces{ get; set; }

        /// <summary>
        /// 递增费
        /// </summary>
        public decimal AdditionalFee { get; set; }

        /// <summary>
        /// 满多少钱包邮
        /// </summary>
        public decimal FreeShippingOverAmount { get; set; }

        /// <summary>
        /// 满多少件包邮
        /// </summary>
        public int FreeShippingOverPieces { get; set; }
    }
}
