using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Product.Api.Model
{
    /// <summary>
    /// 产品相册
    /// </summary>
    public class ProductPhotos : TenantBase
    {
        /// <summary>
        /// 产品Id
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public MediaTypeOfPhotos MediaType { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string? Path { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 是否可用，删除照片时设置为false
        /// </summary>
        public bool Enabled { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }
}
