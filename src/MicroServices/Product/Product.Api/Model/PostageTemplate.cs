using EfCoreExtensions.EntityBase;

namespace Product.Api.Model
{
    /// <summary>
    /// 邮费模板
    /// </summary>
    public class PostageTemplate : TenantBase
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 发货地 - 省
        /// </summary>
        public int DeliveryProvinceCode { get; set; }

        public string? DeliveryProvinceName { get; set; }

        /// <summary>
        /// 发货地 - 市
        /// </summary>
        public int DeliveryCityCode { get; set; }

        public string? DeliveryCityName { get; set; }

        /// <summary>
        /// 是否包邮
        /// </summary>
        public bool IsFreeShipping { get; set; }

        /// <summary>
        /// 处理时间(小时)，24小时内发货
        /// </summary>
        public int ProcessingTime  { get; set; }

        /// <summary>
        /// 是否首选、默认
        /// </summary>
        public bool FirstChoice { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }
}
