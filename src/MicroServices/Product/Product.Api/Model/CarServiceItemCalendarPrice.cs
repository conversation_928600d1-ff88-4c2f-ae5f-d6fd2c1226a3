using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Product.Api.Model;

/// <summary>
/// 用车 - 服务项目日历价
/// </summary>
public class CarServiceItemCalendarPrice : TenantBase
{
    /// <summary>
    /// 用车产品id
    /// </summary>
    public long CarProductId { get; set; }

    /// <summary>
    /// 服务项目id
    /// </summary>
    public long CarServiceItemId { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 基础售价
    /// </summary>
    public decimal SalePrice { get; set; }
    
    #region 产品价格配置信息

    /// <summary>
    /// 产品价格配置- 价格基准类型
    /// </summary>
    public PriceBasisType PriceBasisType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整类型
    /// </summary>
    public PriceAdjustmentType PriceAdjustmentType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整值
    /// </summary>
    public decimal? PriceAdjustmentValue { get; set; }

    #endregion

    public DateTime UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }
}
