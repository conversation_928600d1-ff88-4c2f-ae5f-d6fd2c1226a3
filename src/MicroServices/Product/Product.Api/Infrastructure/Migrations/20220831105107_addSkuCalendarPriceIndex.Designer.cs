// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Product.Api.Infrastructure;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20220831105107_addSkuCalendarPriceIndex")]
    partial class addSkuCalendarPriceIndex
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Product.Api.Model.Group", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CreateTime");

                    b.HasIndex("TenantId");

                    b.ToTable("Group", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.GroupItems", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("GroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("GroupId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupItems", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.MailProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CreateTime");

                    b.HasIndex("Enabled");

                    b.HasIndex("SellingDateBegin");

                    b.HasIndex("SellingDateEnd");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TenantId");

                    b.ToTable("MailProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DeliveryCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DeliveryProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("FirstChoice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsFreeShipping")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ProcessingTime")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CreateTime");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplateRegion", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<long>("ItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ItemId");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplateRegion", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplateSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AdditionalFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("AdditionalPieces")
                        .HasColumnType("int");

                    b.Property<decimal>("FreeShippingOverAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("FreeShippingOverPieces")
                        .HasColumnType("int");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("StartFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("StartPieces")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("PostageTemplateId");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplateSetting", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MediaType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Path")
                        .HasColumnType("varchar(255)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductPhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductRedundantData", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AvailableInventory")
                        .HasColumnType("int");

                    b.Property<bool>("HasCommission")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("MaxLinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MaxPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MinLinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MinPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sales")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AvailableInventory");

                    b.HasIndex("MaxPrice");

                    b.HasIndex("MinPrice");

                    b.HasIndex("ProductId")
                        .IsUnique();

                    b.HasIndex("Sales");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductRedundantData", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductResource", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CityCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProvinceCode")
                        .HasColumnType("bigint");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ResourceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketBusinessType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ResourceId");

                    b.HasIndex("ResourceType");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductResource", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AfterPurchaseDays")
                        .HasColumnType("int");

                    b.Property<string>("CostDescription")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ImagePath")
                        .HasColumnType("varchar(255)");

                    b.Property<decimal>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("NumberOfNights")
                        .HasColumnType("int");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("ValidityType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SellingPrice");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductSkuReservationCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductSkuId");

                    b.HasIndex("TenantId");

                    b.HasIndex("ProductId", "ProductSkuId", "Date")
                        .IsUnique();

                    b.ToTable("ProductSkuReservationCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Restaurant", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("HouseNumber")
                        .HasColumnType("varchar(10)");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("OperatingModel")
                        .HasColumnType("tinyint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ServiceTime")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("Location");

                    b.HasIndex("TenantId");

                    b.ToTable("Restaurant", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.RestaurantPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("RestaurantId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("RestaurantId");

                    b.HasIndex("TenantId");

                    b.ToTable("RestaurantPhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ShoppingCart", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Amount")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("ShoppingCart", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.TicketProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("AutoRefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("NeedConfirmReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedWriteOff")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TicketSaleType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CreateTime");

                    b.HasIndex("Enabled");

                    b.HasIndex("SellingDateBegin");

                    b.HasIndex("SellingDateEnd");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketBusinessType");

                    b.ToTable("TicketProduct", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
