using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class UpdateFieldV2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329021L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329022L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329037L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329070L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372343L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372345L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372348L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372351L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372354L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372357L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372360L);

            migrationBuilder.DeleteData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388078L);

            migrationBuilder.AlterColumn<string>(
                name: "DefaultValue",
                table: "Fields",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(100)",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329020L,
                columns: new[] { "DefaultValue", "FieldName", "FieldType", "Group", "GroupName" },
                values: new object[] { "{\"name\":null,\"firstName\":null,\"lastName\":null}", "姓名", (sbyte)9, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329030L,
                column: "DefaultValue",
                value: "{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"address\":null,\"longitude\":null,\"latitude\":null}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329036L,
                columns: new[] { "DefaultValue", "FieldCode", "FieldName", "FieldType", "Group", "GroupName", "ValueRange" },
                values: new object[] { "{\"cardType\":2,\"cardNo\":null}", "Card", "证件号", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329064L,
                column: "DefaultValue",
                value: "{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"address\":null,\"longitude\":null,\"latitude\":null}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329071L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"dialingCode\":\"[+86]\",\"phone\":null}", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372344L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"dialingCode\":\"[+86]\",\"phone\":null}", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372346L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"dialingCode\":\"[+86]\",\"phone\":null}", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372349L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"socialMediaType\":1,\"socialMedia\":null}", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372352L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"dialingCode\":\"[+86]\",\"phone\":null}", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372355L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"socialMediaType\":1,\"socialMedia\":null}", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372358L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"dialingCode\":\"[+86]\",\"phone\":null}", (sbyte)9, null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372361L,
                columns: new[] { "DefaultValue", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"socialMediaType\":1,\"socialMedia\":null}", null, null, null });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388072L,
                column: "DefaultValue",
                value: "{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"address\":null,\"longitude\":null,\"latitude\":null}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388079L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { "{\"dialingCode\":\"[+86]\",\"phone\":null}", (sbyte)9, null, null, null });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "DefaultValue",
                table: "Fields",
                type: "varchar(100)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329020L,
                columns: new[] { "DefaultValue", "FieldName", "FieldType", "Group", "GroupName" },
                values: new object[] { null, "中文姓名", (sbyte)0, "NameGroup", "姓名" });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329030L,
                column: "DefaultValue",
                value: null);

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329036L,
                columns: new[] { "DefaultValue", "FieldCode", "FieldName", "FieldType", "Group", "GroupName", "ValueRange" },
                values: new object[] { "1", "CardType", "证件类型", (sbyte)1, "CardGroup", "证件号", "[{\"code\":\"0\",\"name\":\"身份证\"},{\"code\":\"1\",\"name\":\"护照\"},{\"code\":\"2\",\"name\":\"港澳通行证\"},{\"code\":\"3\",\"name\":\"台湾通行证\"},{\"code\":\"4\",\"name\":\"驾驶证\"},{\"code\":\"5\",\"name\":\"台胞证\"},{\"code\":\"6\",\"name\":\"回乡证\"},{\"code\":\"7\",\"name\":\"军官证\"},{\"code\":\"8\",\"name\":\"外国人永久居留证\"},{\"code\":\"9\",\"name\":\"学生证\"},{\"code\":\"10\",\"name\":\"警官证\"}]" });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329064L,
                column: "DefaultValue",
                value: null);

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329071L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "DepartureHotelPhoneGroup", "出发酒店电话", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372344L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "ContactPhoneGroup", "联系手机号", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372346L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "LocalPhoneGroup", "当地手机号", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372349L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "ContactSocialMediaGroup", "社交媒体", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372352L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "TourGuidePhoneGroup", "导游电话", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372355L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "TourGuideSocialMediaGroup", "导游社交媒体", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372358L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "DriverPhoneGroup", "司机电话", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313372361L,
                columns: new[] { "DefaultValue", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, "DriverSocialMediaGroup", "司机社交媒体", (sbyte)1 });

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388072L,
                column: "DefaultValue",
                value: null);

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388079L,
                columns: new[] { "DefaultValue", "FieldType", "Group", "GroupName", "GroupSort" },
                values: new object[] { null, (sbyte)0, "DestinationHotelPhoneGroup", "目的酒店电话", (sbyte)1 });

            migrationBuilder.InsertData(
                table: "Fields",
                columns: new[] { "Id", "DefaultValue", "FieldCode", "FieldEnName", "FieldName", "FieldType", "FieldsGroupType", "Group", "GroupName", "GroupSort", "IsMultiple", "MaxDate", "Maxlength", "MinDate", "MinLength", "Pattern", "Placeholder", "Precision", "Remark", "Unit", "ValueRange" },
                values: new object[,]
                {
                    { 1281498705313329021L, null, "FirstName", "First Name", "英文名", (sbyte)0, (sbyte)0, "NameGroup", "姓名", (sbyte)1, null, null, 30m, null, null, null, "请输入", null, null, null, null },
                    { 1281498705313329022L, null, "LastName", "Last Name", "英文姓", (sbyte)0, (sbyte)0, "NameGroup", "姓名", (sbyte)2, null, null, 30m, null, null, null, "请输入", null, null, null, null },
                    { 1281498705313329037L, null, "CardNo", null, "证件号", (sbyte)0, (sbyte)1, "CardGroup", "证件号", (sbyte)1, null, null, 30m, null, null, null, "请输入证件号", null, null, null, null },
                    { 1281498705313329070L, "[+86]", "DepartureHotelDialingCode", null, "出发酒店电话区号", (sbyte)0, (sbyte)3, "DepartureHotelPhoneGroup", "出发酒店电话", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]" },
                    { 1281498705313372343L, "[+86]", "ContactDialingCode", null, "联系手机区号", (sbyte)0, (sbyte)2, "ContactPhoneGroup", "联系手机号", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]" },
                    { 1281498705313372345L, "[+86]", "LocalDialingCode", null, "当地手机区号", (sbyte)0, (sbyte)2, "LocalPhoneGroup", "当地手机号", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]" },
                    { 1281498705313372348L, "WeChat", "ContactSocialMediaType", null, "社交媒体类型", (sbyte)0, (sbyte)2, "ContactSocialMediaGroup", "社交媒体", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"WeChat\",\"name\":\"微信\"},{\"code\":\"WhatsApp\",\"name\":\"WhatsApp\"},{\"code\":\"Line\",\"name\":\"Line\"},{\"code\":\"Telegram\",\"name\":\"Telegram\"},{\"code\":\"Skype\",\"name\":\"Skype\"},{\"code\":\"Facebook\",\"name\":\"Facebook\"},{\"code\":\"WangWang\",\"name\":\"旺旺\"},{\"code\":\"KakaoTalk\",\"name\":\"KakaoTalk\"}]" },
                    { 1281498705313372351L, "[+86]", "TourGuideDialingCode", null, "导游电话区号", (sbyte)0, (sbyte)2, "TourGuidePhoneGroup", "导游电话", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]" },
                    { 1281498705313372354L, "WeChat", "TourGuideSocialMediaType", null, "导游社交媒体类型", (sbyte)0, (sbyte)2, "TourGuideSocialMediaGroup", "导游社交媒体", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"WeChat\",\"name\":\"微信\"},{\"code\":\"WhatsApp\",\"name\":\"WhatsApp\"},{\"code\":\"Line\",\"name\":\"Line\"},{\"code\":\"Telegram\",\"name\":\"Telegram\"},{\"code\":\"Skype\",\"name\":\"Skype\"},{\"code\":\"Facebook\",\"name\":\"Facebook\"},{\"code\":\"WangWang\",\"name\":\"旺旺\"},{\"code\":\"KakaoTalk\",\"name\":\"KakaoTalk\"}]" },
                    { 1281498705313372357L, "[+86]", "DriverDialingCode", null, "司机电话区号", (sbyte)0, (sbyte)2, "DriverPhoneGroup", "司机电话", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]" },
                    { 1281498705313372360L, "WeChat", "DriverSocialMediaType", null, "司机社交媒体类型", (sbyte)0, (sbyte)2, "DriverSocialMediaGroup", "司机社交媒体", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"WeChat\",\"name\":\"微信\"},{\"code\":\"WhatsApp\",\"name\":\"WhatsApp\"},{\"code\":\"Line\",\"name\":\"Line\"},{\"code\":\"Telegram\",\"name\":\"Telegram\"},{\"code\":\"Skype\",\"name\":\"Skype\"},{\"code\":\"Facebook\",\"name\":\"Facebook\"},{\"code\":\"WangWang\",\"name\":\"旺旺\"},{\"code\":\"KakaoTalk\",\"name\":\"KakaoTalk\"}]" },
                    { 1281498705313388078L, "[+86]", "DestinationHotelDialingCode", null, "目的酒店电话区号", (sbyte)0, (sbyte)3, "DestinationHotelPhoneGroup", "目的酒店电话", (sbyte)0, null, null, null, null, null, null, "请选择", null, null, null, "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]" }
                });
        }
    }
}
