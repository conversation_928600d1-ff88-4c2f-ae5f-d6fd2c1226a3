using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class UpdateLineProductSkuCalendarPriceIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductSkuId",
                table: "LineProductSkuCalendarPrice");

            migrationBuilder.CreateIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductId_TenantId_Date",
                table: "LineProductSkuCalendarPrice",
                columns: new[] { "LineProductId", "TenantId", "Date" });

            migrationBuilder.CreateIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductSkuId_TenantId_Date",
                table: "LineProductSkuCalendarPrice",
                columns: new[] { "LineProductSkuId", "TenantId", "Date" });

            migrationBuilder.CreateIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductSkuTypeItemId_TenantI~",
                table: "LineProductSkuCalendarPrice",
                columns: new[] { "LineProductSkuTypeItemId", "TenantId", "Date" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductId_TenantId_Date",
                table: "LineProductSkuCalendarPrice");

            migrationBuilder.DropIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductSkuId_TenantId_Date",
                table: "LineProductSkuCalendarPrice");

            migrationBuilder.DropIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductSkuTypeItemId_TenantI~",
                table: "LineProductSkuCalendarPrice");

            migrationBuilder.CreateIndex(
                name: "IX_LineProductSkuCalendarPrice_LineProductSkuId",
                table: "LineProductSkuCalendarPrice",
                column: "LineProductSkuId");
        }
    }
}
