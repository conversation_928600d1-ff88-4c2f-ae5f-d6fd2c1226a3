using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class AddPriceSettingHotelSaleType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StaffTag",
                table: "AgencyChannelPriceSettings");

            migrationBuilder.AddColumn<sbyte>(
                name: "HotelSaleType",
                table: "AgencyChannelPriceSettings",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HotelSaleType",
                table: "AgencyChannelPriceSettings");

            migrationBuilder.AddColumn<bool>(
                name: "StaffTag",
                table: "AgencyChannelPriceSettings",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);
        }
    }
}
