using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class UpdateProductOperatorUser : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreateTime",
                table: "ProductOperatorUser",
                type: "datetime",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "OperatorAssistantUserId",
                table: "ProductOperatorUser",
                type: "bigint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreateTime",
                table: "ProductOperatorUser");

            migrationBuilder.DropColumn(
                name: "OperatorAssistantUserId",
                table: "ProductOperatorUser");
        }
    }
}
