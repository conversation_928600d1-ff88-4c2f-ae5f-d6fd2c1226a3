using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class UpdateFieldsV16 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ValueFormat",
                table: "Fields");

            migrationBuilder.AddColumn<string>(
                name: "UIExtend",
                table: "Fields",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329031L,
                column: "UIExtend",
                value: "{\"dateType\":\"date\",\"format\":\"YYYY-MM-DD\",\"valueFormat\":\"yyyy-MM-dd\"}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329038L,
                column: "UIExtend",
                value: "{\"dateType\":\"date\",\"format\":\"YYYY-MM-DD\",\"valueFormat\":\"yyyy-MM-dd\"}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329039L,
                column: "UIExtend",
                value: "{\"dateType\":\"date\",\"format\":\"YYYY-MM-DD\",\"valueFormat\":\"yyyy-MM-dd\"}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388085L,
                column: "UIExtend",
                value: "{\"dateType\":\"datetime\",\"format\":\"YYYY-MM-DD HH:mm\",\"valueFormat\":\"yyyy-MM-dd HH:mm\"}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388086L,
                column: "UIExtend",
                value: "{\"dateType\":\"datetime\",\"format\":\"YYYY-MM-DD HH:mm\",\"valueFormat\":\"yyyy-MM-dd HH:mm\"}");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UIExtend",
                table: "Fields");

            migrationBuilder.AddColumn<string>(
                name: "ValueFormat",
                table: "Fields",
                type: "varchar(100)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329031L,
                column: "ValueFormat",
                value: "yyyy-MM-dd");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329038L,
                column: "ValueFormat",
                value: "yyyy-MM-dd");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329039L,
                column: "ValueFormat",
                value: "yyyy-MM-dd");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388085L,
                column: "ValueFormat",
                value: "yyyy-MM-dd HH:mm:ss");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388086L,
                column: "ValueFormat",
                value: "yyyy-MM-dd HH:mm:ss");
        }
    }
}
