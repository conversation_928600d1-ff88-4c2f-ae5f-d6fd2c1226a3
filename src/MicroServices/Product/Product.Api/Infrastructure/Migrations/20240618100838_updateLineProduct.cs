using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class updateLineProduct : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "DevelopUserId",
                table: "TicketProduct",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "OperatorUserId",
                table: "TicketProduct",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "DevelopUserId",
                table: "LineProduct",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "OperatorUserId",
                table: "LineProduct",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "DevelopUserId",
                table: "CarHailingProduct",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "OperatorUserId",
                table: "CarHailingProduct",
                type: "bigint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DevelopUserId",
                table: "TicketProduct");

            migrationBuilder.DropColumn(
                name: "OperatorUserId",
                table: "TicketProduct");

            migrationBuilder.DropColumn(
                name: "DevelopUserId",
                table: "LineProduct");

            migrationBuilder.DropColumn(
                name: "OperatorUserId",
                table: "LineProduct");

            migrationBuilder.DropColumn(
                name: "DevelopUserId",
                table: "CarHailingProduct");

            migrationBuilder.DropColumn(
                name: "OperatorUserId",
                table: "CarHailingProduct");
        }
    }
}
