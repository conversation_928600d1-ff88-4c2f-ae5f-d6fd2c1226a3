using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class AddProductCurrencyCode : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CostCurrencyCode",
                table: "TicketProduct",
                type: "varchar(20)",
                nullable: false,
                defaultValue: "CNY")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SaleCurrencyCode",
                table: "TicketProduct",
                type: "varchar(20)",
                nullable: false,
                defaultValue: "CNY")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CostCurrencyCode",
                table: "LineProduct",
                type: "varchar(20)",
                nullable: false,
                defaultValue: "CNY")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SaleCurrencyCode",
                table: "LineProduct",
                type: "varchar(20)",
                nullable: false,
                defaultValue: "CNY")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CostCurrencyCode",
                table: "CarHailingProduct",
                type: "varchar(20)",
                nullable: false,
                defaultValue: "CNY")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SaleCurrencyCode",
                table: "CarHailingProduct",
                type: "varchar(20)",
                nullable: false,
                defaultValue: "CNY")
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CostCurrencyCode",
                table: "TicketProduct");

            migrationBuilder.DropColumn(
                name: "SaleCurrencyCode",
                table: "TicketProduct");

            migrationBuilder.DropColumn(
                name: "CostCurrencyCode",
                table: "LineProduct");

            migrationBuilder.DropColumn(
                name: "SaleCurrencyCode",
                table: "LineProduct");

            migrationBuilder.DropColumn(
                name: "CostCurrencyCode",
                table: "CarHailingProduct");

            migrationBuilder.DropColumn(
                name: "SaleCurrencyCode",
                table: "CarHailingProduct");
        }
    }
}
