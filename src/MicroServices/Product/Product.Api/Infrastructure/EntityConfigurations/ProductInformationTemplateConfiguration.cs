using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Product.Api.Infrastructure.EntityConfigurations;

public class ProductInformationTemplateConfiguration : TenantBaseConfiguration<Model.ProductInformationTemplate>, IEntityTypeConfiguration<Model.ProductInformationTemplate>
{
    public void Configure(EntityTypeBuilder<Model.ProductInformationTemplate> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.ProductId)
            .HasColumnType("bigint")
            .IsRequired();

        builder.Property(s => s.ProductSkuId)
         .HasColumnType("bigint");

        builder.Property(s => s.TemplateId)
            .HasColumnType("bigint")
            .IsRequired();

        builder.Property(s => s.TemplateType)
            .HasColumnType("tinyint")
            .IsRequired();

        builder.Property(s => s.ProductType)
          .HasColumnType("tinyint")
          .IsRequired();

        builder.Property(s => s.ProductTemplateType)
          .HasColumnType("tinyint")
          .IsRequired();

        builder.Property(s => s.CreateTime)
              .HasColumnType("datetime");

    }
}
