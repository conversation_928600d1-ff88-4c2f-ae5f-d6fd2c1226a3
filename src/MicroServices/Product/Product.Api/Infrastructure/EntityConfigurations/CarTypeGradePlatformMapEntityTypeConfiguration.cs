using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Product.Api.Infrastructure.EntityConfigurations
{
    public class CarTypeGradePlatformMapEntityTypeConfiguration : TenantBaseConfiguration<Model.CarTypeGradePlatformMap>, IEntityTypeConfiguration<Model.CarTypeGradePlatformMap>
    {
        public void Configure(EntityTypeBuilder<Model.CarTypeGradePlatformMap> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.PlatformMapKey)
                .HasColumnType("varchar(128)")
                .IsRequired();

            builder.Property(s => s.CarTypeGradeId)
                .HasColumnType("bigint")
                .IsRequired();

            builder.Property(s => s.SupplierApiType)
                .HasColumnType("int")
                .IsRequired();
        }
    }
}
