using Contracts.Common.Product.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Product.Api.Infrastructure.EntityConfigurations
{
    public class OpenSupplierBasicProductEntityTypeConfiguration : KeyBaseConfiguration<Model.OpenSupplierBasicProduct>,
        IEntityTypeConfiguration<Model.OpenSupplierBasicProduct>
    {
        public void Configure(EntityTypeBuilder<Model.OpenSupplierBasicProduct> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.OpenSupplierType)
                .HasColumnType("tinyint");

            builder.Property(s => s.TimeSlotId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.TimeSlotName)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.SkuId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.SkuName)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.OptionId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.OptionName)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.ProductId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.ProductName)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.IsTimeSlot)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.MatchStatus)
                .HasColumnType("tinyint")
                .HasDefaultValue(OpenSupplierBasicProductMatchStatus.WaitingMatch);
            
            builder.Property(s => s.SkuPriceType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

        }
    }
}