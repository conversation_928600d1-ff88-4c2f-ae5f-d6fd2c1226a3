using Contracts.Common.Payment.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Product.Api.Infrastructure.EntityConfigurations
{
    public class CarHailingProductEntityTypeConfiguration : TenantBaseConfiguration<Model.CarHailingProduct>, IEntityTypeConfiguration<Model.CarHailingProduct>
    {
        public void Configure(EntityTypeBuilder<Model.CarHailingProduct> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Title)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.LanguageType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.CarHailingType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.DeparturePointId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.DestinationPointId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.ReservationDaysInAdvance)
                .HasColumnType("int");
            
            builder.Property(s => s.Instructions)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.KindReminder)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.Content)
                .HasColumnType("mediumtext");
            
            builder.Property(s => s.IsSupportRefund)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.RefundRate)
                .HasColumnType("decimal(8,4)")
                .IsRequired(false);

            builder.Property(s => s.RefundBeforeTravelDateDay)
                .HasColumnType("int")
                .IsRequired(false);

            builder.Property(s => s.RefundTravelDateTime)
                .HasColumnType("time")
                .IsRequired(false);

            builder.Property(s => s.SellingDateBegin)
                .HasColumnType("datetime")
                .IsRequired(false);

            builder.Property(s => s.SellingDateEnd)
                .HasColumnType("datetime")
                .IsRequired(false);

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.CostCurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString())
                .IsRequired();
            
            builder.Property(s => s.SaleCurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString())
                .IsRequired();

            builder.Property(s => s.DevelopUserId)
             .HasColumnType("bigint");

            builder.Property(s => s.OperatorUserId)
            .HasColumnType("bigint");
        }
    }
}
