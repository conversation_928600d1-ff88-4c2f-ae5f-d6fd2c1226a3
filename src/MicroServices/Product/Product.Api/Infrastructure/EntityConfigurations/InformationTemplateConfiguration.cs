using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Product.Api.Infrastructure.EntityConfigurations;

public class InformationTemplateConfiguration : TenantBaseConfiguration<Model.InformationTemplate>, IEntityTypeConfiguration<Model.InformationTemplate>
{
    public void Configure(EntityTypeBuilder<Model.InformationTemplate> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.TemplateName)
            .HasColumnType("varchar(50)")
            .IsRequired();

        builder.Property(s => s.TemplateType)
            .HasColumnType("tinyint")
            .IsRequired();

        builder.Property(s => s.CreateTime)
              .HasColumnType("datetime");

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.IsDelete)
           .HasColumnType("tinyint(1)")
           .HasDefaultValue(false);
    }
}
