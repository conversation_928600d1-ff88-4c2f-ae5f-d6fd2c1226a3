using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Product.DTOs.CarHailingProduct;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services.Interfaces;
using static System.Net.HttpStatusCode;
namespace Product.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class CarHailingProductController : ControllerBase
{
    private readonly ICarHailingProductService _carHailingProductService;
    public CarHailingProductController(
        ICarHailingProductService carHailingProductService)
    {
        _carHailingProductService = carHailingProductService;
    }

    /// <summary>
    /// 修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long),(int)OK)]
    [SwaggerResponseExt(default,ErrorTypes.Product.DuplicateCarHailingPoint)]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> Add(AddCarHailingProductInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        var user = HttpContext.GetCurrentUser();
        if (!input.DevelopUserId.HasValue)
            input.DevelopUserId = user.userid;
        var result = await _carHailingProductService.Add(tenantId,input);
        return Ok(result);
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default,ErrorTypes.Product.DuplicateCarHailingPoint)]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> Edit(EditCarHailingProductInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        await _carHailingProductService.Edit(tenantId,input);
        return Ok();
    }

    /// <summary>
    /// 详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetCarHailingProductOutput),(int)OK)]
    public async Task<IActionResult> Get(long id)
    {
        var result = await _carHailingProductService.Get(new GetCarHailingProductInput
        {
            Id = id
        });

        return Ok(result);
    }

    /// <summary>
    /// 微商城-获取产品详情
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(GetCarHailingProductOutput),(int)OK)]
    public async Task<IActionResult> MallGet(GetCarHailingProductInput input)
    {
        input.InSale = true;
        var result = await _carHailingProductService.Get(input);
        return Ok(result);
    }

    /// <summary>
    /// 分页
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(
        typeof(PagingModel<SearchCarHailingProductOutput, SearchCarHailingProductSummary>), (int)OK)]
    public async Task<IActionResult> Search(
        SearchCarHailingProductInput input)
    {
        input.NeedSummary = true;
        var result = await _carHailingProductService.Search(input);
        return Ok(result);
    }

    /// <summary>
    /// 设置上下架
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default,ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> SetEnabled(BatchEnabledCarHailingProductInput input)
    {
        await _carHailingProductService.SetEnabled(input);
        return Ok();
    }
}