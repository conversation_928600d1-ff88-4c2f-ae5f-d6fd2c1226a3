using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Product.DTOs.CarHailingCalendarPrice;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services.Interfaces;
using static System.Net.HttpStatusCode;

namespace Product.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class CarHailingCalendarPricesController : ControllerBase
{
    private readonly ICarHailingCalendarPricesService _calendarPricesService;
    public CarHailingCalendarPricesController(
        ICarHailingCalendarPricesService calendarPricesService)
    {
        _calendarPricesService = calendarPricesService;
    }

    [HttpPost]
    public async Task<IActionResult> Update(UpdateCarHailingCalendarPricesInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        await _calendarPricesService.Update(tenantId,input);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<GetCarHailingCalendarPriceOutput>),(int)OK)]
    public async Task<IActionResult> Get(GetCarHailingCalendarPriceInput input)
    {
        var result = await _calendarPricesService.Get(input);
        return Ok(result);
    }
    
    [HttpPost]
    [ProducesResponseType(typeof(List<GetCarHailingCalendarPriceOutput>),(int)OK)]
    public async Task<IActionResult> MallGet(GetCarHailingCalendarPriceInput input)
    {
        input.Enabled = true;
        var data = await _calendarPricesService.Get(input);
        var result = data
            .Where(x => x.PriceAndQuantityInfos.Any(y => y.Enabled))
            .ToList();
        if (result.Any())
        {
            foreach (var item in result
                         .SelectMany(items => items.PriceAndQuantityInfos))
            {
                item.CostPrice = null;
            }
        }
        return Ok(result);
    }
}