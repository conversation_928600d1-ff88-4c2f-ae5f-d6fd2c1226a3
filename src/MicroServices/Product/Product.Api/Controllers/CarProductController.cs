using Common.Swagger;
using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarProductSku;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services;
using Product.Api.Services.Interfaces;
using System.Net;

namespace Product.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class CarProductController : ControllerBase
{

    private readonly ICarProductService _carProductService;
    public CarProductController(
        ICarProductService carProductService)
    {
        _carProductService = carProductService;
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> Add(AddCarProductInput input)
    {
        var result = await _carProductService.Add(input);
        return Ok(result);
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> Update(UpdateCarProductInput input)
    {
        await _carProductService.Update(input);
        return Ok();
    }


    /// <summary>
    /// 分页
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(
        typeof(PagingModel<SearchCarProductOuput, SearchCarProductSummary>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchCarProductInput input)
    {
        var result = await _carProductService.Search(input);
        return Ok(result);
    }

    /// <summary>
    /// 详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(CarProductDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var result = (await _carProductService.Details(id)).FirstOrDefault();
        return Ok(result);
    }

    /// <summary>
    /// 多产品详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<CarProductDetailOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Details(params long[] ids)
    {
        var result = await _carProductService.Details(ids);
        return Ok(result);
    }

    /// <summary>
    /// 设置上下架
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetEnabled(SetCarProductEnabledInput input)
    {
        await _carProductService.SetEnabled(input);
        return Ok();
    }


    /// <summary>
    /// 套餐价格和项目价格
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(
        typeof(PagingModel<GetCarProductsAndSkuOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetProductsAndSku(GetProductsAndSkuInput input)
    {
        var result = await _carProductService.GetProductsAndSku(input);
        return Ok(result);
    }

    /// <summary>
    /// 产品明细、套餐价格和项目价格-当天全部可售的
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(CarProductDetailCalendarPricesOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetProductPriceDetails(CarProductDetailCalendarPricesInput input)
    {
        var result = await _carProductService.GetProductPriceDetails(input);
        return Ok(result);
    }

    /// <summary>
    /// 产品套餐、项目、语言下拉
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(CarProductSelectOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetProductSelects(GetProductSelectsInput input)
    {
        var result = await _carProductService.GetProductSelects(input);
        return Ok(result);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetMozio(List<long> productIdsInput)
    {
        var result = await _carProductService.SetMozio(productIdsInput);
        return Ok(result);
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetCompensationCarProductOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCompensationCarProduct()
    {
        var result = await _carProductService.GetCompensationCarProduct();
        return Ok(result);
    }
}
