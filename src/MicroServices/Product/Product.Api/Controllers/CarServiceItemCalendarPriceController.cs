using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.DTOs.CarServiceItemCalendarPrices;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services;
using Product.Api.Services.Interfaces;

namespace Product.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class CarServiceItemCalendarPriceController : ControllerBase
{
    private readonly ICarServiceItemCalendarPricesService _carServiceItemCalendarPricesService;
    public CarServiceItemCalendarPriceController(ICarServiceItemCalendarPricesService carServiceItemCalendarPricesService)
    {
        _carServiceItemCalendarPricesService = carServiceItemCalendarPricesService;
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(UpdateCarServiceItemCalendarPriceInput input)
    {
        await _carServiceItemCalendarPricesService.Update(input);
        return Ok();
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> BatchUpdate(BatchUpdateCarServiceItemCalendarPriceInput input)
    {
        await _carServiceItemCalendarPricesService.BatchUpdate(input);
        return Ok();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetCarServiceItemCalendarPriceOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get(GetCarServiceItemCalendarPriceInput input)
    {
        var result = await _carServiceItemCalendarPricesService.Get(input);
        return Ok(result);
    }
}
