using Common.Swagger;
using Contracts.Common.Product.DTOs.ProductRedundantData;
using Contracts.Common.Product.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services.Interfaces;

namespace Product.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class ProductRedundantDataController : ControllerBase
    {
        private readonly IProductRedundantDataService _productRedundantDataService;
        public ProductRedundantDataController(
            IProductRedundantDataService productRedundantDataService) 
        {
            _productRedundantDataService = productRedundantDataService;
        }

        [HttpPost]
        [ProducesResponseType(typeof(List<GetProductRedundantDataOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetByIds(List<long> productIds) 
        {
            var result = await _productRedundantDataService.GetByIds(productIds);
            return Ok(result);
        }

        #region CapSubscribe

        [NonAction]
        [CapSubscribe(CapTopics.Product.SetProductHasCommission)]
        public async Task SetProductHasCommission(SetProductHasCommissionMessage recevie)
        {
            await _productRedundantDataService.SetProductHasCommission(recevie);
        }

        [NonAction]
        [CapSubscribe(CapTopics.Product.SetProductRedundantSubscribe)]
        public async Task SetProductRedundant(SetProductRedundantMessage recevie)
        {
            await _productRedundantDataService.SetProductRedundantSubscribe(recevie);
        }

        #endregion
    }
}
