using Contracts.Common.Product.DTOs;
using Contracts.Common.Product.DTOs.OpenChannel;
using Contracts.Common.Product.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Product.Api.Services.Interfaces;

namespace Product.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class BaseProductController : ControllerBase
{
    private readonly IBaseProductService _baseProductService;

    public BaseProductController(IBaseProductService baseProductService)
    {
        _baseProductService = baseProductService;
    }

    #region 订阅-停用供应商时，下架关联产品

    [NonAction]
    [CapSubscribe(CapTopics.Product.SetProductDisabled)]
    public async Task SetProductDisabled(SetProductDisabledMessage receive)
    {
        await _baseProductService.SetProductDisabled(receive);
    }

    #endregion

    /// <summary>
    /// 获取产品首图
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(List<ProductFirstPhoto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetFirstPhoto(params long[] productIds)
    {
        var result = await _baseProductService.GetFirstPhoto(productIds.ToList());
        return Ok(result);
    }

    /// <summary>
    /// 开放平台-渠道产品校验
    /// </summary>
    /// <param name="channelProductSkuId"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<OpenChannelCheckProductOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CheckOpenChannelProduct(params long[] channelProductSkuIds)
    {
        var result = await _baseProductService.CheckOpenChannelProduct(channelProductSkuIds);
        return Ok(result);
    }
}
