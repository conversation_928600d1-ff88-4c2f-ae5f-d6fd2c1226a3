using Contracts.Common.Product.DTOs.LineProductTimeSlot;

namespace Product.Api.Services.Interfaces;

public interface ILineProductTimeSlotService
{
    /// <summary>
    /// 检查时间段
    /// <remarks>
    /// <para>拉取供应端时段场次数据与saas现有时段处理</para>
    /// </remarks>
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CheckTimeSlotOutput>> CheckTimeSlot(CheckTimeSlotInput input,long tenantId);
}