using Contracts.Common.Product.DTOs.Store;
using EfCoreExtensions.Abstract;

namespace Product.Api.Services.Interfaces;

public interface IStoreService
{
    /// <summary>
    /// 新增资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Add(AddStoreInput input);

    /// <summary>
    /// 修改资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Update(UpdateStoreInput input);

    /// <summary>
    /// 查询资源-资源ID
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetStoreOutput> Get(long id);

    /// <summary>
    /// 查询资源-资源ID集合
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<List<GetStoreByIdsOutPut>> GetStoreByIds(GetStoreByIdsInput input);

    /// <summary>
    /// 分页查询资源
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchStoreOutput>> Search(SearchStoreInput input);

    /// <summary>
    /// 获取商户所有门店
    /// </summary>
    /// <returns></returns>
    Task<List<GetAllStoreOutput>> GetAll();
}