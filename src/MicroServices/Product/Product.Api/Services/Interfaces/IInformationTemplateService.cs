using Contracts.Common.Order.DTOs.HotelBillTemplate;
using Contracts.Common.Product.DTOs.InformationTemplate;
using EfCoreExtensions.Abstract;

namespace Product.Api.Services.Interfaces;

public interface IInformationTemplateService
{
    Task<long> Save(SaveInformationTemplateInput input);

    Task<PagingModel<SearchInformationTemplateOutput>> Search(SearchInformationTemplateInput input);

    Task<List<InformationTemplateOutput>> Details(List<long> ids);

    Task Delete(List<long> ids);
}
