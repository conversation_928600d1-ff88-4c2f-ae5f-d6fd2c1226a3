using Contracts.Common.Product.DTOs.Group;
using EfCoreExtensions.Abstract;

namespace Product.Api.Services.Interfaces
{
    public interface IGroupService
    {
        /// <summary>
        /// 新增分组
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task AddGroup(AddGroupInput input);

        /// <summary>
        /// 修改分组
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task SetGroup(SetGroupInput input);

        /// <summary>
        /// 删除分组
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task RemoveGroup(long id);

        /// <summary>
        /// 查询分组-分组ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<GetGroupByIdOutput> GetGroupById(long id);

        /// <summary>
        /// 查询所有分组
        /// </summary>
        /// <returns></returns>
        Task<List<GetAllGroupOutput>> GetAllGroup();

        /// <summary>
        /// 查询产品分组
        /// </summary>
        /// <param name="productIds"></param>
        /// <returns></returns>
        Task<List<GetGroupByProductIdsOutput>> GetGroupByProductIds(List<long> productIds);

        /// <summary>
        /// 查询产品分组
        /// </summary>
        /// <param name="productIds"></param>
        /// <returns></returns>
        Task<List<GetProductGroupsOutput>> GetProductGroups(IEnumerable<long> productIds);

        /// <summary>
        /// 查询分组
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<SearchGroupOutput>> SearchGroup(SearchGroupInput input);

        /// <summary>
        /// 新增分组项
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task SetGroupItems(SetGroupItemsInput input);

        /// <summary>
        /// 批量设置产品分组
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<bool> UpdateGroupItems(UpdateGroupItemInput input);

        /// <summary>
        /// 分组ID查询分页产品
        /// </summary>
        Task<PagingModel<GetProductsOutput>> GetProducts(GetProductsInput input);

        /// <summary>
        /// 统计分组下的产品信息
        /// </summary>
        /// <returns></returns>
        Task<List<GetProductStatisticsOutput>> GetStatistics(long groupId);
    }
}
