using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.DTOs.CarServiceItem;

namespace Product.Api.Services.Interfaces;

public interface ICarProductSkuService
{
    Task<long> Add(AddCarProductSkuInput input);

    Task Update(UpdateCarProductSkuInput input);

    ValueTask<List<CarProductSkuOutput>> Details(CarProductSkuInput input);

    Task Delete(List<long> ids);

    Task SetB2bSellingStatus(Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput input);

    Task SetEnabled(SetCarProductSkuEnabledInput input);
}
