using Contracts.Common.Product.DTOs.CarHailingProduct;
using EfCoreExtensions.Abstract;

namespace Product.Api.Services.Interfaces;

public interface ICarHailingProductService
{
    /// <exception cref="ErrorTypes.Product.DuplicateCarHailingPoint"></exception>
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    Task<long> Add(long tenantId, AddCarHailingProductInput input);

    /// <exception cref="ErrorTypes.Product.DuplicateCarHailingPoint"></exception>
    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    Task Edit(long tenantId, EditCarHailingProductInput input);

    Task<GetCarHailingProductOutput> Get(GetCarHailingProductInput input);

    Task<PagingModel<SearchCarHailingProductOutput, SearchCarHailingProductSummary>> Search(
        SearchCarHailingProductInput input);

    /// <exception cref="ErrorTypes.Tenant.SupplierInvalid"></exception>
    Task SetEnabled(BatchEnabledCarHailingProductInput input);
}