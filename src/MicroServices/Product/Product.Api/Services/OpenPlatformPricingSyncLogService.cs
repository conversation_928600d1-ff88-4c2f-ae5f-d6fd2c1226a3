using Contracts.Common.Product.DTOs.OpenPlatformPricingSyncLog;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Product.Api.Services.Interfaces;

namespace Product.Api.Services;

public class OpenPlatformPricingSyncLogService : IOpenPlatformPricingSyncLogService
{
    private readonly CustomDbContext _dbContext;
    public OpenPlatformPricingSyncLogService(
        CustomDbContext dbContext)
    {
        _dbContext = dbContext;
    }
    
    public async Task Add(AddSyncLogInput input)
    {
        if(input.Logs.Any() is false) return;
        
        var logs = new List<OpenPlatformPricingSyncLog>();
        foreach (var item in input.Logs)
        {
            var log = new OpenPlatformPricingSyncLog
            {
                ProductType = item.ProductType,
                PlatformType = item.PlatformType,
                ChannelType = item.ChannelType,
                SyncType = item.SyncType,
                SyncResult = item.SyncResult,
                FailedMessage = item.FailedMessage,
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                SkuId = item.SkuId,
                SkuName = item.SkuName,
                SkuTypeItemName = item.SkuTypeItemName,
                SyncStartDate = item.SyncStartDate,
                SyncEndDate = item.SyncEndDate,
                SupplierIsSale = item.SupplierIsSale,
            };
            log.SetTenantId(item.TenantId);
            logs.Add(log);
        }
        await _dbContext.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<PagingModel<SearchSyncLogOutput>> Search(SearchSyncLogInput input)
    {
        var result = await _dbContext.OpenPlatformPriceInventorySyncLogs.AsNoTracking()
            .Where(x => x.ProductId == input.ProductId)
            .Where(x => x.ProductType == input.ProductType)
            .Where(x => x.PlatformType == input.PlatformType)
            .Select(x => new SearchSyncLogOutput
            {
                Id = x.Id,
                ProductType = x.ProductType,
                PlatformType = x.PlatformType,
                ChannelType = x.ChannelType,
                SyncType = x.SyncType,
                SyncResult = x.SyncResult,
                FailedMessage = x.FailedMessage,
                ProductId = x.ProductId,
                ProductName = x.ProductName,
                SkuId = x.SkuId,
                SkuName = x.SkuName,
                SkuTypeItemName = x.SkuTypeItemName,
                SyncStartDate = x.SyncStartDate,
                SyncEndDate = x.SyncEndDate,
                CreateTime = x.CreateTime,
                SupplierIsSale = x.SupplierIsSale
            })
            .OrderByDescending(x => x.Id)
            .PagingAsync(input.PageIndex, input.PageSize);

        return result;
    }
}