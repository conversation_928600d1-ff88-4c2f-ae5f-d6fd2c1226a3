using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.FunProduct;
using Contracts.Common.Product.Enums;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using Product.Api.Model;
using Product.Api.Services.Interfaces;
using System.Linq;

namespace Product.Api.Services;

public class FunProductService : IFunProductService
{
    private readonly CustomDbContext _dbContext;

    public FunProductService(CustomDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<CarProductSearchOutput>> CarProductSearch(SearchInput input)
    {
        Point? targetLocation = null;
        if (input.TargetLatitude.HasValue && input.TargetLongitude.HasValue)
        {
            var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);
            targetLocation = geometryFactory.CreatePoint(new Coordinate(input.TargetLongitude!.Value, input.TargetLatitude!.Value));
        }
        //int aheadDays = (int)Math.Floor(input.TravelDate.Subtract(DateTime.Today).TotalDays);
        var query = _dbContext.CarProducts
            .Join(_dbContext.CarProductSkus, c => c.Id, s => s.CarProductId, (CarProduct, CarProductSku) => new
            {
                Product = new
                {
                    CarProduct,
                    CarProductSku
                }
            })
            .IgnoreQueryFilters()
            .Where(x => x.Product.CarProduct.TenantId == input.TenantId
                && x.Product.CarProduct.Enabled
                && (!x.Product.CarProduct.SellingDateBegin.HasValue || x.Product.CarProduct.SellingDateBegin >= DateTime.Today)
                && (!x.Product.CarProduct.SellingDateEnd.HasValue || x.Product.CarProduct.SellingDateEnd <= DateTime.Today)
            )
            //.Where(x => x.Product.CarProduct.ReservationDaysInAdvance <= aheadDays)
            .Where(x => !x.Product.CarProductSku.IsDeleted && x.Product.CarProductSku.Enabled && x.Product.CarProductSku.B2bSellingStatus)
            .WhereIF(input.CityCode.HasValue, x => x.Product.CarProduct.CityCode == input.CityCode!.Value)
            .WhereIF(input.CarProductTypes?.Any() is true, x => input.CarProductTypes!.Contains(x.Product.CarProduct.CarProductType))
            .WhereIF(input.AirportTransferTypes?.Any() is true, x => input.AirportTransferTypes!.Contains(x.Product.CarProductSku.AirportTransferType.Value))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), x => x.Product.CarProduct.Title.Contains(input.Keyword!))
            .WhereIF(input.TargetDistance.HasValue && targetLocation != null,
                    x => x.Product.CarProduct.Location.IsWithinDistance(targetLocation,
                    input.TargetDistance!.Value * 1000))
            .Select(g => new CarProductSearchOutput
            {
                Id = g.Product.CarProduct.Id,
                Title = g.Product.CarProduct.Title,
                EnTitle = g.Product.CarProduct.EnTitle,
                CarProductType = g.Product.CarProduct.CarProductType,
                CountryCode = g.Product.CarProduct.CountryCode,
                CountryName = g.Product.CarProduct.CountryName,
                CityCode = g.Product.CarProduct.CityCode,
                CityName = g.Product.CarProduct.CityName,
                AirportId = g.Product.CarProduct.AirportId,
                AirportName = g.Product.CarProduct.AirportName,
                CostCurrencyCode = g.Product.CarProduct.CostCurrencyCode,
                SaleCurrencyCode = g.Product.CarProduct.SaleCurrencyCode,
                CarProductSkuId = g.Product.CarProductSku.Id,
                //CostPrice = g.CostPrice,
                //SalePrice = g.SalePrice,
                CoordinateType = g.Product.CarProduct.CoordinateType,
                GooglePalceId = g.Product.CarProduct.GooglePalceId,
                Location = g.Product.CarProduct.Location,
                Distance = g.Product.CarProduct.Location.Distance(targetLocation) / 1000,
                CancelType = g.Product.CarProduct.CancelType,
                PurchaseSourceType = g.Product.CarProduct.PurchaseSourceType,
            })
            .OrderByDescending(x => x.Id);

        var result = await query.Take(input.Size).ToListAsync();
        var travelDate = input.TravelDate ?? DateTime.Today;
        foreach (var item in result) { item.TravelDate = travelDate; }

        var carProductSkuIds = result.Select(x => x.CarProductSkuId).ToArray();
        var prices = await _dbContext.CarProductSkuCalendarPrices.IgnoreQueryFilters()
            .Where(x => x.TenantId == input.TenantId && carProductSkuIds.Contains(x.CarProductSkuId))
            .Where(x => x.Date >= travelDate)
            .GroupBy(x => x.CarProductSkuId)
            .Select(x => new { CarProductSkuId = x.Key, Date = x.Min(s => s.Date) })
            .Join(_dbContext.CarProductSkuCalendarPrices.IgnoreQueryFilters(), q => new { q.CarProductSkuId, q.Date },
                x => new { x.CarProductSkuId, x.Date }, (q, x) => new
                {
                    x.TenantId,
                    x.CarProductSkuId,
                    x.Date,
                    x.CostPrice,
                    x.SalePrice
                })
            .Where(x => x.TenantId == input.TenantId)
            .ToListAsync();
        foreach (var item in result)
        {
            var price = prices.FirstOrDefault(x => x.CarProductSkuId == item.CarProductSkuId);
            if (price is not null)
            {
                item.CostPrice = price.CostPrice;
                item.SalePrice = price.SalePrice;
                item.TravelDate = price.Date;
            }
            // 因为接口产品的价格是不确定的，是根据出发地、目的地查询才有，所以设置日历价的时候，这里都是 0 那样前端显示也是 0 了
            // 设置为 null , 进详情查看
            if (item.PurchaseSourceType == CarProductPurchaseSourceType.InterfaceDock)
            {
                item.CostPrice = null;
                item.SalePrice = null;
            }
        }
        return result;
    }

    public async Task<List<LineProductSearchOutput>> LineProductSearch(SearchInput input)
    {
        Point? targetLocation = null;
        if (input.TargetLatitude.HasValue && input.TargetLongitude.HasValue)
        {
            var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);
            targetLocation = geometryFactory.CreatePoint(new Coordinate(input.TargetLongitude!.Value, input.TargetLatitude!.Value));
        }
        //int aheadDays = (int)Math.Floor(input.TravelDate.Subtract(DateTime.Today).TotalDays);
        TimeSpan timeOfDay = DateTime.Now.TimeOfDay;
        var query = _dbContext.LineProduct
            .Join(_dbContext.LineProductSku, l => l.Id, s => s.LineProductId, (LineProduct, LineProductSku) => new
            {
                Product = new
                {
                    LineProduct,
                    LineProductSku
                }
            })
            //.Join(_dbContext.LineProductSkuCalendarPrice, l => l.LineProductSku.Id, p => p.LineProductSkuId, (Product, Price) => new
            //{
            //    Product,
            //    Price
            //})
            .IgnoreQueryFilters()
            .Where(x => x.Product.LineProduct.TenantId == input.TenantId)
            .Where(x => !x.Product.LineProduct.IsDeleted && x.Product.LineProduct.Enabled
                && (!x.Product.LineProduct.SellingDateBegin.HasValue || x.Product.LineProduct.SellingDateBegin >= DateTime.Today)
                && (!x.Product.LineProduct.SellingDateEnd.HasValue || x.Product.LineProduct.SellingDateEnd <= DateTime.Today)
            )
            //.Where(x => x.Product.LineProduct.ReservationDaysInAdvance < aheadDays
            //    || (x.Product.LineProduct.ReservationDaysInAdvance == aheadDays && x.Product.LineProduct.ReservationTimeInAdvance > timeOfDay))
            .Where(x => x.Product.LineProductSku.Enabled && x.Product.LineProductSku.B2bSellingStatus)
            //.Where(x => x.Price.Type == LineSkuPriceType.Adult)
            //.Where(x => x.Price.Date == input.TravelDate)
            .WhereIF(input.CityCode.HasValue, x => x.Product.LineProduct.DestinationCityId == input.CityCode!.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), x => x.Product.LineProduct.Title.Contains(input.Keyword!))
            .WhereIF(input.TargetDistance.HasValue && targetLocation != null,
                    x => x.Product.LineProduct.DestinationLocation.IsWithinDistance(targetLocation,
                    input.TargetDistance!.Value * 1000))
            .Select(x => new LineProductSearchOutput
            {
                Id = x.Product.LineProduct.Id,
                Title = x.Product.LineProduct.Title,
                EnTitle = x.Product.LineProduct.EnTitle,
                CityCode = x.Product.LineProduct.DestinationCityId,
                CityName = x.Product.LineProduct.DestinationCityName,
                CountryCode = x.Product.LineProduct.DestinationCountryId,
                CountryName = x.Product.LineProduct.DestinationCountryName,
                DepartureCityCode = x.Product.LineProduct.DepartureCityId,
                DepartureCityName = x.Product.LineProduct.DepartureCityName,
                Days = x.Product.LineProduct.Days,
                SellPointDescribe = x.Product.LineProduct.SellPointDescribe,
                CostCurrencyCode = x.Product.LineProduct.CostCurrencyCode,
                SaleCurrencyCode = x.Product.LineProduct.SaleCurrencyCode,
                LinkProductSkuId = x.Product.LineProductSku.Id,
                //Type = x.Price.Type,
                //SalePrice = x.Price.Price ?? 0,
                //CostPrice = x.Price.CostPrice ?? 0,
                CoordinateType = x.Product.LineProduct.DestinationCoordinateType,
                GooglePalceId = x.Product.LineProduct.DestinationGooglePalceId,
                Location = x.Product.LineProduct.DestinationLocation,
                Distance = x.Product.LineProduct.DestinationLocation.Distance(targetLocation) / 1000
            })
            .OrderByDescending(x => x.Id);
        var result = await query.Take(input.Size).ToListAsync();
        var travelDate = input.TravelDate ?? DateTime.Today;
        foreach (var item in result) { item.TravelDate = travelDate; }

        var linkProductSkuIds = result.Select(x => x.LinkProductSkuId).ToArray();
        var skuCalendarPrices = await _dbContext.LineProductSkuCalendarPrice.IgnoreQueryFilters()
            .Where(x => x.TenantId == input.TenantId && linkProductSkuIds.Contains(x.LineProductSkuId) &&
                        x.Type != LineSkuPriceType.RoomPriceDifference)
            .Where(x => x.Date >= travelDate)
            .Select(x => new
            {
                x.TenantId,
                x.LineProductSkuId,
                x.Date,
                x.Type,
                x.CostPrice,
                x.Price
            })
            .ToListAsync();//查出线路套餐的价格
        var groupPrices = skuCalendarPrices.GroupBy(x => x.LineProductSkuId)
            .Select(x => new
            {
                LineProductSkuId = x.Key,
                Date = x.Min(s => s.Date),
                Type = x.Min(s => s.Type)
            })
            .ToList();//获取最小的日期和最小的年龄段类型
        var prices = (from skuPrice in skuCalendarPrices
                      join groupPrice in groupPrices on skuPrice.LineProductSkuId equals groupPrice.LineProductSkuId
                      where skuPrice.Date == groupPrice.Date && skuPrice.Type == groupPrice.Type
                      select new
                      {
                          skuPrice.TenantId,
                          skuPrice.LineProductSkuId,
                          skuPrice.Date,
                          skuPrice.Type,
                          skuPrice.CostPrice,
                          skuPrice.Price
                      })
            .ToList();//获取最小的日期和最小的年龄段类型对应的价格

        foreach (var item in result)
        {
            var price = prices.FirstOrDefault(x => x.LineProductSkuId == item.LinkProductSkuId);
            if (price is not null)
            {
                item.Type = price.Type;
                item.CostPrice = price.CostPrice;
                item.SalePrice = price.Price;
                item.TravelDate = price.Date;
            }
        }
        return result;
    }
}
