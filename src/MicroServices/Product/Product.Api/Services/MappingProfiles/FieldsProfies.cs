using AutoMapper;
using Contracts.Common.Product.DTOs.Fields;
using Newtonsoft.Json;

namespace Product.Api.Services.MappingProfiles;

public class FieldsProfies : Profile
{
    public FieldsProfies()
    {
        CreateMap<Fields, FieldsOutput>()
            .ForMember(x => x.FieldId, m => m.MapFrom(f => f.Id))
            //.ForMember(x => x.Columns, m => m.MapFrom(f => GetKeyValueRanges(f.Columns)))
            ;
    }

    public KeyValueRange[] GetKeyValueRanges(string json)
    {
        if (json != null)
        {
            return JsonConvert.DeserializeObject<KeyValueRange[]>(json);
        }
        return null;
    }

}