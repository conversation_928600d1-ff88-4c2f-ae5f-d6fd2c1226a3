using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Product.DTOs.OpenPlatformPricingSyncLog;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.DTOs.ProductPriceAdjustment;
using Contracts.Common.Product.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Product.Api.Services.Interfaces;
using Product.Api.Services.OpenPlatform.Contracts.Channel;
using Product.Api.Services.OpenPlatform.Contracts.Supplier;
using Product.Api.Services.OpenPlatform.Interfaces;

namespace Product.Api.Services;

public class LineProductSkuTypeItemService : ILineProductSkuTypeItemService
{
    private readonly CustomDbContext _dbContext;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly IOpenChannelService _openChannelService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IOpenPlatformPricingSyncLogService _openPlatformPriceInvSyncLogService;
    private readonly IBaseProductService _baseProductService;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly SemaphoreSlim _semaphore;

    private readonly ILogger<LineProductSkuTypeItemService> _logger;
    private readonly ICapPublisher _capPublisher;
    private const int _semaphoreTimeoutSeconds = 15;//超时时间
    private const int _successCode = 200;
    
    public LineProductSkuTypeItemService(
        CustomDbContext dbContext,
        IOpenSupplierService openSupplierService,
        IOpenChannelService openChannelService,
        IOpenPlatformBaseService openPlatformBaseService,
        IBaseProductService baseProductService,
        ICapPublisher capPublisher, 
        IServiceScopeFactory serviceScopeFactory,
        ILogger<LineProductSkuTypeItemService> logger,
        IOpenPlatformPricingSyncLogService openPlatformPriceInvLogService)
    {
        _dbContext = dbContext;
        _openSupplierService = openSupplierService;
        _openChannelService = openChannelService;
        _openPlatformBaseService = openPlatformBaseService;
        _openPlatformPriceInvSyncLogService = openPlatformPriceInvLogService;
        _baseProductService = baseProductService;
        

        _logger = logger;
        _capPublisher = capPublisher;
        _serviceScopeFactory = serviceScopeFactory;
        _semaphore = new SemaphoreSlim(10);//最大并发数
    }
    
    [UnitOfWork]
    public async Task Add(AddLineSkuTypeItemInput input)
    {
        /*
         * 1.判断匹配状态是否允许添加
         * 2.判断是否已经添加到saas中
         */
        var basicProducts = await _dbContext.OpenSupplierBasicProducts.AsNoTracking()
            .Where(x=> input.MatchIds.Contains(x.Id))
            .Where(x=> x.MatchStatus == OpenSupplierBasicProductMatchStatus.MatchSuccess)
            .ToListAsync();
        if (basicProducts.Any() is false)
            return;
        
        var matchIds = basicProducts.Select(x=>x.Id).ToList();
        var itemMatchIds = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
            .Where(x => x.LineProductId == input.LineProductId)
            .Where(x => matchIds.Contains(x.BasicProductMatchId))
            .Select(x => x.BasicProductMatchId)
            .ToListAsync();
        
        var newMatchIds = matchIds.Except(itemMatchIds).ToList();
        if(newMatchIds.Any() is false)
            return;

        /*
         * 1. 以 [时段场次] 创建套餐.
         * 2. 关联多个 [sku票种]
         */
        var lineProductSkuList = new List<LineProductSku>();
        var lineProductSkuTypeItems = new List<LineProductSkuTypeItem>();
        basicProducts = basicProducts.Where(x => newMatchIds.Contains(x.Id)).ToList();
        
        //查询是否存在已经存在同[OptionsId]下同[时段场次]的线路套餐数据
        var optionsIds = basicProducts.Select(x=>x.OptionId).Distinct().ToList();
        var dbLineSkus = await _dbContext.LineProductSku.AsNoTracking()
            .Where(x => x.LineProductId == input.LineProductId && optionsIds.Contains(x.PackageId))
            .ToListAsync();
        
        var optionsGroup = basicProducts.GroupBy(x => new
            {
                x.ProductId, x.OptionId,
            })
            .Select(x => new
            {
                x.Key.ProductId,
                x.First().ProductName,
                x.Key.OptionId, 
                x.First().OptionName,
                child = x.Select(c => c).ToList()
            })
            .ToList();
        foreach (var group in optionsGroup)
        {
            foreach (var child in group.child)
            {
                //生成线路套餐
                var timeSlotName = child.TimeSlotName ?? "全天";
                var name = $"{group.OptionName} {timeSlotName}";

                //判断是否已经存在创建的lineProductSku
                LineProductSku? lineProductSku = dbLineSkus.FirstOrDefault(x => x.PackageId == group.OptionId
                                                                               && x.TimeSlotName == child.TimeSlotName);
                if (lineProductSku == null)
                {
                    //不存在则创建
                    if (lineProductSkuList.Any(x => x.Name == name))
                    {
                        lineProductSku = lineProductSkuList.FirstOrDefault(x => x.Name == name)!;
                    }
                    else
                    {
                        lineProductSku = new LineProductSku
                        {
                            LineProductId = input.LineProductId,
                            Name = name,
                            ActivityId = group.ProductId,
                            PackageId = group.OptionId,
                            IsTimeSlot = child.IsTimeSlot,
                            TimeSlotName = child.TimeSlotName,
                            TimeSlotId = child.TimeSlotId,
                        };
                        lineProductSkuList.Add(lineProductSku);
                    }
                }
                
                //生成线路套餐票种数据
                var skuTypeItem = new LineProductSkuTypeItem
                {
                    LineProductId = input.LineProductId,
                    LineProductSkuId = lineProductSku.Id,
                    BasicProductMatchId = child.Id,
                    Name = child.SkuName,
                    ActivityId = group.ProductId,
                    PackageId = group.OptionId,
                    SkuId = child.SkuId,
                    TimeSlotId = lineProductSku.TimeSlotId,
                    TimeSlotName = lineProductSku.TimeSlotName,
                    SkuPriceType = child.SkuPriceType,
                    SupplierIsSale = true
                };
                lineProductSkuTypeItems.Add(skuTypeItem);
            }
        }
        
        if(lineProductSkuList.Any())
            await _dbContext.AddRangeAsync(lineProductSkuList);
        if(lineProductSkuTypeItems.Any())
            await _dbContext.AddRangeAsync(lineProductSkuTypeItems);
    }

    [UnitOfWork]
    public async Task<NotifySyncLineThirdInventoryOutput> NotifySync(NotifySyncThirdInventoryInput input)
    {
        var output = new NotifySyncLineThirdInventoryOutput();
        var outProductId = input.ActivityId;
        var outOptionId = input.PackageId;
        var outSkuId = input.SkuId;
        var relateProductSupplierSettings = await _dbContext.LineProductOpenSupplierSettings.IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.ActivityId == outProductId)
            .ToListAsync();//查出所有匹配的线路产品配置
        var relateProductIds = relateProductSupplierSettings.Select(x => x.LineProductId).ToList();
        if(relateProductIds.Any() is false) return output;
        var purchaseSourceType = _openPlatformBaseService.ConvertOpenSupplierType(input.OpenSupplierType);
        var relateProducts = await _dbContext.LineProduct.IgnoreQueryFilters()
            .Where(x => relateProductIds.Contains(x.Id))
            .Where(x => x.IsDeleted == false)
            .Where(x=> x.PriceInventorySource == purchaseSourceType.priceInventorySource)
            .ToListAsync();//查询所有匹配的未删除的线路产品
        if(relateProducts.Any() is false) return output;
        var tenantIds = relateProducts.Select(x=>x.TenantId).Distinct().ToList();
        
        //查询租户对应的-供应端产品详情
        var productResponseDataList = new List<TenantProductResponseData>();
        var productRequest = new SupplierProductDetailRequest
        { 
            OutProductId = outProductId,
            SupplierType = input.OpenSupplierType.ToString().ToLowerInvariant()
        };
        foreach (var tenantId in tenantIds)
        {
            var productResponse = await _openSupplierService.SupplierProductDetail(productRequest, tenantId);
            if(productResponse.Code != 200) continue;
            if(productResponse?.Data is null || productResponse.Data.SkuList.Any() is false) continue;
            
            productResponseDataList.Add(new TenantProductResponseData(tenantId, productResponse.Data));
        }
        if(productResponseDataList.Any() is false) return output;
        
        //维护basic product数据
        await SyncBasicProduct(input,productResponseDataList.First().Data);

        //维护需同步的产品
        var needSyncSupplierSettings = relateProductSupplierSettings
            .Where(x => x.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
            .ToList();//过滤掉无需同步的产品
        var needSyncProductIds = needSyncSupplierSettings.Select(x => x.LineProductId).ToList();
        var needSyncLineProducts = relateProducts.Where(x => needSyncProductIds.Contains(x.Id)).ToList();//需要同步的线路产品
        if (needSyncLineProducts.Any())
        {
            //维护对应套餐价库
            var syncSkuTypeItemIds =  await SyncPriceInventory(input,needSyncLineProducts,needSyncSupplierSettings,productResponseDataList,OpenPlatformPricingSyncType.AsyncNotification);
            output.SyncSkuTypeItemIds = syncSkuTypeItemIds;//记录触发价库同步的id数据
        }
        
        return output;
    }
    
    public async Task<List<GetOpenSupplierBasicProductOutput>> GetBasicProducts(GetBasicProductInput input)
    {
        var items = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
            .Where(x => x.LineProductId == input.LineProductId)
            .WhereIF(input.LineProductSkuId.HasValue,x=>x.LineProductSkuId == input.LineProductSkuId)
            .ToListAsync();
        
        var matchIds = items.Select(x=>x.BasicProductMatchId).ToList();
        var basicProducts = await _dbContext.OpenSupplierBasicProducts.AsNoTracking()
            .Where(x => matchIds.Contains(x.Id))
            .Select(x=> new GetOpenSupplierBasicProductOutput
            {
                MatchId = x.Id,
                ProductId = x.ProductId,
                ProductName = x.ProductName,
                MatchStatus = x.MatchStatus,
                TimeSlotId = x.TimeSlotId,
                TimeSlotName = x.TimeSlotName,
                SkuId = x.SkuId,
                SkuName = x.SkuName,
                OptionId = x.OptionId,
                OptionName = x.OptionName,
                SkuPriceType = x.SkuPriceType
            })
            .ToListAsync();
        return basicProducts;
    }

    public async Task<List<QueryLineSkuTypeItemOutput>> Query(QueryLineSkuTypeItemInput input)
    {
        var result = new List<QueryLineSkuTypeItemOutput>();
        if (input.LineProductId is null && input.LineProductSkuId is null && input.SkuTypeItemIds.Any() is false) return result;

        var items = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
            .WhereIF(input.LineProductId.HasValue, x => x.LineProductId == input.LineProductId)
            .WhereIF(input.LineProductSkuId.HasValue, x => x.LineProductSkuId == input.LineProductSkuId)
            .WhereIF(input.SkuTypeItemIds.Any(),x=> input.SkuTypeItemIds.Contains(x.Id))
            .ToListAsync();
        if (items.Any() is false) return result;
        
        var matchIds = items.Select(x=>x.BasicProductMatchId).ToList();
        var basicProducts = await _dbContext.OpenSupplierBasicProducts.AsNoTracking()
            .Where(x => matchIds.Contains(x.Id))
            .ToListAsync();
        
        foreach (var item in items)
        {
            var resultItem = new QueryLineSkuTypeItemOutput
            {
                LineProductId = item.LineProductId,
                LineProductSkuId = item.LineProductSkuId,
                SkuTypeItemId = item.Id,
                SkuTypeItemName = item.Name,
                SupplierProductId = item.ActivityId,
                SupplierOptionId = item.PackageId,
                SupplierSkuId = item.SkuId,
                SkuPriceType = item.SkuPriceType,
                SupplierIsSale = item.SupplierIsSale,
                CostDiscountRate = item.CostDiscountRate
            };
            var basicProduct = basicProducts.FirstOrDefault(x => x.Id == item.BasicProductMatchId);
            resultItem.MatchStatus = basicProduct?.MatchStatus switch
            {
                OpenSupplierBasicProductMatchStatus.WaitingMatch => BasicProductMatchStatus.WaitingInit,
                OpenSupplierBasicProductMatchStatus.MatchSuccess => BasicProductMatchStatus.Added,
                OpenSupplierBasicProductMatchStatus.MatchFailed => BasicProductMatchStatus.MatchFailed,
                null => BasicProductMatchStatus.MatchFailed,
                _ => throw new ArgumentOutOfRangeException()
            };
            
            result.Add(resultItem);
        }
        
        return result;
    }

    public async Task<List<OpenSupplierQueryProductStockPollOutput>> QueryProductStockPoll(OpenSupplierQueryProductStockPollInput input)
    {
        var output = new List<OpenSupplierQueryProductStockPollOutput>();
        var lineProducts = await _dbContext.LineProduct.IgnoreQueryFilters().AsNoTracking()
            .Where(x => x.Enabled && x.IsDeleted == false)
            .Where(x => x.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
            .Where(x => input.PriceInventorySources.Contains(x.PriceInventorySource))
            .Select(x => new {x.Id, x.PriceInventorySource, x.TenantId})
            .ToListAsync();
        if (lineProducts.Any() is false) return output;
        
        var lineProductIds = lineProducts.Select(x => x.Id).ToList();
        var lineProductSkuIds = await _dbContext.LineProductSku.IgnoreQueryFilters().AsNoTracking()
            .Where(x => lineProductIds.Contains(x.LineProductId))
            .Where(x => x.Enabled)
            .Select(x => x.Id)
            .ToListAsync();
        if(lineProductSkuIds.Any() is false) return output;
        
        var lineProductOpenSupplierSettings = await _dbContext.LineProductOpenSupplierSettings.IgnoreQueryFilters().AsNoTracking()
            .Where(x => lineProductIds.Contains(x.LineProductId))
            .Where(x => x.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
            .ToListAsync();

        var skuTypeItems = await _dbContext.LineProductSkuTypeItems.IgnoreQueryFilters().AsNoTracking()
            .Where(x => lineProductIds.Contains(x.LineProductId))
            .Where(x => lineProductSkuIds.Contains(x.LineProductSkuId))
            .ToListAsync();

        foreach (var lineProduct in lineProducts)
        {
            var settings = lineProductOpenSupplierSettings.FirstOrDefault(x => x.LineProductId == lineProduct.Id);
            if (settings == null) continue;
            
            var skuTypeItemsStockPoll = skuTypeItems.Where(x => x.LineProductId == lineProduct.Id)
                .Select(x=> new OpenSupplierQueryProductStockPollOutput
                {
                    TenantId = lineProduct.TenantId,
                    PriceInventorySource = lineProduct.PriceInventorySource,
                    ActivityId = settings.ActivityId,
                    PackageId = x.PackageId,
                    SkuId = x.SkuId,
                    PriceInventorySyncType = settings.PriceInventorySyncType,
                    SyncDateRange = settings.SyncDateRange,
                    SyncInterval = settings.SyncInterval
                })
                .ToList();
            output.AddRange(skuTypeItemsStockPoll);
        }
        
        return output;
    }

    public async Task RunPushToChannel(ChannelPriceStockSyncInput input)
    {
        if (!await _semaphore.WaitAsync(_semaphoreTimeoutSeconds))
        {
            _logger.LogWarning("RunPushToChannel semaphore wait timeout");
        } 
        
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<CustomDbContext>();
            var result = await PushToChannelPriceStock(context, input);
            if (result.SyncLogs.Any())
            {
                var syncLogService = scope.ServiceProvider.GetRequiredService<IOpenPlatformPricingSyncLogService>();
                await syncLogService.Add(new AddSyncLogInput
                {
                    Logs = result.SyncLogs
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("RunPushToChannel error:{@Message}", ex.Message);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task RunPushEmptyToChannel(ChannelPriceStockPushEmptyInput input)
    {
        var lineProduct = await _dbContext.LineProduct.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.LineProductId);
        if (lineProduct == null) return;

        var tenantId = lineProduct.TenantId;
        var lineSkus = await _dbContext.LineProductSku.AsNoTracking()
            .Where(x => x.LineProductId == input.LineProductId)
            .Where(x => x.Enabled)
            .ToListAsync();
        if (lineSkus.Any() is false) return;
        var lineSkuIds = lineSkus.Select(x => x.Id).ToList();

        var skuTypeItems = await _dbContext.LineProductSkuTypeItems.AsNoTracking()
            .Where(x => x.LineProductId == input.LineProductId)
            .Where(x => lineSkuIds.Contains(x.LineProductSkuId)).ToListAsync();
        if (skuTypeItems.Any() is false) return;
        var skuTypeItemIds = skuTypeItems.Select(x => x.Id).ToList();

        var skuTypeCalendars = await _dbContext.LineProductSkuCalendarPrice.AsNoTracking()
            .Where(x => x.LineProductSkuTypeItemId.HasValue)
            .Where(x => skuTypeItemIds.Contains(x.LineProductSkuTypeItemId!.Value))
            .Where(x => x.Date >= DateTime.Today)
            .Select(x => new {x.LineProductSkuTypeItemId, x.PriceChannelType, x.Date})
            .ToListAsync();

        var syncLogs = new List<AddSyncLogItem>();
        var meituanSyncRequestList = new List<ChannelPriceStockUploadRequest>(); //美团价库同步数据
        var meituanSyncLogMinDates = new List<DateTime>();
        var meituanSyncLogMaxDates = new List<DateTime>();
        foreach (var skuTypeItem in skuTypeItems)
        {
            foreach (var setting in input.PushEmptySettingInfos)
            {
                var calendarPriceChannelType =
                    _openPlatformBaseService.MappingCalendarPriceChannelType(setting.PriceInventorySyncChannelType);
                var skuTypeChannelCalendarPrice = skuTypeCalendars
                    .Where(x => x.LineProductSkuTypeItemId == skuTypeItem.Id)
                    .Where(x => x.PriceChannelType == calendarPriceChannelType)
                    .ToList();
                var itemSchedules = skuTypeChannelCalendarPrice
                    .Select(x => new ChannelPriceStockUploadSchedulesItem
                    {
                        OutSkuId = x.LineProductSkuTypeItemId.ToString(),
                        Date = x.Date.ToString("yyyy-MM-dd"),
                        Stock = 0,
                        Price = 0
                    })
                    .ToList();
                //记录同步日志
                var syncStartDate = skuTypeChannelCalendarPrice.Min(x => x.Date);
                var syncEndDate = skuTypeChannelCalendarPrice.Max(x => x.Date);
                
                var outProductId = !string.IsNullOrEmpty(setting.SupplierProductId)
                    ? setting.SupplierProductId
                    : skuTypeItem.Id.ToString();
                if (setting.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Meituan)
                {
                    //判断是否配置了指定的同步skuTypeItemId
                    var settingSyncIds = setting.SyncSkuIds;
                    if (settingSyncIds.Any())
                    {
                        //不包含该skuTypeItemId,跳过[美团]
                        if (!settingSyncIds.Contains(skuTypeItem.Id))
                        {
                            continue;
                        }
                    }
                }

                var lineSku = lineSkus.FirstOrDefault(x => x.Id == skuTypeItem.LineProductSkuId);
                var otaType = _openPlatformBaseService.MappingOtaChannelType(setting.PriceInventorySyncChannelType);
                var syncRequest = new ChannelPriceStockUploadRequest
                {
                    IsCreUpdate = true,
                    OtaType = otaType.ToString().ToLowerInvariant(),
                    ProductType = OtaChannelProductType.Line.ToString().ToLowerInvariant(),
                    OutProductId = outProductId,
                    OtaProductId = setting.ChannelProductId ?? string.Empty,
                    OtaOptionId = skuTypeItem.SkuId?.ToString() ?? string.Empty,
                    OtaSkuId = setting.Area ?? string.Empty,
                    ScheduleType = setting.PriceInventorySyncType switch
                    {
                        PriceInventorySyncType.SyncPrice => OpenChannelPriceStockUploadScheduleType.SyncPrice,
                        PriceInventorySyncType.SyncInventory => OpenChannelPriceStockUploadScheduleType.SyncInventory,
                        _ => OpenChannelPriceStockUploadScheduleType.SyncAll,
                    },
                    Schedules = itemSchedules
                };
                if (setting.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Meituan)
                {
                    meituanSyncRequestList.Add(syncRequest);
                    meituanSyncLogMinDates.Add(syncStartDate);
                    meituanSyncLogMaxDates.Add(syncEndDate);
                }
                else
                {
                    var syncResult = await _openChannelService.ChannelPriceStockUpload(syncRequest, tenantId);
                    syncLogs.Add(new AddSyncLogItem
                    {
                        TenantId = tenantId,
                        ProductType = ProductType.Line,
                        PlatformType = OpenPlatformType.OpenChannel,
                        ChannelType = otaType,
                        SyncType = OpenPlatformPricingSyncType.ActivePush,
                        SyncResult = syncResult.Code == _successCode
                            ? OpenPlatformPricingSyncResult.Success
                            : OpenPlatformPricingSyncResult.Failed,
                        FailedMessage = syncResult.Msg,
                        ProductId = skuTypeItem.LineProductId,
                        ProductName = lineProduct.Title,
                        SkuId = skuTypeItem.LineProductSkuId,
                        SkuName = lineSku.Name,
                        SkuTypeItemName = skuTypeItem.Name,
                        SyncStartDate = syncStartDate,
                        SyncEndDate = syncEndDate
                    });
                }
            }
        }

        #region 美团渠道 - 同步

        if (meituanSyncRequestList.Any())
        {
            /*
             * 1.美团价库同步 需要合并同于productId下的skuTypeItem的库存和价格进行同步
             */

            var mergeSchedules = meituanSyncRequestList.SelectMany(x => x.Schedules).ToList(); //合并Schedules
            var meituanSyncRequest = meituanSyncRequestList.First();
            meituanSyncRequest.Schedules = mergeSchedules;

            var syncResult =
                await _openChannelService.ChannelPriceStockUpload(meituanSyncRequest, lineProduct.TenantId);
            var syncStartDate = meituanSyncLogMinDates.Min(x => x.Date);
            var syncEndDate = meituanSyncLogMaxDates.Max(x => x.Date);
            //记录同步日志
            syncLogs.Add(new AddSyncLogItem
            {
                TenantId = tenantId,
                ProductType = ProductType.Line,
                PlatformType = OpenPlatformType.OpenChannel,
                ChannelType = OtaChannelType.Meituan,
                SyncType = OpenPlatformPricingSyncType.ActivePush,
                SyncResult = syncResult.Code == _successCode
                    ? OpenPlatformPricingSyncResult.Success
                    : OpenPlatformPricingSyncResult.Failed,
                FailedMessage = syncResult.Msg,
                ProductId = lineProduct.Id,
                ProductName = lineProduct.Title,
                SkuId = 0,
                SkuName = string.Empty,
                SkuTypeItemName = string.Empty,
                SyncStartDate = syncStartDate,
                SyncEndDate = syncEndDate
            });
        }

        #endregion

        #region 记录日志

        var addSyncLog = new AddSyncLogInput {Logs = syncLogs};
        await _openPlatformPriceInvSyncLogService.Add(addSyncLog);

        #endregion
    }
    
    public async Task OrderTriggerScheduleChannelSync(LineOrderTriggerScheduleChannelSyncMessage receive)
    {
        var syncChannelBeginDate = receive.TravelDate.Date;
        var syncChannelEndDate = receive.TravelDate.Date.AddDays(2); //出行日期 + 2天
        TimeSpan? time = null;
        if (!string.IsNullOrEmpty(receive.TimeSlotName))
        {
            if (TimeSpan.TryParse(receive.TimeSlotName, out var timeSlot))
            {
                syncChannelBeginDate = syncChannelBeginDate.Add(timeSlot);
                syncChannelEndDate = syncChannelEndDate.Add(timeSlot);

                time = timeSlot;
            }
        }
        var lineProduct = await _dbContext.LineProduct.IgnoreQueryFilters().AsNoTracking()
            .FirstOrDefaultAsync(x=>x.Id == receive.LineProductId);
        if(lineProduct == null) return;
        var openSupplierSetting = await _dbContext.LineProductOpenSupplierSettings.IgnoreQueryFilters().AsNoTracking()
            .FirstOrDefaultAsync(x => x.LineProductId == lineProduct.Id);
        if(openSupplierSetting == null) return;
        
        var productDetailRequest = new SupplierProductDetailRequest
        {
            OutProductId = receive.OutProductId,
            OutProductOptionId = receive.OutProductOptionId,
            OutSkuId = receive.OutSkuId,
            SupplierType = receive.OpenSupplierType.ToString().ToLowerInvariant()
        };
        var productDetailResponse = await _openSupplierService.SupplierProductDetail(productDetailRequest, lineProduct.TenantId);

        var productScheduleRequest = new SupplierSkuScheduleRequest
        {
            OutProductId = receive.OutProductId,
            OutProductOptionId = receive.OutProductOptionId,
            OutSkuId = receive.OutSkuId,
            SupplierType = receive.OpenSupplierType.ToString().ToLowerInvariant(),
            DateFrom = syncChannelBeginDate,
            DateTo = syncChannelEndDate,
            Timeslot = time.HasValue ? time.Value.ToString() : string.Empty
        };
        var productScheduleResponse = await _openSupplierService.SupplierSkuSchedule(productScheduleRequest, lineProduct.TenantId);
        
        var notifyInput = new NotifySyncThirdInventoryInput
        {
            ActivityId = receive.OutProductId,
            PackageId = receive.OutProductOptionId,
            SkuId = receive.OutSkuId,
            IsSale = true
        };
        if (productScheduleResponse.Code == _successCode)
        {
            notifyInput.Schedules = productScheduleResponse.Data.Stocks
                .Select(x => new SyncThirdInventorySchedulesDto
                {
                    Date = x.Time,
                    TotalQuantity = x.Total,
                    AvailableQuantity = x.Available,
                    Price = x.Price,
                    Currency = x.Currency,
                    TimeSlotName = x.TimeSlot
                })
                .ToList();
            if (time.HasValue)
            {
                notifyInput.Schedules = notifyInput.Schedules.Where(x => x.TimeSlotName == time.Value.ToString())
                    .ToList();
            }
        }
        else
        {
            //记录同步日志
            var lineSku = await _dbContext.LineProductSku.IgnoreQueryFilters().AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == receive.LineProductSkuId);
            var lineSkuTypeItem = await _dbContext.LineProductSkuTypeItems.IgnoreQueryFilters().AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == receive.LineProductSkuTypeItemId);
            var addSyncLog = new AddSyncLogInput();
            addSyncLog.Logs.Add(new AddSyncLogItem
            {
                TenantId = lineProduct.TenantId,
                ProductType = ProductType.Line,
                PlatformType = OpenPlatformType.OpenSupplier,
                SyncType = OpenPlatformPricingSyncType.ActivePush,
                SyncResult = OpenPlatformPricingSyncResult.Failed,
                FailedMessage = productScheduleResponse.Msg,
                ProductId = lineProduct.Id,
                ProductName = lineProduct.Title,
                SkuId = receive.LineProductSkuId,
                SkuName = lineSku.Name,
                SkuTypeItemName = lineSkuTypeItem.Name,
                SyncStartDate = syncChannelBeginDate,
                SyncEndDate = syncChannelEndDate,
                SupplierIsSale = false
            });
            await _openPlatformPriceInvSyncLogService.Add(addSyncLog);
        }

        var lineProducts = new List<LineProduct> {lineProduct};
        var supplierSettings = new List<LineProductOpenSupplierSetting> {openSupplierSetting};
        var tenantResponse =
            new List<TenantProductResponseData> {new(lineProduct.TenantId, productDetailResponse.Data)};
        await SyncPriceInventory(notifyInput, lineProducts, supplierSettings, tenantResponse, OpenPlatformPricingSyncType.ActivePush);//拉取供应商价库同步到saas
        await _dbContext.SaveChangesAsync();
    }
    
    #region private

    /// <summary>
    /// 同步维护基础产品信息
    /// </summary>
    /// <param name="input"></param>
    /// <param name="supplierProductDetailResponse"></param>
    private async Task SyncBasicProduct(NotifySyncThirdInventoryInput input,
        SupplierProductDetailResponse supplierProductDetailResponse)
    {
        var outProductId = input.ActivityId;
        var outOptionId = input.PackageId;
        var outSkuId = input.SkuId;
        
        //查出关联的basic product数据.需要对移除套餐,sku的匹配关系处理
        var basicProducts = await _dbContext.OpenSupplierBasicProducts
            .Where(x => x.OpenSupplierType == input.OpenSupplierType)
            .Where(x => x.ProductId == outProductId)
            .ToListAsync();
        foreach (var item in basicProducts)
        {
            //只判断OptionId和SkuId 是否在供应端返回的详情数据中有存在
            var supplierSkuItem = supplierProductDetailResponse.SkuList
                .FirstOrDefault(x => x.OutProductOptionId == item.OptionId
                                     && x.OutSkuId == item.SkuId);

            item.MatchStatus = supplierSkuItem == null 
                ? OpenSupplierBasicProductMatchStatus.MatchFailed  //不存在标记为匹配失败
                : OpenSupplierBasicProductMatchStatus.MatchSuccess; //存在标记为匹配成功
            item.UpdateTime = DateTime.Now;
        }
        
        var currentSkuResponse = supplierProductDetailResponse.SkuList
            .FirstOrDefault(x => x.OutProductOptionId == outOptionId
                                 && x.OutSkuId == outSkuId);//取到供应端对应sku信息
        if(currentSkuResponse == null) return;//供应端产品详情不存在.不做其他处理
        
        //时段场次判断
        var isTimeSlot = currentSkuResponse.IsTimeSlot;
        var timeSlotNames = new List<string>();
        if(isTimeSlot)
        {
            timeSlotNames = input.Schedules
                .Where(x=> !string.IsNullOrEmpty(x.TimeSlotName))
                .Select(x => x.TimeSlotName!)
                .Distinct().ToList();//拿到sku对应的时段场次数据
        }
        
        //以最小粒度插入[时段场次]
        var relateSkuBasicProducts = basicProducts
            .Where(x => x.OptionId == outOptionId && x.SkuId == outSkuId)
            .ToList();
        
        //处理outSkuId关联的旧数据
        foreach (var basicProductItem in relateSkuBasicProducts)
        {
            //更新基础信息
            basicProductItem.ProductName = supplierProductDetailResponse.ProductName;
            basicProductItem.OptionName = currentSkuResponse.OutProductOptionName;
            basicProductItem.SkuName = currentSkuResponse.OutSkuName;
            basicProductItem.UpdateTime = DateTime.Now;
            
            //判断是否无匹配
            if (timeSlotNames.Any())
            {
                var isMatch = !string.IsNullOrEmpty(basicProductItem.TimeSlotName) &&
                              timeSlotNames.Contains(basicProductItem.TimeSlotName!);
                basicProductItem.MatchStatus = isMatch
                    ? OpenSupplierBasicProductMatchStatus.MatchSuccess
                    : OpenSupplierBasicProductMatchStatus.MatchFailed;
            }
            else
            {
                //如果旧数据是时段场次也是无匹配
                if (basicProductItem.IsTimeSlot)
                {
                    basicProductItem.MatchStatus = OpenSupplierBasicProductMatchStatus.MatchFailed;
                }
            }
        }
        
        //处理outSkuId关联的新数据
        var newBasicProducts = new List<OpenSupplierBasicProduct>();
        if (isTimeSlot)
        {
            //有场次-处理新增的场次
            foreach (var timeSlotName in timeSlotNames)
            {
                var added = relateSkuBasicProducts.Any(x => x.TimeSlotName == timeSlotName);
                if (added) continue;

                var newBasicProduct = new OpenSupplierBasicProduct
                {
                    OpenSupplierType = input.OpenSupplierType,
                    TimeSlotName = timeSlotName,
                    SkuId = outSkuId,
                    SkuName = currentSkuResponse.OutSkuName,
                    OptionId = outOptionId,
                    OptionName = currentSkuResponse.OutProductOptionName,
                    ProductId = outProductId,
                    ProductName = supplierProductDetailResponse.ProductName,
                    IsTimeSlot = isTimeSlot,
                    MatchStatus = OpenSupplierBasicProductMatchStatus.MatchSuccess,
                    SkuPriceType = _openPlatformBaseService.ConvertSupplierProductSkuType(currentSkuResponse.SkuType)
                };
                newBasicProducts.Add(newBasicProduct);
            }
        }
        else
        {
            //无场次-处理新增
            if (relateSkuBasicProducts.Any() is false)
            {
                var newBasicProduct = new OpenSupplierBasicProduct
                {
                    OpenSupplierType = input.OpenSupplierType,
                    SkuId = outSkuId,
                    SkuName = currentSkuResponse.OutSkuName,
                    OptionId = outOptionId,
                    OptionName = currentSkuResponse.OutProductOptionName,
                    ProductId = outProductId,
                    ProductName = supplierProductDetailResponse.ProductName,
                    IsTimeSlot = isTimeSlot,
                    MatchStatus = OpenSupplierBasicProductMatchStatus.MatchSuccess,
                    SkuPriceType = _openPlatformBaseService.ConvertSupplierProductSkuType(currentSkuResponse.SkuType)
                };
                newBasicProducts.Add(newBasicProduct);
            }
        }

        if (newBasicProducts.Any())
            await _dbContext.AddRangeAsync(newBasicProducts);
    }

    record TenantProductResponseData(long TenantId,SupplierProductDetailResponse Data);
    /// <summary>
    ///日历价库处理
    /// </summary>
    /// <param name="TenantId"></param>
    /// <param name="Data"></param>
    private async Task<List<long>> SyncPriceInventory(NotifySyncThirdInventoryInput input,
        List<LineProduct> lineProducts,
        List<LineProductOpenSupplierSetting> supplierSettings,
        List<TenantProductResponseData> supplierProductDetailResponse,
        OpenPlatformPricingSyncType syncType)
    {
        var result = new List<long>();
        var outProductId = input.ActivityId;
        var outOptionId = input.PackageId;
        var outSkuId = input.SkuId;
        var relateProductIds = lineProducts.Select(x => x.Id).ToList();
        
        //查询已上架的sku信息
        var lineProductSkus = await _dbContext.LineProductSku.IgnoreQueryFilters()
            .Where(x => relateProductIds.Contains(x.LineProductId))
            .ToListAsync();
        if(lineProductSkus.Any() is false) return result;
        
        //查询相关的sku票种数据
        var lineProductSkuTypeItems = await _dbContext.LineProductSkuTypeItems.IgnoreQueryFilters()
            .Where(x => relateProductIds.Contains(x.LineProductId))
            .Where(x => x.ActivityId == outProductId && x.PackageId == outOptionId && x.SkuId == outSkuId)
            .ToListAsync();
        if(lineProductSkuTypeItems.Any() is false) return result;
        var skuTypeItemIds = lineProductSkuTypeItems.Select(x => x.Id).ToList();
        
        //查出sku票种对应的日历价数据
        var skuTypeItemPrices = await _dbContext.LineProductSkuCalendarPrice.IgnoreQueryFilters()
            .Where(x => x.LineProductSkuTypeItemId.HasValue)
            .Where(x => skuTypeItemIds.Contains(x.LineProductSkuTypeItemId!.Value))
            .Where(x=>x.Date >= DateTime.Today)
            .ToListAsync();
        
        //日历价格渠道枚举数据
        var calendarPriceChannelTypes = Enum.GetValues<CalendarPriceChannelType>()
            .Where(x => x != CalendarPriceChannelType.System)
            .ToList(); 
        var newCalendarPriceList = new List<LineProductSkuCalendarPrice>();//新增日历价
        var syncCalendarInventoryData = new List<SyncSaasCalendarInventoryData>();//同步日历价库存数据
        var syncLogs = new List<AddSyncLogItem>();//同步价库日志
        
        //处理汇率
        var calcExchangeRateCurrencyList = (from product in lineProducts
                let openCostCurrencyCode =
                    supplierProductDetailResponse.FirstOrDefault(x => x.TenantId == product.TenantId)?.Data?.SkuList
                        .FirstOrDefault()?.Currency ?? product.CostCurrencyCode
                select (product.Id, openCostCurrencyCode, product.CostCurrencyCode, product.SaleCurrencyCode))
            .ToList();
        var exchangeRateList = await CalcSyncExchangeRate(calcExchangeRateCurrencyList);

        //循环处理日历价库
        foreach (var skuTypeItem in lineProductSkuTypeItems)
        {
            var lineProduct = lineProducts.FirstOrDefault(x => x.Id == skuTypeItem.LineProductId);
            var lineSku = lineProductSkus.FirstOrDefault(x => x.Id == skuTypeItem.LineProductSkuId);
            
            var tenantSupplierProductDetailResponse =
                supplierProductDetailResponse.FirstOrDefault(x => x.TenantId == skuTypeItem.TenantId);
            if (tenantSupplierProductDetailResponse == null) continue;
            
            var currentSkuResponse = tenantSupplierProductDetailResponse.Data.SkuList
                .FirstOrDefault(x => x.OutProductOptionId == outOptionId && x.OutSkuId == outSkuId);//取到供应端对应sku信息
            var isTimeSlot = currentSkuResponse?.IsTimeSlot ?? !string.IsNullOrEmpty(skuTypeItem.TimeSlotName);
            var currentSchedules = new List<SyncThirdInventorySchedulesDto>();//票种价库
            var syncInventoryList = new List<BatchSyncThirdCalendarInventoryItem>();//同步库存数据
            
            skuTypeItem.CostDiscountRate = currentSkuResponse?.CommissionRate ?? 0; // 票种采购折扣比例
            skuTypeItem.SupplierIsSale = input.IsSale;// [常规性&运维性]根据是否可售更新票种[供应商可售]状态
            skuTypeItem.UpdateTime = DateTime.Now;
            
            //区分正常价库还是场次价库
            currentSchedules = isTimeSlot
                ? input.Schedules.Where(x => x.TimeSlotName == skuTypeItem.TimeSlotName).ToList()
                : input.Schedules.ToList();
            
            var days = input.EndDate.Subtract(input.StartDate).Days;
            for (int i = 0; i <= days; i++)
            {
                var date = input.StartDate.AddDays(i);
                var daySchedule = currentSchedules.FirstOrDefault(x => x.Date.Date == date);//供应端价库数据
                var supplierSetting = supplierSettings.FirstOrDefault(x => x.LineProductId == skuTypeItem.LineProductId);//供应端同步配置
                var exchangeRate = exchangeRateList.FirstOrDefault(x => x.ProductId == skuTypeItem.LineProductId);
                var toSaasCostExchangeRate = exchangeRate.ToSaasCostExchangeRate; //供应端采购价币种转saas采购价币种汇率
                var toSaasSaleExchangeRate = exchangeRate.ToSaasSaleExchangeRate; //saas采购币种转供应端销售价币种汇率

                //根据同步配置处理日历价
                if (supplierSetting.PriceInventorySyncType is PriceInventorySyncType.SyncAll
                    or PriceInventorySyncType.SyncPrice)
                {
                    foreach (var priceChannelType in calendarPriceChannelTypes)
                    {
                        //关联多渠道日历价
                        var datePrice = skuTypeItemPrices
                            .Where(x => x.LineProductSkuTypeItemId == skuTypeItem.Id &&
                                        x.Type == skuTypeItem.SkuPriceType)
                            .Where(x => x.Date == date)
                            .FirstOrDefault(x => x.PriceChannelType == priceChannelType);

                        //供应端采购价
                        decimal? newCostPrice = daySchedule?.Price.HasValue == true 
                            ? Math.Round(daySchedule.Price.Value * toSaasCostExchangeRate, 2)
                            : null;

                        if (datePrice != null)
                        {
                            //计算价格调整后的售价
                            var newPrice = _baseProductService.CalcAdjustmentSellingPrice(
                                new CalcPriceAdjustmentInput
                                {
                                    PriceBasisType = datePrice.PriceBasisType,
                                    PriceAdjustmentType = datePrice.PriceAdjustmentType,
                                    PriceAdjustmentValue = datePrice.PriceAdjustmentValue,
                                    CostPrice = newCostPrice,
                                    CostPriceExchangeRate = toSaasSaleExchangeRate,
                                });

                            datePrice.CostPrice = newCostPrice;
                            datePrice.Price = newPrice;
                            datePrice.UpdateTime = DateTime.Now;
                        }
                        else
                        {
                            var newCalendarPrice = new LineProductSkuCalendarPrice
                            {
                                LineProductId = skuTypeItem.LineProductId,
                                LineProductSkuId = skuTypeItem.LineProductSkuId,
                                LineProductSkuTypeItemId = skuTypeItem.Id,
                                Type = skuTypeItem.SkuPriceType,
                                Date = date,
                                CostPrice = newCostPrice,
                                Price = null,
                                PriceBasisType = PriceBasisType.CostPrice,
                                PriceAdjustmentType = PriceAdjustmentType.AddRate,
                                PriceAdjustmentValue = null,
                                PriceChannelType = priceChannelType,
                            };
                            newCalendarPrice.SetTenantId(skuTypeItem.TenantId);
                            newCalendarPriceList.Add(newCalendarPrice);
                        }
                    }
                }

                //根据同步配置处理价库
                if (supplierSetting.PriceInventorySyncType is PriceInventorySyncType.SyncAll
                    or PriceInventorySyncType.SyncInventory)
                {
                    var supplierAvq = daySchedule?.AvailableQuantity ?? 0; //供应端有效库存
                    var supplierTq = daySchedule?.TotalQuantity ?? 0; //供应端总库存
                    var saasTotalQty = Math.Max(supplierAvq, supplierTq); //处理总库存
                    bool? invEnabled = skuTypeItem.SupplierIsSale ? null : false; //供应端不可售 - 停用库存售卖

                    var syncInventoryItem = new BatchSyncThirdCalendarInventoryItem
                    {
                        ProductId = skuTypeItem.LineProductId,
                        SkuId = skuTypeItem.Id,
                        Date = date,
                        TotalQuantity = saasTotalQty,
                        AvailableQuantity = supplierAvq,
                        Enabled = invEnabled
                    };
                    syncInventoryList.Add(syncInventoryItem);
                }
                
            }

            if (syncInventoryList.Any())
            {
                syncCalendarInventoryData.Add(new SyncSaasCalendarInventoryData(
                    TenantId: skuTypeItem.TenantId,
                    OtherDatesPushEmpty: false,
                    SyncItem: syncInventoryList));
            }
            
            syncLogs.Add(new AddSyncLogItem
            {
                TenantId = skuTypeItem.TenantId,
                ProductType = ProductType.Line,
                PlatformType = OpenPlatformType.OpenSupplier,
                SyncType = syncType,
                SyncResult = OpenPlatformPricingSyncResult.Success,
                ProductId = skuTypeItem.LineProductId,
                ProductName = lineProduct.Title,
                SkuId = skuTypeItem.LineProductSkuId,
                SkuName = lineSku.Name,
                SkuTypeItemName = skuTypeItem.Name,
                SyncStartDate = input.StartDate,
                SyncEndDate = input.EndDate,
                SupplierIsSale = input.IsSale
            });
            
            //记录本次同步的skuTypeItemId
            result.Add(skuTypeItem.Id);
        }
        
        if(newCalendarPriceList.Any())
            await _dbContext.AddRangeAsync(newCalendarPriceList);

        if (syncCalendarInventoryData.Any())
            await SyncSaasCalendarInventory(syncCalendarInventoryData.ToArray());
        
        //同步价库记录
        var addSyncLogInput = new AddSyncLogInput
        {
            Logs = syncLogs
        };
        await _openPlatformPriceInvSyncLogService.Add(addSyncLogInput);
        
        return result;
    }
         
    record SyncSaasCalendarInventoryData(long TenantId, bool OtherDatesPushEmpty, IEnumerable<BatchSyncThirdCalendarInventoryItem> SyncItem);
    /// <summary>
    /// 同步saas日历库存
    /// </summary>
    private async Task SyncSaasCalendarInventory(params SyncSaasCalendarInventoryData[] syncDataList)
    {
        if (syncDataList.Any() is false) return;
        foreach (var syncData in syncDataList)
        {
            var groupItems = syncData.SyncItem
                .GroupBy(x => new
                {
                    x.ProductId,
                    x.SkuId,
                    x.Date
                })
                .Select(x => x.First())
                .ToList();
            var byteItem = MessagePack.MessagePackSerializer.Typeless.Serialize(groupItems);//压缩
            BatchSyncThirdCalendarInventoryInput syncInput = new()
            {
                OtherDatesPushEmpty = syncData.OtherDatesPushEmpty,
                ByteItem = byteItem
            };
            var headers = new List<KeyValuePair<string, string>>
            {
                new("tenant", syncData.TenantId.ToString())
            };
            _ = _baseProductService.BatchSyncThirdCalendarInventory(syncInput,headers);
        }
    }

    
    record CalcSyncExchangeRateData(long ProductId,decimal ToSaasCostExchangeRate,decimal ToSaasSaleExchangeRate);
    /// <summary>
    /// 计算币种汇率
    /// </summary>
    /// <param name="currencyData"></param>
    /// <returns></returns>
    private async Task<List<CalcSyncExchangeRateData>> CalcSyncExchangeRate(List<(long ProductId,string openCostCurrencyCode,
        string saasCostCurrencyCode,string saasSaleCurrencyCode)> currencyData)
    {
        var result = new List<CalcSyncExchangeRateData>();
        
        //取出币种.计算汇率
        var toSaasCostExchangeRateRequest = currencyData
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.openCostCurrencyCode,
                TargetCurrencyCode = x.saasCostCurrencyCode
            });
        var toSaasSaleExchangeRateRequest = currencyData
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.saasCostCurrencyCode,
                TargetCurrencyCode = x.saasSaleCurrencyCode
            });
        var getExchangeRateRequest = toSaasCostExchangeRateRequest
            .Concat(toSaasSaleExchangeRateRequest)
            .ToList();
        var exchangeRateList = await _baseProductService.GetCurrencyExchangeRateList(getExchangeRateRequest);
        
        foreach (var item in currencyData)
        {
            var toSaasCostExchangeRate = exchangeRateList
                .FirstOrDefault(x => x.BaseCurrencyCode == item.openCostCurrencyCode
                                     && x.TargetCurrencyCode == item.saasCostCurrencyCode)?.ExchangeRate ?? 1;
            var toSaasSaleExchangeRate = exchangeRateList
                .FirstOrDefault(x => x.BaseCurrencyCode == item.saasCostCurrencyCode
                                     && x.TargetCurrencyCode == item.saasSaleCurrencyCode)?.ExchangeRate ?? 1;
            result.Add(new CalcSyncExchangeRateData(item.ProductId,toSaasCostExchangeRate,toSaasSaleExchangeRate));
        }
        return result;
    }

    record PushToChannelPriceStockResult(List<AddSyncLogItem> SyncLogs);
    /// <summary>
    /// 推送日历价库存到渠道
    /// </summary>
    /// <param name="currentDbContext"></param>
    /// <param name="input"></param>
    private async Task<PushToChannelPriceStockResult> PushToChannelPriceStock(CustomDbContext currentDbContext, ChannelPriceStockSyncInput input)
    {
        var result = new PushToChannelPriceStockResult(new List<AddSyncLogItem>());
        if (input.LineSkuTypeItemIds.Any() is false) return result;

        //套餐子项数据
        var lineSkuTypeItems = await currentDbContext.LineProductSkuTypeItems.IgnoreQueryFilters().AsNoTracking()
            .Where(x => input.LineSkuTypeItemIds.Contains(x.Id))
            .ToListAsync();

        //日历价
        var lineSkuTypeItemPrices = await currentDbContext.LineProductSkuCalendarPrice.IgnoreQueryFilters().AsNoTracking()
            .Where(x => x.LineProductSkuTypeItemId != null &&
                        input.LineSkuTypeItemIds.Contains(x.LineProductSkuTypeItemId!.Value))
            .Where(x => x.Date >= input.StartDate)
            .WhereIF(input.EndDate.HasValue, x => x.Date <= input.EndDate!)
            .ToListAsync();

        //渠道配置
        var lineProductIds = lineSkuTypeItems.Select(x => x.LineProductId).Distinct().ToList();
        var lineSkuIds = lineSkuTypeItems.Select(x => x.LineProductSkuId).Distinct().ToList();
        var lineProducts = await currentDbContext.LineProduct.IgnoreQueryFilters().AsNoTracking()
            .Where(x => lineProductIds.Contains(x.Id))
            .Select(x => new {x.Id, x.Title,x.TenantId})
            .ToListAsync();
        var lineSkus = await currentDbContext.LineProductSku.IgnoreQueryFilters().AsNoTracking()
            .Where(x => lineSkuIds.Contains(x.Id))
            .Select(x => new {x.Id, x.Name})
            .ToListAsync();
        var openChannelSettings = await currentDbContext.LineProductOpenChannelSettings.IgnoreQueryFilters().AsNoTracking()
            .Where(x => lineProductIds.Contains(x.LineProductId))
            .Where(x => x.SettingType == OpenChannelSettingType.PriceInventorySync)
            .Where(x => x.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync)
            .ToListAsync();
        var openChannelSettingIds = openChannelSettings.Select(x => x.Id).ToList();
        var openChannelSettingItems = await currentDbContext.LineProductOpenChannelSettingItems.IgnoreQueryFilters().AsNoTracking()
            .Where(x => lineProductIds.Contains(x.LineProductId) &&
                        openChannelSettingIds.Contains(x.LineProductOpenChannelSettingId))
            .ToListAsync();

        //查询日历库存
        var invStartDate = lineSkuTypeItemPrices.Select(x => x.Date).DefaultIfEmpty(DateTime.Today).Min();
        var invEndDate = lineSkuTypeItemPrices.Select(x => x.Date).DefaultIfEmpty(DateTime.Today.AddDays(179)).Max();
        var inventoryRequest = new GetCalendarInventoryInput
        {
            StartDate = invStartDate,
            EndDate = invEndDate,
            LimitDateRange = false,
            CalendarProducts = new List<CalendarProduct>()
        };
        inventoryRequest.CalendarProducts = lineSkuTypeItems.GroupBy(x => x.LineProductId)
            .Select(x => new CalendarProduct {ProductId = x.Key, ItemIds = x.Select(y => y.Id).ToList()});
        var skuItemInventories = await _baseProductService.GetCalendarInventory(inventoryRequest);

        //循环处理同步
        var syncLogs = new List<AddSyncLogItem>();
        var days = invEndDate.Subtract(invStartDate).Days;
        var meituanSyncRequestList = new List<(long,ChannelPriceStockUploadRequest)>();
        foreach (var skuTypeItem in lineSkuTypeItems)
        {
            var productName = lineProducts.FirstOrDefault(x => x.Id == skuTypeItem.LineProductId)?.Title ?? string.Empty;
            var lineSku = lineSkus.FirstOrDefault(x => x.Id == skuTypeItem.LineProductSkuId);
            var skuName = lineSku?.Name ?? string.Empty;
            var lineSkuId = lineSku?.Id;
            
            //判断是否存在需要同步的渠道
            var skuTypeItemOpenChannelSettings = openChannelSettings
                .Where(x => x.LineProductId == skuTypeItem.LineProductId)
                .ToList();
            if (skuTypeItemOpenChannelSettings.Any() is false) continue;
            var skuTypeItemOpenChannelSettingItems = openChannelSettingItems
                .Where(x => x.LineProductId == skuTypeItem.LineProductId)
                .ToList();
            var syncChannelTypes = skuTypeItemOpenChannelSettings.Select(x => x.PriceInventorySyncChannelType)
                .Distinct().ToList();//同步渠道

            #region 组装日历价

            var skuTypeChannelSchedules =
                new List<(PriceInventorySyncChannelType priceInventorySyncChannelType, List<ChannelPriceStockUploadSchedulesItem> schedulesItems)>();
            foreach (var syncChannelType in syncChannelTypes)
            {
                var schedulesItems = new List<ChannelPriceStockUploadSchedulesItem>();
                var priceChannelType = _openPlatformBaseService.MappingCalendarPriceChannelType(syncChannelType);
                for (int i = 0; i <= days; i++)
                {
                    var date = invStartDate.AddDays(i);
                    var datePrice = lineSkuTypeItemPrices.FirstOrDefault(x =>
                        x.Date == date && x.LineProductSkuTypeItemId == skuTypeItem.Id && x.PriceChannelType == priceChannelType);
                    var dateInventory = skuItemInventories
                        ?.FirstOrDefault(x => x.ItemId == skuTypeItem.Id)
                        ?.Inventories
                        ?.FirstOrDefault(x => x.Date == date);
                    
                    schedulesItems.Add(new ChannelPriceStockUploadSchedulesItem
                    {
                        OutSkuId = skuTypeItem.Id.ToString(),
                        Date = date.ToString("yyyy-MM-dd"),
                        Price = Convert.ToInt32((datePrice?.Price ?? 0) * 100),
                        Stock = dateInventory?.AvailableQuantity ?? 0
                    });
                }
                skuTypeChannelSchedules.Add((syncChannelType, schedulesItems));
            }

            #endregion

            #region 多渠道同步

            foreach (var setting in skuTypeItemOpenChannelSettings)
            {
                var outProductId = !string.IsNullOrEmpty(setting.SupplierProductId)
                    ? setting.SupplierProductId
                    : skuTypeItem.Id.ToString();
                if (setting.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Meituan)
                {
                    //判断是否配置了指定的同步skuTypeItemId
                    var settingSyncIds = skuTypeItemOpenChannelSettingItems
                        .Where(x => x.LineProductOpenChannelSettingId == setting.Id)
                        .Select(x => x.SyncSkuId)
                        .ToList();
                    if (settingSyncIds.Any())
                    {
                        //不包含该skuTypeItemId,跳过[美团]
                        if (!settingSyncIds.Contains(skuTypeItem.Id))
                        {
                            continue;
                        }
                    }
                }

                var skuTypeSchedules = skuTypeChannelSchedules.Where(x =>
                        x.priceInventorySyncChannelType == setting.PriceInventorySyncChannelType)
                    .Select(x => x.schedulesItems)
                    .FirstOrDefault();
                foreach (var schedulesItem in skuTypeSchedules)
                {
                    //0库存阈值判断
                    if(schedulesItem.Stock <= setting.ZeroStockThreshold)
                        schedulesItem.Stock = 0;
                }

                var otaType = _openPlatformBaseService.MappingOtaChannelType(setting.PriceInventorySyncChannelType);
                var syncRequest = new ChannelPriceStockUploadRequest
                {
                    IsCreUpdate = true,
                    OtaType = otaType.ToString().ToLowerInvariant(),
                    ProductType = OtaChannelProductType.Line.ToString().ToLowerInvariant(),
                    OutProductId = outProductId,
                    OtaProductId = setting.ChannelProductId ?? string.Empty,
                    OtaOptionId = lineSkuId?.ToString() ?? string.Empty,
                    OtaSkuId = setting.Area ?? string.Empty,
                    ScheduleType = setting.PriceInventorySyncType switch
                    {
                        PriceInventorySyncType.SyncPrice => OpenChannelPriceStockUploadScheduleType.SyncPrice,
                        PriceInventorySyncType.SyncInventory => OpenChannelPriceStockUploadScheduleType.SyncInventory,
                        _ => OpenChannelPriceStockUploadScheduleType.SyncAll,
                    },
                    Schedules = skuTypeSchedules
                };

                if (setting.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Meituan)
                {
                    //美团渠道同步特殊处理 -- 合并同步
                    meituanSyncRequestList.Add(new ValueTuple<long, ChannelPriceStockUploadRequest>(skuTypeItem.LineProductId, syncRequest));
                }
                else
                {
                    var syncResult = await _openChannelService.ChannelPriceStockUpload(syncRequest, skuTypeItem.TenantId);
                    //记录同步日志
                    syncLogs.Add(new AddSyncLogItem()
                    {
                        TenantId = skuTypeItem.TenantId,
                        ProductType = ProductType.Line,
                        PlatformType = OpenPlatformType.OpenChannel,
                        ChannelType = otaType,
                        SyncType = OpenPlatformPricingSyncType.ActivePush,
                        SyncResult = syncResult.Code == _successCode
                            ? OpenPlatformPricingSyncResult.Success
                            : OpenPlatformPricingSyncResult.Failed,
                        FailedMessage = syncResult.Msg,
                        ProductId = skuTypeItem.LineProductId,
                        ProductName = productName,
                        SkuId = skuTypeItem.LineProductSkuId,
                        SkuName = skuName,
                        SkuTypeItemName = skuTypeItem.Name,
                        SyncStartDate = invStartDate,
                        SyncEndDate = invEndDate
                    });
                }
            }

            #endregion
        }

        #region 美团渠道 - 同步
        
        /*
         * 1.美团价库同步 需要合并同于productId下的skuTypeItem的库存和价格进行同步
         */
        
        foreach (var lineProduct in lineProducts)
        {
            //查询当前产品是否存在需要同步到美团的数据
            var meituanRequestList = meituanSyncRequestList.Where(x => x.Item1 == lineProduct.Id)
                .Select(x => x.Item2)
                .ToList();
            if(meituanRequestList.Any() is false) continue;
            
            var mergeSchedules = meituanRequestList.SelectMany(x => x.Schedules).ToList();//合并Schedules
            var meituanSyncRequest = meituanRequestList.First();
            meituanSyncRequest.Schedules = mergeSchedules;
            
            var syncResult = await _openChannelService.ChannelPriceStockUpload(meituanSyncRequest, lineProduct.TenantId);
            //记录同步日志
            syncLogs.Add(new AddSyncLogItem()
            {
                TenantId = lineProduct.TenantId,
                ProductType = ProductType.Line,
                PlatformType = OpenPlatformType.OpenChannel,
                ChannelType = OtaChannelType.Meituan,
                SyncType = OpenPlatformPricingSyncType.ActivePush,
                SyncResult = syncResult.Code == _successCode
                    ? OpenPlatformPricingSyncResult.Success
                    : OpenPlatformPricingSyncResult.Failed,
                FailedMessage = syncResult.Msg,
                ProductId = lineProduct.Id,
                ProductName = lineProduct.Title,
                SkuId = 0,
                SkuName = string.Empty,
                SkuTypeItemName = string.Empty,
                SyncStartDate = invStartDate,
                SyncEndDate = invEndDate
            });
        }

        #endregion

        return new(syncLogs);
    }
    #endregion
}