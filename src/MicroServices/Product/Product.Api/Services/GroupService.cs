using AutoMapper;
using Contracts.Common.Product.DTOs.Group;
using Contracts.Common.Product.Enums;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Product.Api.Services.Interfaces;

namespace Product.Api.Services
{
    public class GroupService : IGroupService
    {
        private readonly CustomDbContext _dbContext;
        private readonly IMapper _mapper;

        public GroupService(IMapper mapper, CustomDbContext dbContext)
        {
            _mapper = mapper;
            _dbContext = dbContext;
        }

        public async Task AddGroup(AddGroupInput input)
        {
            Group entity = _mapper.Map<Group>(input);
            await _dbContext.Groups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();
        }

        public async Task SetGroup(SetGroupInput input)
        {
            Group group = await _dbContext.Groups.FindAsync(input.Id);
            group.Name = input.Name;
            group.Remark = input.Remark;
            group.UpdateTime = DateTime.Now;
            await _dbContext.SaveChangesAsync();
        }

        public async Task RemoveGroup(long id)
        {
            Group group = await _dbContext.Groups.FindAsync(id);
            _dbContext.Groups.Remove(group);
            List<GroupItems> groupItems = await _dbContext.GroupItems.Where(x => x.GroupId == id).ToListAsync();
            _dbContext.RemoveRange(groupItems);
            await _dbContext.SaveChangesAsync();
        }

        public async Task<GetGroupByIdOutput> GetGroupById(long id)
        {
            Group group = await _dbContext.Groups.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);
            GetGroupByIdOutput result = _mapper.Map<GetGroupByIdOutput>(group);
            return result;
        }

        public async Task<List<GetAllGroupOutput>> GetAllGroup()
        {
            List<GetAllGroupOutput> result = await _dbContext.Groups.AsNoTracking()
                .Select(x => new GetAllGroupOutput
                {
                    Id = x.Id,
                    Name = x.Name,
                    Remark = x.Remark
                })
                .ToListAsync();
            return result;
        }

        public async Task<List<GetGroupByProductIdsOutput>> GetGroupByProductIds(List<long> productIds)
        {
            List<GetGroupByProductIdsOutput> result = await _dbContext.GroupItems.AsNoTracking()
                .GroupJoin(_dbContext.Groups.AsNoTracking(), item => item.GroupId, group => group.Id, (item, group) =>
                    new
                    {
                        item,
                        group
                    })
                .SelectMany(s => s.group.DefaultIfEmpty(), (x, group) => new
                {
                    x.item,
                    group
                })
                .Where(x => productIds.Contains(x.item.ProductId))
                .Select(x => new GetGroupByProductIdsOutput
                {
                    Name = x.group.Name,
                    ProductId = x.item.ProductId,
                    Remark = x.group.Remark
                })
                .ToListAsync();
            return result;
        }

        public async Task<List<GetProductGroupsOutput>> GetProductGroups(IEnumerable<long> productIds)
        {
            var query = from g in _dbContext.Groups
                        join gi in _dbContext.GroupItems
                        on g.Id equals gi.GroupId
                        where productIds.Contains(gi.ProductId)
                        group g by gi.ProductId
                        into t
                        select new GetProductGroupsOutput
                        {
                            ProductId = t.Key,
                            GroupItems = t.Select(s => new ProductGroupItem
                            {
                                Id = s.Id,
                                Name = s.Name,
                                Remark = s.Remark
                            })
                        };
            return await query.ToListAsync();
        }

        public async Task<PagingModel<SearchGroupOutput>> SearchGroup(SearchGroupInput input)
        {
            IQueryable<SearchGroupOutput> data = from item in from g in _dbContext.Groups.AsNoTracking()
                                                              join gi in _dbContext.GroupItems.AsNoTracking() on g.Id equals gi.GroupId into items
                                                              from v in items.DefaultIfEmpty()
                                                              where string.IsNullOrWhiteSpace(input.Name) ? true : g.Name.Contains(input.Name)
                                                              select new
                                                              {
                                                                  GroupId = g.Id,
                                                                  GroupName = g.Name,
                                                                  g.UpdateTime,
                                                                  g.CreateTime,
                                                                  GroupItems = v == null ? 0 : v.Id
                                                              }
                                                 group item by item.GroupId into groupChild
                                                 select new SearchGroupOutput
                                                 {
                                                     GroupId = groupChild.Key,
                                                     GroupName = groupChild.Min(x => x.GroupName),
                                                     ProductQuantity = groupChild.Count(x => x.GroupItems > 0)
                                                 };
            return await data
                .OrderByDescending(x => x.GroupId)
                .PagingAsync(input.PageIndex, input.PageSize);
        }

        public async Task SetGroupItems(SetGroupItemsInput input)
        {
            List<long> productIds = input.Products.Select(x => x.ProductId).ToList();
            List<GroupItems> products = await _dbContext.GroupItems
                .Where(x => productIds.Contains(x.ProductId))
                .ToListAsync();
            _dbContext.GroupItems.RemoveRange(products);
            if (input.GroupId is > 0)
            {
                List<GroupItems> entitys = new List<GroupItems>();
                foreach (AddGroupItems item in input.Products)
                {
                    entitys.Add(new GroupItems
                    {
                        GroupId = input.GroupId.Value,
                        ProductId = item.ProductId,
                        ProductType = item.ProductType
                    });
                }
                await _dbContext.GroupItems.AddRangeAsync(entitys);
            }
            await _dbContext.SaveChangesAsync();
        }

        public async Task<bool> UpdateGroupItems(UpdateGroupItemInput input)
        {
            if (input.RequestType == 2)
            {
                List<GroupItems> delGroupItems = await _dbContext.GroupItems.Where(x =>
                        input.ProductIds.Contains(x.ProductId)
                        && input.GroupIds.Contains(x.GroupId))
                    .ToListAsync();
                _dbContext.GroupItems.RemoveRange(delGroupItems);
            }
            else if (input.RequestType == 1)
            {
                var oldGroupItems = await _dbContext.GroupItems.AsNoTracking()
                    .Where(x => input.ProductIds.Contains(x.ProductId))
                    .ToListAsync();

                var addGroupItems = new List<GroupItems>();
                input.ProductIds.ForEach(productId =>
                {
                    input.GroupIds.ForEach(groupId =>
                    {
                        if (!oldGroupItems.Any(x => x.ProductId == productId && x.GroupId == groupId)) 
                        {
                            addGroupItems.Add(new GroupItems
                            {
                                ProductId = productId,
                                GroupId = groupId,
                                ProductType = input.ProductType
                            });                            
                        }
                    });
                });
                await _dbContext.GroupItems.AddRangeAsync(addGroupItems);
            }
            else
            {
                return false;
            }
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<PagingModel<GetProductsOutput>> GetProducts(GetProductsInput input)
        {
            var products = await _dbContext.GroupItems.AsNoTracking()
                .Where(x => x.GroupId == input.GroupId)
                .OrderByDescending(x=> x.CreateTime)
                .Select(x => new
                {
                    x.ProductId,
                    x.ProductType
                })
                .PagingAsync(input.PageIndex, input.PageSize);

            var productData = new List<GetProductsOutput>();

            #region 票券产品

            var ticketProductIds = products.Data.Where(v => v.ProductType == ProductType.Ticket)
                .Select(x => x.ProductId)
                .ToList();
            if (ticketProductIds.Any()) 
            {
                var ticketProducts = await _dbContext.TicketProducts
                    .Where(x => ticketProductIds.Contains(x.Id))
                    .WhereIF(input.InSale, x => x.Enabled
                                                && (!x.SellingDateBegin.HasValue || x.SellingDateBegin <= DateTime.Now)
                                                && (!x.SellingDateEnd.HasValue ||
                                                    x.SellingDateEnd > DateTime.Now.AddDays(-1)))
                    .Select(x => new GetProductsOutput
                    {
                        ProductId = x.Id,
                        ProductName = x.Title,
                        ProductType = ProductType.Ticket,
                        TicketBusinessType = x.TicketBusinessType
                    })
                    .ToListAsync();

                if (ticketProducts.Any())
                {
                    ticketProductIds = ticketProducts.Select(x => x.ProductId).ToList();
                    var cities = await _dbContext.ProductResources.AsNoTracking()
                        .Where(x => ticketProductIds.Contains(x.ProductId))
                        .Select(x => new
                        {
                            x.ProductId,
                            x.CityName,
                            x.CountryCode,
                            x.CountryName
                        })
                        .ToListAsync();

                    ticketProducts.ForEach(x =>
                    {
                        x.CityName = cities.Where(c => c.ProductId == x.ProductId)
                            .GroupBy(c => c.CityName)
                            .Select(c => c.Key)
                            .ToList();
                        x.CountryName = cities.Where(c => c.ProductId == x.ProductId)
                            .GroupBy(c => c.CountryName)
                            .Select(c => c.Key)
                            .ToList();
                    });

                    productData.AddRange(ticketProducts);
                }
            }

            #endregion

            #region 邮寄产品

            var mailProductIds = products.Data.Where(v => v.ProductType == ProductType.Mail)
                .Select(x => x.ProductId)
                .ToList();
            if (mailProductIds.Any()) 
            {
                var mailProducts = await _dbContext.MailProducts
                    .Where(x => mailProductIds.Contains(x.Id))
                    .WhereIF(input.InSale, x => x.Enabled
                                                && (!x.SellingDateBegin.HasValue || x.SellingDateBegin <= DateTime.Now)
                                                && (!x.SellingDateEnd.HasValue ||
                                                    x.SellingDateEnd > DateTime.Now.AddDays(-1)))
                    .Select(x => new GetProductsOutput
                    {
                        ProductId = x.Id,
                        ProductName = x.Title,
                        ProductType = ProductType.Mail
                    })
                    .ToListAsync();
                
                    productData.AddRange(mailProducts);
            }

            #endregion

            #region 线路产品

            var lineProductIds = products.Data.Where(v => v.ProductType == ProductType.Line)
                .Select(x => x.ProductId)
                .ToList();
            if (lineProductIds.Any()) 
            {
                var lineProducts = await _dbContext.LineProduct
                       .Where(x => lineProductIds.Contains(x.Id))
                       .WhereIF(input.InSale, x => x.Enabled
                                                   && (!x.SellingDateBegin.HasValue || x.SellingDateBegin <= DateTime.Now)
                                                   && (!x.SellingDateEnd.HasValue ||
                                                       x.SellingDateEnd > DateTime.Now.AddDays(-1)))
                       .Select(x => new GetProductsOutput
                       {
                           ProductId = x.Id,
                           ProductName = x.Title,
                           ProductType = ProductType.Line,
                           DepartureCountryName = x.DepartureCountryName,
                           DepartureCityName = x.DepartureCityName,
                           DestinationCountryName = x.DestinationCountryName,
                           DestinationCityName = x.DestinationCityName
                       })
                       .ToListAsync();
                
                    productData.AddRange(lineProducts);
            }

            #endregion

            var productIds = products.Data.Select(x => x.ProductId).ToList();
            var result = new List<GetProductsOutput>();
            foreach (var productId in productIds) 
            {
                var product = productData.FirstOrDefault(x => x.ProductId == productId);
                if (product is null)
                    continue;

                result.Add(product);
            }

            #region  产品首图

            var productPhotos = await _dbContext.ProductPhotos
                .Where(x => productIds.Contains(x.ProductId) 
                    && x.MediaType == MediaTypeOfPhotos.Picture
                    && x.Enabled
                    && x.Sort == 0)
                .Select(x => new
                {
                    x.ProductId,
                    x.Path
                })
                .ToListAsync();
            result.ForEach(x => 
            { 
                x.Cover = productPhotos.FirstOrDefault(p => p.ProductId == x.ProductId)?.Path ?? ""; 
            });

            #endregion

            #region 最低价

            var productRedundantDatas = await _dbContext.ProductRedundantDatas
                .Where(x => productIds.Contains(x.ProductId))
                .Select(x => new
                {
                    x.ProductId,
                    x.MinPrice,
                    x.MinLinePrice
                })
                .ToListAsync();
            result.ForEach(x =>
            {
                var data = productRedundantDatas.FirstOrDefault(w => w.ProductId == x.ProductId);
                x.MinPrice = data?.MinPrice ?? 0;  
                x.MinLinePrice = data?.MinLinePrice ?? 0;
            });

            #endregion

            return new PagingModel<GetProductsOutput>() 
            {
                Total = products.Total,
                PageIndex = input.PageIndex,
                PageSize = input.PageSize,
                Data = result
            };
        }
        
        public async Task<List<GetProductStatisticsOutput>> GetStatistics(long groupId)
        {
            var result = new List<GetProductStatisticsOutput>();
            var groupItems = await _dbContext.GroupItems.AsNoTracking()
                .Where(x => x.GroupId == groupId)
                .ToListAsync();

            if (groupItems.Any() is false) return result;
            result = groupItems.GroupBy(x => x.ProductType)
                .Select(x => new GetProductStatisticsOutput
                {
                    ProductType = x.Key,
                    Count = x.Count()
                })
                .ToList();
            return result;
        }
    }
}