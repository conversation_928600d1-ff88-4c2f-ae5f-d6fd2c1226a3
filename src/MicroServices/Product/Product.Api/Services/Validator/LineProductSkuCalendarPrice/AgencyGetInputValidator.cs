using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using FluentValidation;

namespace Product.Api.Services.Validator.LineProductSkuCalendarPrice;

public class AgencyGetInputValidator : AbstractValidator<AgencyGetInput>
{
    public AgencyGetInputValidator()
    {
        RuleFor(x => x.StartDate)
            .NotEmpty()
            .GreaterThanOrEqualTo(DateTime.Today.AddDays(-1));
        RuleFor(x => x.EndDate)
            .NotEmpty()
            .GreaterThanOrEqualTo(x => x.StartDate);
    }
}
