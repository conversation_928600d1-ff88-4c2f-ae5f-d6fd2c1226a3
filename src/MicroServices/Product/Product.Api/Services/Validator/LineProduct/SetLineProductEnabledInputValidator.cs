using Contracts.Common.Product.DTOs.LineProduct;
using FluentValidation;

namespace Product.Api.Services.Validator.LineProduct;

public class SetLineProductEnabledInputValidator : AbstractValidator<SetLineProductEnabledInput>
{
    public SetLineProductEnabledInputValidator() 
    {
        RuleFor(x => x.ProductIds).Must(x => x.Count > 0 && x.All(w => w > 0));
        RuleFor(x => x.IsEnabled).NotNull();
    }
}
