using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Hotel.Api.ConfigModel;
using Hotel.Api.Infrastructure;
using Hotel.Api.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Hotel.UnitTest;

public class HotelServiceTest : TestBase<CustomDbContext>
{
    private long tenantId = 897049819561590784;

    public HotelService CreateService(
        ILogger<HotelService> logger = null,
        ICapPublisher capPublisher = null,
        IHttpClientFactory httpClientFactory = null,
        IOptions<ServicesAddress> options = null,
        IMapper mapper = null,
        CustomDbContext dbContext = null,
        IHttpContextAccessor httpContextAccessor = null,
        MediatR.IMediator mediator = null,
        IRedisClient redisClient = null,
        IOptions<HuiZhiHotelConfig> huiZhiHotelConfig=null)
    {
        if (options is null)
            options = Options.Create(new ServicesAddress());

        return new HotelService(
            capPublisher, 
            mapper,
            httpClientFactory,
            options,
            httpContextAccessor,
            dbContext,
            mediator,
            redisClient,
            huiZhiHotelConfig
        );
    }

    [Fact(DisplayName = "获取酒店营业信息")]
    public async Task GetOperateInfo()
    {
        var tenantId = 1;
        var dbContext = GetNewDbContext(tenantId);
        var service = CreateService(dbContext: dbContext);

        //fake data
        var hotel = new Api.Model.Hotel()
        {
            OperatingModel = OperatingModel.Agency,
            ZHName = "测试酒店",
            ENName = "test hotel",
            CountryCode = 10,
            CountryName = "中国",
            ProvinceCode = 101,
            ProvinceName = "广东",
            CityCode = 1001,
            CityName = "深圳",
            Address = "",
            ServiceTimeBegin = new TimeSpan(10, 0, 0),
            ServiceTimeEnd = new TimeSpan(2, 0, 0),
            ServiceEndtimeInNextDay = true,
            Enabled = true
        };
        hotel.SetLocation(0, 0);
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();

        //act
        var result = await service.GetOperateInfo(hotel.Id);

        //assert
        Assert.NotNull(result);
    }

    [Fact(DisplayName = "按城市分组获取所有酒店")]
    public async Task GetAllGroupbyCity()
    {
        //arrange
        var tenantId = 1;
        var dbContext = GetNewDbContext(tenantId);
        var service = CreateService(dbContext: dbContext);

        //fake data
        var hotels = new List<Api.Model.Hotel>()
        {
            new()
            {
                CityCode = 1001,
                CityName = "广州",
                Enabled = true
            },
            new()
            {
                CityCode = 1002,
                CityName = "深圳",
                Enabled = true
            },
            new()
            {
                CityCode = 1001,
                CityName = "广州",
                Enabled = true
            },
            new()
            {
                CityCode = 1003,
                CityName = "佛山",
                Enabled = true
            },
            new()
            {
                CityCode = 1003,
                CityName = "佛山",
                Enabled = true
            },
        };
        hotels.ForEach(x =>
        {
            x.ZHName = "";
            x.ENName = "";
            x.CountryName = "";
            x.ProvinceName = "";
            x.Address = "";
            x.SetLocation(0, 0);
        });
        await dbContext.AddRangeAsync(hotels);
        await dbContext.SaveChangesAsync();

        //act
        var result = await service.GetAllGroupbyCity();

        //assert
        Assert.True(result.Count() == 3);
    }

    [Fact(DisplayName = "检查酒店是否有效")]
    public async Task CheckIsEnabled()
    {
        //arrange
        var tenantId = 1;
        var dbContext = GetNewDbContext(tenantId);
        var service = CreateService(dbContext: dbContext);

        //fake data
        var hotels = new List<Api.Model.Hotel>()
        {
            new() { Enabled = true },
            new() { Enabled = false },
            new() { Enabled = true }
        };
        hotels.ForEach(x =>
        {
            x.ZHName = "";
            x.ENName = "";
            x.CountryName = "";
            x.ProvinceName = "";
            x.CityName = "";
            x.Address = "";
            x.SetLocation(0, 0);
        });
        await dbContext.AddRangeAsync(hotels);
        await dbContext.SaveChangesAsync();

        //act
        var hotelIds = hotels.Select(x => x.Id).ToList();
        hotelIds.Add(1);
        var result = await service.CheckIsEnabled(new CheckIsEnabledInput() { HotelIds = hotelIds });

        //assert
        Assert.True(result.Count(x => !x.Enabled) == 2);
    }

    #region 获取日历房酒店列表

    [Fact(DisplayName = "获取日历房酒店列表_成功")]
    public async Task Search_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        var mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Api.Model.Hotel, SearchHotelsOutput>();
            cfg.CreateMap<PagingModel<Api.Model.Hotel>, PagingModel<SearchHotelsOutput>>();
        }).CreateMapper();
        //act
        var service = CreateService(
            dbContext: dbContext,
            mapper: mapper);
        var input = new SearchHotelsInput
        {
            CityCode = 440100,
            Enabled = true,
            KeyWord = "上海衡山大酒店",
            OperatingModel = OperatingModel.Agency,
            PageIndex = 1,
            PageSize = 10
        };
        var output = await service.Search(input);
        //assert
        Assert.True(output.Data.Any());
    }

    #endregion

    #region 修改日历房酒店上架状态

    [Fact(DisplayName = "修改日历房酒店上架状态_成功")]
    public async Task UpdateHotelEnabled_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var mediator = new Mock<MediatR.IMediator>();
        mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
            .Returns(Task.CompletedTask);
        var service = CreateService(dbContext: dbContext, mediator: mediator.Object);
        var input = new UpdateHotelEnabledInput
        {
            Enabled = true,
            Id = hotel.Id
        };
        await service.UpdateHotelEnabled(input);
        //assert
        Assert.True(true);
    }

    #endregion

    #region 获取商户已添加酒店列表

    [Fact(DisplayName = "获取商户已添加酒店列表_成功")]
    public async Task GetHotels_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var input = new CheckRefInput { HotelIds = new List<long> { 904927434775461898 } };
        var output = await service.CheckRefByResourceHotelIds(input);
        //assert
        Assert.True(output.Any());
    }

    #endregion

    #region 获取酒店详情

    [Fact(DisplayName = "获取酒店详情_成功")]
    public async Task Detail_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Api.Model.Hotel, GetHotelDetailsOutput>();
        }).CreateMapper();

        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(
            dbContext: dbContext,
            mapper: mapper
        );
        var result = await service.Detail(hotel.Id);
        //assert
        Assert.NotNull(result);
    }

    [Fact(DisplayName = "获取酒店详情_无数据")]
    public async Task Detail_Success_NoData()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Api.Model.Hotel, GetHotelDetailsOutput>();
        }).CreateMapper();
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(
            dbContext: dbContext,
            mapper: mapper
        );
        try
        {
            await service.Detail(904927434775461888);
        }
        catch (BusinessException ex)
        {
            //assert
            Assert.True(ex.Message == "无数据");
        }
    }

    #endregion

    #region 修改酒店详情

    [Fact(DisplayName = "修改酒店详情_成功")]
    public async Task Update_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<UpdateHotelDetailsInput, Api.Model.Hotel>();
        }).CreateMapper();
        var hotel = GetHitelModel();

        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var mediator = new Mock<MediatR.IMediator>();
        mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
            .Returns(Task.CompletedTask);
        var service = CreateService(
            dbContext: dbContext,
            mapper: mapper,
            mediator: mediator.Object
        );
        var input = new UpdateHotelDetailsInput
        {
            Id = hotel.Id,
            Address = "shang hai shi bao shan ou hu tai lu 4 7 8 8 hao gu cun gong yuan & huan jinɡ zhi zhong",
            CheckinPolicy = "0-11岁儿童使用现有床铺免费",
            CityCode = 440100,
            CityName = "广州",
            CountryCode = 10,
            CountryName = "中国",
            DecorateDate = DateTime.Now,
            DistrictCode = 440104,
            DistrictName = "越秀",
            Floors = 1,
            HotelType = HotelType.FeaturedAccommodation,
            ImportantNotices = "客人在抵达时须支付THB 2000的保证金",
            Intro = "下榻吉隆坡大道旅馆-圣吉尔斯酒店，感受吉隆坡的独特魅力",
            OpeningDate = DateTime.Now,
            OperatingModel = OperatingModel.Agency,
            ProvinceCode = 440000,
            ProvinceName = "广东",
            Rooms = 1,
            ServiceEndtimeInNextDay = true,
            ServiceTimeBegin = DateTime.Now.TimeOfDay,
            ServiceTimeEnd = DateTime.Now.TimeOfDay,
            StarLevel = 5,
            SurroundingFacilities = "到市中心10 公里,到最近的机场",
            Telefax = "-021-56046120",
            Telephone = "13751729087",
            Latitude = 113.87079,
            Longitude = 35.283348,
            FacilitiesList = new List<long>
            {
                900668034491518978,
                900668034491518988
            }
        };
        await service.Update(input);
        //assert
        Assert.True(true);
    }

    [Fact(DisplayName = "修改酒店详情_成功_没位置")]
    public async Task Update_Success_NoLongitude()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var mapper = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<UpdateHotelDetailsInput, Api.Model.Hotel>();
        }).CreateMapper();
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var mediator = new Mock<MediatR.IMediator>();
        mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
            .Returns(Task.CompletedTask);
        var service = CreateService(
            dbContext: dbContext,
            mapper: mapper,
            mediator: mediator.Object
        );
        var input = new UpdateHotelDetailsInput
        {
            Id = hotel.Id,
            Address = "shang hai shi bao shan ou hu tai lu 4 7 8 8 hao gu cun gong yuan & huan jinɡ zhi zhong",
            CheckinPolicy = "0-11岁儿童使用现有床铺免费",
            CityCode = 440100,
            CityName = "广州",
            CountryCode = 10,
            CountryName = "中国",
            DecorateDate = DateTime.Now,
            DistrictCode = 440104,
            DistrictName = "越秀",
            Floors = 1,
            HotelType = HotelType.FeaturedAccommodation,
            ImportantNotices = "客人在抵达时须支付THB 2000的保证金",
            Intro = "下榻吉隆坡大道旅馆-圣吉尔斯酒店，感受吉隆坡的独特魅力",
            OpeningDate = DateTime.Now,
            OperatingModel = OperatingModel.Agency,
            ProvinceCode = 440000,
            ProvinceName = "广东",
            Rooms = 1,
            ServiceEndtimeInNextDay = true,
            ServiceTimeBegin = DateTime.Now.TimeOfDay,
            ServiceTimeEnd = DateTime.Now.TimeOfDay,
            StarLevel = 5,
            SurroundingFacilities = "到市中心10 公里,到最近的机场",
            Telefax = "-021-56046120",
            Telephone = "13751729087",
            FacilitiesList = new List<long>
            {
                900668034491518978,
                900668034491518988
            }
        };
        await service.Update(input);
        //assert
        Assert.True(true);
    }

    #endregion

    #region 获取酒店图片列表

    [Fact(DisplayName = "获取酒店图片列表_成功")]
    public async Task GetHotelPhotos_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelList();
        var room = GetHotelRoom(hotel.FirstOrDefault().Id);
        var roomphoto = GetHotelPhotos(hotel.FirstOrDefault().Id, room.FirstOrDefault().Id);

        await dbContext.AddRangeAsync(hotel);
        await dbContext.AddRangeAsync(room);
        await dbContext.AddRangeAsync(roomphoto);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var result = await service.GetHotelPhotos(hotel.FirstOrDefault().Id);
        //assert
        Assert.NotNull(result);
    }

    #endregion

    #region 修改酒店图片

    [Fact(DisplayName = "修改酒店图片_成功")]
    public async Task UpdateHotelPhotos_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelList();
        var room = GetHotelRoom(hotel.FirstOrDefault().Id);
        var roomphoto = GetHotelPhotos(hotel.FirstOrDefault().Id, room.FirstOrDefault().Id);

        await dbContext.AddRangeAsync(hotel);
        await dbContext.AddRangeAsync(room);
        await dbContext.AddRangeAsync(roomphoto);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);

        var input = GetUpdateHotelPhotosInput(hotel.FirstOrDefault().Id, room.FirstOrDefault().Id, roomphoto.FirstOrDefault().Id);
        await service.UpdateHotelPhotos(input);
        //assert
        Assert.True(true);
    }

    #endregion

    #region 获取酒店房型列表

    [Fact(DisplayName = "获取酒店房型列表_成功")]
    public async Task GetHotelRooms_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        var room = GetHotelRoomModel(hotel.Id);
        await dbContext.AddAsync(hotel);
        await dbContext.AddAsync(room);

        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var input = new GetHotelRoomsInput { HotelId = hotel.Id };
        var result = await service.GetHotelRooms(input);
        //assert
        Assert.NotNull(result);
    }

    [Fact(DisplayName = "获取酒店房型列表_无数据")]
    public async Task GetHotelRooms_Success_NoData()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        var room = GetHotelRoomModel(hotel.Id);
        await dbContext.AddAsync(hotel);
        await dbContext.AddAsync(room);

        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var input = new GetHotelRoomsInput { HotelId = 904927434775461888 };
        var result = await service.GetHotelRooms(input);
        //assert
        Assert.NotNull(result);
    }

    #endregion

    #region 获取房型详情

    [Fact(DisplayName = "获取房型详情_成功")]
    public async Task GetHotelRoomDetails_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        var room = GetHotelRoomModel(hotel.Id);
        await dbContext.AddAsync(hotel);
        await dbContext.AddAsync(room);

        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var result = await service.GetHotelRoomDetails(new List<long> { room.Id });
        //assert
        Assert.NotNull(result);
    }

    #endregion

    #region 修改房型

    [Fact(DisplayName = "修改房型_成功")]
    public async Task UpdateHotelRoom_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var httpContextAccessor = GetHttpContextAccessor(new CurrentUser
        {
            userid = 901019344460090000,
            tenant = tenantId
        });

        var hotel = GetHitelModel();
        var room = GetHotelRoomModel(hotel.Id);
        await dbContext.AddAsync(hotel);
        await dbContext.AddAsync(room);
        await dbContext.SaveChangesAsync();

        var capPublisher = GetCapPublisher();
        var mediator = new Mock<MediatR.IMediator>();
        mediator.Setup(s => s.Publish(It.IsAny<MediatR.INotification>(), default))
            .Returns(Task.CompletedTask);
        var service = CreateService(
            dbContext: dbContext,
            httpContextAccessor: httpContextAccessor,
            capPublisher: capPublisher,
            mediator: mediator.Object
        );
        var input = new UpdateHotelRoomInput()
        {
            AreaMax = room.AreaMax,
            AreaMin = room.AreaMin,
            BedType = new List<BedType>(),
            ENName = room.ENName,
            FloorMax = room.FloorMax,
            FloorMin = room.FloorMin,
            HotelId = room.HotelId,
            MaximumOccupancy = room.MaximumOccupancy,
            RoomQuantity = 10,
            WindowType = WindowType.Have,
            ZHName = room.ZHName,
            Id = room.Id
        };
        await service.UpdateHotelRoom(input);
        //assert
        Assert.True(true);
    }

    #endregion

    #region 修改房型排序

    [Fact(DisplayName = "修改房型排序_成功")]
    public async Task UpdateHotelRoomSort_Success()
    {
        //arrange

        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelList();
        var room = GetHotelRoom(hotel.FirstOrDefault().Id);

        await dbContext.AddRangeAsync(hotel);
        await dbContext.AddRangeAsync(room);

        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(
            dbContext: dbContext
        );

        var input = new UpdateHotelRoomSortInput()
        {
            Id = new List<long>()
            {
                room.FirstOrDefault().Id,
                room.LastOrDefault().Id
            }
        };
        await service.UpdateHotelRoomSort(input);
        //assert
        Assert.True(true);
    }

    [Fact(DisplayName = "修改房型排序_成功_不排序")]
    public async Task UpdateHotelRoomSort_Success_NoSort()
    {
        //arrange

        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelList();
        var room = GetHotelRoom(hotel.FirstOrDefault().Id);

        await dbContext.AddRangeAsync(hotel);
        await dbContext.AddRangeAsync(room);

        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);

        var input = new UpdateHotelRoomSortInput() { Id = new List<long>() { 904930535221755904 } };
        await service.UpdateHotelRoomSort(input);
        //assert
        Assert.True(true);
    }

    #endregion

    #region 获取酒店简介

    [Fact(DisplayName = "获取酒店简介_成功")]
    public async Task GetIntro_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var result = await service.GetOperateInfo(hotel.Id);
        //assert
        Assert.NotNull(result);
    }

    #endregion

    #region 获取商户全部酒店

    [Fact(DisplayName = "获取商户全部酒店_成功")]
    public async Task GetAllGroupbyCity_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var result = await service.GetAllGroupbyCity();
        //assert
        Assert.NotNull(result);
    }

    #endregion

    #region 根据酒店id效验酒店是否上架

    [Fact(DisplayName = "根据酒店id效验酒店是否上架_成功")]
    public async Task CheckIsEnabled_Success()
    {
        //arrange
        var dbContext = GetNewDbContext(tenantId);
        var hotel = GetHitelModel();
        await dbContext.AddAsync(hotel);
        await dbContext.SaveChangesAsync();
        //act
        var service = CreateService(dbContext: dbContext);
        var input = new CheckIsEnabledInput() { HotelIds = new List<long>() { hotel.Id } };
        var result = await service.CheckIsEnabled(input);
        //assert
        Assert.NotNull(result);
    }

    #endregion

    #region 公共数据

    public Api.Model.Hotel GetHitelModel()
    {
        var model = new Api.Model.Hotel
        {
            Address = "shang hai shi bao shan ou hu tai lu 4 7 8 8 hao gu cun gong yuan & huan jinɡ zhi zhong",
            CheckinPolicy = "0-11岁儿童使用现有床铺免费",
            CityCode = 440100,
            CityName = "广州",
            CountryCode = 10,
            CountryName = "中国",
            CreateTime = DateTime.Now,
            DecorateDate = DateTime.Now,
            DistrictCode = 440104,
            DistrictName = "越秀",
            Enabled = true,
            ENName = "Deluxe big Room",
            Floors = 1,
            HopId = 8,
            HotelType = HotelType.FeaturedAccommodation,
            ImportantNotices = "客人在抵达时须支付THB 2000的保证金",
            Intro = "下榻吉隆坡大道旅馆-圣吉尔斯酒店，感受吉隆坡的独特魅力",
            OpeningDate = DateTime.Now,
            OperatingModel = OperatingModel.Agency,
            ProvinceCode = 440000,
            ProvinceName = "广东",
            ResourceHotelId = 904927434775461898,
            Rooms = 1,
            ServiceEndtimeInNextDay = true,
            ServiceTimeBegin = DateTime.Now.TimeOfDay,
            ServiceTimeEnd = DateTime.Now.TimeOfDay,
            StarLevel = 5,
            SurroundingFacilities = "到市中心10 公里,到最近的机场",
            Telefax = "-021-56046120",
            Telephone = "13751729087",
            UpdateTime = DateTime.Now,
            ZHName = "123上海衡山大酒店"
        };
        model.SetLocation(113.87079, 35.283348);
        return model;
    }

    public List<Api.Model.Hotel> GetHitelList()
    {
        var list = new List<Api.Model.Hotel>();
        var model = new Api.Model.Hotel
        {
            Address = "shang hai shi bao shan ou hu tai lu 4 7 8 8 hao gu cun gong yuan & huan jinɡ zhi zhong",
            CheckinPolicy = "0-11岁儿童使用现有床铺免费",
            CityCode = 440100,
            CityName = "广州",
            CountryCode = 10,
            CountryName = "中国",
            CreateTime = DateTime.Now,
            DecorateDate = DateTime.Now,
            DistrictCode = 440104,
            DistrictName = "越秀",
            Enabled = true,
            ENName = "Deluxe big Room",
            Floors = 1,
            HopId = 8,
            HotelType = HotelType.FeaturedAccommodation,
            ImportantNotices = "客人在抵达时须支付THB 2000的保证金",
            Intro = "下榻吉隆坡大道旅馆-圣吉尔斯酒店，感受吉隆坡的独特魅力",
            OpeningDate = DateTime.Now,
            OperatingModel = OperatingModel.Agency,
            ProvinceCode = 440000,
            ProvinceName = "广东",
            ResourceHotelId = 904927434775461898,
            Rooms = 1,
            ServiceEndtimeInNextDay = true,
            ServiceTimeBegin = DateTime.Now.TimeOfDay,
            ServiceTimeEnd = DateTime.Now.TimeOfDay,
            StarLevel = 5,
            SurroundingFacilities = "到市中心10 公里,到最近的机场",
            Telefax = "-021-56046120",
            Telephone = "13751729087",
            UpdateTime = DateTime.Now,
            ZHName = "123上海衡山大酒店"
        };
        model.SetLocation(113.87079, 35.283348);
        list.Add(model);
        return list;
    }

    public List<Api.Model.HotelPhotos> GetHotelPhotos(long hotelId, long roomId)
    {
        var list = new List<Api.Model.HotelPhotos>();
        list.Add(new Api.Model.HotelPhotos
        {
            CreateTime = DateTime.Now,
            Enabled = true,
            HotelId = hotelId,
            HotelRoomId = 0,
            Path = "/test/123.png",
            Sort = 1
        });
        list.Add(new Api.Model.HotelPhotos
        {
            CreateTime = DateTime.Now,
            Enabled = true,
            HotelId = hotelId,
            HotelRoomId = roomId,
            Path = "/test/123.png",
            Sort = 2
        });
        return list;
    }

    public Api.Model.HotelRoom GetHotelRoomModel(long hotelId)
    {
        return new Api.Model.HotelRoom
        {
            AreaMax = 120,
            AreaMin = 12,
            BedType = "",
            CreateTime = DateTime.Now,
            ENName = "big room",
            FloorMax = 10,
            FloorMin = 1,
            HotelId = hotelId,
            MaximumOccupancy = 10,
            RoomQuantity = 10,
            Sort = 1,
            WindowType = WindowType.Have,
            ZHName = "高级大床房11"
        };
    }

    public List<Api.Model.HotelRoom> GetHotelRoom(long hotelId)
    {
        var list = new List<Api.Model.HotelRoom>();
        list.Add(new Api.Model.HotelRoom
        {
            AreaMax = 120,
            AreaMin = 12,
            BedType = "",
            CreateTime = DateTime.Now,
            ENName = "big room",
            FloorMax = 10,
            FloorMin = 1,
            HotelId = hotelId,
            MaximumOccupancy = 10,
            RoomQuantity = 10,
            Sort = 1,
            WindowType = WindowType.Have,
            ZHName = "高级大床房11"
        });
        list.Add(new Api.Model.HotelRoom
        {
            AreaMax = 120,
            AreaMin = 12,
            BedType = "",
            CreateTime = DateTime.Now,
            ENName = "big room",
            FloorMax = 10,
            FloorMin = 1,
            HotelId = hotelId,
            MaximumOccupancy = 10,
            RoomQuantity = 10,
            Sort = 1,
            WindowType = WindowType.Have,
            ZHName = "高级大床房22"
        });
        return list;
    }

    public UpdateHotelPhotosInput GetUpdateHotelPhotosInput(long hotelId, long roomId,
        long photosId)
    {
        var models = new UpdateHotelPhotosInput();
        models.Id = hotelId;
        models.Rooms = new List<Rooms>();

        var rooms = new Rooms();
        rooms.Id = roomId;
        rooms.Photos = new List<Photo>();

        //新增
        var photos = new Photo();
        photos.Id = photosId;
        photos.Url = "/1222/test.png";
        rooms.Photos.Add(photos);

        //修改
        var upphotos = new Photo();
        upphotos.Id = 0;
        upphotos.Url = "/1222/test.png";
        rooms.Photos.Add(upphotos);
        models.Rooms.Add(rooms);

        return models;
    }

    #endregion
}