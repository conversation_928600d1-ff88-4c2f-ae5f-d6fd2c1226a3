// <auto-generated />
using System;
using Hotel.Api.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20230602030957_AddLocationToApiHotel")]
    partial class AddLocationToApiHotel
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Hotel.Api.Model.ApiHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<Point>("Location")
                        .HasColumnType("point");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("StarLevel")
                        .HasColumnType("decimal(2,1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ResourceHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("ApiHotel", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ApiHotelHository", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AddApiHotelStatus")
                        .HasColumnType("tinyint");

                    b.Property<int>("AddedQuantity")
                        .HasColumnType("int");

                    b.Property<int?>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<int>("PageCount")
                        .HasColumnType("int");

                    b.Property<int>("PageIndex")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalAddQuantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ApiHotelHository", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ApiHotelSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AfterSaleStaffId")
                        .HasColumnType("bigint");

                    b.Property<int>("ChannelType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MarkupType")
                        .HasColumnType("tinyint");

                    b.Property<long>("PreSaleStaffId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(12,4)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ApiHotelSetting", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ChannelMarkup", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("ChannelType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("MarkupType")
                        .HasColumnType("tinyint");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(12,4)");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("PriceStrategyId");

                    b.HasIndex("TenantId");

                    b.HasIndex("PriceStrategyId", "ChannelType")
                        .IsUnique();

                    b.ToTable("ChannelMarkup", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ConfirmationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HeadPicture")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("SealPicture")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ConfirmationTemplate", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.Hotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CheckinPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DecorateDate")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ENName")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("Floors")
                        .HasColumnType("int");

                    b.Property<int>("HopId")
                        .HasColumnType("int");

                    b.Property<int>("HotelType")
                        .HasColumnType("int");

                    b.Property<string>("ImportantNotices")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Intro")
                        .HasColumnType("varchar(2000)");

                    b.Property<bool>("IsAutoConfirmRoomStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<DateTime>("OpeningDate")
                        .HasColumnType("datetime");

                    b.Property<int>("OperatingModel")
                        .HasColumnType("int");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<int>("Rooms")
                        .HasColumnType("int");

                    b.Property<bool>("ServiceEndtimeInNextDay")
                        .HasColumnType("tinyint(1)");

                    b.Property<TimeSpan>("ServiceTimeBegin")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("ServiceTimeEnd")
                        .HasColumnType("time");

                    b.Property<decimal>("StarLevel")
                        .HasColumnType("decimal(2,1)");

                    b.Property<string>("SurroundingFacilities")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Telefax")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Telephone")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Location");

                    b.HasIndex("ResourceHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("Hotel", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelFacilities", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("FacilityId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelFacilities", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("HotelRoomId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelPhotos", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelRedundantData", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("HasCommission")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("HotelRedundantData", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelRoom", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AreaMax")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("AreaMin")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("BedType")
                        .IsRequired()
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ENName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("FloorMax")
                        .HasColumnType("int");

                    b.Property<int>("FloorMin")
                        .HasColumnType("int");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<int>("MaximumOccupancy")
                        .HasColumnType("int");

                    b.Property<long>("ResourceRoomId")
                        .HasColumnType("bigint");

                    b.Property<int>("RoomQuantity")
                        .HasColumnType("int");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Viewable")
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<int>("WindowType")
                        .HasColumnType("int");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelRoom", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.OtaSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("OtaSetting", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.PriceStrategy", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("BookingHoursInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("EndRoomBookingDateBegin")
                        .HasColumnType("int");

                    b.Property<int>("EndRoomBookingDateEnd")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("EndRoomBookingTimeBegin")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("EndRoomBookingTimeEnd")
                        .HasColumnType("time");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsAutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("NumberOfBreakfast")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfNights")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRooms")
                        .HasColumnType("int");

                    b.Property<sbyte>("PriceStrategyType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("HotelRoomId");

                    b.HasIndex("TenantId");

                    b.ToTable("PriceStrategy", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.PriceStrategyCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("PriceStrategyId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "PriceStrategyId", "Date")
                        .IsUnique();

                    b.ToTable("PriceStrategyCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.PriceStrategyCancelRules", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("BeforeCheckInDays")
                        .HasColumnType("int");

                    b.Property<sbyte>("CancelChargeType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("CancelRulesType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ChargeValue")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("CheckInDateTime")
                        .HasColumnType("time");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("PriceStrategyId");

                    b.HasIndex("TenantId");

                    b.ToTable("PriceStrategyCancelRules", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.SupplySetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsSupply")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MarkupType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(12,4)");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TenantId");

                    b.ToTable("SupplySetting", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
