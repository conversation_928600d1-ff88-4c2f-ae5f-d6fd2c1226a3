using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    public partial class AddHotelCombination : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HotelCombination",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Description = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorId = table.Column<long>(type: "bigint", nullable: false),
                    CreatorName = table.Column<string>(type: "varchar(255)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterId = table.Column<long>(type: "bigint", nullable: false),
                    UpdaterName = table.Column<string>(type: "varchar(255)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelCombination", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HotelCombinationDestination",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    HotelCombinationId = table.Column<long>(type: "bigint", nullable: false),
                    CityCode = table.Column<int>(type: "int", nullable: false),
                    CityName = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelCombinationDestination", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HotelCombinationSetting",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    HotelCombinationId = table.Column<long>(type: "bigint", nullable: false),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    HotelName = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    HotelRoomId = table.Column<long>(type: "bigint", nullable: false),
                    HotelRoomName = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PriceStrategyId = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    IsDirect = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    Tag = table.Column<int>(type: "int", nullable: true),
                    PriceStrategyName = table.Column<string>(type: "varchar(200)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    Nights = table.Column<int>(type: "int", nullable: false),
                    NumberOfRoom = table.Column<int>(type: "int", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelCombinationSetting", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_HotelCombination_Name_TenantId",
                table: "HotelCombination",
                columns: new[] { "Name", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HotelCombination_TenantId",
                table: "HotelCombination",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_HotelCombinationDestination_TenantId",
                table: "HotelCombinationDestination",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_HotelCombinationSetting_TenantId",
                table: "HotelCombinationSetting",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_HotelCombinationSetting_TenantId_HotelCombinationId_PriceStr~",
                table: "HotelCombinationSetting",
                columns: new[] { "TenantId", "HotelCombinationId", "PriceStrategyId", "Nights", "NumberOfRoom" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HotelCombination");

            migrationBuilder.DropTable(
                name: "HotelCombinationDestination");

            migrationBuilder.DropTable(
                name: "HotelCombinationSetting");
        }
    }
}
