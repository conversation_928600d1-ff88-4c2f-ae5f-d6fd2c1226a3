// <auto-generated />
using System;
using Hotel.Api.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250711085804_Remove_ApiHotel_Tenant")]
    partial class Remove_ApiHotel_Tenant
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Hotel.Api.Model.ApiHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ENAddress")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ENName")
                        .HasColumnType("varchar(200)");

                    b.Property<Point>("Location")
                        .HasColumnType("point");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<int>("ReunionRoom")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("SaleFlag")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("StaffTag")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("StarLevel")
                        .HasColumnType("decimal(2,1)");

                    b.Property<int?>("Tags")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ResourceHotelId")
                        .IsUnique();

                    b.ToTable("ApiHotel", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ApiHotelExtend", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("ApiHotelSourceType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("ThirdHotelId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ResourceHotelId");

                    b.HasIndex("TenantId");

                    b.HasIndex("ThirdHotelId");

                    b.HasIndex("TenantId", "ResourceHotelId", "ApiHotelSourceType")
                        .IsUnique();

                    b.ToTable("ApiHotelExtend", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ApiHotelHository", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AddApiHotelStatus")
                        .HasColumnType("tinyint");

                    b.Property<int>("AddedQuantity")
                        .HasColumnType("int");

                    b.Property<int?>("Code")
                        .HasColumnType("int");

                    b.Property<sbyte?>("CodeType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<int>("PageCount")
                        .HasColumnType("int");

                    b.Property<int>("PageIndex")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalAddQuantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ApiHotelHository", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ApiHotelSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AfterSaleStaffId")
                        .HasColumnType("bigint");

                    b.Property<int>("ChannelType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MarkupType")
                        .HasColumnType("tinyint");

                    b.Property<long>("PreSaleStaffId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(12,4)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ApiHotelSetting", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ApiHotelTenantConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApiHotelId")
                        .HasColumnType("bigint");

                    b.Property<bool>("OnTop")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("WeightValue")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("ApiHotelId", "ResourceHotelId", "TenantId")
                        .IsUnique();

                    b.ToTable("ApiHotelTenantConfig", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ChannelMarkup", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("ChannelType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("MarkupType")
                        .HasColumnType("tinyint");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(12,4)");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("PriceStrategyId");

                    b.HasIndex("TenantId");

                    b.HasIndex("PriceStrategyId", "ChannelType")
                        .IsUnique();

                    b.ToTable("ChannelMarkup", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.ConfirmationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HeadPicture")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("SealPicture")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ConfirmationTemplate", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HopHotelNightlyPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOversell")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("PriceStrategyId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PriceStrategyId", "Date")
                        .IsUnique();

                    b.ToTable("HopHotelNightlyPrice", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.Hotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CheckinPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DecorateDate")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ENAddress")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ENCheckinPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ENIntro")
                        .HasColumnType("text");

                    b.Property<string>("ENName")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ENSurroundingFacilities")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("Floors")
                        .HasColumnType("int");

                    b.Property<int>("HopId")
                        .HasColumnType("int");

                    b.Property<int>("HotelType")
                        .HasColumnType("int");

                    b.Property<string>("ImportantNotices")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Intro")
                        .HasColumnType("text");

                    b.Property<bool>("IsAutoConfirmRoomStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("NonSmokingRoomsOrFloors")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("OpeningDate")
                        .HasColumnType("datetime");

                    b.Property<int>("OperatingModel")
                        .HasColumnType("int");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ReceiptEmail")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RoomVoltage")
                        .HasColumnType("int");

                    b.Property<int>("Rooms")
                        .HasColumnType("int");

                    b.Property<bool>("ServiceEndtimeInNextDay")
                        .HasColumnType("tinyint(1)");

                    b.Property<TimeSpan>("ServiceTimeBegin")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("ServiceTimeEnd")
                        .HasColumnType("time");

                    b.Property<decimal>("StarLevel")
                        .HasColumnType("decimal(2,1)");

                    b.Property<string>("SurroundingFacilities")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Telefax")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Telephone")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Location");

                    b.HasIndex("ResourceHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("Hotel", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelCombination", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("OtaProductId")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UpdaterId")
                        .HasColumnType("bigint");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelCombination", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelCombinationChannel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelCombinationId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("SellingChannel")
                        .HasColumnType("tinyint");

                    b.Property<long>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelCombinationChannel", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelCombinationDestination", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelCombinationId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelCombinationDestination", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelCombinationMarkupRule", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("BeginDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelCombinationChannelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("MarkupType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ValidWeeks")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelCombinationMarkupRule", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelCombinationSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelCombinationId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelENName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelRoomENName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelRoomZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("HotelZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDirect")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("MaxDays")
                        .HasColumnType("int");

                    b.Property<int>("MaxOccupancy")
                        .HasColumnType("int");

                    b.Property<int>("MinAdvHours")
                        .HasColumnType("int");

                    b.Property<int>("MinDays")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Nights")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRoom")
                        .HasColumnType("int");

                    b.Property<string>("PriceStrategyId")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PriceStrategyName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("Tag")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelCombinationSku", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelExtend", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("CanCheckInChildrenType")
                        .HasColumnType("int");

                    b.Property<TimeSpan?>("CheckInFrom")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("CheckInTo")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("CheckOutFrom")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("CheckOutTo")
                        .HasColumnType("time");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("DepositCurrency")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("DescribeInfo")
                        .HasColumnType("text");

                    b.Property<int>("ExistingBedMax")
                        .HasColumnType("int");

                    b.Property<int>("ExtraBedMax")
                        .HasColumnType("int");

                    b.Property<sbyte?>("FrequencyType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("HotelApplicabilityType")
                        .HasColumnType("tinyint");

                    b.Property<int?>("HotelExtraBedType")
                        .HasColumnType("int");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<bool?>("IsHasBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsHasChildrenExistingBed")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsHasDeposit")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("MaxAge")
                        .HasColumnType("int");

                    b.Property<int?>("MixAge")
                        .HasColumnType("int");

                    b.Property<int?>("PayTypes")
                        .HasColumnType("int");

                    b.Property<sbyte?>("RefundTimeType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("RefundType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelExtend", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelExtraBedPolicy", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("BedType")
                        .HasColumnType("tinyint");

                    b.Property<int?>("ChargeFrequency")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<int>("FeeType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("LimitType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Max")
                        .HasColumnType("int");

                    b.Property<int>("Min")
                        .HasColumnType("int");

                    b.Property<decimal?>("Percentage")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelExtraBedPolicy", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelFacilities", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("FacilityId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelFacilities", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelMapping", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorInfos")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ExtHotelId")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ExtHotelName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("LastUpdateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("MappingStatus")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelMapping", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelMealChildPolicy", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("LimitType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Max")
                        .HasColumnType("int");

                    b.Property<int>("Min")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelMealChildPolicy", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelMealPolicy", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BusinessHourInfos")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Currency")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Extend")
                        .HasColumnType("varchar(2000)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<int?>("ServeTypes")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int?>("Types")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelMealPolicy", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelOperationConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AllowSelfMaintainHotel")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("AllowSelfMaintainRoom")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OperationConfigType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOperationConfig", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("HotelPhotoType")
                        .HasColumnType("tinyint");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("PhotoName")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("HotelRoomId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelPhotos", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelPolicyExtend", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("ChildType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelPolicyExtend", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelPriceStrategyMapping", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorInfos")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ExtPriceStrategyId")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ExtPriceStrategyName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelRoomnId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("LastUpdateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("MappingStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelPriceStrategyMapping", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelRedundantData", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("HasCommission")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("HotelRedundantData", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelRoom", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AreaMax")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("AreaMin")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("BedType")
                        .IsRequired()
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(300)");

                    b.Property<string>("ENName")
                        .HasColumnType("varchar(100)");

                    b.Property<decimal?>("ExtraBedFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ExtraBedFeeCurrency")
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("ExtraBedType")
                        .HasColumnType("int");

                    b.Property<int>("FloorMax")
                        .HasColumnType("int");

                    b.Property<int>("FloorMin")
                        .HasColumnType("int");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<bool?>("IsHasChildren")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("IsHasChildrenExistingBed")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("MaximumOccupancy")
                        .HasColumnType("int");

                    b.Property<bool?>("NonSmoking")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ResourceRoomId")
                        .HasColumnType("bigint");

                    b.Property<int>("RoomQuantity")
                        .HasColumnType("int");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Viewable")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("WindowType")
                        .HasColumnType("int");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelRoom", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelRoomExtraBedPolicy", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("BedType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("LimitType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Max")
                        .HasColumnType("int");

                    b.Property<int>("Min")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("HotelRoomId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelRoomExtraBedPolicy", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelRoomMapping", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorInfos")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ExtRoomId")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ExtRoomName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("LastUpdateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("MappingStatus")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelRoomMapping", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.HotelTag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApiHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TagId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ApiHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelTag", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.OtaSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("OtaSetting", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.PriceStrategy", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("BookingHoursInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("EndRoomBookingDateBegin")
                        .HasColumnType("int");

                    b.Property<int>("EndRoomBookingDateEnd")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("EndRoomBookingTimeBegin")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("EndRoomBookingTimeEnd")
                        .HasColumnType("time");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsAutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("MaximumOccupancy")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("NumberOfBreakfast")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfNights")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRooms")
                        .HasColumnType("int");

                    b.Property<sbyte>("PriceStrategyType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("HotelRoomId");

                    b.HasIndex("TenantId");

                    b.ToTable("PriceStrategy", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.PriceStrategyCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("PriceStrategyId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "PriceStrategyId", "Date")
                        .IsUnique();

                    b.ToTable("PriceStrategyCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.PriceStrategyCancelRules", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("BeforeCheckInDays")
                        .HasColumnType("int");

                    b.Property<sbyte>("CancelChargeType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("CancelRulesType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ChargeValue")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("CheckInDateTime")
                        .HasColumnType("time");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("PriceStrategyId");

                    b.HasIndex("TenantId");

                    b.ToTable("PriceStrategyCancelRules", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.PriceStrategyNationality", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("PriceStrategyNationality", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.SpecializedHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SpecializedHotel", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.SpecializedHotelDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("EnHotelName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("SpecializedHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SpecializedHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("SpecializedHotelDetail", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.SpecializedHotelImportLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("FailCount")
                        .HasColumnType("int");

                    b.Property<string>("FileUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("ImportStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("OperationUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OperationUserName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ResultFileUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("SpecializedHotelId")
                        .HasColumnType("bigint");

                    b.Property<int>("SuccessCount")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SpecializedHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("SpecializedHotelImportLog", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.SupplySetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsSupply")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MarkupType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(12,4)");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TenantId");

                    b.ToTable("SupplySetting", (string)null);
                });

            modelBuilder.Entity("Hotel.Api.Model.Tag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("EnName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("ShowPageTypes")
                        .HasColumnType("int");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name")
                        .IsUnique();

                    b.ToTable("Tag", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
