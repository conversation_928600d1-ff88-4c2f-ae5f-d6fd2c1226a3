using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    public partial class UpdateHotelMealPolicyV3 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ExtraBedFee",
                table: "HotelRoom",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ExtraBedType",
                table: "HotelRoom",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsHasChildren",
                table: "HotelRoom",
                type: "tinyint(1)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExtraBedFee",
                table: "HotelRoom");

            migrationBuilder.DropColumn(
                name: "ExtraBedType",
                table: "HotelRoom");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON>hildren",
                table: "HotelRoom");
        }
    }
}
