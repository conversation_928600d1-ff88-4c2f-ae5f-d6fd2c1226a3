using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    public partial class AddENNameToApiHotel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ENN<PERSON>",
                table: "ApiHotel",
                type: "varchar(200)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ENName",
                table: "ApiHotel");
        }
    }
}
