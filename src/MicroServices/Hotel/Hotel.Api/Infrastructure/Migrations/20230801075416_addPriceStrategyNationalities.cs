using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    public partial class addPriceStrategyNationalities : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PriceStrategyNationality",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    PriceStrategyId = table.Column<long>(type: "bigint", nullable: false),
                    CountryCode = table.Column<int>(type: "int", nullable: false),
                    ZHName = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PriceStrategyNationality", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_PriceStrategyNationality_TenantId",
                table: "PriceStrategyNationality",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PriceStrategyNationality");
        }
    }
}
