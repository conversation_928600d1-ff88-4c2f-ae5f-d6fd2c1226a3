using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hotel.Api.Infrastructure.EntityConfigurations;

public class SpecializedHotelEntityTypeConfiguration : TenantBaseConfiguration<Model.SpecializedHotel>, IEntityTypeConfiguration<Model.SpecializedHotel>
{
    public void Configure(EntityTypeBuilder<Model.SpecializedHotel> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.Name)
            .HasColumnType("varchar(50)");
        ;

        builder.Property(s => s.Enable)
           .HasColumnType("tinyint(1)");

        builder.Property(s => s.Sort)
           .HasColumnType("int");

        builder.Property(s => s.CreateTime)
             .HasColumnType("datetime");

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");
    }
}
