using CanalSharp.Protocol;
using Common.ServicesHttpClient;
using Contracts.Common.Resource.Enums;
using Hotel.Api.EsDocument;
using Hotel.Api.HostedService.CanalMonitorMappedFiles;
using Hotel.Api.Services.Interfaces;
using MediatR;
using Microsoft.Extensions.Options;
using Nest;
using Contracts.Common.Resource.DTOs;
using Hotel.Api.Extensions;
using Newtonsoft.Json;

namespace Hotel.Api.Notification.Handler;

/// <summary>
/// 租户聚合资源同步es通知处理器
/// <value>商圈,景点,线路,酒店,用车
/// <br/>
/// 索引: aggregate-resource-tenant-*
/// </value>
/// </summary>
public class EsAggregateTenantResourceSyncNotifyHandler : INotificationHandler<EsSyncNotification>
{
    private readonly ILogger<EsAggregateTenantResourceSyncNotifyHandler> _logger;
    private readonly IElasticClient _elasticClient;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IEsCreateIndexService _esCreateIndexService;
    private readonly ServicesAddress _servicesAddress;

    private readonly string[] _tableNames = { "TradingArea","ScenicSpot","LineProduct","CarProduct"};
    private const string _indexNamePrefix = "aggregate-resource-tenant-";
    private static readonly Dictionary<string, EsAggregateResourceParentType> _tableNameToTypeMap =
        new(StringComparer.OrdinalIgnoreCase)
        {
            {"TradingArea", EsAggregateResourceParentType.TradingArea},
            {"ScenicSpot", EsAggregateResourceParentType.ScenicSpot},
            {"LineProduct", EsAggregateResourceParentType.TravelLine},
            {"CarProduct", EsAggregateResourceParentType.Car}
        };
    
    public EsAggregateTenantResourceSyncNotifyHandler(
        ILogger<EsAggregateTenantResourceSyncNotifyHandler> logger,
        IElasticClient elasticClient,
        IHttpClientFactory httpClientFactory,
        IEsCreateIndexService esCreateIndexService,
        IOptions<ServicesAddress> servicesAddress)
    {
        _logger = logger;
        _elasticClient = elasticClient;
        _httpClientFactory = httpClientFactory;
        _esCreateIndexService = esCreateIndexService;
        _servicesAddress = servicesAddress.Value;
    }
    
    public async Task Handle(EsSyncNotification notification, CancellationToken cancellationToken)
    {
        try
        {
            // 判断表名
            if(!_tableNames.Contains(notification.TableName, StringComparer.OrdinalIgnoreCase))
                return;
            
            var indexName = $"{_indexNamePrefix}{notification.TenantId}";
            
            //初始化索引
            await IndicesInit(indexName);
            
            //执行批量操作
            await PerformBulkCrudOperations(notification, indexName);
            
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,"[Elasticsearch]租户资源聚合同步es通知处理:{@Message}", notification);
        }
    }
    
    /// <summary>
    /// 索引初始化
    /// <value>不做数据初始化，只创建索引</value>
    /// </summary>
    private async Task IndicesInit(string indexName)
    {
        var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(indexName);
        if (indexExistsResponse.Exists is false)
        {
            var createResponse = await _esCreateIndexService.CreateAggregateResourceIndex(indexName);
        }
    }

    /// <summary>
    /// 执行批量操作
    /// </summary>
    /// <param name="notification"></param>
    private async Task PerformBulkCrudOperations(EsSyncNotification notification, string indexName)
    {
        if (_tableNameToTypeMap.TryGetValue(notification.TableName, out var parentType))
        {
            var bulkDescriptor = new BulkDescriptor();
            
            //获取城市编码
            var cityCodeMap = new List<int>();
            switch (parentType)
            {
                case EsAggregateResourceParentType.TradingArea:
                    cityCodeMap = notification.Data.Select(x => x.Document as CanalMonitorMappedTradingAreaFile)
                        .Select(x => x.CityCode)
                        .Distinct()
                        .ToList();
                    break;
                case EsAggregateResourceParentType.ScenicSpot:
                    cityCodeMap = notification.Data.Select(x => x.Document as CanalMonitorMappedScenicSpotFile)
                        .Select(x => x.CityCode)
                        .Distinct()
                        .ToList();
                    break;
                case EsAggregateResourceParentType.TravelLine:
                    cityCodeMap = notification.Data.Select(x => x.Document as CanalMonitorMappedTravelLineFile)
                        .Select(x => x.DestinationCityId)
                        .Distinct()
                        .ToList();
                    break;
                case EsAggregateResourceParentType.Car:
                    cityCodeMap = notification.Data.Select(x => x.Document as CanalMonitorMappedCarProductFile)
                        .Select(x => x.CityCode)
                        .Distinct()
                        .ToList();
                    break;
            }

            var queryCityRequest = new QueryInput
            {
                CityCodes = cityCodeMap.ToArray()
            };
            
            //查询城市数据
            var cityData = (await QueryCity(queryCityRequest)).ToList();
            
            foreach (var item in notification.Data)
            {
                //创建索引文档
                var documentData = new AggregateResourceDocument
                {
                    ResourceParentType = parentType
                };

                // 产品是否上架
                var isEnabled = true;
                
                //不同产品类型数赋值
                switch (parentType)
                {
                    case EsAggregateResourceParentType.TradingArea:
                        var tradingAreaSyncData = item.Document as CanalMonitorMappedTradingAreaFile;
                        
                        // 商圈不同步景点类型的数据
                        if(tradingAreaSyncData.TradingAreaType == TradingAreaType.ScenicSpot)
                            continue;
                        
                        documentData.RelatedId = tradingAreaSyncData.Id;
                        documentData.EsKeyId = CreateEsKeyId(documentData.RelatedId, parentType);
                        documentData.ResourceSubType = tradingAreaSyncData.TradingAreaType switch
                        {
                            TradingAreaType.District => EsAggregateResourceSubType.District,
                            TradingAreaType.BusinessQuarter => EsAggregateResourceSubType.BusinessQuarter,
                            TradingAreaType.SubwayLine => EsAggregateResourceSubType.SubwayLine,
                            TradingAreaType.Station => EsAggregateResourceSubType.Station,
                            TradingAreaType.Hospital => EsAggregateResourceSubType.Hospital,
                            TradingAreaType.University => EsAggregateResourceSubType.University,
                            TradingAreaType.PerformanceVenue => EsAggregateResourceSubType.PerformanceVenue,
                            _ => EsAggregateResourceSubType.None
                        };
                        documentData.ResourceZhName = tradingAreaSyncData.Name;
                        documentData.CityCode = tradingAreaSyncData.CityCode;
                        documentData.Location = tradingAreaSyncData.Location;
                        documentData.AddressZhName = tradingAreaSyncData.Address;
                        
                        break;
                    case EsAggregateResourceParentType.ScenicSpot:
                        var scenicSpotSyncData = item.Document as CanalMonitorMappedScenicSpotFile;
                        
                        //判断是否上架
                        isEnabled = scenicSpotSyncData.Enabled;
                        
                        documentData.RelatedId = scenicSpotSyncData.Id;
                        documentData.EsKeyId = CreateEsKeyId(documentData.RelatedId, parentType);
                        documentData.ResourceSubType = EsAggregateResourceSubType.ScenicSpot;
                        documentData.ResourceZhName = scenicSpotSyncData.Name;
                        documentData.ResourceEnName = scenicSpotSyncData.ENName;
                        documentData.CityCode = scenicSpotSyncData.CityCode;
                        documentData.CityZhName = scenicSpotSyncData.CityName;
                        documentData.ProvinceCode = scenicSpotSyncData.ProvinceCode;
                        documentData.ProvinceZhName = scenicSpotSyncData.ProvinceName;
                        documentData.CountryCode = scenicSpotSyncData.CountryCode;
                        documentData.CountryZhName = scenicSpotSyncData.CountryName;
                        documentData.Location = scenicSpotSyncData.Location;
                        documentData.AddressZhName = scenicSpotSyncData.Address;
                        
                        break;
                    case EsAggregateResourceParentType.TravelLine:
                        var lineProductSyncData = item.Document as CanalMonitorMappedTravelLineFile;
                        
                        //判断是否上架.线路存在删除操作.
                        //未删除且已上架
                        isEnabled = lineProductSyncData.Enabled && !lineProductSyncData.IsDeleted;
                        
                        documentData.RelatedId = lineProductSyncData.Id;
                        documentData.EsKeyId = CreateEsKeyId(documentData.RelatedId, parentType);
                        documentData.ResourceSubType = EsAggregateResourceSubType.TravelLine;
                        documentData.ResourceZhName = lineProductSyncData.Title;
                        documentData.CityCode = lineProductSyncData.DestinationCityId;
                        documentData.CityZhName = lineProductSyncData.DestinationCityName;
                        documentData.CountryCode = lineProductSyncData.DestinationCountryId;
                        documentData.CountryZhName = lineProductSyncData.DestinationCountryName;
                        
                        break;
                    case EsAggregateResourceParentType.Car:
                        var carProductSyncData = item.Document as CanalMonitorMappedCarProductFile;
                        
                        //判断是否上架
                        isEnabled = carProductSyncData.Enabled;
                        
                        documentData.RelatedId = carProductSyncData.Id;
                        documentData.EsKeyId = CreateEsKeyId(documentData.RelatedId, parentType);
                        documentData.ResourceSubType = EsAggregateResourceSubType.Car;
                        documentData.ResourceZhName = carProductSyncData.Title;
                        documentData.CityCode = carProductSyncData.CityCode;
                        documentData.CityZhName = carProductSyncData.CityName;
                        documentData.CountryCode = carProductSyncData.CountryCode;
                        documentData.CountryZhName = carProductSyncData.CountryName;
                        
                        break;
                }
                
                // 已上架的产品数据补充
                if (isEnabled)
                {
                    var city = cityData.FirstOrDefault(c => c.CityCode == documentData.CityCode);
                    if (city != null)
                    {
                        documentData.CityCode = city.CityCode;
                        documentData.CityZhName = city.ZHName;
                        documentData.CityEnName = city.ENName;
                        documentData.ProvinceCode = city.ProvinceCode;
                        documentData.ProvinceZhName = city.ProvinceName;
                        documentData.ProvinceEnName = city.ProvinceEnName;
                        documentData.CountryCode = city.CountryCode;
                        documentData.CountryZhName = city.CountryName;
                        documentData.CountryEnName = city.CountryEnName;
                    }
                }
                
                //操作事件类型,只同步已上架的产品
                switch (item.EventType)
                {
                    //插入事件且产品上架
                    case EventType.Insert when isEnabled :
                        bulkDescriptor.Index<AggregateResourceDocument>(i=>i
                            .Index(indexName)
                            .Id(documentData.EsKeyId)
                            .Document(documentData));
                        break;
                    
                    //更新事件且产品上架
                    case EventType.Update when isEnabled :
                        bulkDescriptor.Update<AggregateResourceDocument>(i=>i
                            .Index(indexName)
                            .Id(documentData.EsKeyId)
                            .Doc(documentData)
                            .Upsert(documentData));
                        break;
                    
                    //删除事件 或者 更新事件且产品下架
                    case EventType.Update when !isEnabled:
                    case EventType.Delete:
                        bulkDescriptor.Delete<AggregateResourceDocument>(i=>i
                            .Index(indexName)
                            .Id(documentData.EsKeyId));
                        break;
                }
            }
            var response = await _elasticClient.BulkAsync(bulkDescriptor);
        }
    }

    private async Task<IEnumerable<CityOutput>> QueryCity(QueryInput input)
    {
        var cityUri = _servicesAddress.Resource_City_Query();
        var httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<IEnumerable<CityOutput>>(
            cityUri,
            httpContent: httpContent);
        return response;
    }
    
    private string CreateEsKeyId(long relateId, EsAggregateResourceParentType parentType)
    {
        return $"{parentType}_{relateId}";
    }
}