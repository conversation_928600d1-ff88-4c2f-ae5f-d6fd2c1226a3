using Cit.Storage.Redis;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Hotel.Api.Services.Interfaces;

namespace Hotel.Api.HostedService;

public class ApiHotelWeightHostedService : BackgroundService
{
    public readonly static string WeightValueListKey = "hotel:apihotel:weightvaluelist";
    private readonly static string _lockKey = "hotel:apihotel:weightvalue";
    private readonly TimeSpan _delayTimeSpan = TimeSpan.FromMinutes(1);
    private readonly long _limitCount = 1000;
    private readonly IRedisClient _redisClient;
    private readonly ILogger<ApiHotelWeightHostedService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public ApiHotelWeightHostedService(IRedisClient redisClient,
        ILogger<ApiHotelWeightHostedService> logger,
        IServiceProvider serviceProvider)
    {
        _redisClient = redisClient;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var count = await _redisClient.ListLengthAsync(WeightValueListKey);

            if (count > _limitCount) count = _limitCount;

            await PopWeightValues(count);

            await Task.Delay(_delayTimeSpan, stoppingToken);
        }
    }

    private async Task PopWeightValues(long count)
    {
        var lockSceret = Guid.NewGuid().ToString();
        try
        {
            await _redisClient.LockTakeWaitingAsync(_lockKey, lockSceret, _delayTimeSpan);

            var length = await _redisClient.ListLengthAsync(WeightValueListKey);
            if (length == 0) return;

            var weightValueInputs = await _redisClient.ListRightPopAsync<PushWeightValueInput>(WeightValueListKey, count);

            if (!weightValueInputs.Any())
                return;

            var inputs = weightValueInputs
                .GroupBy(x => new {x.TenantId,x.HotelId})
                .Select(x => new HotelWeightValueInput
                {
                    HotelId = x.Key.HotelId,
                    TenantId = x.Key.TenantId,
                    Value = x.Sum(s => s.Value)
                });
            using var scope = _serviceProvider.CreateScope();
            var apiHotelService = scope.ServiceProvider.GetService<IApiHotelService>();
            await apiHotelService.SetWeightValuesV2(inputs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,"设置API酒店权重异常");
        }
        finally
        {
            await _redisClient.LockReleaseAsync(_lockKey, lockSceret);
        }
    }

}
