using Contracts.Common.Hotel.DTOs.ConfirmationTemplate;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Hotel.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class ConfirmationTemplateController : ControllerBase
    {
        private readonly IConfirmationTemplateService _confirmationTemplateService;

        public ConfirmationTemplateController(IConfirmationTemplateService confirmationTemplateService)
        {
            _confirmationTemplateService = confirmationTemplateService;
        }

        [HttpPost]
        public async Task<IActionResult> Add(ConfirmationTemplateDto input)
        {
            await _confirmationTemplateService.Add(input);
            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Update(ConfirmationTemplateDto input)
        {
            await _confirmationTemplateService.Update(input);
            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Delete(long id)
        {
            await _confirmationTemplateService.Delete(id);
            return Ok();
        }

        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<ConfirmationTemplateDto>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetAll()
        {
            var result = await _confirmationTemplateService.GetAll();
            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> SendConfirmationOrder([FromForm] SendConfirmationOrderInput input)
        {
            await _confirmationTemplateService.SendConfirmationOrder(input);
            return Ok();
        }
    }
}
