using Contracts.Common.Hotel.Enums;
using EfCoreExtensions.EntityBase;

namespace Hotel.Api.Model;

public class ApiHotelHository : TenantBase
{
    /// <summary>
    /// 操作人ID
    /// </summary>
    public long OperatorId { get; set; }

    /// <summary>
    /// 总添加数量
    /// </summary>
    public int TotalAddQuantity { get; set; }

    /// <summary>
    /// 已添加数量
    /// </summary>
    public int AddedQuantity { get; set; }

    /// <summary>
    /// 查询文本
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public int? Code { get; set; }

    /// <summary>
    /// 当前页
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int PageCount { get; set; }

    /// <summary>
    /// 添加第三方酒店状态
    /// </summary>
    public AddApiHotelStatus AddApiHotelStatus { get; set; }

    /// <summary>
    /// 编码类型1-国家 2-省份 3-城市
    /// </summary>
    public ThirdHotelSearchCodeType? CodeType { get; set; }
}
