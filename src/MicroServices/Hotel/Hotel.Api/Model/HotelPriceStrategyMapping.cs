using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Hotel.Api.Model;

public class HotelPriceStrategyMapping : TenantBase
{
    public SellingPlatform SellingPlatform { get; set; }

    public long HotelId { get; set; }

    public long HotelRoomnId { get; set; }

    public long PriceStrategyId { get; set; }

    public string? ExtPriceStrategyId { get; set; }

    public string? ExtPriceStrategyName { get; set; }

    public MappingStatus MappingStatus { get; set; }

    public string? ErrorInfos { get; set; }

    public DateTime LastUpdateTime { get; set; } = DateTime.Now;

    public DateTime CreateTime { get; set; }
}

