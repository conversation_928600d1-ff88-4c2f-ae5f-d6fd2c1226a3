using Contracts.Common.Hotel.Enums;
using EfCoreExtensions.EntityBase;

namespace Hotel.Api.Model;

public class HotelCombinationMarkupRule : TenantBase
{
    /// <summary>
    /// 打包渠道id
    /// </summary>
    public long HotelCombinationChannelId { get; set; }

    public DateTime BeginDate { get; set; }

    public DateTime EndDate { get; set; }

    /// <summary>
    /// 周集合 0-6:周日-周一
    /// </summary>
    public string ValidWeeks { get; set; }

    /// <summary>
    /// 加价基础类型
    /// </summary>
    public MarkupPriceType PriceType { get; set; }

    /// <summary>
    /// 加价方式
    /// </summary>
    public MarkupType MarkupType { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public decimal Value { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}
