using CanalSharp.Protocol;
using Hotel.Api.EsDocument;
using Hotel.Api.Services.Interfaces;
using Nest;

namespace Hotel.Api.Services.EsSyncProcessing;

/// <summary>
/// 高定酒店索引数据同步处理服务
/// </summary>
public class GdsHotelSyncProcessingService :IEsSyncProcessingService
{
    private readonly ILogger<GdsHotelSyncProcessingService> _logger;
    private readonly IElasticClient _elasticClient;
    private readonly IEsCreateIndexService _esCreateIndexService;
    private readonly IBaseHotelService _baseHotelService;
    
    private const string _gdsHotelIndexName = "hotel-gds-resource";
    
    public GdsHotelSyncProcessingService(
        ILogger<GdsHotelSyncProcessingService> logger,
        IElasticClient esElasticClient,
        IEsCreateIndexService esCreateIndexService,
        IBaseHotelService baseHotelService)
    {
        _logger = logger;
        _elasticClient = esElasticClient;
        _esCreateIndexService = esCreateIndexService;
        _baseHotelService = baseHotelService;
    }

    public string SchemaName => "Resource";
    public string TableName => "GdsHotel";
    public object Document => new GdsHotelDocument();
    
    public async Task Sync(List<(object document, long tenantId, EventType eventType)> input)
    {
        try
        {
            await IndicesInit();
            await PerformBulkCrudOperations(input);
        }
        catch (Exception e)
        {
            _logger.LogError("GdsHotelSyncProcessing Error,Message:{@Message}", e.Message);
        }
    }

    /// <summary>
    /// 索引初始化
    /// </summary>
    private async Task IndicesInit()
    {
        var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_gdsHotelIndexName);
        if (indexExistsResponse.Exists is false)
        {
            var createResponse = await _esCreateIndexService.CreateGdsHotelIndex(_gdsHotelIndexName);
        }
    }

    /// <summary>
    /// 执行批量操作
    /// </summary>
    /// <param name="syncData"></param>
    private async Task PerformBulkCrudOperations(List<(object document, long tenantId, EventType eventType)> syncData)
    {
        var bulkDescriptor = new BulkDescriptor();
        var nonDeleteSyncDocuments = syncData
            .Where(x=>x.eventType != EventType.Delete)
            .Select(x => x.document as GdsHotelDocument)
            .ToList();
        
        var gdsHotelIds = nonDeleteSyncDocuments.Select(x => x.Id).Distinct().ToList();
        var facilities = await FacilitySupplement(gdsHotelIds);//补充基础设施id数据
        
        foreach (var syncItem in syncData)
        {
            var syncDocument = syncItem.document as GdsHotelDocument;

            // 补充基础设施id数据
            syncDocument.FacilityIds =
                facilities.Where(x => x.hotelId == syncDocument.Id)
                    .Select(x => x.facilityId)
                    .Distinct()
                    .ToList();
            
            // temporary 记录置顶更新时间
            syncDocument.OnTopTime = syncDocument.OnTop ? syncDocument.UpdateTime : null;
            
            switch (syncItem.eventType)
            {
                case EventType.Insert:

                    bulkDescriptor.Index<GdsHotelDocument>(i=>i
                        .Index(_gdsHotelIndexName)
                        .Id(syncDocument.Id)
                        .Document(syncDocument));
                    
                    break;
                case EventType.Update:

                    bulkDescriptor.Update<GdsHotelDocument>(i => i
                        .Index(_gdsHotelIndexName)
                        .Id(syncDocument.Id)
                        .Doc(syncDocument)
                        .Upsert(syncDocument));
                    
                    break;
                case EventType.Delete:
                    
                    bulkDescriptor.Delete<GdsHotelDocument>(i=>i
                        .Index(_gdsHotelIndexName)
                        .Id(syncDocument.Id));
                    
                    break;
            }
            
        }
        
        var response = await _elasticClient.BulkAsync(bulkDescriptor);
    }
    
    /// <summary>
    /// 补充数据 关联GDS酒店的基础设施数据
    /// </summary>
    /// <param name="gdsHotelIds"></param>
    /// <returns></returns>
    private async Task<List<(long hotelId, long facilityId)>> FacilitySupplement(List<long> gdsHotelIds)
    {
        var result = new List<(long, long)>();
        if(gdsHotelIds.Any() is false) return result;
        try
        {
            var gdsHotelFacilities = await _baseHotelService.GetGDSFacilities(gdsHotelIds.ToArray());
            result = gdsHotelFacilities
                .Select(x => new ValueTuple<long, long>(x.HotelId, x.FacilityId))
                .ToList();
        }
        catch (Exception e)
        {
            return result;
        }
        return result;
    }
}