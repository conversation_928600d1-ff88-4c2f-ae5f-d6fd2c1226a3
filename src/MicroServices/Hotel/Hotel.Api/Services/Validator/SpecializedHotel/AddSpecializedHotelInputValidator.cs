using Contracts.Common.Hotel.DTOs.SpecializedHotel;
using FluentValidation;

namespace Hotel.Api.Services.Validator.SpecializedHotel;

public class AddSpecializedHotelInputValidator : AbstractValidator<AddSpecializedHotelInput>
{
    public AddSpecializedHotelInputValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("专题名称不能为空")
            .MaximumLength(10).WithMessage("专题名称不能超过10个字符");

    }

}
