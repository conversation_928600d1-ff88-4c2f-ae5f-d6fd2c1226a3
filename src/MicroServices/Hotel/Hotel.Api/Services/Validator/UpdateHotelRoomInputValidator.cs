using Contracts.Common.Hotel.DTOs.Hotel;
using FluentValidation;

namespace Hotel.Api.Services.Validator;

public class UpdateHotelRoomInputValidator : AbstractValidator<UpdateHotelRoomInput>
{
    public UpdateHotelRoomInputValidator()
    {
        RuleFor(x => x.ZHName).NotEmpty();

        RuleFor(s => s.BedType)
            .Must((t, s) =>
            {
                if (t.BedType == null || t.BedType.Count() < 1)
                    return false;
                return true;
            })
            .WithMessage("床型不能为空");

        RuleFor(s => s.BedType)
            .Must((t, s) =>
            {
                if (t.BedType != null && t.BedType.Count() > 2)
                    return false;
                return true;
            })
            .WithMessage("床型不能超过3个");

        RuleFor(s => s.BedType)
            .Must((t, s) =>
            {
                if (t.BedType != null && t.BedType.Count() == 2
                                      && t.BedType.Where(x => x.main == "多张床").Any())
                    return false;
                return true;
            })
            .WithMessage("(大床、双床、单人）与多张床只能任选其1");

        RuleFor(r => r.Description)
            .Length(1, 300)
            .When(r => !string.IsNullOrEmpty(r.Description))
            .WithMessage("房型说明不能超过300个字符");

    }
}