using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using FluentValidation;

namespace Hotel.Api.Services.Validator.PriceStrategyCalendarPrice
{
    public class GetByHotelInputValidator : AbstractValidator<GetByHotelInput>
    {
        public GetByHotelInputValidator() 
        {
            RuleFor(x => x.HotelId).NotNull().GreaterThan(0);
            RuleFor(x => x.SalesChannel).NotNull().IsInEnum();
            RuleFor(x => x.BeginDate).NotNull().GreaterThanOrEqualTo(DateTime.Today);
            RuleFor(x => x.EndDate).NotNull().GreaterThan(x => x.BeginDate);
        }
    }
}
