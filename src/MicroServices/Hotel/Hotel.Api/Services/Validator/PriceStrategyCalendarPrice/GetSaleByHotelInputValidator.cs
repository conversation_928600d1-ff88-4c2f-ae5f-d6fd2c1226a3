using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using FluentValidation;

namespace Hotel.Api.Services.Validator.PriceStrategyCalendarPrice
{
    public class GetSaleByHotelInputValidator : AbstractValidator<GetSaleByHotelInput>
    {
        public GetSaleByHotelInputValidator() 
        {
            RuleFor(x => x.HotelId).NotNull().GreaterThan(0);
            RuleFor(x => x.BeginDate).NotNull().GreaterThanOrEqualTo(DateTime.Today);
            RuleFor(x => x.EndDate).NotNull().GreaterThan(x => x.BeginDate);
        }
    }
}
