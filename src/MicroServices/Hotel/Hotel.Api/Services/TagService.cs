using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Hotel.DTOs.Tag;
using EfCoreExtensions.Extensions;
using Hotel.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Hotel.Api.Services;

public class TagService : ITagService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;

    public TagService(
        CustomDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    /// <summary>
    /// 查询分页数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<SearchTagOutput>> Search(SearchTagInput input)
    {
        var query = _dbContext.Tags.AsNoTracking()
            .WhereIF(!string.IsNullOrEmpty(input.Name), x => x.Name.Contains(input.Name))
            .WhereIF(input.Ids is not null && input.Ids.Any(), x => input.Ids.Contains(x.Id))
            .OrderBy(x => x.Sort).ThenBy(x => x.CreateTime)
            .Select(x => x);

        if (input.ShowPageTypes.Any())
        {
            var showPageTypes = input.ShowPageTypes.Aggregate((current, next) => current | next);
            query = query.Where(x => (showPageTypes & x.ShowPageTypes) != 0);
        }

        var tags = await query.ToListAsync();
        var result = _mapper.Map<List<SearchTagOutput>>(tags);
        return result;
    }

    /// <summary>
    /// 新增标签
    /// </summary>
    /// <param name="input"></param>
    public async Task Add(AddTagInput input)
    {
        var entity = _mapper.Map<Tag>(input);
        if (await _dbContext.Tags.AsNoTracking().AnyAsync(x => x.Name.Equals(input.Name)))
            throw new BusinessException(ErrorTypes.Hotel.TagIsExists);

        await _dbContext.AddAsync(entity);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 编辑标签
    /// </summary>
    /// <param name="input"></param>
    public async Task Edit(EditTagInput input)
    {
        var tag = await _dbContext.Tags.FirstOrDefaultAsync(x => x.Id.Equals(input.Id));
        if (tag is null) return;

        var isExists = await _dbContext.Tags
            .AnyAsync(x => !x.Id.Equals(input.Id) && x.Name.Equals(input.Name));
        if (isExists) throw new BusinessException(ErrorTypes.Hotel.TagIsExists);
        _mapper.Map(input, tag);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 删除标签
    /// </summary>
    /// <param name="id"></param>
    public async Task Delete(long id)
    {
        var entity = await _dbContext.Tags.FirstOrDefaultAsync(x => x.Id == id);
        if (entity is null) return;
        _dbContext.Remove(entity);

        var hotelTags = await _dbContext.HotelTags
            .Where(x => x.TagId == id).ToListAsync();
        _dbContext.RemoveRange(hotelTags);

        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 获取租户下的标签列表
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<GetTagsOutput>> GetAll()
    {
        var queryable = _dbContext.Tags.AsNoTracking()
            .OrderBy(x => x.Sort)
            .Select(x => new GetTagsOutput
            {
                Id = x.Id,
                Name = x.Name
            });

        return await queryable.ToListAsync();
    }

    public async Task UpdateSort(UpdateSortInput input)
    {
        var dbRooms = await _dbContext.Tags
            .Where(a => input.Ids.Contains(a.Id))
            .ToListAsync();

        for (int i = 0; i < input.Ids.Count; i++)
        {
            var model = dbRooms.FirstOrDefault(a => a.Id == input.Ids[i]);
            if (model != null && model.Sort != i)
            {
                model.Sort = i;
            }
        }

        await _dbContext.SaveChangesAsync();
    }
}
