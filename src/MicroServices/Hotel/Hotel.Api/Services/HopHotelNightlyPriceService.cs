using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.HopHotel;
using Contracts.Common.Hotel.DTOs.HotelCombination;
using Contracts.Common.Resource.DTOs.SelfsupportHotel;
using DotNetCore.CAP;
using EfCoreExtensions.UOW;
using Hotel.Api.Extensions;
using Hotel.Api.Requests;
using Hotel.Api.Services.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NightlyPriceDto = Contracts.Common.Hotel.DTOs.HotelCombination.NightlyPriceDto;

namespace Hotel.Api.Services;

public class HopHotelNightlyPriceService : IHopHotelNightlyPriceService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICapPublisher _capPublisher;
    private readonly IMediator _mediator;
    private readonly ServicesAddress _servicesAddress;

    public HopHotelNightlyPriceService(CustomDbContext dbContext,
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory,
        ICapPublisher capPublisher,
        IMediator mediator)
    {
        _dbContext = dbContext;
        _httpClientFactory = httpClientFactory;
        _capPublisher = capPublisher;
        _mediator = mediator;
        _servicesAddress = servicesAddress.Value;
    }

    public async Task<List<NightlyPriceDto>> GetNightlyPrices(GetNightlyPricesInput input)
    {
        var prices = await _dbContext.HopHotelNightlyPrices
           .Where(x => x.PriceStrategyId == input.PriceStrategyId)
           .Where(x => x.Date >= input.BeginDate && x.Date <= input.EndDate)
           .OrderBy(x => x.Date)
           .Select(x => new NightlyPriceDto
           {
               Date = x.Date,
               CostPrice = x.CostPrice,
               CostCurrencyCode = x.CostCurrencyCode,
               Quantity = x.Quantity,
               Enabled = x.Enabled,
               IsOversell = x.IsOversell
           })
           .ToListAsync();
        return prices;
    }

    public async Task SetNightlyPrices(SetNightlyPricesInput input)
    {
        var begin = input.Nightlies.Min(x => x.Date);
        var end = input.Nightlies.Max(x => x.Date);
        var prices = await _dbContext.HopHotelNightlyPrices
            .Where(x => x.PriceStrategyId == input.PriceStrategyId)
            .Where(x => x.Date >= begin && x.Date <= end)
            .ToListAsync();
        var nightlyPrices = new List<HopHotelNightlyPrice>();
        foreach (var nightly in input.Nightlies)
        {
            var price = prices.FirstOrDefault(x => x.Date == nightly.Date);
            if (price != null)
            {
                price.CostPrice = nightly.CostPrice;
                price.CostCurrencyCode = nightly.CostCurrencyCode;
                price.Quantity = nightly.Quantity;
                price.IsOversell = nightly.IsOversell;
                price.Enabled = nightly.Enabled;
            }
            else
            {
                nightlyPrices.Add(new HopHotelNightlyPrice
                {
                    Date = nightly.Date,
                    Enabled = nightly.Enabled,
                    IsOversell = nightly.IsOversell,
                    PriceStrategyId = input.PriceStrategyId,
                    CostCurrencyCode = nightly.CostCurrencyCode,
                    CostPrice = nightly.CostPrice,
                    Quantity = nightly.Quantity
                });
            }
        }
        if (nightlyPrices.Any())
            await _dbContext.AddRangeAsync(nightlyPrices);
        await _dbContext.SaveChangesAsync();

        if (input.IsModify)
        {
            await _mediator.Send(new HotelCombinationSyncChannelPriceStockRequest
            {
                PriceStrategyId = input.PriceStrategyId,
                BeginDate = begin,
                EndDate = end
            });
        }
    }

    public async Task PriceStrategyPushBind(PriceStrategyPushBindInput input)
    {
        PriceStrategiesBindInput priceStrategiesBindInput = new()
        {
            PriceStrategyIds = input.PriceStrategyIds,
            Type = input.Type,
        };
        using var httpContent = new StringContent(JsonConvert.SerializeObject(priceStrategiesBindInput), Encoding.UTF8, "application/json");
        var url = _servicesAddress.Resource_SelfsupportHotelPriceStrategiesBind();
        await _httpClientFactory.InternalPostAsync(url, httpContent: httpContent);
    }

    [UnitOfWork]
    public async Task InitNightlyPrices(List<HotelPriceStrategyInput> input)
    {
        var priceStrategyIds = input.Select(x => x.PriceStrategyId);
        var existsPriceStrategyIds = await _dbContext.HopHotelNightlyPrices
             .Where(x => priceStrategyIds.Contains(x.PriceStrategyId) && x.Date >= DateTime.Today)
             .GroupBy(x => x.PriceStrategyId)
             .Select(x => x.Key)
             .ToListAsync();
        var hotelIds = input.Select(x => x.HotelId);
        var apiHotels = await _dbContext.ApiHotels
            .Where(x => hotelIds.Contains(x.Id))
            .Select(x => new { x.Id, x.ResourceHotelId })
            .ToListAsync();
        var getPriceStrategyPricesInputs = input.GroupBy(x => x.HotelId)
            .Select(x =>
            {
                var apiHotel = apiHotels.FirstOrDefault(s => s.Id == x.Key);
                var ps = x.Select(s => s.PriceStrategyId).Except(existsPriceStrategyIds).ToList();
                GetPriceStrategyPricesInput getPriceStrategyPricesInput = new()
                {
                    ResourceHotelId = apiHotel?.ResourceHotelId ?? 0,
                    PriceStrategyIds = ps,
                    BeginDate = DateTime.Today,
                    EndDate = DateTime.Today.AddYears(1),
                };
                return getPriceStrategyPricesInput;
            });
        foreach (var getPriceStrategyPricesInput in getPriceStrategyPricesInputs)
        {
            if (getPriceStrategyPricesInput.ResourceHotelId <= 0)
                continue;
            if (getPriceStrategyPricesInput.PriceStrategyIds.Any() is not true)
                continue;
            using var httpContent = new StringContent(JsonConvert.SerializeObject(getPriceStrategyPricesInput), Encoding.UTF8, "application/json");
            var url = _servicesAddress.Resource_SelfsupportHotelGetPrices();
            var response = await _httpClientFactory.InternalPostAsync<GetPriceStrategyPricesOutput>(url, httpContent: httpContent);
            var setNightlyPricesInputs = response.Prices?.GroupBy(x => x.PriceStrategyId)
                .Select(p => new SetNightlyPricesInput
                {
                    IsModify = true,
                    PriceStrategyId = p.Key,
                    Nightlies = p.SelectMany(x => x.Nightlies)
                        .Select(s => new NightlyPriceDto
                        {
                            CostPrice = s.Price,
                            Date = s.Date,
                            Enabled = s.Status,
                            Quantity = s.Num,
                            IsOversell = s.IsOversell,
                        })
                });
            if (setNightlyPricesInputs?.Any() is true)
            {
                foreach (var item in setNightlyPricesInputs)
                {
                    await SetNightlyPrices(item);
                }
            }
        }
    }

    public async Task<IEnumerable<string>> ClearUnrecitedNightlyPrices(List<HotelPriceStrategyInput> input)
    {
        var priceStrategyIds = input.Select(x => x.PriceStrategyId);
        var existsPriceStrategyIds = await _dbContext.HotelCombinationSkus.IgnoreQueryFilters()
            .Where(x => priceStrategyIds.Contains(x.PriceStrategyId) && !x.IsDeleted)
            .GroupBy(x => x.PriceStrategyId)
            .Select(x => x.Key)
            .ToListAsync();
        var clearPriceStrategyIds = priceStrategyIds.Except(existsPriceStrategyIds);
        if (clearPriceStrategyIds.Any())
        {
            var hotelNightlyPrices = await _dbContext.HopHotelNightlyPrices
                .Where(x => clearPriceStrategyIds.Contains(x.PriceStrategyId))
                .ToListAsync();
            _dbContext.RemoveRange(hotelNightlyPrices);
            _dbContext.SaveChanges();
        }
        return clearPriceStrategyIds;
    }

}
