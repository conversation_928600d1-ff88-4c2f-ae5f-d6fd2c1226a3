using Contracts.Common.Hotel.DTOs.HotelOperationConfig;
using EfCoreExtensions.Extensions;
using Hotel.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Hotel.Api.Services;

public class HotelOperationConfigService : IHotelOperationConfigService
{
    private readonly CustomDbContext _dbContext;

    public HotelOperationConfigService(CustomDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<HotelOperationConfigOutput>> GetConfigs(GetHotelOperationConfigsInput input)
    {
        var configs = await _dbContext.HotelOperationConfigs
            .Where(x => input.HotelIds.Contains(x.HotelId))
            .WhereIF(input.OperationConfigType.HasValue,x=> x.OperationConfigType == input.OperationConfigType)
            .GroupBy(x => x.HotelId)
            .Select(x => new HotelOperationConfigOutput
            {
                HotelId = x.Key,
                Configs = x.Select(s => new HotelOperationConfigDto
                {
                    AllowSelfMaintainHotel = s.AllowSelfMaintainHotel,
                    AllowSelfMaintainRoom = s.AllowSelfMaintainRoom,
                    OperationConfigType = s.OperationConfigType
                }).ToArray(),
            })
            .ToListAsync();
        return configs;

    }

    public async Task Config(HotelOperationConfigInput input)
    {
        var configs = await _dbContext.HotelOperationConfigs
            .Where(x => x.HotelId == input.HotelId)
            .ToListAsync();
        List<HotelOperationConfig> hotelOperationConfigs = new();
        foreach (var item in input.Configs)
        {
            var config = configs.FirstOrDefault(x => x.OperationConfigType == item.OperationConfigType);
            if (config is null)
            {
                hotelOperationConfigs.Add(new HotelOperationConfig
                {
                    AllowSelfMaintainHotel = item.AllowSelfMaintainHotel,
                    AllowSelfMaintainRoom = item.AllowSelfMaintainRoom,
                    HotelId = input.HotelId,
                    OperationConfigType = item.OperationConfigType,
                });
            }
            else
            {
                config.AllowSelfMaintainHotel = item.AllowSelfMaintainHotel;
                config.AllowSelfMaintainRoom = item.AllowSelfMaintainRoom;
            }
        }

        await _dbContext.HotelOperationConfigs.AddRangeAsync(hotelOperationConfigs);
        await _dbContext.SaveChangesAsync();
    }

}
