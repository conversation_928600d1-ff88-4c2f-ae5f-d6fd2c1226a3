using Contracts.Common.Hotel.DTOs.SaleChannel;

namespace Hotel.Api.Services.Interfaces;

public interface ISalesChannelsService
{
    /// <summary>
    /// 获取酒店渠道
    /// </summary>
    Task<List<GetHotelSellingChannelsOutput>> GetHotelSalesChannels(long hotelId);

    /// <summary>
    /// 获取酒店渠道加价设置详情
    /// </summary>
    Task<List<GetHotelChannelMarkupOutput>> GetHotelChannelMarkup(GetHotelChannelMarkupInput input);

    /// <summary>
    /// 保存酒店渠道加价设置详情
    /// </summary>
    Task SetHotelChannelMarkup(UpdateHotelChannelMarkupInput input);
}