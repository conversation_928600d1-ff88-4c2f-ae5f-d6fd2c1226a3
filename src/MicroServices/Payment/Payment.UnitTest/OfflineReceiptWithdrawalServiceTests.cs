using Common.ServicesHttpClient;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using Contracts.Common.Tenant.DTOs.Supplier;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using System;
using System.Net.Http;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest
{
    public class OfflineReceiptWithdrawalServiceTests : TestBase<CustomDbContext>
    {
        private const long _tenantId = 1;

        private static OfflineReceiptWithdrawalService GetService(CustomDbContext dbContext,
          IHttpClientFactory httpClientFactory,
          IOptions<ServicesAddress> servicesAddress,
          ICapPublisher capPublisher)
        {
            return new OfflineReceiptWithdrawalService(dbContext,
               httpClientFactory,
               servicesAddress,
               capPublisher);
        }

        [Fact(DisplayName = "供应商发起提现_成功")]
        public async Task Withdraw_Success()
        {
            //arrange
            var dbContext = GetNewDbContext(_tenantId);
            long supplierId = 920620631543513088;
            OfflineReceiptOrder offlineReceiptOrder = new()
            {
                SupplierId = supplierId,
                Status = OfflineReceiptOrderStatus.Received,
                CreateTime = DateTime.Now,
                OrderAmount = 1,
                PaymentFee = 0.3m
            };
            OfflineReceiptOrderFlow offlineReceiptOrderFlow = new()
            {
                OfflineReceiptOrderId = offlineReceiptOrder.Id,
                Amount = 1m,
                PaymentFee = 0.3m,
                FlowType = OfflineReceiptOrderFlowType.Receive,
                Status = OfflineReceiptOrderStatus.Received
            };
            await dbContext.AddAsync(offlineReceiptOrder);
            await dbContext.AddAsync(offlineReceiptOrderFlow);
            await dbContext.SetTenantId(_tenantId).SaveChangesAsync();
            //act
            var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = new StringContent(JsonConvert.SerializeObject(new GetSupplierOutput
                {
                    AccountName = "账户名",
                    BankAccount = "123456",
                    BankCode = "ICBC",
                    BankName = "工商银行",
                    FullName = "测试"
                }))
            });

            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(x => x.Value)
                    .Returns(new ServicesAddress
                    {
                        Tenant = "http://localhsot"
                    });
            var user = new Common.Jwt.CurrentUser
            {
                nickname = "test",
                userid = 1,
                tenant = _tenantId,
                provider = supplierId
            };
            var service = GetService(dbContext,
                httpClientFactory,
                servicesAddress.Object,
                GetCapPublisher());
            await service.Withdraw(user);
            //assert
            Assert.True(offlineReceiptOrder.Status == OfflineReceiptOrderStatus.Withdrawing);
        }

        [Fact(DisplayName = "转账预付结果处理_成功")]
        public async Task TransferOrderResult_Success()
        {
            //arrange
            var dbContext = GetNewDbContext();
            long supplierId = 920620631543513088;
            OfflineReceiptOrder offlineReceiptOrder = new()
            {
                SupplierId = supplierId,
                Status = OfflineReceiptOrderStatus.Withdrawing,
                CreateTime = DateTime.Now,
                OrderAmount = 1,
                PaymentFee = 0.3m
            };
            OfflineReceiptOrderFlow offlineReceiptOrderFlow = new()
            {
                OfflineReceiptOrderId = offlineReceiptOrder.Id,
                Amount = 1m,
                PaymentFee = 0.3m,
                FlowType = OfflineReceiptOrderFlowType.Receive,
                Status = OfflineReceiptOrderStatus.Received
            };
            OfflineReceiptWithdrawal offlineReceiptWithdrawal = new()
            {
                SupplierId = supplierId,
                TransferOrderNo = 123456,
                Amount = 0.7m,
                AccountName = "账户名",
                AccountNumber = "123456",
                BankCode = "ICBC",
                BankName = "工商银行",
                CreateTime = DateTime.Now,
                Status = OfflineReceiptOrderWithdrawalStatus.Processing
            };
            OfflineReceiptWithdrawalDetail offlineReceiptWithdrawalDetail = new()
            {
                OfflineReceiptOrderId = offlineReceiptOrder.Id,
                OfflineReceiptOrderWithdrawalId = offlineReceiptWithdrawal.Id
            };
            await dbContext.AddAsync(offlineReceiptOrder);
            await dbContext.AddAsync(offlineReceiptOrderFlow);
            await dbContext.AddAsync(offlineReceiptWithdrawal);
            await dbContext.AddAsync(offlineReceiptWithdrawalDetail);
            await dbContext.SetTenantId(_tenantId).SaveChangesAsync();
            //act

            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(x => x.Value)
                    .Returns(new ServicesAddress
                    {
                        Tenant = "http://localhsot"
                    });
            var user = new Common.Jwt.CurrentUser
            {
                nickname = "test",
                userid = 1,
                tenant = _tenantId,
                provider = supplierId
            };
            var service = GetService(dbContext,
                null,
                servicesAddress.Object,
                GetCapPublisher());
            var command = new TransferOrderResultMessage
            {
                ExtOrderNo = "111",
                FailReason = "",
                IsSuccess = true,
                TenantId = _tenantId,
                TransferOrderNo = offlineReceiptWithdrawal.TransferOrderNo
            };
            await service.TransferOrderResult(command);
            //assert
            Assert.True(offlineReceiptWithdrawal.PayOrderNo > 0);
            Assert.True(offlineReceiptOrder.Status == OfflineReceiptOrderStatus.Withdrawing);
        }

        [Fact(DisplayName = "转账预付结果处理_失败")]
        public async Task TransferOrderResult_Fail()
        {
            //arrange
            var dbContext = GetNewDbContext();
            long supplierId = 920620631543513088;
            OfflineReceiptOrder offlineReceiptOrder = new()
            {
                SupplierId = supplierId,
                Status = OfflineReceiptOrderStatus.Withdrawing,
                CreateTime = DateTime.Now,
                OrderAmount = 1,
                PaymentFee = 0.3m
            };
            OfflineReceiptOrderFlow offlineReceiptOrderFlow = new()
            {
                OfflineReceiptOrderId = offlineReceiptOrder.Id,
                Amount = 1m,
                PaymentFee = 0.3m,
                FlowType = OfflineReceiptOrderFlowType.Receive,
                Status = OfflineReceiptOrderStatus.Received
            };
            OfflineReceiptWithdrawal offlineReceiptWithdrawal = new()
            {
                SupplierId = supplierId,
                TransferOrderNo = 123456,
                Amount = 0.7m,
                AccountName = "账户名",
                AccountNumber = "123456",
                BankCode = "ICBC",
                BankName = "工商银行",
                CreateTime = DateTime.Now,
                Status = OfflineReceiptOrderWithdrawalStatus.Processing
            };
            OfflineReceiptWithdrawalDetail offlineReceiptWithdrawalDetail = new()
            {
                OfflineReceiptOrderId = offlineReceiptOrder.Id,
                OfflineReceiptOrderWithdrawalId = offlineReceiptWithdrawal.Id
            };
            await dbContext.AddAsync(offlineReceiptOrder);
            await dbContext.AddAsync(offlineReceiptOrderFlow);
            await dbContext.AddAsync(offlineReceiptWithdrawal);
            await dbContext.AddAsync(offlineReceiptWithdrawalDetail);
            await dbContext.SetTenantId(_tenantId).SaveChangesAsync();
            //act

            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(x => x.Value)
                    .Returns(new ServicesAddress
                    {
                        Tenant = "http://localhsot"
                    });
            var user = new Common.Jwt.CurrentUser
            {
                nickname = "test",
                userid = 1,
                tenant = _tenantId,
                provider = supplierId
            };
            var service = GetService(dbContext,
                null,
                servicesAddress.Object,
                GetCapPublisher());
            var command = new TransferOrderResultMessage
            {
                ExtOrderNo = "111",
                FailReason = "转账失败",
                IsSuccess = false,
                TenantId = _tenantId,
                TransferOrderNo = offlineReceiptWithdrawal.TransferOrderNo
            };
            await service.TransferOrderResult(command);
            //assert
            Assert.True(offlineReceiptWithdrawal.Status == OfflineReceiptOrderWithdrawalStatus.Failed);
            Assert.True(offlineReceiptOrder.Status == OfflineReceiptOrderStatus.Received);
        }

        [Fact(DisplayName = "提现结果处理_成功")]
        public async Task WithdrawalResult_Success()
        {
            //arrange
            var dbContext = GetNewDbContext(_tenantId);
            long supplierId = 920620631543513088;
            OfflineReceiptOrder offlineReceiptOrder = new()
            {
                SupplierId = supplierId,
                Status = OfflineReceiptOrderStatus.Withdrawing,
                CreateTime = DateTime.Now,
                OrderAmount = 1,
                PaymentFee = 0.3m
            };
            OfflineReceiptOrderFlow offlineReceiptOrderFlow = new()
            {
                OfflineReceiptOrderId = offlineReceiptOrder.Id,
                Amount = 1m,
                PaymentFee = 0.3m,
                FlowType = OfflineReceiptOrderFlowType.Receive,
                Status = OfflineReceiptOrderStatus.Received
            };
            OfflineReceiptWithdrawal offlineReceiptWithdrawal = new()
            {
                SupplierId = supplierId,
                TransferOrderNo = 123456,
                PayOrderNo = 111111,
                Amount = 0.7m,
                AccountName = "账户名",
                AccountNumber = "123456",
                BankCode = "ICBC",
                BankName = "工商银行",
                CreateTime = DateTime.Now,
                Status = OfflineReceiptOrderWithdrawalStatus.Processing
            };
            OfflineReceiptWithdrawalDetail offlineReceiptWithdrawalDetail = new()
            {
                OfflineReceiptOrderId = offlineReceiptOrder.Id,
                OfflineReceiptOrderWithdrawalId = offlineReceiptWithdrawal.Id
            };
            await dbContext.AddAsync(offlineReceiptOrder);
            await dbContext.AddAsync(offlineReceiptOrderFlow);
            await dbContext.AddAsync(offlineReceiptWithdrawal);
            await dbContext.AddAsync(offlineReceiptWithdrawalDetail);
            await dbContext.SetTenantId(_tenantId).SaveChangesAsync();
            //act

            var servicesAddress = new Mock<IOptions<ServicesAddress>>();
            servicesAddress.Setup(x => x.Value)
                    .Returns(new ServicesAddress
                    {
                        Tenant = "http://localhsot"
                    });
            var user = new Common.Jwt.CurrentUser
            {
                nickname = "test",
                userid = 1,
                tenant = _tenantId,
                provider = supplierId
            };
            var service = GetService(dbContext,
                null,
                servicesAddress.Object,
                GetCapPublisher());
            var command = new AccountPayOrderResultMessage
            {
                FailReason = "",
                IsSuccess = true,
                TenantId = _tenantId,
                PayOrderNo = offlineReceiptWithdrawal.PayOrderNo
            };
            await service.WithdrawalResult(command);
            //assert
            Assert.True(offlineReceiptWithdrawal.Status == OfflineReceiptOrderWithdrawalStatus.Succeed);
            Assert.True(offlineReceiptOrder.Status == OfflineReceiptOrderStatus.Withdrawed);
        }
    }
}
