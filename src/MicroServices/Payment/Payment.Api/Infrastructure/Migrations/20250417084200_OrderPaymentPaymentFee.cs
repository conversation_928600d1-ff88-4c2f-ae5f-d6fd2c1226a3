using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Payment.Api.Infrastructure.Migrations
{
    public partial class OrderPaymentPaymentFee : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "RefundPaymentFee",
                table: "OrderRefund",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "PaymentFee",
                table: "OrderPayment",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RefundPaymentFee",
                table: "OrderRefund");

            migrationBuilder.DropColumn(
                name: "PaymentFee",
                table: "OrderPayment");
        }
    }
}
