// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Payment.Api.Infrastructure;

#nullable disable

namespace Payment.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20240517105139_ReceiptPrepaymentFlowsAddUniqueOrderId")]
    partial class ReceiptPrepaymentFlowsAddUniqueOrderId
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Payment.Api.Model.AccountInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AccountStatus")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("AccountType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("Balance")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AccountInfo", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.AccountInfoDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AccountInfoId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("AccountTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("Expenditure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ExtNo")
                        .HasColumnType("varchar(64)");

                    b.Property<decimal>("Income")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte?>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AccountInfoDetail", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.AccountPaymentBill", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("BeginDate")
                        .HasColumnType("datetime");

                    b.Property<decimal>("BillAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("VerifiedTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Verifier")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("VerifierId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AccountPaymentBill", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.AccountPayOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BankAccountType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Comments")
                        .HasColumnType("varchar(64)");

                    b.Property<decimal>("DebitAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ExtOrderNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("Fee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte?>("FeeChargeSide")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsReversed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MerchantNo")
                        .HasColumnType("varchar(64)");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("OrderTime")
                        .HasColumnType("datetime");

                    b.Property<long>("PayOrderNo")
                        .HasColumnType("bigint");

                    b.Property<string>("ReceiptComments")
                        .HasColumnType("varchar(256)");

                    b.Property<decimal>("ReceiveAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("ReceiveType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ReceiverAccountName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ReceiverAccountNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ReceiverBankCode")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime?>("ReverseTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("PayOrderNo")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("AccountPayOrder", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.AccountTransferOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("DebitAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ExtOrderNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(256)");

                    b.Property<decimal>("Fee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("FeeChargeSide")
                        .HasColumnType("tinyint");

                    b.Property<string>("FeeMerchantNo")
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FromMerchantNo")
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ReceiveAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ToMerchantNo")
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TransferOrderNo")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TransferOrderType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TransferStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TransferType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Usage")
                        .HasColumnType("varchar(64)");

                    b.Property<sbyte>("UsageType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TransferOrderNo")
                        .IsUnique();

                    b.ToTable("AccountTransferOrder", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.Bank", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("BankNumber")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ShortName")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.ToTable("Bank", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 102100099996L,
                            BankCode = "ICBC",
                            BankNumber = 102100099996L,
                            Name = "中国工商银行",
                            ShortName = "工行"
                        },
                        new
                        {
                            Id = 103100024015L,
                            BankCode = "ABC",
                            BankNumber = 103100024015L,
                            Name = "中国农业银行",
                            ShortName = "农行"
                        },
                        new
                        {
                            Id = 104100000004L,
                            BankCode = "BOC",
                            BankNumber = 104100000004L,
                            Name = "中国银行",
                            ShortName = "中行"
                        },
                        new
                        {
                            Id = 105100000017L,
                            BankCode = "CCB",
                            BankNumber = 105100000017L,
                            Name = "中国建设银行",
                            ShortName = "建行"
                        },
                        new
                        {
                            Id = 301290000007L,
                            BankCode = "BOCO",
                            BankNumber = 301290000007L,
                            Name = "交通银行",
                            ShortName = "交行"
                        },
                        new
                        {
                            Id = 302100011000L,
                            BankCode = "ECITIC",
                            BankNumber = 302100011000L,
                            Name = "中信银行",
                            ShortName = "中信"
                        },
                        new
                        {
                            Id = 303100000006L,
                            BankCode = "CEB",
                            BankNumber = 303100000006L,
                            Name = "中国光大银行",
                            ShortName = "光大"
                        },
                        new
                        {
                            Id = 304100040000L,
                            BankCode = "HXB",
                            BankNumber = 304100040000L,
                            Name = "华夏银行",
                            ShortName = "华夏"
                        },
                        new
                        {
                            Id = 305100000013L,
                            BankCode = "CMBC",
                            BankNumber = 305100000013L,
                            Name = "中国民生银行",
                            ShortName = "民生"
                        },
                        new
                        {
                            Id = 307584007998L,
                            BankCode = "SDB",
                            BankNumber = 307584007998L,
                            Name = "平安银行(深圳发展银行)",
                            ShortName = "平安"
                        },
                        new
                        {
                            Id = 308584000013L,
                            BankCode = "CMBCHINA",
                            BankNumber = 308584000013L,
                            Name = "招商银行",
                            ShortName = "招行"
                        },
                        new
                        {
                            Id = 309391000011L,
                            BankCode = "CIB",
                            BankNumber = 309391000011L,
                            Name = "兴业银行",
                            ShortName = "兴业"
                        },
                        new
                        {
                            Id = 310290000013L,
                            BankCode = "SPDB",
                            BankNumber = 310290000013L,
                            Name = "上海浦东发展银行",
                            ShortName = "浦发"
                        },
                        new
                        {
                            Id = 313100000013L,
                            BankCode = "BCCB",
                            BankNumber = 313100000013L,
                            Name = "北京银行",
                            ShortName = "北京"
                        },
                        new
                        {
                            Id = 313110000017L,
                            BankCode = "TJYH",
                            BankNumber = 313110000017L,
                            Name = "天津银行",
                            ShortName = "天津"
                        },
                        new
                        {
                            Id = 322290000011L,
                            BankCode = "SHRCB",
                            BankNumber = 322290000011L,
                            Name = "上海农村商业银行",
                            ShortName = "上海农商"
                        },
                        new
                        {
                            Id = 325290000012L,
                            BankCode = "SHYH",
                            BankNumber = 325290000012L,
                            Name = "上海银行",
                            ShortName = "上海"
                        },
                        new
                        {
                            Id = 402100000018L,
                            BankCode = "BRCB",
                            BankNumber = 402100000018L,
                            Name = "北京农村商业银行",
                            ShortName = "北京农商"
                        },
                        new
                        {
                            Id = 403100000004L,
                            BankCode = "PSBC",
                            BankNumber = 403100000004L,
                            Name = "中国邮政储蓄银行",
                            ShortName = "邮储"
                        },
                        new
                        {
                            Id = 313551088886L,
                            BankCode = "CSCB",
                            BankNumber = 313551088886L,
                            Name = "长沙银行",
                            ShortName = "长沙"
                        },
                        new
                        {
                            Id = 314593800018L,
                            BankCode = "GDXXRCB",
                            BankNumber = 314593800018L,
                            Name = "广东新兴农村商业银行",
                            ShortName = "广东新兴农商"
                        });
                });

            modelBuilder.Entity("Payment.Api.Model.CurrencyExchangeRate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BaseCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(18,6)");

                    b.Property<string>("TargetCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("BaseCurrencyCode", "TargetCurrencyCode", "Date")
                        .IsUnique();

                    b.ToTable("CurrencyExchangeRate", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.DarenWithdrawal", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(256)");

                    b.Property<decimal>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("PayOrderNo")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TransferOrderNo")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("WithdrawApplyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("WithdrawalStatus")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DarenWithdrawal", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.FundAccountBill", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("AccountTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("BillBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyName")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("FundAccountBillStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .IsConcurrencyToken()
                        .HasColumnType("timestamp(3)");

                    b.HasKey("Id");

                    b.HasIndex("BusinessOrderId")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("FundAccountBill", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.FundAccountBillFlow", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BillFlowOperationType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("BillFlowStatus")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("CanRetryIfFail")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Expenditure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ExtNo")
                        .HasColumnType("varchar(64)");

                    b.Property<decimal>("Fee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("FundAccountBillId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Income")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .IsConcurrencyToken()
                        .HasColumnType("timestamp(3)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId")
                        .IsUnique();

                    b.ToTable("FundAccountBillFlow", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OfflineReceipt", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OfflineReceipt", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OfflineReceiptOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DiscountName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("OfflineReceiptId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PayChannel")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("PayTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("PaymentFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("RefundOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("WithdrawalId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OfflineReceiptOrder", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OfflineReceiptOrderFlow", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("FlowType")
                        .HasColumnType("tinyint");

                    b.Property<long>("OfflineReceiptOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("OperatorName")
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("PaymentFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OfflineReceiptOrderFlow", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OfflineReceiptRefundOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ExtRefundNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("FailedReason")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Reason")
                        .HasColumnType("varchar(64)");

                    b.Property<decimal>("RefundAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OfflineReceiptRefundOrder", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OfflineReceiptWithdrawal", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("varchar(64)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("BackTransferOrderNo")
                        .HasColumnType("bigint");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("BankFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("PayOrderNo")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TransferOrderNo")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OfflineReceiptWithdrawal", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OfflineReceiptWithdrawalDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("OfflineReceiptOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("OfflineReceiptOrderWithdrawalId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OfflineReceiptWithdrawalDetail", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OrderPayment", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BankOrderId")
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CsSuccessDate")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("FundProcessType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("LastUpdateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("MerchantFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Message")
                        .HasColumnType("varchar(200)");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderPaymentType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PayChannel")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PayStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PayWay")
                        .HasColumnType("tinyint");

                    b.Property<string>("PrePayTn")
                        .HasColumnType("varchar(2000)");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UniqueOrderNo")
                        .HasColumnType("varchar(64)");

                    b.Property<decimal>("YpSettleAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderPayment", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OrderPaymentDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ExtNo")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("OrderPaymentId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PaymentScene")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PaymentUserType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderPaymentDetail", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OrderRefund", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderRefundType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("RefundAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("RefundOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("RefundStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<decimal>("ReturnMerchantFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UniqueOrderNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("UniqueRefundNo")
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderRefund", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.ProfitDivide", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("DivideStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("DivideWay")
                        .HasColumnType("tinyint");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UniqueOrderNo")
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ProfitDivide", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.ProfitDivideBack", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BizSystemNo")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("DivideBackStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("DivideId")
                        .HasColumnType("bigint");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("RefundOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UniqueDivideBackNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("UniqueOrderNo")
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ProfitDivideBack", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.ProfitDivideBackDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("DivideBackId")
                        .HasColumnType("bigint");

                    b.Property<string>("DivideBackReason")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("DivideDetailId")
                        .HasColumnType("bigint");

                    b.Property<string>("DivideDetailNo")
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("DivideBackId");

                    b.ToTable("ProfitDivideBackDetail", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.ProfitDivideDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DivideDetailDesc")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("DivideDetailNo")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("DivideId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("DivideType")
                        .HasColumnType("tinyint");

                    b.Property<string>("LedgerNo")
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("DivideId");

                    b.ToTable("ProfitDivideDetail", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.ReceiptPrepayment", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Balance")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiptPrepayment", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.ReceiptPrepaymentFlow", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Expenditure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Income")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ReceiptPrepaymentId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UniqueOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiptPrepaymentFlow", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.ReceiptPrepaymentRefund", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ChargeId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte?>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Proof")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("ReceiptPrepaymentId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("RefundAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiptPrepaymentRefund", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.SettlementPayOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AccountNo")
                        .HasColumnType("varchar(32)");

                    b.Property<long?>("BackTransferOrderNo")
                        .HasColumnType("bigint");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("OrderTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("PayOrderNo")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("SettlementOrderTransferRecordId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TransferFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("TransferOrderNo")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("SettlementOrderTransferRecordId", "Status")
                        .IsUnique();

                    b.ToTable("SettlementPayOrder", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.TenantBankAccount", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("AccountNo")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("BankAccountType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)5);

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("OpeningBankCode")
                        .HasColumnType("varchar(30)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("TenantBankAccountType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("UpdatorName")
                        .HasColumnType("longtext");

                    b.Property<bool>("YeeAccount")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("Id");

                    b.ToTable("TenantBankAccount", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.TenantBankAccountAbroadSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("SwiftCode")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantBankAccountId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.ToTable("TenantBankAccountAbroadSetting", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.WithdrawOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AccountNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("BackTransferOrderNo")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BankAccountType")
                        .HasColumnType("tinyint");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("DebitAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("Fee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("OrderTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("PayOrderNo")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("ReceiveAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceiveCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<sbyte>("ReceiveType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<string>("SwiftCode")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TransferOrderNo")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("WithdrawOrderType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("WithdrawOrder", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.WithdrawOrderAudit", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Attachments")
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("AuditId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AuditStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("Auditor")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("OrderTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("WithdrawOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("WithdrawOrderAudit", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeeConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AesSecretKey")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("AppKey")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("HmacSecretKey")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ParentMetchantNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PriviteKey")
                        .HasColumnType("varchar(2000)");

                    b.Property<string>("ServerRoot")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("YopPublishKey")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("YosServerRoot")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.ToTable("YeeConfig", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeeFileBind", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Destination")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Source")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("YeeFileBind", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeeMerConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ApplicationNo")
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CompletedTime")
                        .HasColumnType("datetime");

                    b.Property<string>("MerchantNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int>("MerchantNoType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("RequestNo")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("YeeMerConfig", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeePayRegister", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AgreementSignUrl")
                        .HasColumnType("longtext")
                        .HasComment("varchar(500)");

                    b.Property<string>("ApplicationNo")
                        .HasColumnType("varchar(64)");

                    b.Property<sbyte>("ApplicationStatus")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<string>("AuditOpinion")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("BusinessRole")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("MerchantNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ProgressDescription")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("RequestNo")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("YeePayRegister", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeeProductInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("FixedRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("PercentRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductCode")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("RateType")
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Undertaker")
                        .HasColumnType("varchar(32)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("YeeProductInfo", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeeSubjectInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("BankAccountType")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BankCardNo")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("City")
                        .HasColumnType("bigint");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ContactLicenceNo")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ContactMobile")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ContactName")
                        .HasColumnType("varchar(512)");

                    b.Property<long>("District")
                        .HasColumnType("bigint");

                    b.Property<string>("HandLicenceUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("LegalLicenceBackUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("LegalLicenceFrontUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("LegalLicenceNo")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("LegalLicenceType")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("LegalName")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("LicenceNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("LicenceUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("OpenAccountLicenceNo")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("OpenAccountLicenceUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("Province")
                        .HasColumnType("bigint");

                    b.Property<string>("ServicePhone")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("SettlementDirection")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ShortName")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("SignName")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("SignType")
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("YeeSubjectInfo", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeeWechatAuth", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ApplicantIdCard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ApplicantName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ApplicantPhone")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ApplicantType")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ApplymentId")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ApplymentState")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AuthorizeState")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CertCopy")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ChannelId")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("CompanyAddress")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CompanyProveCopy")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("IdentificationAddress")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("IdentificationBackCopy")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("IdentificationFrontCopy")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("IdentificationType")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("IdentificationValidDate")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("LicenceValidDate")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("QrcodeUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("RejectParam")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("RejectReason")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ReportFee")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ReportMerchantNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("RequestNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("SubMerchantNo")
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TransactorInfo")
                        .HasColumnType("varchar(1000)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("YeeWechatAuth", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.YeeWechatConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AppId")
                        .HasColumnType("varchar(64)");

                    b.Property<sbyte>("AppIdType")
                        .HasColumnType("tinyint");

                    b.Property<string>("AppSecret")
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FailReason")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Status")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("SubscribeAppId")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TradeAuthDir")
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("YeeWechatConfig", (string)null);
                });

            modelBuilder.Entity("Payment.Api.Model.OfflineReceiptOrder", b =>
                {
                    b.OwnsOne("Payment.Api.Model.OfflineReceiptOrderPayerInfo", "PayerInfo", b1 =>
                        {
                            b1.Property<long>("OfflineReceiptOrderId")
                                .HasColumnType("bigint");

                            b1.Property<long?>("CustomerUserId")
                                .HasColumnType("bigint");

                            b1.Property<string>("NickName")
                                .HasColumnType("varchar(200)");

                            b1.Property<string>("UserID")
                                .HasColumnType("varchar(64)");

                            b1.HasKey("OfflineReceiptOrderId");

                            b1.ToTable("OfflineReceiptOrder");

                            b1.WithOwner()
                                .HasForeignKey("OfflineReceiptOrderId");
                        });

                    b.Navigation("PayerInfo")
                        .IsRequired();
                });

            modelBuilder.Entity("Payment.Api.Model.WithdrawOrder", b =>
                {
                    b.OwnsOne("Payment.Api.Model.PaymentBill", "PaymentBill", b1 =>
                        {
                            b1.Property<long>("WithdrawOrderId")
                                .HasColumnType("bigint");

                            b1.Property<DateTime?>("BeginDate")
                                .HasColumnType("datetime");

                            b1.Property<DateTime?>("EndDate")
                                .HasColumnType("datetime");

                            b1.Property<long?>("Id")
                                .HasColumnType("bigint");

                            b1.HasKey("WithdrawOrderId");

                            b1.ToTable("WithdrawOrder");

                            b1.WithOwner()
                                .HasForeignKey("WithdrawOrderId");
                        });

                    b.Navigation("PaymentBill");
                });
#pragma warning restore 612, 618
        }
    }
}
