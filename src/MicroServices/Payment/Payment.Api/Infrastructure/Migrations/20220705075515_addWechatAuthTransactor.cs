using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Payment.Api.Infrastructure.Migrations
{
    public partial class addWechatAuthTransactor : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ApplicantType",
                table: "YeeWechatAuth",
                type: "varchar(100)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "IdentificationAddress",
                table: "YeeWechatAuth",
                type: "varchar(256)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "TransactorInfo",
                table: "YeeWechatAuth",
                type: "varchar(1000)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApplicantType",
                table: "YeeWechatAuth");

            migrationBuilder.DropColumn(
                name: "IdentificationAddress",
                table: "YeeWechatAuth");

            migrationBuilder.DropColumn(
                name: "TransactorInfo",
                table: "YeeWechatAuth");
        }
    }
}
