using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Payment.Api.Infrastructure.Migrations
{
    public partial class AddWithdrawOrderAudit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "ReceiveAmount",
                table: "WithdrawOrder",
                type: "decimal(18,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "WithdrawOrder",
                type: "varchar(100)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ReceiveCurrencyCode",
                table: "WithdrawOrder",
                type: "varchar(20)",
                nullable: false,
                defaultValue: "CNY")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "SwiftCode",
                table: "WithdrawOrder",
                type: "varchar(20)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<sbyte>(
                name: "WithdrawOrderType",
                table: "WithdrawOrder",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)1);

            migrationBuilder.CreateTable(
                name: "WithdrawOrderAudit",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    CompanyName = table.Column<string>(type: "varchar(200)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    WithdrawOrderId = table.Column<long>(type: "bigint", nullable: false),
                    Attachments = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "varchar(200)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AuditStatus = table.Column<sbyte>(type: "tinyint", nullable: false),
                    AuditId = table.Column<long>(type: "bigint", nullable: true),
                    Auditor = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OrderTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WithdrawOrderAudit", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_WithdrawOrderAudit_TenantId",
                table: "WithdrawOrderAudit",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WithdrawOrderAudit");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "WithdrawOrder");

            migrationBuilder.DropColumn(
                name: "ReceiveCurrencyCode",
                table: "WithdrawOrder");

            migrationBuilder.DropColumn(
                name: "SwiftCode",
                table: "WithdrawOrder");

            migrationBuilder.DropColumn(
                name: "WithdrawOrderType",
                table: "WithdrawOrder");

            migrationBuilder.AlterColumn<decimal>(
                name: "ReceiveAmount",
                table: "WithdrawOrder",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldNullable: true);
        }
    }
}
