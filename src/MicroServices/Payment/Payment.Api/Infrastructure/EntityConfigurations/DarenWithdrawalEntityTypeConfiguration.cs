using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class DarenWithdrawalEntityTypeConfiguration : TenantBaseConfiguration<Model.DarenWithdrawal>, IEntityTypeConfiguration<Model.DarenWithdrawal>
    {
        public void Configure(EntityTypeBuilder<Model.DarenWithdrawal> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.WithdrawApplyId)
                .HasColumnType("bigint");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.InvoiceAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.BankCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.AccountNumber)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.AccountName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.PayOrderNo)
                .HasColumnType("bigint");

            builder.Property(s => s.TransferOrderNo)
                .HasColumnType("bigint");

            builder.Property(s => s.FailReason)
                .HasColumnType("varchar(256)");

            builder.Property(s => s.WithdrawalStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
        }
    }
}
