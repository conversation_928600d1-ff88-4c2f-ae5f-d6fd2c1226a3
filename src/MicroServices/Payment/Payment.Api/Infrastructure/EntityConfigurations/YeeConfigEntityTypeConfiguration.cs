using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class YeeConfigEntityTypeConfiguration : KeyBaseConfiguration<Model.YeeConfig>, IEntityTypeConfiguration<Model.YeeConfig>
    {
        public void Configure(EntityTypeBuilder<Model.YeeConfig> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ServerRoot)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.YosServerRoot)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.ParentMetchantNo)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.AppKey)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.YopPublishKey)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.PriviteKey)
                .HasColumnType("varchar(2000)");

            builder.Property(s => s.AesSecretKey)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.HmacSecretKey)
                .HasColumnType("varchar(500)");
        }
    }
}
