using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class VirtualCreditCardOperationRecordEntityTypeConfiguration : TenantBaseConfiguration<Model.VirtualCreditCardOperationRecord>, IEntityTypeConfiguration<Model.VirtualCreditCardOperationRecord>
    {
        public void Configure(EntityTypeBuilder<Model.VirtualCreditCardOperationRecord> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.PurchaseId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.CardNumber)
                .HasColumnType("varchar(100)");
            
            builder.Property(s => s.RecordType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");
            
            builder.Property(s => s.ErrorMessage)
                .HasColumnType("varchar(200)");
            
            builder.Property(s => s.OperatorId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.Operator)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            //index
            builder.HasIndex(s => s.BaseOrderId);
        }
    }
}
