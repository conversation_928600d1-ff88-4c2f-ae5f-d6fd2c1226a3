using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class ProfitDivideEntityTypeConfiguration : TenantBaseConfiguration<Model.ProfitDivide>, IEntityTypeConfiguration<Model.ProfitDivide>
    {
        public void Configure(EntityTypeBuilder<Model.ProfitDivide> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.OrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.UniqueOrderNo)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.DivideWay)
                .HasColumnType("tinyint");

            builder.Property(s => s.DivideStatus)
                .HasColumnType("tinyint")
                .IsConcurrencyToken();

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Ignore(s => s.ProfitDivideDetails);

            builder.Property(s => s.OrderPaymentId)
                .HasColumnType("bigint");
        }
    }
}
