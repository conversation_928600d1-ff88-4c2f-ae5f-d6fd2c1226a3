using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Payment.Api.Infrastructure.EntityConfigurations;

public class TenantBankAccountVccSettingEntityTypeConfiguration : TenantBaseConfiguration<TenantBankAccountVccSetting>, IEntityTypeConfiguration<TenantBankAccountVccSetting>
{
    public void Configure(EntityTypeBuilder<TenantBankAccountVccSetting> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.SupplierType)
            .HasColumnType("int")
            .IsRequired();

        builder.Property(s => s.SupplierName)
            .HasColumnType("varchar(64)")
            .IsRequired();

        builder.Property(s => s.UserName)
            .HasColumnType("varchar(64)")
            .IsRequired();

        builder.Property(s => s.Password)
            .HasColumnType("varchar(64)")
            .IsRequired();
        
        builder.Property(s => s.OrgBankId)
            .HasColumnType("varchar(100)")
            .IsRequired();
        
        builder.Property(s => s.OrgCompanyId)
            .HasColumnType("varchar(100)")
            .IsRequired();

        builder.HasIndex(x => new { x.TenantId, x.SupplierName }).IsUnique();
    }
}
