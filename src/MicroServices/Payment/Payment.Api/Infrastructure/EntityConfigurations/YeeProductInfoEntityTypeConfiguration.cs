using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class YeeProductInfoEntityTypeConfiguration : TenantBaseConfiguration<Model.YeeProductInfo>, IEntityTypeConfiguration<Model.YeeProductInfo>
    {
        public void Configure(EntityTypeBuilder<Model.YeeProductInfo> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ProductCode)
                .HasColumnType("varchar(128)");

            builder.Property(s => s.RateType)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.PercentRate)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.FixedRate)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Undertaker)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.PaymentMethod)
                .HasColumnType("varchar(32)");
        }
    }
}
