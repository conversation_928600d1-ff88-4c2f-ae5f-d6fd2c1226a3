using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class ProfitDivideDetailEntityTypeConfiguration : KeyBaseConfiguration<Model.ProfitDivideDetail>, IEntityTypeConfiguration<Model.ProfitDivideDetail>
    {
        public void Configure(EntityTypeBuilder<Model.ProfitDivideDetail> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.DivideId)
                .HasColumnType("bigint");

            builder.Property(s => s.DivideType)
              .HasColumnType("tinyint");

            builder.Property(s => s.LedgerNo)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.DivideDetailDesc)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.DivideDetailNo)
                .HasColumnType("varchar(64)");

            builder.HasIndex(s => s.DivideId);
        }
    }
}
