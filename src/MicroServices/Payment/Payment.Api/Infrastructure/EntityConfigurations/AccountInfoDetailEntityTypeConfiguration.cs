using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class AccountInfoDetailEntityTypeConfiguration : TenantBaseConfiguration<Model.AccountInfoDetail>, IEntityTypeConfiguration<Model.AccountInfoDetail>
    {
        public void Configure(EntityTypeBuilder<Model.AccountInfoDetail> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.AccountInfoId)
                .HasColumnType("bigint");

            builder.Property(s => s.BusinessType)
                .HasColumnType("tinyint");

            builder.Property(s => s.OrderType)
                .HasColumnType("tinyint");

            builder.Property(s => s.BusinessOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.ExtNo)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.Income)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Expenditure)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Balance)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.AccountTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
        }
    }
}
