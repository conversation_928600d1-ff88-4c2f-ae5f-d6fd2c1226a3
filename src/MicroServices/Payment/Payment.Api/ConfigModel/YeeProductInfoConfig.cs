namespace Payment.Api.ConfigModel;

public class YeeProductInfoConfig
{
    public List<YeeProductInfoDto> ProductInfos { get; set; }
}

public class YeeProductInfoDto
{
    /// <summary>
    /// 产品码
    /// </summary>
    public string? ProductCode { get; set; }

    /// <summary>
    /// 计费策略 SINGLE_PERCENT:单笔百分比SINGLE_FIXED:单笔固定值FIXED_MIX_PERCENT:单笔固定值+单笔百分比PERCENT_MIX_CAP:单笔百分比+封顶值。
    /// </summary>
    public string? RateType { get; set; }

    /// <summary>
    /// <para>单笔百分比	计费策略为SINGLE_PERCENT、FIXED_MIX_PERCENT、PERCENT_MIX_CAP时，必填。PERCENT_MIX_CAP时，必填</para>
    /// <para>1、该字段单位：%；如0.5%，上传0.5即可。</para> 
    /// <para>2、规则:rate(0 - 100),整数位最多3位，小数位最多保留2位</para> 
    /// </summary>
    public decimal PercentRate { get; set; }

    /// <summary>
    /// 单笔固定值 计费策略为SINGLE_FIXED、FIXED_MIX_PERCENT、PERCENT_MIX_CAP时，必填。。
    /// 1、单位：元/笔；如5元/笔，上传5即可。
    /// 2、规则:0 小于等于 rate,整数位最多6位，小数位最多保留2位
    /// </summary>
    public decimal FixedRate { get; set; }

    /// <summary>
    /// 需要承担该商户交易/结算等产品手续费的商户对应的角色：如入驻商户，可上送 SETTLED_MERCHANT（入驻商户，即本身）、USER（用户）；
    /// 如标准商户，可上送 ORDINARY_MERCHANT（标准商户）、USER（用户）；
    /// 不传默认商户本身承担手续费。如需平台商（PLATFORM_MERCHANT）、服务商（SAAS_SERVICE_PROVIDER）承担手续费，需要联系销售申请。
    /// </summary>
    public string? Undertaker { get; set; }

    /// <summary>
    /// 手续费收取方式
    /// 根据商户实际业务情况上送即可，REAL_TIME（实收）、PREPAID_REAL（预付实扣）、UN_REAL_TIME（后收，部分行业商户不支持，以跟商务约定的实际情况为准）
    /// </summary>
    public string? PaymentMethod { get; set; }
}