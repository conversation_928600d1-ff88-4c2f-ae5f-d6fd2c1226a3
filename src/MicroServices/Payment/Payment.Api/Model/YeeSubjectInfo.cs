using EfCoreExtensions.EntityBase;

namespace Payment.Api.Model
{
    /// <summary>
    /// 商户主体信息
    /// </summary>
    public class YeeSubjectInfo : TenantBase
    {
        #region /*商户主体信息 MerchantSubjectInfo*/

        /// <summary>
        /// 商户签约类型	String	是	32	
        /// INDIVIDUAL(个体工商户)：一般为个体户、个体工商户、个体经营。
        /// ENTERPRISE(企业)：一般为有限公司、有限责任公司。
        /// INSTITUTION(事业单位)：包括国内各级、各类政府机构、事业单位等（如：公安、党 团、司法、交通、旅游、工商税务、市政、医疗、教育、学校等机构）。
        /// </summary>
        public string? SignType { get; set; } = "ENTERPRISE";

        /// <summary>
        /// 商户证件编号	String	是	64	
        /// 统一社会信用代码证编号、事业单位法人证书编号、社会团体证书编号等，与商户签约类型匹配。
        /// </summary>
        public string? LicenceNo { get; set; }

        /// <summary>
        /// 商户证件照片	String	是	256	
        /// 上传图片前需调用文件上传接口将文件上传至易宝服务器。（请提供证件副本）
        /// </summary>
        public string? LicenceUrl { get; set; }

        /// <summary>
        /// 商户签约名称	String	是	128	
        /// 与商户证件主体名称一致。
        /// </summary>
        public string? SignName { get; set; }

        /// <summary>
        /// 商户简称	String	是	128	
        /// 将在收银台页面或者支付完成页向买家展示。
        /// </summary>

        public string? ShortName { get; set; }

        /// <summary>
        /// 开户许可证编号	String	是	128	
        /// 开户许可证编号和开户许可证照片需要同时上传；如无开户许可证编号，可传基本存款账户编号
        /// </summary>
        public string? OpenAccountLicenceNo { get; set; }

        /// <summary>
        /// 开户许可证照片	String	是	256	
        /// 为增加商户入网审核通过率，请上传开户许可证；如无开户许可证，请上传基本存款账户信息表照片。上传图片前需调用文件上传接口将文件上传至易宝服务器。
        /// </summary>
        public string? OpenAccountLicenceUrl { get; set; }

        /// <summary>
        /// 手持营业执照在经营场所的照片	String	是	256	
        /// 上传图片前需调用文件上传接口将文件上传至易宝服务器。
        /// </summary>
        public string? HandLicenceUrl { get; set; }

        #endregion

        #region /*商户法人信息 MerchantCorporationInfo*/

        /// <summary>
        /// 法人姓名	String	是	128	请填写经营者/法人对应身份证件的姓名。
        /// </summary>
        public string? LegalName { get; set; }

        /// <summary>
        /// 法人证件类型	String	是	32	ID_CARD(法人身份证)
        /// PASSPORT(护照) HM_VISITORPASS(港澳居民往来内地通行证) TAIWAN(台胞证) SOLDIER(士兵证) OFFICERS(军官证)
        /// </summary>
        public string? LegalLicenceType { get; set; } = "ID_CARD";

        /// <summary>
        /// 法人证件号码	String	是	256	请填写经营者/法人对应身份证件号码。
        /// </summary>
        public string? LegalLicenceNo { get; set; }

        /// <summary>
        /// 法人证件正面照片	String	是	256	
        /// 请上传带有人像面的法人证件照片；上传图片前需调用文件上传接口将文件上传至易宝服务器。
        /// </summary>
        public string? LegalLicenceFrontUrl { get; set; }

        /// <summary>
        /// 法人证件反面照片	String	是	256	
        /// 如为身份证，请上传国徽面照片； 其余法人证件（如港澳台通行证）请上传人像面反面照片；
        /// 如该类型法人证件无反面（护照），再次上传正面照片即可。上传图片前需调用文件上传接口将文件上传至易宝服务器。
        /// </summary>
        public string? LegalLicenceBackUrl { get; set; }

        #endregion

        #region /*商户联系人信息 MerchantContactInfo*/

        /// <summary>
        /// 商户联系人姓名	String	是	512	
        /// 用于商户与易宝之间的业务联系，请按照真实联系人信息填写。
        /// </summary>
        public string? ContactName { get; set; }

        /// <summary>
        /// 商户联系人证件号码	String	否	256	
        /// 用于商户与易宝之间的业务联系，请按照真实联系人信息填写。不传默认法人证件号。
        /// </summary>
        public string? ContactLicenceNo { get; set; }

        /// <summary>
        /// 商户联系人手机号	String	是	256	
        /// 用于商户与易宝之间的业务联系，请按照真实联系人信息填写。
        /// </summary>
        public string? ContactMobile { get; set; }

        /// <summary>
        /// 商户联系人邮箱	String	是	256	
        /// 1.可能用于商户使用的产品/服务升级维护通知发送。2.可能用于商户通道报备。
        /// </summary>
        public string? ContactEmail { get; set; }

        /// <summary>
        /// 客服电话	String	否	64	
        /// 展示在订单支付详情，方便买家联系商家。
        /// </summary>
        public string? ServicePhone { get; set; }

        #endregion

        #region /*商户经营地址*/

        /// <summary>
        /// 商户实际经营地所在省	String	是	16	要求按照商户实际经营地址选择对应的省编号
        /// </summary>
        public long Province { get; set; }

        /// <summary>
        /// 商户实际经营地所在市	String	是	16	要求按照商户实际经营地址选择对应的市编号	
        /// </summary>
        public long City { get; set; }

        /// <summary>
        /// 商户实际经营地所在区	String	是	16	要求按照商户实际经营地址选择对应的市编号
        /// </summary>
        public long District { get; set; }

        /// <summary>
        /// 商户实际经营详细地址	String	是	1024	不需要再次上送省市区。
        /// </summary>
        public string? Address { get; set; }

        #endregion

        #region 商户结算账户信息 SettlementAccountInfo

        /// <summary>
        /// 结算方向 1、ACCOUNT(结算到支付账户)
        /// 2、BANKCARD(结算到银行账户，
        /// 如结算到结算账户时对公账户/单位结算卡账户名称系统默认处理为商户签约名称；借记卡/存折账户名称系统默认处理为商户经营者/法人姓名。）
        /// </summary>
        public string? SettlementDirection { get; set; }

        /// <summary>
        /// 银行账户类型
        /// 企业：对公账户/单位结算卡
        /// 个体户：对公账户/借记卡/存折UNIT_SETTLEMENT_CARD(单位结算卡)ENTERPRISE_ACCOUNT(对公账户)DEBIT_CARD(借记卡)PASSBOOK(存折)
        /// </summary>
        public string? BankAccountType { get; set; }

        /// <summary>
        /// 银行账户号码
        /// </summary>
        public string? BankCardNo { get; set; }

        /// <summary>
        /// 银行账户开户总行编码 
        /// </summary>
        public string? BankCode { get; set; }

        #endregion
    }
}
