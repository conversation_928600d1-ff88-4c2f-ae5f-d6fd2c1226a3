using Contracts.Common.Payment.Enums;
using EfCoreExtensions.EntityBase;

namespace Payment.Api.Model
{
    /// <summary>
    /// 订单支付流水
    /// </summary>
    public class OrderPaymentDetail : TenantBase
    {
        /// <summary>
        /// 订单支付id
        /// </summary>
        public long OrderPaymentId { get; set; }

        /// <summary>
        /// 流水号
        /// </summary>
        public string? ExtNo { get; set; }

        /// <summary>
        /// 收/付款方类型
        /// </summary>
        public PaymentUserType PaymentUserType { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 支付场景
        /// </summary>
        public PaymentScene PaymentScene { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
