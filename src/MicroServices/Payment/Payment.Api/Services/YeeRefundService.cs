using Cit.Payment.Yeepay.Service;
using Cit.Payment.Yeepay.Service.Request.Trade;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Microsoft.Extensions.Options;
using Payment.Api.ConfigModel;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Services
{
    public class YeeRefundService : IYeeRefundService
    {
        private readonly ITradeService _tradeService;
        private readonly IYeeMerConfigService _yeeMerConfigService;
        private readonly IAccountInfoService _accountInfoService;
        private readonly YeePaySetting _yeePaySetting;

        public YeeRefundService(ITradeService tradeService,
            IYeeMerConfigService yeeMerConfigService,
            IOptions<YeePaySetting> yeePaySetting,
            IAccountInfoService accountInfoService)
        {
            _tradeService = tradeService;
            _yeeMerConfigService = yeeMerConfigService;
            _accountInfoService = accountInfoService;
            _yeePaySetting = yeePaySetting.Value;
        }

        public async Task<OrderRefundOutDto> Refund(OrderRefundDto dto)
        {
            var yeeMerConfig = await _yeeMerConfigService.GetYeeMerConfig(dto.TenantId);
            if (yeeMerConfig.MerchantNoType == YeePaymentMerchantNoType.Agent)
            {
                var tenantAccountInfo = await _accountInfoService.GetTenantAccountInfo(dto.TenantId);
                if (tenantAccountInfo.FundAccountBalance < dto.RefundAmount)
                    return new OrderRefundOutDto
                    {
                        FailReason = "账户余额不足",
                        RefundStatus = RefundStatus.FAILED,
                    };
            }
            var merchantNo = yeeMerConfig.MerchantNo;
            RefundRequest request = new()
            {
                MerchantNo = merchantNo,
                OrderId = dto.OrderPaymentId.ToString(),
                RefundRequestId = dto.OrderRefundId.ToString(),
                UniqueOrderNo = dto.UniqueOrderNo ?? "",
                RefundAmount = dto.RefundAmount,
                Description = dto.Description,
                NotifyUrl = _yeePaySetting.YeeRefundNotifyUri//退款通知uri
            };
            var response = await _tradeService.Refund(request);
            var result = response.Result;
            //OPR12002订单不存在 特殊处理兼容旧版易宝订单支付
            if (result?.Code == "OPR12002")
            {
                request.OrderId = dto.OrderId;
                response = await _tradeService.Refund(request);
                result = response.Result;
            }
            if (result?.IsSuccess is not true)
            {
                return new OrderRefundOutDto
                {
                    RefundStatus = RefundStatus.FAILED,
                    FailReason = result?.Message ?? response.SubMessage
                };
            }
            _ = Enum.TryParse(result.Status, out RefundStatus refundStatus);
            return new OrderRefundOutDto
            {
                RefundStatus = refundStatus,
                FailReason = result.Message,
                UniqueRefundNo = result.UniqueRefundNo,
                RefundMerchantFee = result.RefundMerchantFee
            };
        }

    }
}
