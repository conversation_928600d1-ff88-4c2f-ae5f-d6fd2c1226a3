using Contracts.Common.Payment.DTOs.OfflineReceipt;
using Contracts.Common.Payment.Enums;
using FluentValidation;

namespace Payment.Api.Services.Validator
{
    public class OfflineReceiptOrderInputValidator : AbstractValidator<OfflineReceiptOrderInput>
    {
        public OfflineReceiptOrderInputValidator()
        {
            RuleFor(x => x.OfflineReceiptId).GreaterThan(0);
            RuleFor(x => x.OrderAmount).GreaterThan(0);
        }
    }

    public class OfflineReceiptOrderPayInputValidator : AbstractValidator<OfflineReceiptOrderPayInput>
    {
        public OfflineReceiptOrderPayInputValidator()
        {
            RuleFor(x => x.OfflineReceiptOrderId).GreaterThan(0);

            RuleFor(x => x.PayType).IsInEnum().Must(x => x == PayType.YeePay || x == PayType.UserStoredValueCardPay);
            RuleFor(x => x.PayChannel).IsInEnum();
            RuleFor(x => x.PayWay).IsInEnum();
        }
    }
}
