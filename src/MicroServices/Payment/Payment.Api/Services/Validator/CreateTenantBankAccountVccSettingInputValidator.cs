using Contracts.Common.Payment.DTOs.TenantBankAccountVccSetting;
using FluentValidation;

namespace Payment.Api.Services.Validator;

public class CreateTenantBankAccountVccSettingInputValidator : AbstractValidator<CreateInput>
{
    public CreateTenantBankAccountVccSettingInputValidator()
    {
        RuleFor(x => x.SupplierType).NotEmpty().IsInEnum();
        RuleFor(x => x.SupplierName).NotEmpty().Length(1, 64);
        RuleFor(x => x.UserName).NotEmpty().Length(1, 64);
        RuleFor(x => x.Password).NotEmpty().Length(1, 64);
        RuleFor(x => x.OrgBankId).NotEmpty().Length(1, 100);
        RuleFor(x => x.OrgCompanyId).NotEmpty().Length(1, 100);
    }
}
