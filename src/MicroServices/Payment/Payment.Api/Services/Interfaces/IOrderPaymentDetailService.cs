using Contracts.Common.Payment.DTOs;
using EfCoreExtensions.Abstract;

namespace Payment.Api.Services.Interfaces
{
    /// <summary>
    /// 订单支付流水
    /// </summary>
    public interface IOrderPaymentDetailService
    {
        /// <summary>
        /// 支付流水
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<OrderPaymentDetailOutput>> GetOrderPaymentDetails(OrderPaymentDetailInput input);

        /// <summary>
        /// 账户对账单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<GetOrderStatementOutput>> GetOrderStatements(GetOrderStatementInput input);

        /// <summary>
        /// 账户对账单[导出数据，不分页]
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<IEnumerable<GetOrderStatementOutput>> GetOrderStatementExportData(GetOrderStatementInput input);
    }
}
