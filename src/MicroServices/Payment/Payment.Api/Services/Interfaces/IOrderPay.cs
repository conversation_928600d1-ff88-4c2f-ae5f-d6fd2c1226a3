using Contracts.Common.Payment.DTOs;

namespace Payment.Api.Services.Interfaces
{
    /// <summary>
    /// 支付方法接口
    /// </summary>
    public interface IOrderPay
    {
        Task<OrderPayOutDto> Pay(OrderPayDto orderPayDto);

        /// <summary>
        /// 计算支付手续费 实现了 IOrderPay 接口的类可以选择性地重写 CalculateFee 方法以提供特定的手续费计算逻辑
        /// </summary>
        /// <param name="calculatePaymentFeeDto">订单支付信息</param>
        /// <returns>手续费金额</returns>
        ValueTask<decimal> CalculatePaymentFee(CalculatePaymentFeeDto calculatePaymentFeeDto)
        {
            return ValueTask.FromResult(0m);
        }
    }
}
