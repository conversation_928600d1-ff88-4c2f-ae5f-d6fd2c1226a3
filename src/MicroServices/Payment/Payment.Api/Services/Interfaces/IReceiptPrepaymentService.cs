using Contracts.Common.Payment.DTOs.ReceiptPrepayment;
using Contracts.Common.Payment.Messages;
using EfCoreExtensions.Abstract;

namespace Payment.Api.Services.Interfaces;

public interface IReceiptPrepaymentService : IOrderPay, IOrderRefund
{
    /// <summary>
    /// 查询分销商预收款信息
    /// </summary>
    /// <param name="agencyIds"></param>
    /// <returns></returns>
    Task<IEnumerable<DetailOutput>> GetDetails(params long[] agencyIds);

    /// <summary>
    /// 搜索流水明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchFlowOutput>> SearchFlows(SearchFlowInput input);

    Task<List<SearchFlowOutput>> SearchFlows(QueryFlowsInput input);

    /// <summary>
    /// 预收款充值
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task<ReceiptPrepaymentRechargeFlowMessage> Recharge(ReceiptPrepaymentRechargeMessage receive);

    /// <summary>
    /// 预收款退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RefundOutput> Refund(RefundInput input);

    /// <summary>
    /// 线下收款方式 确认收款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<OfflineRefundConfirmOutput> OfflineRefundConfirm(OfflineRefundConfirmInput input);

    /// <summary>
    /// 退款结果处理
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task RefundResult(OrderRefundResultHandleMessage command);

    Task<List<GetRefundListOutput>> GetRefundList(GetRefundListInput input);

}
