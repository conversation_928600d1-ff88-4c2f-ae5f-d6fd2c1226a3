using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Messages;
using EfCoreExtensions.Abstract;

namespace Payment.Api.Services.Interfaces
{
    public interface IDarenWithdrawalsService
    {
        /// <summary>
        /// 达人提现申请
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task WithdrawalApply(DarenWithdrawalApplyMessage receive);

        /// <summary>
        /// 达人提现平台付款结果处理
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        Task DarenWithdrawalResult(AccountPayOrderResultMessage command);

        /// <summary>
        /// 达人提现申请列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<DarenWithdrawalTransferOrderOutput>> GetDarenWithdrawalTransferOrders(GetDarenWithdrawalTransferOrdersInput input);

        /// <summary>
        /// 达人提现重新发起转账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task TransferOrder(DarenWithdrawalTransferInput input);
    }
}
