using Contracts.Common.Payment.DTOs.AccountTransferOrder;
using Contracts.Common.Payment.Messages;

namespace Payment.Api.Services.Interfaces
{
    public interface IAccountTransferOrderService
    {
        /// <summary>
        /// 转账下单
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task TransferOrder(TransferOrderMessage receive);

        /// <summary>
        /// 转账子商户下单
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task TransferOrderToMer(TransferOrderMessage receive);

        /// <summary>
        /// 转账查询
        /// </summary>
        /// <param name="transferOrderNo">转账单号</param>
        /// <param name="fromMerchatNo">转出方商编</param>
        /// <returns></returns>
        Task TransferOrderQuery(long transferOrderNo, string fromMerchatNo);

        /// <summary>
        /// 搜索转账单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<IEnumerable<SearchOutput>> Search(SearchInput input);
    }
}
