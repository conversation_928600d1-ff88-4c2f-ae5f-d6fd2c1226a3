using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Messages;

namespace Payment.Api.Services.Interfaces
{
    public interface IAccountPayOrderService
    {
        /// <summary>
        /// 订阅 - 付款下单
        /// </summary>
        /// <param name="receive"></param>
        /// <returns></returns>
        Task PayOrder(AccountPayOrderMessage receive);

        /// <summary>
        /// 付款下单结果处理
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task AccountPayOrderResult(AccountPayOrderResultInput input);
    }
}
