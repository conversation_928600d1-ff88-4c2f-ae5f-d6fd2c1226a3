using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OfflineReceipt;
using Contracts.Common.Payment.Enums;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Services
{
    public class OfflineReceiptOrderFlowService : IOfflineReceiptOrderFlowService
    {
        private readonly CustomDbContext _dbContext;

        public OfflineReceiptOrderFlowService(CustomDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        #region 获取收款码明细

        public async Task<PagingModel<OfflineReceiptOrderFlowOutput, OfflineReceiptOrderFlowStat>> GetOfflineReceiptOrderFlows(GetOfflineReceiptOrderFlowsInput input, long supplierId)
        {
            var query = from flow in _dbContext.OfflineReceiptOrderFlows.AsNoTracking()
                        join order in _dbContext.OfflineReceiptOrders.AsNoTracking()
                        on flow.OfflineReceiptOrderId equals order.Id
                        where order.SupplierId == supplierId
                        && flow.CreateTime >= input.BeginDate
                        && flow.CreateTime < input.EndDate.Date.AddDays(1)
                        select new OfflineReceiptOrderFlowOutput
                        {
                            OrderInfo = new OfflineReceiptOrderInfo
                            {
                                Id = order.Id,
                                OrderAmount = order.OrderAmount,
                                Title = order.Title,
                                PayerInfo = new OfflineReceiptOrderPayerInfoDto
                                {
                                    UserID = order.PayerInfo.UserID,
                                    NickName = order.PayerInfo.NickName
                                },
                                PayTime = order.PayTime,
                                Status = order.Status
                            },
                            FlowType = flow.FlowType,
                            Amount = flow.Amount,
                            PaymentFee = flow.PaymentFee,
                            CreateTime = flow.CreateTime,
                            Remark = flow.Remark,
                            OperatorName = flow.OperatorName
                        };

            var result = await query
                .OrderByDescending(x => x.CreateTime)
                .PagingAsync(input.PageIndex, input.PageSize, x => x);

            var stat = await query
                .GroupBy(x => new { x.FlowType, x.OrderInfo.Status })
                .Select(x => new
                {
                    x.Key.FlowType,
                    x.Key.Status,
                    Amount = x.Sum(s => s.Amount),
                    PaymentFee = x.Sum(s => s.PaymentFee),
                    Count = x.Count()
                })
                .ToListAsync();
            OfflineReceiptOrderFlowStat flowStat = new()
            {
                ReceivedAmount = stat
                    .Where(x =>
                        x.FlowType == OfflineReceiptOrderFlowType.Receive)
                    .Sum(x => x.Amount),
                ReceivedCount = stat
                    .Where(x =>
                        x.FlowType == OfflineReceiptOrderFlowType.Receive)
                    .Sum(x => x.Count),
                PaymentFee = stat
                    .Where(x =>
                        x.FlowType == OfflineReceiptOrderFlowType.Receive
                        && x.Status == OfflineReceiptOrderStatus.Received)
                    .Sum(x => x.PaymentFee),
                RefundAmount = stat
                    .Where(x =>
                        x.FlowType == OfflineReceiptOrderFlowType.Refund)
                    .Sum(x => x.Amount),
                RefundCount = stat
                    .Where(x =>
                        x.FlowType == OfflineReceiptOrderFlowType.Refund)
                    .Sum(x => x.Count)
            };

            return new PagingModel<OfflineReceiptOrderFlowOutput, OfflineReceiptOrderFlowStat>
            {

                Data = result.Data,
                PageSize = input.PageSize,
                PageIndex = input.PageIndex,
                Total = result.Total,
                Supplement = flowStat
            };
        }

        #endregion

        public async Task<PagingModel<OfflineReceiptOrderSearchOutput>> Search(OfflineReceiptOrderSearchInput input)
        {

            var query = _dbContext.OfflineReceiptOrders.AsNoTracking()
                .Where(x => x.Status != OfflineReceiptOrderStatus.WaitingForPay &&
                         x.Status != OfflineReceiptOrderStatus.Canceled)//排除待支付 未支付已取消
                .WhereIF(input.BeginDate.HasValue,
                    x => x.CreateTime >= input.BeginDate!.Value)
                .WhereIF(input.EndDate.HasValue,
                    x => x.CreateTime < input.EndDate!.Value.Date.AddDays(1))
                .WhereIF(input.SupplierId.HasValue,
                    x => x.SupplierId == input.SupplierId)
                .WhereIF(input.OfflineReceiptId.HasValue,
                    x => x.OfflineReceiptId == input.OfflineReceiptId);
            switch (input.SearchType)
            {
                case 1:
                    if (!string.IsNullOrWhiteSpace(input.Keyword))
                        query = query.Where(x => x.PayerInfo.NickName.Contains(input.Keyword));
                    break;
                case 2:
                    if (long.TryParse(input.Keyword, out long offlineReceiptOrderId))
                    {
                        query = query.Where(x => x.Id == offlineReceiptOrderId);
                    }
                    break;
                case 3:
                    if (long.TryParse(input.Keyword, out long withdrawalId))
                    {
                        query = query.Where(x => x.WithdrawalId != null && x.WithdrawalId == withdrawalId);
                    }
                    break;
            }
            var result = await query
                .OrderByDescending(x => x.CreateTime)
                .PagingAsync(input.PageIndex, input.PageSize,
                 x => new OfflineReceiptOrderSearchOutput
                 {
                     OfflineReceiptOrderId = x.Id,
                     OfflineReceiptId = x.OfflineReceiptId,
                     SupplierId = x.SupplierId,
                     SupplierName = x.SupplierName,
                     Title = x.Title,
                     OrderAmount = x.OrderAmount,
                     DiscountAmount = x.DiscountAmount,
                     DiscountName = x.DiscountName,
                     PaymentFee = x.PaymentFee,
                     SettleAmount = x.OrderAmount - x.DiscountAmount - x.PaymentFee,
                     PayChannel = x.PayChannel,
                     PayerInfo = new OfflineReceiptOrderPayerInfoDto
                     {
                         CustomerUserId = x.PayerInfo.CustomerUserId,
                         UserID = x.PayerInfo.UserID,
                         NickName = x.PayerInfo.NickName
                     },
                     PayTime = x.PayTime,
                     PayType = x.PayType,
                     Remark = x.Remark,
                     CreateTime = x.CreateTime,
                     RefundOrderId = x.RefundOrderId,
                     WithdrawalId = x.WithdrawalId,
                     Status = x.Status
                 });
            return result;
        }
    }
}
