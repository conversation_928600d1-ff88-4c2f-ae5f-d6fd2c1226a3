using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.OrderRefund;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Services;

public class OrderRefundService : IOrderRefundService
{
    private readonly CustomDbContext _dbContext;
    private readonly IOrderPaymentService _orderPaymentService;
    private readonly Func<PayType, IOrderRefund> _orderRefundFunc;
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;

    public OrderRefundService(CustomDbContext dbContext,
        IOrderPaymentService orderPaymentService,
        Func<PayType, IOrderRefund> orderRefundFunc,
        IMapper mapper,
        ICapPublisher capPublisher)
    {
        _dbContext = dbContext;
        _orderPaymentService = orderPaymentService;
        _orderRefundFunc = orderRefundFunc;
        _mapper = mapper;
        _capPublisher = capPublisher;
    }

    #region 退款

    [UnitOfWork]
    public async Task<OrderRefundResultMessage> Refund(OrderRefundMessage receive)
    {
        var orderRefund = await OrderRefundGenerated(receive);
        //非退款中或退款失败 返回结果
        if (orderRefund.RefundStatus != RefundStatus.PROCESSING && orderRefund.RefundStatus != RefundStatus.FAILED)
        {
            return _mapper.Map<OrderRefundResultMessage>(orderRefund);
        }
        /*支付平台退款*/
        IOrderRefund refund = _orderRefundFunc(orderRefund.PayType.Value);
        var orderRefundDto = new OrderRefundDto
        {
            OrderRefundType = orderRefund.OrderRefundType,
            Description = orderRefund.Description,
            OrderId = orderRefund.OrderId.ToString(),
            OrderPaymentType = orderRefund.OrderPaymentType,
            OrderType = orderRefund.OrderType,
            OrderRefundId = orderRefund.RefundOrderId.ToString(),
            RefundAmount = decimal.Round(orderRefund.RefundAmount, 2),
            TenantId = orderRefund.TenantId,
            UniqueOrderNo = orderRefund.UniqueOrderNo
        };
        OrderRefundOutDto result = await refund.Refund(orderRefundDto);

        /*更新退款单状态*/
        await RefundResult(new OrderRefundResultInput
        {
            FailReason = result.FailReason,
            RefundStatus = result.RefundStatus,
            RefundOrderId = orderRefund.RefundOrderId.ToString(),
            UniqueRefundNo = result.UniqueRefundNo,
            ReturnMerchantFee = result.RefundMerchantFee
        });

        var command = new OrderRefundResultMessage
        {
            RefundOrderId = orderRefund.RefundOrderId,
            RefundStatus = result.RefundStatus,
            FailReason = result.FailReason,
            UniqueRefundNo = result.UniqueRefundNo
        };
        return command;
    }

    [UnitOfWork]
    public async Task<OrderRefundResultMessage> Refund(OrderRefundDetailDto orderRefund)
    {
        //非退款中或退款失败 返回结果
        if (orderRefund.RefundStatus != RefundStatus.PROCESSING && orderRefund.RefundStatus != RefundStatus.FAILED)
        {
            return _mapper.Map<OrderRefundResultMessage>(orderRefund);
        }
        /*支付平台退款*/
        IOrderRefund refund = _orderRefundFunc(orderRefund.PayType!.Value);
        var orderRefundDto = new OrderRefundDto
        {
            OrderPaymentId = orderRefund.OrderPaymentId,
            OrderRefundType = orderRefund.OrderRefundType,
            Description = orderRefund.Description,
            OrderId = orderRefund.OrderId.ToString(),
            OrderPaymentType = orderRefund.OrderPaymentType,
            OrderType = orderRefund.OrderType,
            OrderRefundId = orderRefund.RefundOrderId.ToString(),
            RefundAmount = decimal.Round(orderRefund.RefundAmount + orderRefund.RefundPaymentFee, 2),
            TenantId = orderRefund.TenantId,
            UniqueOrderNo = orderRefund.UniqueOrderNo
        };
        OrderRefundOutDto result = await refund.Refund(orderRefundDto);

        /*更新退款单状态*/
        await RefundResult(new OrderRefundResultInput
        {
            FailReason = result.FailReason,
            RefundStatus = result.RefundStatus,
            RefundOrderId = orderRefund.RefundOrderId.ToString(),
            UniqueRefundNo = result.UniqueRefundNo,
            ReturnMerchantFee = result.RefundMerchantFee
        });

        var command = new OrderRefundResultMessage
        {
            RefundOrderId = orderRefund.RefundOrderId,
            RefundStatus = result.RefundStatus,
            FailReason = result.FailReason,
            UniqueRefundNo = result.UniqueRefundNo
        };
        return command;
    }

    [UnitOfWork]
    public async Task<OrderRefundDetailDto> OrderRefundGenerated(OrderRefundMessage receive)
    {
        /*查询支付成功的信息*/
        var orderPayment = await _orderPaymentService.GetPaidOrderPayment(receive.TenantId,
            receive.OrderId, receive.PayType);

        var orderRefund = await _dbContext.OrderRefunds
            .IgnoreQueryFilters()
            .Where(r => r.TenantId == receive.TenantId
                       && r.RefundOrderId == receive.RefundOrderId
                       && r.OrderId == receive.OrderId)
            .WhereIF(receive.PayType.HasValue, r => r.PayType == receive.PayType!.Value)//限定支付类型
            .FirstOrDefaultAsync();

        if (orderRefund is not null)
        {
            orderRefund.RefundStatus = RefundStatus.PROCESSING;//处理中

            var refund = _mapper.Map<OrderRefundDetailDto>(orderRefund);
            refund.OrderPaymentId = orderPayment.Id;
            refund.OrderPaymentType = orderPayment.OrderPaymentType;
            refund.OrderType = orderPayment.OrderType;
            refund.OrderType = orderPayment.OrderType;
            refund.PaymentFee = orderPayment.PaymentFee;
            return refund;
        }

        /*生成退款单 如果已分账 则按比例分账回退*/
        orderRefund = new OrderRefund()
        {
            OrderRefundType = receive.OrderRefundType,
            PayType = receive.PayType,
            OrderId = receive.OrderId,
            RefundOrderId = receive.RefundOrderId,
            Description = receive.Description,
            RefundAmount = receive.RefundAmount,
            RefundStatus = RefundStatus.PROCESSING
        };
        if (!orderRefund.PayType.HasValue)
            orderRefund.PayType = orderPayment.PayType;
        if (string.IsNullOrWhiteSpace(orderRefund.UniqueOrderNo))
            orderRefund.UniqueOrderNo = orderPayment.UniqueOrderNo;
        orderRefund.SetTenantId(receive.TenantId);
        //全额退款 退手续费
        if (orderRefund.RefundAmount == orderPayment.OrderAmount && orderPayment.PaymentFee > 0)
            orderRefund.RefundPaymentFee = orderPayment.PaymentFee;

        await _dbContext.AddAsync(orderRefund);

        //是否易宝分账 存在分账则分账回退
        if (orderPayment.PayType == PayType.YeePay
            && orderPayment.FundProcessType == FundProcessType.DELAY_SETTLE)
        {
            var command = new OrderProfitDivideBackMessage
            {
                Percentage = Math.Round(orderRefund.RefundAmount / orderPayment.OrderAmount, 2),//回退比例
                DivideBackReason = "订单退款",
                OrderId = orderRefund.OrderId,
                TenantId = receive.TenantId,
                RefundOrderId = orderRefund.RefundOrderId,
            };
            await _capPublisher.PublishAsync(CapTopics.Payment.ProfitDivideBackApply, command);
        }

        var result = _mapper.Map<OrderRefundDetailDto>(orderRefund);
        result.OrderPaymentId = orderPayment.Id;
        result.OrderPaymentType = orderPayment.OrderPaymentType;
        result.OrderType = orderPayment.OrderType;
        result.PaymentFee = orderPayment.PaymentFee;
        return result;
    }

    #endregion

    #region 退款结果处理

    [UnitOfWork]
    public async Task RefundResult(OrderRefundResultInput input)
    {
        long orderRefundId = long.Parse(input.RefundOrderId);
        var orderRefund = await _dbContext.OrderRefunds.AsQueryable()
            .IgnoreQueryFilters()
            .Where(r => r.RefundOrderId == orderRefundId)
            .FirstOrDefaultAsync();

        if (orderRefund is null)
            throw new BusinessException("退款单不存在");

        if (orderRefund.RefundStatus == RefundStatus.SUCCESS)
            return;//订单已退款成功

        if (!string.IsNullOrWhiteSpace(input.UniqueRefundNo))
            orderRefund.UniqueRefundNo = input.UniqueRefundNo;

        if (input.ReturnMerchantFee.HasValue)
            orderRefund.ReturnMerchantFee = input.ReturnMerchantFee.Value;

        //退款状态是否变更
        var statusChange = orderRefund.RefundStatus != input.RefundStatus;

        orderRefund.RefundStatus = input.RefundStatus;
        orderRefund.FailReason = input.FailReason;

        /*查询支付成功的信息*/
        var orderPayment = await _orderPaymentService.GetPaidOrderPayment(orderRefund.TenantId,
            orderRefund.OrderId,
            orderRefund.PayType);
        if (orderPayment is null)
            throw new BusinessException("订单支付信息不存在");

        if (statusChange && orderRefund.RefundStatus == RefundStatus.SUCCESS)
        {
            orderRefund.FinishTime = DateTime.Now;//退款完结时间
            //新增支付流水
            var orderPaymentDetail = new OrderPaymentDetail
            {
                OrderPaymentId = orderPayment.Id,
                PaymentScene = PaymentScene.OrderRefund,
                Amount = -orderRefund.RefundAmount,
                ExtNo = orderRefund.UniqueRefundNo,
                PaymentUserType = PaymentUserType.Individual
            };
            orderPaymentDetail.SetTenantId(orderRefund.TenantId);
            await _dbContext.AddAsync(orderPaymentDetail);
        }

        var resultStatus = new RefundStatus[] { RefundStatus.SUCCESS, RefundStatus.FAILED };
        //退款单事件发布条件：(状态变更 && 最终退款状态)
        bool needStatusChangePublish = statusChange && resultStatus.Contains(orderRefund.RefundStatus);
        if (needStatusChangePublish)
        {
            //订单退款结果事件
            var command = new OrderRefundResultHandleMessage
            {
                TenantId = orderRefund.TenantId,
                RefundOrderId = orderRefund.RefundOrderId,
                OrderId = orderRefund.OrderId,
                IsSuccess = orderRefund.RefundStatus == RefundStatus.SUCCESS,
                FailedReason = orderRefund.FailReason,
                ExtRefundNo = orderRefund.UniqueRefundNo,
                RefundAmount = orderRefund.RefundAmount
            };
            string publishName = orderRefund.OrderRefundType switch
            {
                OrderRefundType.Order => orderPayment.OrderPaymentType switch
                {
                    OrderPaymentType.OfflineReceiptOrderPay => CapTopics.Payment.OfflineReceiptOrderRefundResult,
                    _ => CapTopics.Order.RefundResult
                },
                OrderRefundType.OffsetOrder => CapTopics.Order.OffsetOrderRefundResult,
                OrderRefundType.ReceiptPrepayment => CapTopics.Payment.ReceiptPrepaymentRefundResult,
                _ => string.Empty
            };
            if (!string.IsNullOrWhiteSpace(publishName))
            {
                await _capPublisher.PublishAsync(publishName, command);
            }
            if (orderPayment.PayType == PayType.YeePay
                && orderRefund.RefundStatus == RefundStatus.SUCCESS
                && orderRefund.FinishTime.HasValue)
            {
                var income = orderRefund.ReturnMerchantFee;
                var back = await _dbContext.ProfitDivideBacks.IgnoreQueryFilters()
                    .Where(x => x.TenantId == orderRefund.TenantId
                    && x.RefundOrderId == orderRefund.RefundOrderId
                    && x.DivideBackStatus != DivideBackStatus.FAIL)
                    .Select(x => new { x.Id, x.UniqueDivideBackNo })
                    .FirstOrDefaultAsync();
                if (back is not null)
                {
                    var backAmount = await _dbContext.ProfitDivideBackDetails.IgnoreQueryFilters()
                        .Where(x => x.DivideBackId == back.Id)
                        .Select(x => x.Amount)
                        .SumAsync();
                    income += backAmount;
                }
                await _capPublisher.PublishAsync(CapTopics.Payment.AccountInfoDetailRecord,
                    new AccountInfoDetailRecordMessage
                    {
                        TenantId = orderRefund.TenantId,
                        BusinessType = AccountBusinessType.OrderDivideBack,
                        BusinessOrderId = orderPayment.OrderId,
                        OrderType = orderPayment.OrderType,
                        ExtNo = back?.UniqueDivideBackNo,
                        Income = income,
                        AccountTime = orderRefund.FinishTime!.Value.AddSeconds(-1)
                    });

                AccountBusinessType? businessType = orderPayment.OrderPaymentType switch
                {
                    OrderPaymentType.OrderPay => AccountBusinessType.OrderRefund,
                    OrderPaymentType.ReservationOrderPay => AccountBusinessType.ReservationOrderRefund,
                    OrderPaymentType.OfflineReceiptOrderPay => AccountBusinessType.OfflineReceiptPayRefund,
                    OrderPaymentType.AgencyCreditCharge => AccountBusinessType.AgencyCreditChargeRefund,
                    OrderPaymentType.GroupBookingOrder => AccountBusinessType.OrderRefund,
                    _ => null
                };
                if (businessType is not null)
                    await _capPublisher.PublishAsync(CapTopics.Payment.AccountInfoDetailRecord,
                        new AccountInfoDetailRecordMessage
                        {
                            TenantId = orderPayment.TenantId,
                            BusinessType = businessType.Value,
                            BusinessOrderId = orderPayment.OrderId,
                            OrderType = orderPayment.OrderType,
                            Expenditure = orderRefund.RefundAmount,
                            ExtNo = orderRefund.UniqueOrderNo,
                            AccountTime = orderRefund.FinishTime!.Value
                        });
            }
        }
    }

    #endregion

    public async Task<List<SearchOrderRefundOutput>> SearchOrderRefunds(SearchOrderRefundInput input)
    {
        var query = _dbContext.OrderRefunds
            .Join(_dbContext.OrderPayments.IgnoreQueryFilters(),
            r => r.OrderId, p => p.OrderId, (r, p) => new
            {
                OrderRefund = r,
                OrderPayment = p
            })
            .Where(x => x.OrderPayment.PayStatus == PayStatus.Paid && x.OrderPayment.PayType == x.OrderRefund.PayType)
            .WhereIF(input.PayType.HasValue, x => x.OrderRefund.PayType == input.PayType!.Value)
            .WhereIF(input.OrderPaymentTypes?.Length is > 0, x => input.OrderPaymentTypes!.Contains(x.OrderPayment.OrderPaymentType))
            .WhereIF(input.RefundStatus.HasValue, x => x.OrderRefund.RefundStatus == input.RefundStatus!.Value)
            .WhereIF(input.BeginDate.HasValue, x => x.OrderRefund.CreateTime >= input.BeginDate!.Value)
            .WhereIF(input.EndDate.HasValue, x => x.OrderRefund.CreateTime < input.EndDate!.Value.AddDays(1))
            .WhereIF(input.OrderIds?.Length is > 0, x => input.OrderIds!.Contains(x.OrderRefund.OrderId))
            .OrderBy(x => x.OrderRefund.CreateTime)
            .Select(x => new SearchOrderRefundOutput
            {
                RefundOrderId = x.OrderRefund.RefundOrderId,
                OrderId = x.OrderRefund.OrderId,
                PayType = x.OrderPayment.PayType,
                OrderPaymentType = x.OrderPayment.OrderPaymentType,
                OrderType = x.OrderPayment.OrderType,
                PayChannel = x.OrderPayment.PayChannel,
                PayWay = x.OrderPayment.PayWay,
                UniqueOrderNo = x.OrderRefund.UniqueOrderNo,
                RefundAmount = x.OrderRefund.RefundAmount,
                OrderRefundType = x.OrderRefund.OrderRefundType,
                Description = x.OrderRefund.Description,
                UniqueRefundNo = x.OrderRefund.UniqueRefundNo,
                ReturnMerchantFee = x.OrderRefund.ReturnMerchantFee,
                RefundStatus = x.OrderRefund.RefundStatus,
                FailReason = x.OrderRefund.FailReason,
                CreateTime = x.OrderRefund.CreateTime,
                FinishTime = x.OrderRefund.FinishTime
            });
        return await query.ToListAsync();
    }
}
