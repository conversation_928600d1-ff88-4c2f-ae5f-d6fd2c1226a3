using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class AccountPayOrderController : ControllerBase
{
    private readonly IAccountPayOrderService _accountPayOrderService;

    public AccountPayOrderController(IAccountPayOrderService accountPayOrderService)
    {
        _accountPayOrderService = accountPayOrderService;
    }

    #region CapSubscribe

    [NonAction]
    [CapSubscribe(CapTopics.Payment.AccountPayOrder)]
    public async Task PayOrder(AccountPayOrderMessage receive)
    {
        await _accountPayOrderService.PayOrder(receive);
    }

    #endregion
}
