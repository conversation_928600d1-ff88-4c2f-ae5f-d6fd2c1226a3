using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.AccountInfo;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class AccountInfoController : ControllerBase
{
    private readonly IAccountInfoService _accountInfoService;

    public AccountInfoController(IAccountInfoService accountInfoService)
    {
        _accountInfoService = accountInfoService;
    }

    /// <summary>
    /// 获取商户支付账户信息
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(TenantAccountInfoOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get(long tenantId)
    {
        var result = await _accountInfoService.GetTenantAccountInfo(tenantId);
        return Ok(result);
    }

    /// <summary>
    /// 获取主商户账户信息
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(AccountInfoOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetParentAccountInfo()
    {
        var result = await _accountInfoService.GetParentAccountInfo();
        return Ok(result);
    }

    /// <summary>
    /// 账户交易明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchDetailsOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchDetails(SearchDetailsInput input)
    {
        var result = await _accountInfoService.SearchDetails(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取所有账户交易明细(excel导出使用)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<SearchDetailsOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchAccountInfoDetailsList(SearchDetailsInput input)
    {
        var result = await _accountInfoService.SearchAccountInfoDetailsList(input);
        return Ok(result);
    }


    #region CapSubscribe

    /// <summary>
    /// 记录账户交易明细
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Payment.AccountInfoDetailRecord)]
    public async Task AccountInfoDetailRecord(AccountInfoDetailRecordMessage receive)
    {
        await _accountInfoService.AccountInfoDetailRecord(receive);
    }

    #endregion
}
