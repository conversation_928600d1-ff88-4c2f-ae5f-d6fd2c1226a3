using Contracts.Common.WeChat.DTOs.WechatAuthorizer;
using Microsoft.AspNetCore.Mvc;
using WeChat.Api.Services.Interfaces;

namespace WeChat.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class WechatAuthorizerController : ControllerBase
{
    private readonly IWechatAuthorizerService _wechatAuthorizerService;

    public WechatAuthorizerController(IWechatAuthorizerService wechatAuthorizerService)
    {
        _wechatAuthorizerService = wechatAuthorizerService;
    }

    /// <summary>
    /// 取消授权
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> UnAuthorized(UnAuthorizedInput input)
    {
        await _wechatAuthorizerService.UnAuthorized(input);
        return Ok();
    }
}
