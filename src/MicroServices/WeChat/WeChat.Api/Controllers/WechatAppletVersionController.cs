using Contracts.Common.WeChat.DTOs.WechatAppletVersion;
using Contracts.Common.WeChat.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using WeChat.Api.Services.Interfaces;

namespace WeChat.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class WechatAppletVersionController : ControllerBase
{
    private readonly IWechatAppletVersionService _wechatAppletVersionService;

    public WechatAppletVersionController(IWechatAppletVersionService wechatAppletVersionService)
    {
        _wechatAppletVersionService = wechatAppletVersionService;
    }

    /// <summary>
    /// 搜索小程序版本列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchVersionOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        var result = await _wechatAppletVersionService.Search(input);
        return Ok(result);
    }

    /// <summary>
    /// 提交代码列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<CommitSearchVersionOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CommitSearch(CommitSearchInput input)
    {
        var result = await _wechatAppletVersionService.CommitSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 审核版本列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<AuditSearchVersionOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AuditSearch(AuditSearchInput input)
    {
        var result = await _wechatAppletVersionService.AuditSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 发布版本列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<ReleaseSearchVersionOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ReleaseSearch(ReleaseSearchInput input)
    {
        var result = await _wechatAppletVersionService.ReleaseSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 小程序版本数量统计
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(WechatAppletVersionStatOuptput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Stat()
    {
        var result = await _wechatAppletVersionService.WechatAppletVersionStat();
        return Ok(result);
    }

    /// <summary>
    /// 提交代码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> CommitCode(CommitCodeInput input)
    {
        await _wechatAppletVersionService.CommitCode(input);
        return Ok();
    }

    /// <summary>
    /// 获取体验版二维码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> GetTrialQRCode([FromQuery] GetTrialQRCodeInput input)
    {
        var result = await _wechatAppletVersionService.GetTrialQRCode(input);
        return File(result, "image/jpeg");
    }

    /// <summary>
    /// 审核版本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> AuditVersion(AuditVersionInput input)
    {
        await _wechatAppletVersionService.AuditVersion(input);
        return Ok();
    }

    /// <summary>
    /// 获取永久图片素素材
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IActionResult> GetImageMaterial([FromQuery] GetImageMaterialInput input)
    {
        var result = await _wechatAppletVersionService.GetImageMaterial(input);
        return File(result, "image/jpeg");
    }

    /// <summary>
    /// 发布版本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> ReleaseVersion(ReleaseVersionInput input)
    {
        await _wechatAppletVersionService.ReleaseVersion(input);
        return Ok();
    }

    /// <summary>
    /// 获取版本记录列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<VersionRecordOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchVersionRecords(SearchVersionRecordsInput input)
    {
        var result = await _wechatAppletVersionService.SearchVersionRecords(input);
        return Ok(result);
    }

    #region

    [NonAction]
    [CapSubscribe(CapTopics.Wechat.WechatAppletCommitCode)]
    public async Task CommitCode(CommitCodeMessage command)
    {
        await _wechatAppletVersionService.CommitCode(command);
    }

    [NonAction]
    [CapSubscribe(CapTopics.Wechat.WechatAppletAuditVersion)]
    public async Task AuditVersion(AuditVersionMessage command)
    {
        await _wechatAppletVersionService.AuditVersion(command);
    }

    [NonAction]
    [CapSubscribe(CapTopics.Wechat.WechatAppletReleaseVersion)]
    public async Task ReleaseVersion(ReleaseVersionMessage command)
    {
        await _wechatAppletVersionService.ReleaseVersion(command);
    }

    #endregion
}
