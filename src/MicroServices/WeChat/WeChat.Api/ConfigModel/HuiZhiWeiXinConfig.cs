namespace WeChat.Api.ConfigModel
{
    public class HuiZhiWeiXinConfig
    {
        public string MerchantName { get; set; }

        public string WeixinAppId { get; set; }

        public string WeixinAppSecret { get; set; }

        public string QrCodeUrl { get; set; }

        public string AccountBindUrl { get; set; }

        /// <summary>
        /// 授权中转页面url
        /// </summary>
        public string AuthRedirectUrl { get; set; }

        /// <summary>
        /// 小程序appid
        /// </summary>
        public string? AppletAppId { get; set; }

        /// <summary>
        /// 小程序appsecret
        /// </summary>
        public string? AppletAppSecret { get; set; }

        /// <summary>
        /// 代收代付 - 小程序appid
        /// </summary>
        public string? ProxyAppletAppId { get; set; }

        /// <summary>
        /// 代收代付 - 小程序appsecret
        /// </summary>
        public string? ProxyAppletAppSecret { get; set; }
    }

}
