using Contracts.Common.WeChat.Enums;
using EfCoreExtensions.EntityBase;

namespace WeChat.Api.Model;

public class WechatAppletVersionRecord : TenantBase
{
    public long WechatAppletVersionInfoId { get; set; }

    /// <summary>
    /// 1-提交代码 2-审核版本 3-发布版本
    /// </summary>
    public WechatAppletVersionRecordTargetType TargetType { get; set; }

    /// <summary>
    /// 各类型id
    /// </summary>
    public long TargetId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    public string? UseVersion { get; set; }

    /// <summary>
    /// 状态 1-提交代码 2-提交成功 3-提交失败 4-提交审核 5-审核成功 6-审核失败 7-提交发布 8-发布成功 9-发布失败
    /// </summary>
    public WechatAppletVersionRecordStatus Status { get; set; }

    public string? Reason { get; set; }

    public string? Screenshot { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}

