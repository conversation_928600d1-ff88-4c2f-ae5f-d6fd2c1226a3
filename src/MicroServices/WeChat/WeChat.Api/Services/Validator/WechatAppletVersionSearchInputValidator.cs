using Contracts.Common.WeChat.DTOs.WechatAppletVersion;
using FluentValidation;

namespace WeChat.Api.Services.Validator;

public class WechatAppletVersionSearchInputValidator : AbstractValidator<SearchInput>
{
    public WechatAppletVersionSearchInputValidator()
    {
        RuleFor(x => x.PageSize).GreaterThan(0);
        RuleFor(x => x.PageSize).GreaterThan(0);
    }
}

public class CommitSearchInputValidator : AbstractValidator<CommitSearchInput>
{
    public CommitSearchInputValidator()
    {
        RuleFor(x => x.PageSize).GreaterThan(0);
        RuleFor(x => x.PageSize).GreaterThan(0);
    }
}

public class AuditSearchInputValidator : AbstractValidator<AuditSearchInput>
{
    public AuditSearchInputValidator()
    {
        RuleFor(x => x.PageSize).GreaterThan(0);
        RuleFor(x => x.PageSize).GreaterThan(0);
    }
}

public class ReleaseSearchInputValidator : AbstractValidator<ReleaseSearchInput>
{
    public ReleaseSearchInputValidator()
    {
        RuleFor(x => x.PageSize).GreaterThan(0);
        RuleFor(x => x.PageSize).GreaterThan(0);
    }
}