using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.Enums;
using SKIT.FlurlHttpClient.Wechat.Api;
using SKIT.FlurlHttpClient.Wechat.Api.Models;
using System.Threading;
using WeChat.Api.Model;
using WeChat.Api.Services.Interfaces;

namespace WeChat.Api.Services;

public class WxaApiService : IWxaApiService
{
    private readonly IThirdPlatformService _thirdPlatformService;
    private readonly IWechatManagedService _wechatManagedService;

    public WxaApiService(IThirdPlatformService thirdPlatformService,
        IWechatManagedService wechatManagedService)
    {
        _thirdPlatformService = thirdPlatformService;
        _wechatManagedService = wechatManagedService;
    }

    public async Task<AuthorizerTokenInfo> GetAuthorizerTokenInfo(long tenantId, AuthType authType = AuthType.WechatApplet)
    {
        return await _wechatManagedService.GetAuthorizerTokenInfo(authType, tenantId);
    }

    public async Task<WxaCommitResponse> ExecuteWxaCommitAsync(WxaCommitRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteWxaCommitAsync(request);
        return response;
    }

    public async Task<WxaGetVersionInfoResponse> ExecuteWxaGetVersionInfoAsync(WxaGetVersionInfoRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteWxaGetVersionInfoAsync(request);
        return response;
    }

    public async Task<WxaGetQrcodeResponse> ExecuteWxaGetQrcodeAsync(WxaGetQrcodeRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteWxaGetQrcodeAsync(request);
        return response;
    }

    public async Task<WxaSecurityGetCodePrivacyInfoResponse> ExecuteWxaSecurityGetCodePrivacyInfoAsync(WxaSecurityGetCodePrivacyInfoRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteWxaSecurityGetCodePrivacyInfoAsync(request);
        return response;
    }

    public async Task<CgibinWxopenGetCategoryResponse> ExecuteCgibinWxopenGetCategoryAsync(CgibinWxopenGetCategoryRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteCgibinWxopenGetCategoryAsync(request);
        return response;
    }

    public async Task<WxaSubmitAuditResponse> ExecuteWxaSubmitAuditAsync(WxaSubmitAuditRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteWxaSubmitAuditAsync(request);
        return response;
    }

    public async Task<WxaGetLatestAuditStatusResponse> ExecuteWxaGetLatestAuditStatusAsync(WxaGetLatestAuditStatusRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteWxaGetLatestAuditStatusAsync(request);
        return response;
    }

    public async Task<CgibinMaterialGetMaterialResponse> ExecuteCgibinMaterialGetMaterialAsync(CgibinMaterialGetMaterialRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteCgibinMaterialGetMaterialAsync(request);
        return response;
    }

    public async Task<WxaReleaseResponse> ExecuteWxaReleaseAsync(WxaReleaseRequest request)
    {
        var client = _thirdPlatformService.GetWechatApiClient();
        var response = await client.ExecuteWxaReleaseAsync(request);
        return response;
    }

}
