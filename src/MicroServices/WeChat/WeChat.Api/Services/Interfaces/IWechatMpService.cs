using Contracts.Common.WeChat.DTOs;

namespace WeChat.Api.Services.Interfaces
{
    public interface IWechatMpService
    {
        /// <summary>
        /// 获取商户公众号信息
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        Task<GetWechatMPInfoOutput> GetWechatMPInfo(long tenantId);

        /// <summary>
        /// code换取accesstoken
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<WechatOauth2AccessToken> GetAccessToken(string code);

        /// <summary>
        /// openid获取用户信息
        /// </summary>
        /// <param name="openId"></param>
        /// <returns></returns>
        Task<GetUserInfoOutput> GetUserInfo(string openId);

        /// <summary>
        /// 获取网页授权地址
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<string> GetAuthorizeUri(GetAuthorizeUriInput input);

        /// <summary>
        /// 获取公众号二维码url
        /// </summary>
        /// <param name="input"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        Task<CreateMpQrCodeOutput> CreateMpQrCode(CreateMpQrCodeInput input, long tenantId);
    }
}
