using Contracts.Common.WeChat.DTOs;
using SKIT.FlurlHttpClient.Wechat.Api.Models;

namespace WeChat.Api.Services.Interfaces
{
    public interface IThirdWechatAppletService
    {
        /// <summary>
        /// 获取小程序授权信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<GetJsCode2SessionOutput> GetJsCode2Session(string code);


        /// <summary>
        /// 获取小程序手机号
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<GetPhoneNumberOutput> GetPhoneNumber(long tenantId, string code);

        /// <summary>
        /// 获取小程序二维码
        /// 调用本 API 可以获取小程序二维码，适用于需要的码数量较少的业务场景。通过该接口生成的小程序码，永久有效，有数量限制。
        /// </summary>
        /// <param name="input"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        Task<string> CreateWxaQrcode(CreateWxaQrcodeInput input, long tenantId);

        /// <summary>
        /// 获取小程序码
        /// 调用本 API 可以获取小程序码，适用于需要的码数量极多的业务场景。通过该接口生成的小程序码，永久有效，数量暂无限制。
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<string> GetWxacodeUnlimit(GetAppletWxacodeUnlimitInput input);

        /// <summary>
        /// 获取urlLink
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<string> GenerateURLLink(GenerateURLLinkInput input, long tenantId);
    }
}
