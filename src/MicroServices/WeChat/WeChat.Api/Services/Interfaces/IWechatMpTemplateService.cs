using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.Enums;

namespace WeChat.Api.Services.Interfaces
{
    public interface IWechatMpTemplateService
    {
        /// <summary>
        /// 获得模板id
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task<string> AddTemplate(AddWechatMpTemplateDto dto);

        /// <summary>
        /// 发送模板消息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task<long> Send(WechatMessageTempalteSendDto dto);


        /// <summary>
        /// 获取所有消息模板
        /// </summary>
        /// <param name="authType">授权类型 1-微商城微信公众号 2-微商城微信小程序 3-B2BB公众号</param>
        /// <returns></returns>
        Task<string[]> GetAllTemplate(AuthType authType = AuthType.WechatMp);

        /// <summary>
        /// 删除模板
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task DeleteTemplate(DeleteWechatMpTemplateDto dto);
    }
}
