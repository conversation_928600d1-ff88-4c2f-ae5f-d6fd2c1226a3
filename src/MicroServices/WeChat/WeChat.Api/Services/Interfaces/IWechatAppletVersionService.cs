using Contracts.Common.WeChat.DTOs.WechatAppletVersion;
using Contracts.Common.WeChat.Messages;
using EfCoreExtensions.Abstract;
using static WeChat.Api.Services.WechatAppletVersionService;

namespace WeChat.Api.Services.Interfaces;

public interface IWechatAppletVersionService
{
    /// <summary>
    /// 查询小程序版本列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchVersionOutput>> Search(SearchInput input);

    /// <summary>
    /// 提交代码列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<CommitSearchVersionOutput>> CommitSearch(CommitSearchInput input);

    /// <summary>
    /// 审核版本列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<AuditSearchVersionOutput>> AuditSearch(AuditSearchInput input);

    /// <summary>
    /// 发布版本列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ReleaseSearchVersionOutput>> ReleaseSearch(ReleaseSearchInput input);

    /// <summary>
    /// 版本数统计
    /// </summary>
    /// <returns></returns>
    Task<WechatAppletVersionStatOuptput> WechatAppletVersionStat();

    /// <summary>
    /// 提交代码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CommitCode(CommitCodeInput input);

    /// <summary>
    /// 订阅 - 提交代码
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task CommitCode(CommitCodeMessage command);

    /// <summary>
    /// 获取体验版二维码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<byte[]> GetTrialQRCode(GetTrialQRCodeInput input);

    /// <summary>
    /// 审核版本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AuditVersion(AuditVersionInput input);

    /// <summary>
    /// 订阅 - 审核版本
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task AuditVersion(AuditVersionMessage command);

    /// <summary>
    /// 更新审核单状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateWechatAppletAudit(UpdateWechatAppletAuditInput input);

    /// <summary>
    /// 获取永久图片素材
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<byte[]> GetImageMaterial(GetImageMaterialInput input);

    /// <summary>
    /// 发布版本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ReleaseVersion(ReleaseVersionInput input);

    /// <summary>
    /// 订阅 - 发布版本
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task ReleaseVersion(ReleaseVersionMessage command);

    /// <summary>
    /// 版本记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<VersionRecordOutput>> SearchVersionRecords(SearchVersionRecordsInput input);
}
