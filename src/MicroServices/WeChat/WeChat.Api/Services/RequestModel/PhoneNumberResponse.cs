using SKIT.FlurlHttpClient.Wechat.Api;

namespace WeChat.Api.Services.RequestModel
{
    public class PhoneNumberResponse : WechatApiResponse
    {

        /// <summary>
        /// 获取或设置创建帧同步房间时的房间信息。
        /// </summary>
        [Newtonsoft.Json.JsonProperty("phone_info")]
        [System.Text.Json.Serialization.JsonPropertyName("phone_info")]
        public Types.PhoneInfo PhoneInfo { get; set; }


        public static class Types
        {
            public class PhoneInfo
            {
                /// <summary>
                /// 用户绑定的手机号（国外手机号会有区号）
                /// </summary>
                [Newtonsoft.Json.JsonProperty("phoneNumber")]
                [System.Text.Json.Serialization.JsonPropertyName("phoneNumber")]
                public string PhoneNumber { get; set; }

                /// <summary>
                /// 没有区号的手机号
                /// </summary>
                [Newtonsoft.Json.JsonProperty("purePhoneNumber")]
                [System.Text.Json.Serialization.JsonPropertyName("purePhoneNumber")]
                public string PurePhoneNumber { get; set; }

                /// <summary>
                /// 区号
                /// </summary>
                [Newtonsoft.Json.JsonProperty("countryCode")]
                [System.Text.Json.Serialization.JsonPropertyName("countryCode")]
                public string CountryCode { get; set; }


                /// <summary>
                /// 数据水印
                /// </summary>
                [Newtonsoft.Json.JsonProperty("watermark")]
                [System.Text.Json.Serialization.JsonPropertyName("watermark")]
                public Types.CountryCode Watermark { get; set; }


            }

            public class CountryCode
            {
                /// <summary>
                /// 小程序appid
                /// </summary>
                [Newtonsoft.Json.JsonProperty("appid")]
                [System.Text.Json.Serialization.JsonPropertyName("appid")]
                public string AppId { get; set; }


                /// <summary>
                /// 用户获取手机号操作的时间戳
                /// </summary>
                [Newtonsoft.Json.JsonProperty("timestamp")]
                [System.Text.Json.Serialization.JsonPropertyName("timestamp")]
                public long TimeStamp { get; set; }

            }
        }


    }
}
