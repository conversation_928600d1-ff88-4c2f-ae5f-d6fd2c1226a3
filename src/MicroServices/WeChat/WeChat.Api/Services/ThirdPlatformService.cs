using Cit.Storage.Redis;
using Contracts.Common.WeChat.DTOs;
using Microsoft.Extensions.Options;
using SKIT.FlurlHttpClient.Wechat.Api;
using SKIT.FlurlHttpClient.Wechat.Api.Models;
using WeChat.Api.ConfigModel;
using WeChat.Api.Services.Interfaces;

namespace WeChat.Api.Services
{
    public class ThirdPlatformService : IThirdPlatformService
    {
        private readonly WechatOptions _wechatOptions;
        private readonly IRedisClient _redisClient;
        private readonly ILogger<ThirdPlatformService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ThirdPlatformService(IOptions<WechatOptions> wechatOptions,
            IRedisClient redisClient,
            ILogger<ThirdPlatformService> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _wechatOptions = wechatOptions.Value;
            _redisClient = redisClient;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
        }

        public WechatApiClient GetWechatApiClient()
        {
            var option = _wechatOptions.Account;
            var client = new WechatApiClient(new WechatApiClientOptions()
            {
                AppId = option.AppId,
                AppSecret = option.AppSecret
            });
            return client;
        }

        #region 验证票据 component_verify_ticket

        private const string WECHAT_THIRD_PLATFORM_COMPONET_VERIFY_TICHET = "wechat:third_platform:component_verify_ticket";
        public async Task<string> GetComponentVerifyTicket()
        {
            return await _redisClient.StringGetAsync(WECHAT_THIRD_PLATFORM_COMPONET_VERIFY_TICHET);
        }

        public async Task<bool> SetComponentVerifyTicket(string componentVerifyTicket)
        {
            return await _redisClient.StringSetAsync(WECHAT_THIRD_PLATFORM_COMPONET_VERIFY_TICHET,
                componentVerifyTicket, TimeSpan.FromSeconds(7200));
        }

        #endregion

        #region 令牌 component_access_token

        private static readonly ManualResetEventSlim _manualResetEventSlim = new();
        private const string WECHAT_THIRD_PLATFORM_COMPONET_ACCESSTOKEN = "wechat:third_platform:component_accesstoken";

        public async Task<string> GetComponentAccessToken()
        {
            if (await _redisClient.KeyExistsAsync(WECHAT_THIRD_PLATFORM_COMPONET_ACCESSTOKEN))
            {
                return await _redisClient.StringGetAsync(WECHAT_THIRD_PLATFORM_COMPONET_ACCESSTOKEN);
            }
            _manualResetEventSlim.Set();
            _manualResetEventSlim.Wait();
            var component_verify_ticket = await GetComponentVerifyTicket();

            var client = GetWechatApiClient();
            var request = new CgibinComponentApiComponentTokenRequest()
            {
                ComponentVerifyTicket = component_verify_ticket
            };
            var response = await client.ExecuteCgibinComponentApiComponentTokenAsync(request);
            _logger.LogInformation("微信第三方平台获取令牌 入参：{@parameter} ,结果：{@result}", request, response);

            if (response?.IsSuccessful() is true && !string.IsNullOrWhiteSpace(response?.ComponentAccessToken))
            {
                //令牌的获取是有限制的，每个令牌的有效期为 2 小时，请自行做好令牌的管理，在令牌快过期时（比如1小时50分），重新调用接口获取。
                await _redisClient.StringSetAsync(WECHAT_THIRD_PLATFORM_COMPONET_ACCESSTOKEN,
                    response.ComponentAccessToken,
                    TimeSpan.FromMinutes(110));
            }
            _manualResetEventSlim.Reset();
            return response?.ComponentAccessToken;
        }

        #endregion

        #region 预授权码 pre_auth_code

        private const string WECHAT_THIRD_PLATFORM_PRE_AUTHCODE = "wechat:third_platform:pre_authcode";
        public async Task<string> GetPreAuthCode()
        {
            if (await _redisClient.KeyExistsAsync(WECHAT_THIRD_PLATFORM_PRE_AUTHCODE))
            {
                return await _redisClient.StringGetAsync(WECHAT_THIRD_PLATFORM_PRE_AUTHCODE);
            }
            var component_access_token = await GetComponentAccessToken();
            if (string.IsNullOrWhiteSpace(component_access_token))
                return null;

            var client = GetWechatApiClient();
            var request = new CgibinComponentApiCreatePreAuthCodeRequest()
            {
                ComponentAccessToken = component_access_token
            };

            var response = await client.ExecuteCgibinComponentApiCreatePreAuthCodeAsync(request);
            _logger.LogInformation("微信第三方平台获取预授权码 入参：{@parameter} ,结果：{@result}", request, response);

            if (response?.IsSuccessful() is not true)
                return string.Empty;

            await _redisClient.StringSetAsync(WECHAT_THIRD_PLATFORM_PRE_AUTHCODE,
                response.PreAuthCode, TimeSpan.FromSeconds(1700));

            return response.PreAuthCode;
        }

        #endregion

        #region 使用授权码获取授权信息

        public async Task<CgibinComponentApiQueryAuthResponse> QueryAuth(string authorizationCode)
        {
            var client = GetWechatApiClient();
            var componentAccessToken = await GetComponentAccessToken();
            var request = new CgibinComponentApiQueryAuthRequest()
            {
                AuthCode = authorizationCode,
                ComponentAccessToken = componentAccessToken
            };
            var response = await client.ExecuteCgibinComponentApiQueryAuthAsync(request);
            _logger.LogInformation("微信第三方平台使用授权码获取授权信息 入参：{@parameter} ,结果：{@result}", request, response);

            return response;
        }

        #endregion

        #region 获取/刷新接口调用令牌

        private const string WECHAT_THIRD_PLATFORM_AUTHORIZER_ACCESS_TOKEN = "wechat:third_platform:authorizer_access_token:{0}";

        public async Task<AuthorizerTokenInfo> GetAuthorizerToken(string authorizerAppid, string authorizerRefreshToken)
        {
            var key = string.Format(WECHAT_THIRD_PLATFORM_AUTHORIZER_ACCESS_TOKEN, authorizerAppid);
            var authorizerTokenInfo = await _redisClient.StringGetAsync<AuthorizerTokenInfo>(key);
            if (authorizerTokenInfo is not null &&
                authorizerTokenInfo.AuthorizerRefreshToken == authorizerRefreshToken)
            {
                return authorizerTokenInfo;
            }

            var client = GetWechatApiClient();
            var componentAccessToken = await GetComponentAccessToken();
            var response = await client.ExecuteCgibinComponentApiAuthorizerTokenAsync(new CgibinComponentApiAuthorizerTokenRequest
            {
                ComponentAccessToken = componentAccessToken,
                AuthorizerRefreshToken = authorizerRefreshToken,
                AuthorizerAppId = authorizerAppid
            });

            if (response.IsSuccessful())
            {
                authorizerTokenInfo = new()
                {
                    AuthorizerAppid = authorizerAppid,
                    AuthorizerAccessToken = response.AuthorizerAccessToken,
                    ExpiresIn = response.ExpiresIn,
                    AuthorizerRefreshToken = response.AuthorizerRefreshToken
                };
                await SetAuthorizerToken(authorizerTokenInfo);
            }
            else
            {
                _logger.LogInformation("获取/刷新接口调用令牌GetAuthorizerToken失败,response:{@response}", response);
            }

            return authorizerTokenInfo;
        }

        public async Task<bool> SetAuthorizerToken(AuthorizerTokenInfo authorizerTokenInfo)
        {
            var key = string.Format(WECHAT_THIRD_PLATFORM_AUTHORIZER_ACCESS_TOKEN,
                authorizerTokenInfo.AuthorizerAppid);
            //authorizer_access_token 有效期为 2 小时，
            //开发者需要缓存 authorizer_access_token，避免获取/刷新接口调用令牌的 API 调用触发每日限额。
            return await _redisClient.StringSetAsync(key, authorizerTokenInfo, TimeSpan.FromMinutes(110));
        }

        #endregion

        #region 获取授权方的帐号基本信息

        public async Task<CgibinComponentApiGetAuthorizerInfoResponse> GetAuthorizerInfo(string authorizerAppId)
        {
            var client = GetWechatApiClient();
            var componentAccessToken = await GetComponentAccessToken();
            var request = new CgibinComponentApiGetAuthorizerInfoRequest()
            {
                AuthorizerAppId = authorizerAppId,
                ComponentAccessToken = componentAccessToken
            };
            var response = await client.ExecuteCgibinComponentApiGetAuthorizerInfoAsync(request);
            _logger.LogInformation("微信第三方平台获取授权方的帐号基本信息 入参：{@parameter} ,结果：{@result}", request, response);
            if (!response.IsSuccessful())
                return null;
            return response;
        }

        #endregion

    }
}
