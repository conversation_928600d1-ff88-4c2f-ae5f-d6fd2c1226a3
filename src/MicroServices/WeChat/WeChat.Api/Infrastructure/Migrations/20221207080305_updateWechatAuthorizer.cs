using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeChat.Api.Infrastructure.Migrations
{
    public partial class updateWechatAuthorizer : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameTable(
                name: "WechatAppletManaged",
                newName: "WechatAuthorizer");

            migrationBuilder.RenameIndex(
                name: "IX_WechatAppletManaged_TenantId",
                table: "WechatAuthorizer",
                newName: "IX_WechatAuthorizer_TenantId");

            migrationBuilder.DropTable(
                name: "WechatMpUnManaged");

            migrationBuilder.RenameColumn(
                name: "IsManaged",
                table: "WechatConfiguration",
                newName: "IsAuthorized");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameTable(
                 name: "WechatAuthorizer",
                 newName: "WechatAppletManaged");

            migrationBuilder.RenameIndex(
                name: "IX_WechatAuthorizer_TenantId",
                table: "WechatAppletManaged",
                newName: "IX_WechatAppletManaged_TenantId");

            migrationBuilder.RenameColumn(
                name: "IsAuthorized",
                table: "WechatConfiguration",
                newName: "IsManaged");

            migrationBuilder.CreateTable(
                name: "WechatMpUnManaged",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false, comment: "创建时间"),
                    MpTxtPath = table.Column<string>(type: "varchar(200)", nullable: true, comment: "mp.txt地址")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Step = table.Column<int>(type: "int(11)", nullable: false, comment: "步骤"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    WechatConfigurationId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WechatMpUnManaged", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_WechatMpUnManaged_TenantId",
                table: "WechatMpUnManaged",
                column: "TenantId");
        }
    }
}
