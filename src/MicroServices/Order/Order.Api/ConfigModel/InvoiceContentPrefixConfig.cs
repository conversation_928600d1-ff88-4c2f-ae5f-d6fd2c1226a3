using Contracts.Common.Order.Enums;

namespace Order.Api.ConfigModel;

public class InvoiceContentPrefixConfig
{
    public Dictionary<string, string> InvoiceContentPrefixTypes { get; set; } = new Dictionary<string, string>();

    public InvoiceContentPrefixConfig()
    {
        InvoiceContentPrefixTypes.Add(ContentPrefixType.TourismService.ToString(), "3070301000000000000");
        InvoiceContentPrefixTypes.Add(ContentPrefixType.RecreationService.ToString(), "3070302000000000000");
        InvoiceContentPrefixTypes.Add(ContentPrefixType.CateringAccommodationService.ToString(), "3070400000000000000");
        InvoiceContentPrefixTypes.Add(ContentPrefixType.CateringService.ToString(), "3070401000000000000");
        InvoiceContentPrefixTypes.Add(ContentPrefixType.AccommodationService.ToString(), "3070402000000000000");
        InvoiceContentPrefixTypes.Add(ContentPrefixType.DailyLifeOfResidentsService.ToString(), "3070500000000000000");
        InvoiceContentPrefixTypes.Add(ContentPrefixType.InsuranceService.ToString(), "3060300000000000000");
        InvoiceContentPrefixTypes.Add(ContentPrefixType.PassengerTransportService.ToString(), "3010601000000000000");
    }

    /// <summary>
    /// 代开标志
    /// </summary>
    public string DKBZ { get; set; } = "0";

    /// <summary>
    /// 开票类型 1正票、2红票
    /// </summary>
    public string KPLX { get; set; } = "1";

    /// <summary>
    /// 10正票正常开具
    /// 20 退货折让红票
    /// </summary>
    public string CZDM { get; set; } = "10";

    /// <summary>
    /// 发票种类
    /// </summary>
    public string FPZL { get; set; } = "151";

    /// <summary>
    /// 清单标志
    /// </summary>
    public string QDBZ { get; set; } = "1";

    /// <summary>
    /// 目前需要传33.0
    /// </summary>
    public string BMB_BBH { get; set; } = "33.0";

    /// <summary>
    /// 含税标志
    /// 表示项目单价和项目金额是否含税。0表示都不含税，1表示都含税。目前输入含税价,即标志为1
    /// </summary>
    public string HSBZ { get; set; } = "1";

    /// <summary>
    /// 发票行性质
    /// </summary>
    public string FPHXZ { get; set; } = "0";

    /// <summary>
    /// 项目数量
    /// </summary>
    public string XMSL { get; set; } = "0";

    /// <summary>
    /// 是否享受优惠政策：必填
    /// 0：不使用，1使用
    /// </summary>
    public string YHZCBS { get; set; } = "0";

    /// <summary>
    /// 如果税率为0，表示免税,输入小数如0.17 表示17%
    /// </summary>
    public string SL { get; set; } = "0.06";

    /// <summary>
    /// 冲红时填写，由企业定义
    /// </summary>
    public string CHYY { get; set; } = "冲红";
}
