{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"Order.Api": {"commandName": "Project", "launchBrowser": true, "launchUrl": "", "applicationUrl": "http://localhost:5306", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ConnectionStrings:LogicDatabase": "Server=************;Database=Orders;Uid=developer;Pwd=*****************;Character Set=utf8mb4;persist security info=True;"}}, "Order.Api.TesingDB": {"commandName": "Project", "launchBrowser": true, "launchUrl": "", "applicationUrl": "http://localhost:5306", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}