using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.DTOs.B2BWebConfiguration;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Order.Api.Extensions;
using Order.Api.Notification;

namespace Order.Api.Requests.HotelOrder;

public class EditConfirmCodeRequestHandler : IRequestHandler<EditConfirmCodeRequest>
{
    private readonly CustomDbContext _dbContext;
    private readonly IMediator _mediator;
    private readonly ICapPublisher _capPublisher;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOptions<ServicesAddress> _servicesAddress;

    public EditConfirmCodeRequestHandler(CustomDbContext dbContext,
        IMediator mediator,
        ICapPublisher capPublisher,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress)
    {
        _dbContext = dbContext;
        _mediator = mediator;
        _capPublisher = capPublisher;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddress;
    }
    public async Task<Unit> Handle(EditConfirmCodeRequest request, CancellationToken cancellationToken)
    {
        var operationUser = request.OperationUser;
        var order = await _dbContext.HotelOrders
            .IgnoreQueryFilters()
            .Where(x => x.Id == request.OrderId && x.TenantId == request.TenantId)
            .WhereIF(operationUser.UserType == UserType.Supplier, x => x.PriceStrategySupplierId == request.SupplierId)
            .FirstOrDefaultAsync();
        if (order is null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var baseOrder = await _dbContext.BaseOrders
          .IgnoreQueryFilters()
          .Where(x => x.Id == order.BaseOrderId)
          // .Select(x => new { x.Id, x.UserId, x.SellingChannels, x.ChannelOrderNo })
          .FirstOrDefaultAsync();

        //确认号变更
        if (order.ConfirmCode != request.ConfirmCode)
        {
            order.ConfirmCode = request.ConfirmCode;
            var logs = new OrderLogs
            {
                OperationType = OrderOperationType.ConfirmCodeUpdated,
                OrderLogType = OrderLogType.Hotel,
                OrderId = order.BaseOrderId,
                UserId = operationUser.UserId,
                UserName = operationUser.Name,
                OperationRole = operationUser.UserType
            };
            logs.SetTenantId(request.TenantId);
            await _dbContext.AddAsync(logs);
            await _dbContext.SaveChangesAsync();

            //订单状态变更通知
            await _mediator.Publish(new HotelOrderChangeNotification
            {
                TenantId = order.TenantId,
                SellingChannel = baseOrder.SellingChannels,
                UserId = baseOrder.UserId,
                ChannelOrderNo = baseOrder.ChannelOrderNo,
                BaseOrderId = order.BaseOrderId, //主单id
                OrderStatus = order.Status,
                ConfirmCode = order.ConfirmCode
            });

            if (string.IsNullOrEmpty(request.ConfirmCode) is false &&
                order.Status is HotelOrderStatus.WaitingForCheckIn)
            {
                //系统消息通知
                if (baseOrder.SellingPlatform == SellingPlatform.B2BWeb ||
                    baseOrder.SellingPlatform == SellingPlatform.B2BApplet)
                {
                    //站内信通知
                    await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess,
                        new NotifyMessageProcessMessage
                        {
                            NotifyEventSubType = NotifyEventSubType.Hotel_ConfirmNoModified,
                            NotifyMode = NotifyMode.SiteMessage,
                            SendToTheRole = SendToTheRole.AgencyStaff,
                            TenantId = order.TenantId,
                            Variables = new
                            {
                                order.BaseOrderId,
                                baseOrder.AgencyId,
                                baseOrder.UserId,
                                order.HotelName,
                                order.HotelRoomName,
                                order.PriceStrategyName
                            }
                        });

                    //短信通知
                    if (string.IsNullOrEmpty(baseOrder.ContactsPhoneNumber) is false)
                    {
                        await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess,
                          new NotifyMessageProcessMessage
                          {
                              NotifyEventSubType = NotifyEventSubType.Hotel_ConfirmNoModified,
                              NotifyMode = NotifyMode.Sms,
                              SendToTheRole = SendToTheRole.Customer,
                              TenantId = order.TenantId,
                              NotifyChannel = NotifyChannel.B2b,
                              Variables = new
                              {
                                  PhoneNumber = baseOrder.ContactsPhoneNumber,
                                  order.BaseOrderId,
                                  order.CheckInDate,
                                  order.CheckOutDate,
                                  order.ConfirmCode,
                                  order.HotelName,
                                  order.HotelRoomName,
                                  Quantity = order.PriceStrategyRoomsCount,
                              }
                          });
                    }

                    //邮件通知
                    if (string.IsNullOrEmpty(baseOrder.ContactsEmail) is false)
                    {
                        var b2bWebConfiguration = await TenantB2bWebConfigurationGet();
                        var brandName = b2bWebConfiguration.B2BTitleConfigs.FirstOrDefault(x => x.Language.Equals("zh")).BrandName;

                        await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess,
                            new NotifyMessageProcessMessage
                            {
                                NotifyEventSubType = NotifyEventSubType.Hotel_ConfirmNoModified,
                                NotifyMode = NotifyMode.Email,
                                SendToTheRole = SendToTheRole.Customer,
                                TenantId = order.TenantId,
                                NotifyChannel = NotifyChannel.B2b,
                                Variables = new
                                {
                                    Addressee = baseOrder.ContactsEmail,
                                    order.BaseOrderId,
                                    order.CheckInDate,
                                    order.CheckOutDate,
                                    order.ConfirmCode,
                                    order.HotelName,
                                    order.HotelRoomName,
                                    Quantity = order.PriceStrategyRoomsCount,
                                    AgencyUserName = baseOrder.UserNickName,
                                    BrandName = brandName
                                }
                            });
                    }

                    //微信通知
                    await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess,
                           new NotifyMessageProcessMessage
                           {
                               NotifyEventSubType = NotifyEventSubType.Hotel_ConfirmNoModified,
                               NotifyMode = NotifyMode.Wechat,
                               SendToTheRole = SendToTheRole.Customer,
                               TenantId = order.TenantId,
                               NotifyChannel = NotifyChannel.B2b,
                               Variables = new
                               {
                                   baseOrder.UserId,
                                   order.BaseOrderId,
                                   order.CheckInDate,
                                   order.CheckOutDate,
                                   order.ConfirmCode,
                                   order.HotelName,
                               }
                           });

                }

            }

        }

        return Unit.Value;
    }

    private async Task<B2BWebConfigurationDto> TenantB2bWebConfigurationGet()
    {
        var response = await _httpClientFactory.InternalGetAsync<B2BWebConfigurationDto>(
            requestUri: _servicesAddress.Value.Tenant_B2bWebConfiguration_Get());
        return response;
    }
}
