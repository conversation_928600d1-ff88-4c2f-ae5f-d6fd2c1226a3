using AutoMapper;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Tenant.DTOs.Supplier;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Order.Api.Extensions;
using Order.Api.Model;
using Order.Api.Services.Interfaces;
using System.Net.Http;

namespace Order.Api.Services;

public class InsureProductSkuRelationService : IInsureProductSkuRelationService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;

    public InsureProductSkuRelationService(IMapper mapper, 
        CustomDbContext dbContext)
    {
        _mapper = mapper;
        _dbContext = dbContext;
    }

    public async Task<GetInsureProductSkuRelationOutput> Get(GetInsureProductSkuRelationInput input)
    {
        var insurePolicyHolder = await _dbContext.InsureProductSkuRelations.AsNoTracking()
            .WhereIF(input.ProductId.HasValue, x => x.ProductId == input.ProductId)
            .WhereIF(input.ProductSkuId.HasValue, x => x.ProductSkuId == input.ProductSkuId)
            .FirstOrDefaultAsync();

        return _mapper.Map<GetInsureProductSkuRelationOutput>(insurePolicyHolder);
    }

    public async Task Save(SaveInsureProductSkuRelationInput input)
    {
        var entity = await _dbContext.InsureProductSkuRelations
            .Where(x => x.ProductId == input.ProductId && x.ProductSkuId == input.ProductSkuId)
            .FirstOrDefaultAsync();
        if (entity is null)
        {
            var insureProductSkuRelation = _mapper.Map<InsureProductSkuRelation>(input);
            await _dbContext.AddAsync(insureProductSkuRelation);
        }
        else
        {
            _mapper.Map(input, entity);
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task Delete(DeleteInsureProductSkuRelationInput input)
    {
        var entity = await _dbContext.InsureProductSkuRelations
            .WhereIF(input.ProductId.HasValue , x=> x.ProductId == input.ProductId)
            .Where(x => x.ProductSkuId == input.ProductSkuId)
            .FirstOrDefaultAsync();
        if (entity == null)
            return;

        _dbContext.InsureProductSkuRelations.Remove(entity);
        await _dbContext.SaveChangesAsync();
    }
}
