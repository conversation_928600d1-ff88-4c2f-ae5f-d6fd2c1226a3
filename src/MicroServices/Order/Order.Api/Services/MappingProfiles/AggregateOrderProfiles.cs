using AutoMapper;
using Contracts.Common.Order.DTOs.AggregateOrder;
using Contracts.Common.Order.Enums;

namespace Order.Api.Services.MappingProfiles;

public class AggregateOrderProfiles : Profile
{
    public AggregateOrderProfiles()
    {
        CreateMap<AggregateOrder, SearchAggregateOrderOutput>()
            .ForMember(c => c.ValidityEnd, m => m.MapFrom(x => x.SkuValidityEnd))
            .ForMember(c => c.ValidityBegin, m => m.MapFrom(x => x.SkuValidityBegin))
            .ForMember(c => c.OrderCategoryList, m => m.MapFrom<OrderCategoryResolver>());

        CreateMap<ExportAggregateOrderInput, SearchAggregateOrderInput>();

        CreateMap<AggregateOrder, ExportAggregateOrderOutput>()
            .ForMember(c => c.OrderCategoryList, m => m.MapFrom<OrderCategoryResolver>());

        CreateMap<SearchAggregateOrderFinanceOutput, ExportAggregateOrderFinanceOutput>();
    }
    
    private class OrderCategoryResolver : IValueResolver<AggregateOrder, object, List<OrderCategory>>
    {
        private static readonly OrderCategory[] _categories = Enum.GetValues<OrderCategory>();
        public List<OrderCategory> Resolve(
            AggregateOrder source, 
            object destination, 
            List<OrderCategory> destMember, 
            ResolutionContext context)
        {
            return _categories
                .Where(c => (source.OrderCategory & c) == c)
                .ToList();
        }
    }

}
