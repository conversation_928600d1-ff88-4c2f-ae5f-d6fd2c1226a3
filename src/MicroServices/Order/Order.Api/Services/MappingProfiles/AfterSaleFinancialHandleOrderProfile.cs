using AutoMapper;
using Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder;

namespace Order.Api.Services.MappingProfiles;

public class AfterSaleFinancialHandleOrderProfile:Profile
{
    public AfterSaleFinancialHandleOrderProfile()
    {
        CreateMap<TenantBankAccountInfo, TenantBankAccountInfoDto>().ReverseMap();
        CreateMap<AfterSaleFinancialHandleOrder, DetailOutput>();
    }
}
