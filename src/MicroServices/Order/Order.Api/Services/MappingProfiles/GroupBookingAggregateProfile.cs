using AutoMapper;

namespace Order.Api.Services.MappingProfiles;

public class GroupBookingAggregateProfile : Profile
{
    public GroupBookingAggregateProfile()
    {
        CreateMap<GroupBookingApplicationForm, GroupBookingAggregate>()
            .ForMember(x => x.Id, r => r.Ignore())
            .ForMember(x => x.ApplicationFormId, r => r.MapFrom(s => s.Id));
        CreateMap<GroupBookingApplicationDemand, GroupBookingAggregate>()
            .ForMember(x => x.Id, r => r.Ignore())
             .ForMember(x => x.ApplicationDemandId, r => r.MapFrom(s => s.Id));
    }
}
