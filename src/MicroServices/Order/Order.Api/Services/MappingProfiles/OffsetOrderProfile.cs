using AutoMapper;
using Contracts.Common.Order.DTOs.OffsetOrder;
using EfCoreExtensions.Abstract;

namespace Order.Api.Services.MappingProfiles;

public class OffsetOrderProfile : Profile
{
    public OffsetOrderProfile()
    {
        CreateMap<OffsetOrder,SearchOffsetOrderOutput>();
        CreateMap<PagingModel<OffsetOrder>,PagingModel<SearchOffsetOrderOutput>>();
        CreateMap<OffsetOrder,GetOffsetOrderListOutput>();
        CreateMap<OffsetOrder, GetOffsetOrderDetailOutput>();
    }
}