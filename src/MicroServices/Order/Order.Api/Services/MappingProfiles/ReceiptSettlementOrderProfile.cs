using AutoMapper;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using EfCoreExtensions.Abstract;

namespace Order.Api.Services.MappingProfiles;

public class ReceiptSettlementOrderProfile: Profile
{
    public ReceiptSettlementOrderProfile()
    {
        CreateMap<ReceiptInput, ReceiptSettlementOrder>();
        CreateMap<ReceiptSettlementOrder,SearchOutput>();
        CreateMap<ReceiptSettlementOrder,ExportOrderOutput>();
        CreateMap<PagingModel<ReceiptSettlementOrder>,PagingModel<SearchOutput>>();
        CreateMap<PreCreateInput, RelatedReceivablesSearchInput>();
        CreateMap<ReceivablesOrderExportInput, RelatedReceivablesSearchInput>();
        CreateMap<ReceiptSettlementOrderDetail, SearchDetailExportOutput>();
        CreateMap<ReceiptSettlementOrderDetail, SearchDetailExportOutput>();
        CreateMap<ReceiptSettlementOrderRecord, GetReceiptSettlementOrderRecordsOutput>();
        CreateMap<AddReceiptSettlementOrderRecordInput, ReceiptSettlementOrderRecord>();
        CreateMap<ReceiptOrderSetting, ReceiptOrderSettingOutput>();
        CreateMap<UpdateReceiptOrderSettingInput, ReceiptOrderSetting>();
        CreateMap<ReceiptSettlementOrder, UnReceiptByEarlyWarningOutput>()
            .ForMember(x => x.CreateTime, f => f.MapFrom(x => x.CreatTime));
        CreateMap<PagingModel<ReceiptSettlementOrder>, PagingModel<UnReceiptByEarlyWarningOutput>>();
        CreateMap<ReceiptSettlementOrder, UnReceiptDetailsByEarlyWarningOutput>()
            .ForMember(x => x.SettlementOrderId, f => f.MapFrom(x => x.Id));
        CreateMap<PagingModel<ReceiptSettlementOrder>, PagingModel<UnReceiptDetailsByEarlyWarningOutput>>();
    }
}
