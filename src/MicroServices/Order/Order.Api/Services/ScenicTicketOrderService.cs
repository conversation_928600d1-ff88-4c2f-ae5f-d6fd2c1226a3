using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Notify.DTOs.CustomerNotify;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.OrderLog;
using Contracts.Common.Order.DTOs.ReservationOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder.OTA;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.Scenic.DTOs.ScenicSpot;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.AgencyCredit;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using DotNetCore.CAP;
using EfCoreExtensions;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hangfire;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Contracts.Channel;
using Order.Api.Services.OpenPlatform.Interfaces;
using System.Linq.Expressions;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.Api.Services;

public class ScenicTicketOrderService : BaseOrderSeriesNumberService, IScenicTicketOrderService
{
    /// <summary>
    /// 门票凭证来源类型(不包含系统发码)
    /// </summary>
    private CredentialSourceType[] CredentialSourceTypes =>
        new[]
        {
            CredentialSourceType.PurchasingImport,
            CredentialSourceType.InterfaceDock,
            CredentialSourceType.ManualDelivery
        };

    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    private readonly CustomDbContext _dbContext;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IUserCouponOrderService _userCouponOrderService;
    private readonly IMultPriceCalculateService _multPriceCalculateService;
    private readonly IAgencyCreditPayService _agencyCreditPayService;
    private readonly ITicketCodeCreateService _ticketCodeCreateService;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly ICurrencyExchangeRateService _currencyExchangeRateService;
    private readonly IAgencyService _agencyService;
    private readonly IMediator _mediator;
    private readonly IRedisClient _redisClient;
    private readonly IScenicTicketOTAService _scenicTicketOtaService;
    private readonly IScenicTicketOrderMessageService _scenicTicketOrderMessageService;
    private readonly ISupplierService _supplierService;
    private readonly IScenicTicketPurchaseService _scenicTicketPurchaseService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IOrderFieldInformationService _orderFieldInformationService;
    private readonly IOpenChannelService _openChannelService;

    public ScenicTicketOrderService(
        IMapper mapper,
        ICapPublisher capPublisher,
        CustomDbContext dbContext,
        IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory,
        IBackgroundJobClient backgroundJobClient,
        IUserCouponOrderService userCouponOrderService,
        IMultPriceCalculateService multPriceCalculateService,
        IAgencyCreditPayService agencyCreditPayService,
        ITicketCodeCreateService ticketCodeCreateService,
        IMessageNotifyService messageNotifyService,
        ICurrencyExchangeRateService currencyExchangeRateService,
        IAgencyService agencyService,
        IMediator mediator,
        IRedisClient redisClient,
        IScenicTicketOTAService scenicTicketOtaService,
        IScenicTicketOrderMessageService scenicTicketOrderMessageService,
        ISupplierService supplierService,
        IScenicTicketPurchaseService scenicTicketPurchaseService,
        IOpenPlatformBaseService openPlatformBaseService,
        IOrderFieldInformationService orderFieldInformationService,
        IOpenChannelService openChannelService) : base(dbContext)
    {
        _mapper = mapper;
        _capPublisher = capPublisher;
        _dbContext = dbContext;
        _servicesAddress = servicesAddress.Value;
        _httpClientFactory = httpClientFactory;
        _backgroundJobClient = backgroundJobClient;
        _userCouponOrderService = userCouponOrderService;
        _multPriceCalculateService = multPriceCalculateService;
        _agencyCreditPayService = agencyCreditPayService;
        _ticketCodeCreateService = ticketCodeCreateService;
        _messageNotifyService = messageNotifyService;
        _currencyExchangeRateService = currencyExchangeRateService;
        _agencyService = agencyService;
        _mediator = mediator;
        _redisClient = redisClient;
        _scenicTicketOtaService = scenicTicketOtaService;
        _scenicTicketOrderMessageService = scenicTicketOrderMessageService;
        _supplierService = supplierService;
        _scenicTicketPurchaseService = scenicTicketPurchaseService;
        _openPlatformBaseService = openPlatformBaseService;
        _orderFieldInformationService = orderFieldInformationService;
        _openChannelService = openChannelService;
    }

    /// <summary>
    /// 创建景区门票订单-微商城
    /// </summary>
    /// <param name="input"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public async Task<CreateScenicTicketOrderOutput> CreateByCustomer(CreateByCustomerInput input, CurrentUser user)
    {
        #region 查询景区基础信息

        var scenic = await _httpClientFactory.InternalGetAsync<GetScenicSpotDetailOutput>(
            _servicesAddress.Scenic_GetDetail(input.ScenicSpotId));
        if (scenic is null) return new CreateScenicTicketOrderOutput();

        var firstPhoto = scenic.Photos.FirstOrDefault();
        var scenicName = scenic.Name;

        #endregion

        #region 查询门票基础信息

        //兼容旧入参
        input.TravelDate = input.ValidityBegin ?? DateTime.Today;

        var ticketDetail = await GetScenicTicketDetail(new CreateOrderCheckScenicTicketInfoInput
        {
            ScenicTicketId = input.ScenicTicketId,
            TravelDate = input.TravelDate,
            SellingPlatform = input.SellingPlatform
        });

        //查询供应商类型
        OpenSupplierType? openSupplierApiType = null;
        if (ticketDetail.CredentialSourceType is CredentialSourceType.InterfaceDock)
        {
            openSupplierApiType = await CheckSupplierApiType(ticketDetail.SupplierId);
        }

        #endregion

        #region 计算期票/预订票的库存

        await CheckInventories(new CheckInventoriesInput
        {
            ScenicSpotId = ticketDetail.ScenicSpotId,
            TicketId = input.ScenicTicketId,
            TicketName = ticketDetail.Name,
            TimeSlotId = input.TravelTimeSlotId,
            ScenicTicketsType = ticketDetail.TicketsType,
            PriceInventorySource = ticketDetail.PriceInventorySource,
            PriceInventoryType = ticketDetail.PriceInventoryType,
            SellingPlatform = SellingPlatform.System,
            Quantity = input.Quantity,
            TravelDate = input.TravelDate
        });

        #endregion

        #region 计算价格

        //多币种
        string paymentCurrencyCode = Currency.CNY.ToString();
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = ticketDetail.CostCurrencyCode,
            SaleCurrencyCode = ticketDetail.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });

        ScenicTicketPriceInfo scenicTicketPriceInfo = new()
        {
            LinePrice = ticketDetail.LinePrice,
            OrderMultPrice = new OrderMultPriceDto()
            {
                SaleCurrencyCode = ticketDetail.SaleCurrencyCode,
                OrgPrice = ticketDetail.SellingPrice,
                CostCurrencyCode = ticketDetail.CostCurrencyCode,
                OrgCostPrice = ticketDetail.CostPrice,
                CostPrice = ticketDetail.CostPrice,
                PaymentCurrencyCode = paymentCurrencyCode,
                Price = Math.Round(ticketDetail.SellingPrice * priceExchangeRate.ExchangeRate, 2),
                Quantity = input.Quantity,
                CostExchangeRate = priceExchangeRate.CostExchangeRate,
                ExchangeRate = priceExchangeRate.ExchangeRate,
                CostDiscountRate = ticketDetail.CostDiscountRate
            }
        };

        OrderMultPriceDto orderMultPrice = scenicTicketPriceInfo.OrderMultPrice;

        //限时抢购
        if (input.FlashSaleId > 0)
        {
            var flashSaleOrderResponse = await _mediator.Send(new Requests.FlashSaleOrderRequest
            {
                ProductType = ticketDetail.TicketsType switch
                {
                    ScenicTicketsType.Reservation => FlashSaleItemsType.Scenic_Reservation,
                    ScenicTicketsType.PromissoryNote => FlashSaleItemsType.Scenic_PromissoryNote,
                },
                FlashSaleId = input.FlashSaleId,
                FlashSaleOrderItems = new List<Requests.FlashSaleOrderItemRequest> {
                       new Requests.FlashSaleOrderItemRequest() {
                           FlashSaleItemId = input.FlashSaleItemId,
                           ProductId=input.ScenicSpotId,
                           SkuId=input.ScenicTicketId,
                           Quantity=input.Quantity
                       }
                    }
            });
            var flashSaleItem = flashSaleOrderResponse
                .FlashSaleItems
                .FirstOrDefault(x => x.SkuId == input.ScenicTicketId);
            //设置为抢购价
            if (flashSaleItem.CostPriceValue.HasValue)
            {
                orderMultPrice.CostPrice = flashSaleItem.CostPriceType switch
                {
                    FlashSaleItemsPriceType.Appoint => flashSaleItem.CostPriceValue.Value,
                    FlashSaleItemsPriceType.Derate =>
                    orderMultPrice.CostPrice > flashSaleItem.CostPriceValue.Value ?
                    (orderMultPrice.CostPrice - flashSaleItem.CostPriceValue.Value) : 0,
                    _ => orderMultPrice.CostPrice
                };
                orderMultPrice.CostPriceType = OrderPriceType.FlashSalePrice;
            }
            orderMultPrice.Price = flashSaleItem.SellingPriceType switch
            {
                FlashSaleItemsPriceType.Appoint => flashSaleItem.SellingPriceValue * orderMultPrice.ExchangeRate,
                FlashSaleItemsPriceType.Derate =>
                orderMultPrice.Price > flashSaleItem.SellingPriceValue ?
                    (orderMultPrice.Price - flashSaleItem.SellingPriceValue * orderMultPrice.ExchangeRate) : 0,
                _ => orderMultPrice.Price
            };
            orderMultPrice.PriceType = OrderPriceType.FlashSalePrice;
        }

        if (orderMultPrice.PriceType == OrderPriceType.Default)
        {
            //计算会员折扣价
            var multPrice = await _multPriceCalculateService.GetScenicTicketMultPrice(new GetScenicTicketMultPriceInput
            {
                ScenicSpotId = input.ScenicSpotId,
                TicketsId = input.ScenicTicketId,
                SellingPrice = orderMultPrice.OrgPrice
            });
            if (multPrice.VipPrice.HasValue)
            {
                //设置为会员价
                orderMultPrice.PriceType = OrderPriceType.VipPrice;
                orderMultPrice.Price = multPrice.VipPrice.Value;
            }
        }

        var totalAmount = orderMultPrice.Price * orderMultPrice.Quantity;

        #endregion

        #region 验证优惠券

        //优惠金额
        var discountAmount = 0m;
        var discountItems = new List<OrderDiscountItemDto>();
        if (input.UserCouponId > 0)
        {
            var orderUserCoupon = await _userCouponOrderService.GetOrderUserCoupon(
                new GetOrderUserCouponsInput
                {
                    UserId = user.userid,
                    UserCouponId = input.UserCouponId,
                    ProductType = LimitProductType.Scenic,
                    OrderProductInfos = new List<OrderProductInfo>
                    {
                        new OrderProductInfo
                        {
                            ProductId = input.ScenicSpotId,
                            ItemId = input.ScenicTicketId,
                            OrderAmount = totalAmount
                        }
                    }
                });
            if (orderUserCoupon?.IsEnable is not true)
            {
                throw new BusinessException(ErrorTypes.Marketing.UserCouponDisabled); //优惠券不可用
            }
            discountItems.Add(new OrderDiscountItemDto
            {
                DiscountType = OrderDiscountType.UserCoupon,
                DiscountId = orderUserCoupon.UserCouponId,
                DiscountAmount = orderUserCoupon.Discount,
                Title = orderUserCoupon.CouponName
            });
            discountAmount = orderUserCoupon.Discount; //优惠金额
        }

        var paymentAmount = totalAmount - discountAmount;

        #endregion

        #region 获取会员等级信息

        var customerInfo = await _multPriceCalculateService.GetCustomerUserInfo();

        #endregion

        var createInput = new CreateScenicTicketOrderInput
        {
            TenantId = user.tenant!.Value,
            UserId = user.userid,
            UserName = user.nickname,
            VipLevelId = customerInfo.VipLevel?.VipLevelId ?? 0,
            VipLevelName = customerInfo.VipLevel?.Name ?? "",
            SellingPlatform = input.SellingPlatform,
            SellingChannels = input.SellingChannels,
            Quantity = input.Quantity,
            ValidityBegin = ticketDetail.ValidityBegin!.Value,
            ValidityEnd = ticketDetail.ValidityEnd!.Value,
            ResourceName = scenicName,
            EnResourceName = scenic.ENName,
            ScenicSpotPhotoPath = firstPhoto,
            ScenicSpotId = input.ScenicSpotId,
            ScenicTicketId = input.ScenicTicketId,
            TravelerInfos = input.TravelerInfos,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            Message = input.Message,
            TraceId = input.TraceId,
            DiscountItems = discountItems,
            TotalAmount = totalAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            DiscountAmount = discountAmount,
            OperationType = OrderOperationType.Created,
            TicketDetail = ticketDetail,
            ScenicTicketPriceInfo = scenicTicketPriceInfo,
            FlashSaleInfo = new FlashSaleInfo { FlashSaleId = input.FlashSaleId, FlashSaleItemId = input.FlashSaleItemId },
            OpenSupplierType = openSupplierApiType,
            TravelDate = input.TravelDate!.Value,
            SalespersonId = input.SalespersonId,
            SalespersonName = input.SalespersonName,
            OrderFields = input.OrderFields,
        };

        return await Create(createInput, new OperationUserDto
        {
            UserId = user.userid,
            Name = user.nickname,
            UserType = UserType.Customer
        });
    }

    /// <summary>
    /// 创建景区门票手工单
    /// </summary>
    /// <param name="input"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    public async Task<CreateScenicTicketOrderOutput> CreateByManual(CreateByManualInput input, CurrentUser user)
    {
        #region 查询是否失败订单创建

        if (input.SyncFailOrderId.HasValue)
        {
            var openChannelOrder = await _dbContext.OpenChannelSyncFailOrders
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == input.SyncFailOrderId);

            //异常单状态判断
            if (openChannelOrder.SyncStatus != OpenChannelFailOrderSyncStatus.SyncFail)
            {
                throw new BusinessException(ErrorTypes.Order.SyncFailOrderStatusChanged);
            }

            input.SellingPlatform = openChannelOrder.SellingPlatform;
            input.SellingChannels = openChannelOrder.SellingChannels;
        }


        #endregion

        #region 查询景区基础信息

        var scenic = await _httpClientFactory.InternalGetAsync<GetScenicSpotDetailOutput>(
            _servicesAddress.Scenic_GetDetail(input.ScenicSpotId));
        var firstPhoto = scenic.Photos.FirstOrDefault();
        var scenicName = scenic.Name;

        #endregion

        #region 查询门票基础信息

        var ticketDetail = await GetScenicTicketDetail(new CreateOrderCheckScenicTicketInfoInput
        {
            ScenicTicketId = input.ScenicTicketId,
            TimeSlotId = input.TravelTimeSlotId,
            TravelDate = input.TravelDate.Date,
            SellingPlatform = SellingPlatform.System
        });

        //查询供应商类型
        OpenSupplierType? openSupplierApiType = null;
        if (ticketDetail.CredentialSourceType is CredentialSourceType.InterfaceDock)
        {
            openSupplierApiType = await CheckSupplierApiType(ticketDetail.SupplierId);
        }

        #endregion

        #region API对接产品实名制校验

        if (ticketDetail.CredentialSourceType == CredentialSourceType.InterfaceDock)
        {
            CheckTouristInfo(quantity: input.Quantity,
                touristInfoType: ticketDetail.TouristInfoType,
                orderFields: input.OrderFields);
        }

        #endregion

        #region 判断是否是组合订单渠道单号

        var combinationOrderCheck = await CheckIsCombinationOrder(input.ChannelOrderNo);
        if (combinationOrderCheck.combinationOrderId.HasValue && !input.TicketsCombinationOrderId.HasValue)
            throw new BusinessException(ErrorTypes.Order.NotSupportOTACombinationOrder);

        if (input.TicketsCombinationOrderId.HasValue)
        {
            //组合订单目前只支持一个渠道单号
            if (input.ChannelOrderNo.Length > 1)
            {
                throw new BusinessException(ErrorTypes.Order.NotSupportMultiChannelOrderNo);
            }

            if (!combinationOrderCheck.combinationOrderId.HasValue)
            {
                throw new BusinessException(ErrorTypes.Order.NotSupportOrderReplace);
            }

            if (input.TicketsCombinationOrderId.Value != combinationOrderCheck.combinationOrderId.Value)
            {
                throw new BusinessException(ErrorTypes.Order.NotSupportOrderReplace);
            }

            //替换订单需要是采购导入或者接口对接类型的
            if (!(ticketDetail.CredentialSourceType != null &&
                  CredentialSourceTypes.Contains(ticketDetail.CredentialSourceType.Value)))
            {
                throw new BusinessException(ErrorTypes.Order.NotSupportSystemTicketReplace);
            }

            //替换订单需要校验分销商(组合订单分销商校验)
            if (input.AgencyId != combinationOrderCheck.agencyId)
            {
                throw new BusinessException(ErrorTypes.Order.NotSupportAgencyReplace);
            }

        }

        #endregion

        #region 计算期票/预订票的库存

        // 非补差产品才需要校验库存
        if (ticketDetail.IsCompensation == false)
        {
            await CheckInventories(new CheckInventoriesInput
            {
                ScenicSpotId = ticketDetail.ScenicSpotId,
                TicketId = input.ScenicTicketId,
                TicketName = ticketDetail.Name,
                TimeSlotId = input.TravelTimeSlotId,
                ScenicTicketsType = ticketDetail.TicketsType,
                PriceInventorySource = ticketDetail.PriceInventorySource,
                PriceInventoryType = ticketDetail.PriceInventoryType,
                SellingPlatform = SellingPlatform.System,
                Quantity = input.Quantity,
                TravelDate = input.TravelDate.Date
            });
        }

        #endregion

        #region 金额计算

        //多币种
        var agency = await _agencyService.GetAgencyDetail(input.AgencyId);
        string paymentCurrencyCode = agency.CurrencyCode;
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = ticketDetail.CostCurrencyCode,
            SaleCurrencyCode = ticketDetail.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });

        var scenicTicketPriceInfo = new ScenicTicketPriceInfo
        {
            OrderMultPrice = new OrderMultPriceDto()
            {
                SaleCurrencyCode = ticketDetail.SaleCurrencyCode,
                OrgPrice = input.SellingPrice / priceExchangeRate.ExchangeRate,
                CostCurrencyCode = ticketDetail.CostCurrencyCode,
                OrgCostPrice = ticketDetail.CostPrice,
                CostPrice = input.CostPrice,
                PaymentCurrencyCode = paymentCurrencyCode,
                Price = input.SellingPrice,
                Quantity = input.Quantity,
                CostExchangeRate = priceExchangeRate.CostExchangeRate,
                ExchangeRate = priceExchangeRate.ExchangeRate,
                CostDiscountRate = ticketDetail.CostDiscountRate
            },
            LinePrice = ticketDetail.LinePrice
        };

        var totalAmount = input.SellingPrice * input.Quantity;
        var discountAmount = input.DiscountAmount;
        var paymentAmount = input.PaymentAmount.HasValue ? input.PaymentAmount!.Value : (totalAmount - discountAmount);
        #endregion

        var createInput = new CreateScenicTicketOrderInput
        {
            SyncFailOrderId = input.SyncFailOrderId,
            TenantId = user.tenant!.Value,
            UserId = user.userid,
            UserName = user.nickname,
            AgencyId = input.AgencyId,
            AgencyName = input.AgencyName,
            VipLevelId = agency.Level ?? 0,
            VipLevelName = agency.LevelName ?? string.Empty,
            SellingPlatform = input.SellingPlatform,
            SellingChannels = input.SellingChannels,
            Quantity = input.Quantity,
            ValidityBegin = ticketDetail.ValidityBegin!.Value,
            ValidityEnd = ticketDetail.ValidityEnd!.Value,
            ResourceName = scenicName,
            EnResourceName = scenic.ENName,
            ScenicSpotPhotoPath = firstPhoto,
            ScenicSpotId = input.ScenicSpotId,
            ScenicTicketId = input.ScenicTicketId,
            TravelerInfos = input.TravelerInfos,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            Message = "",
            TotalAmount = totalAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            DiscountAmount = discountAmount,
            OperationType = OrderOperationType.Created,
            TicketDetail = ticketDetail,
            ScenicTicketPriceInfo = scenicTicketPriceInfo,
            Remark = input.Remark,
            SupplierOrderRemark = input.SupplierOrderRemark,
            ChannelOrderNo = string.Join(",", input.ChannelOrderNo),
            SupplierOrderId = input.SupplierOrderId,
            OpeningTime = scenic.OpeningTime,
            ScenicSpotAddress = scenic.Address,
            TicketsCombinationOrderId = input.TicketsCombinationOrderId,
            TimeSlotId = input.TravelTimeSlotId,
            TimeSlot = ticketDetail.TimeSlot,
            OpenSupplierType = openSupplierApiType,
            TravelDate = input.TravelDate,
            SalespersonId = agency.SalespersonId,
            SalespersonName = agency.SalespersonName,
            OrderFields = input.OrderFields,
            OrderExtraInfos = input.OrderExtraInfos
        };

        return await Create(createInput, new OperationUserDto
        {
            UserId = user.userid,
            Name = user.nickname,
            UserType = input.UserType
        });
    }

    /// <summary>
    /// 编辑门票手工单
    /// </summary>
    /// <param name="input"></param>
    /// <param name="user"></param>
    public async Task EditByManual(EditByManualInput input, CurrentUser user)
    {
        var baseOrder = await _dbContext.BaseOrders.FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        if (baseOrder is null) throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        var scenicTicketOrder =
            await _dbContext.ScenicTicketOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        if (scenicTicketOrder is null) throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        #region 判断是否是组合订单渠道单号

        if (baseOrder.ChannelOrderNo != input.ChannelOrderNo)
        {
            if (!string.IsNullOrEmpty(input.ChannelOrderNo))
            {
                var combinationOrderCheck = await CheckIsCombinationOrder(input.ChannelOrderNo.Split(','));
                if (combinationOrderCheck.combinationOrderId.HasValue && !scenicTicketOrder.TicketsCombinationOrderId.HasValue)
                    throw new BusinessException(ErrorTypes.Order.NotSupportOTACombinationOrder);
            }
        }

        #endregion

        #region 判断订单是否可以编辑

        //待支付 || 待完成 可以编辑
        if (baseOrder.Status != BaseOrderStatus.WaitingForPay && baseOrder.Status != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        #endregion

        #region 根据是否生成收款单重新计算购买份数金额
        var orderPrice = await _dbContext.OrderPrices
            .Where(x => x.SubOrderId == scenicTicketOrder.Id)
            .FirstOrDefaultAsync();
        //未生成收款单之前允许修改购买份数和折扣金额.
        var hasReceipt = false;
        if (!hasReceipt && input.Quantity < orderPrice.Quantity)
            throw new BusinessException(ErrorTypes.Order.BuyQuantityCanNotReduced);

        var quantity = hasReceipt ? orderPrice.Quantity : input.Quantity;
        var totalAmount = quantity * orderPrice.Price;
        var discountAmount = hasReceipt ? baseOrder.DiscountAmount : input.DiscountAmount;
        var paymentAmount = totalAmount - discountAmount;

        //购买分数未变更
        var qtyUnChanged = quantity == orderPrice.Quantity;
        var diffQty = quantity - orderPrice.Quantity;
        //优惠总额未变更
        var dicUnChanged = discountAmount == baseOrder.DiscountAmount;
        #endregion

        #region 更新主订单

        baseOrder.ContactsName = input.ContactsName;
        baseOrder.ContactsPhoneNumber = input.ContactsPhoneNumber;
        baseOrder.ChannelOrderNo = input.ChannelOrderNo;
        baseOrder.TotalAmount = totalAmount;
        baseOrder.PaymentAmount = paymentAmount;
        baseOrder.DiscountAmount = discountAmount;
        baseOrder.UpdateTime = DateTime.Now;

        #endregion

        #region 更新门票子订单

        if (!qtyUnChanged)
        {
            scenicTicketOrder.Quantity = quantity;
            orderPrice.Quantity = quantity;
        }

        #endregion

        #region 插入订单日志

        var orderLog = new OrderLogs
        {
            OrderId = baseOrder.Id,
            UserId = user.userid,
            UserName = user.nickname,
            OperationRole = input.UserType,
            OperationType = OrderOperationType.Updated,
            OrderLogType = OrderLogType.ScenicTicket
        };
        await _dbContext.AddAsync(orderLog);

        #endregion

        #region 插入订单备注

        if (string.IsNullOrEmpty(input.Remark) is false)
        {
            var orderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = input.Remark,
                CreatorId = user.userid,
                CreatorName = user.nickname
            };

            await _dbContext.AddAsync(orderRemark);
        }

        #endregion

        #region 更新景区门票出行人

        var ticketOrderTravelerEntities = await _dbContext.ScenicTicketOrderTravelers
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .Where(x => x.ScenicTicketOrderId == scenicTicketOrder.Id)
            .ToListAsync();
        _dbContext.RemoveRange(ticketOrderTravelerEntities);

        if (input.TravelerInfos.Any())
        {
            var ticketOrderTravelers = input.TravelerInfos
                .Select(x => new ScenicTicketOrderTravelers
                {
                    BaseOrderId = baseOrder.Id,
                    ScenicTicketOrderId = scenicTicketOrder.Id,
                    Name = x.Name,
                    IDCard = x.IDCard,
                    PhoneNumber = x.PhoneNumber
                });
            await _dbContext.AddRangeAsync(ticketOrderTravelers);
        }

        #endregion

        //订单未完成 且购买份数||优惠金额 变更时.执行授权额度支付操作事件
        if (baseOrder.Status == BaseOrderStatus.UnFinished && (!qtyUnChanged || !dicUnChanged))
        {
            //执行授权额度支付操作事件
            var editPayInput = new OrderEditPayRecordInput
            {
                AgencyId = baseOrder.AgencyId,
                OrderType = OrderType.ScenicTicket,
                OrderId = baseOrder.Id,
                OrderAmount = baseOrder.PaymentAmount
            };
            await _agencyCreditPayService.OrderEditPay(editPayInput);

            #region 生成券码

            if (diffQty > 0)
            {
                var codes = await _ticketCodeCreateService.Get(diffQty);
                var ticketCodes = codes
                    .Select(s => new TicketCode
                    {
                        OrderType = OrderType.ScenicTicket,
                        BaseOrderId = baseOrder.Id,
                        SubOrderId = scenicTicketOrder.Id,
                        Code = s,
                        Status = TicketCodeStatus.WaitingForUse
                    })
                    .ToList();
                await _dbContext.AddRangeAsync(ticketCodes);
            }

            #endregion
        }
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task<CreateScenicTicketOrderOutput> Create(CreateScenicTicketOrderInput input, OperationUserDto operationUserDto)
    {
        var contactFields = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Contacts)?.Fields;
        if (contactFields is not null)
        {
            var contactOrderField = _orderFieldInformationService.ChangeContact(contactFields);
            input.ContactsEmail = contactOrderField.ContactsEmail;
            input.ContactsName = contactOrderField.ContactsName;
            input.ContactsPhoneNumber = contactOrderField.ContactsPhoneNumber;
        }
        #region 主订单 & 订单日志

        //创建主订单
        var baseOrder = new BaseOrder
        {
            UserId = input.UserId,
            UserNickName = input.UserName,
            VipLevelId = input.VipLevelId,
            VipLevelName = input.VipLevelName,
            AgencyId = input.AgencyId,
            AgencyName = input.AgencyName,
            ContactsName = input.ContactsName ?? "",
            ContactsPhoneNumber = input.ContactsPhoneNumber ?? "",
            ContactsEmail = input.ContactsEmail,
            ResourceName = input.ResourceName,
            EnResourceName = input.EnResourceName,
            ProductName = input.TicketDetail.Name,
            EnProductName = input.TicketDetail.EnName,
            ProductSkuName = "",
            OrderType = OrderType.ScenicTicket,
            Status = BaseOrderStatus.WaitingForPay,
            SellingPlatform = input.SellingPlatform,
            SellingChannels = input.SellingChannels,
            TotalAmount = input.TotalAmount,
            DiscountAmount = input.DiscountAmount,
            PaymentAmount = input.PaymentAmount,
            PaymentCurrencyCode = input.PaymentCurrencyCode,
            Message = input.Message,
            ChannelOrderNo = input.ChannelOrderNo,
            DevelopUserId = input.TicketDetail.DevelopUserId,
            SalespersonId = input.SalespersonId,
            SalespersonName = input.SalespersonName,
            OrderCategory = OrderCategory.RegularOrder,
        };

        //订单分类处理
        if (input.TicketDetail.IsCompensation)
        {
            baseOrder.OrderCategory = OrderCategory.CompensationOrder; //补差单
        }

        if (input.TicketsCombinationOrderId.HasValue)
        {
            baseOrder.OrderCategory = OrderCategory.CombinationOrder; //组合单
        }

        //异常订单创单
        if (input.SyncFailOrderId.HasValue)
        {
            //则将异常订单的订单号赋值给主订单
            baseOrder.Id = input.SyncFailOrderId.Value;

            //赋值跟单人id
            var syncFailOrder = await _dbContext.OpenChannelSyncFailOrders.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == input.SyncFailOrderId.Value);
            baseOrder.TrackingUserId = syncFailOrder?.TrackingUserId;
        }

        var platform = input.SellingPlatform;
        // 手工单下单时按照所选“来源渠道”给订单赋值运营人
        if (input.SellingPlatform == SellingPlatform.System)
        {
            platform = baseOrder.SellingChannels switch
            {
                SellingChannels.Ctrip => SellingPlatform.Ctrip,
                SellingChannels.Meituan => SellingPlatform.Meituan,
                SellingChannels.Fliggy => SellingPlatform.Fliggy,
                SellingChannels.B2b => SellingPlatform.B2BWeb,
                _ => SellingPlatform.System,
            };
        }
        var productOperatorUser = input.TicketDetail.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == platform);
        baseOrder.OperatorUserId = productOperatorUser?.OperatorUserId;
        baseOrder.OperatorAssistantUserId = productOperatorUser?.OperatorAssistantUserId;

        await _dbContext.AddAsync(baseOrder);

        //创建订单日志
        var orderLog = new OrderLogs
        {
            OrderId = baseOrder.Id,
            UserId = operationUserDto.UserId,
            UserName = operationUserDto.Name,
            OperationRole = operationUserDto.UserType,
            OperationType = input.OperationType,
            OrderLogType = OrderLogType.ScenicTicket,
        };
        await _dbContext.AddAsync(orderLog);

        #endregion

        #region 景区门票子订单
        OrderMultPriceDto orderMultPrice = input.ScenicTicketPriceInfo.OrderMultPrice;
        var ticketOrder = new ScenicTicketOrder
        {
            BaseOrderId = baseOrder.Id,
            Quantity = input.Quantity,
            ValidityBegin = input.ValidityBegin,
            ValidityEnd = input.ValidityEnd,
            SupplierId = input.TicketDetail.SupplierId,
            SupplierOrderId = input.SupplierOrderId,//采购单号
            ScenicSpotId = input.ScenicSpotId,
            ScenicSpotPhotoPath = input.ScenicSpotPhotoPath,
            ScenicTicketId = input.ScenicTicketId,
            ScenicTicketsType = input.TicketDetail.TicketsType,
            LinePrice = input.ScenicTicketPriceInfo.LinePrice,
            FeeNote = input.TicketDetail.FeeNote,
            FeeNotNote = input.TicketDetail.FeeNotNote,
            OtherNote = input.TicketDetail.OtherNote,
            NeedToExchange = input.TicketDetail.NeedToExchange,
            ExchangeLocation = input.TicketDetail.ExchangeLocation,
            ExchangeProof = (int)input.TicketDetail.ExchangeProofs
                .Aggregate(new ExchangeProofType(), (current, item) => current | item),
            ExchangeNote = input.TicketDetail.ExchangeNote,
            IsSupportRefund = input.TicketDetail.IsSupportRefund,
            RefundDaysInAdvance = input.TicketDetail.RefundDaysInAdvance,
            RefundTimeInAdvance = TimeOnly.FromTimeSpan(input.TicketDetail.RefundTimeInAdvance),
            RefundRate = input.TicketDetail.RefundRate,
            AutoRefundAfterExpiration = input.TicketDetail.AutoRefundAfterExpiration,
            AutoRefundRate = input.TicketDetail.AutoRefundRate,
            CredentialSourceType = input.TicketDetail.CredentialSourceType,
            Status = ScenicTicketOrderStatus.WaitingForDeliver,
            OpeningTime = input.OpeningTime,
            ScenicSpotAddress = input.ScenicSpotAddress,
            SupplierActivityId = input.TicketDetail.ActivityId,
            SupplierPackageId = input.TicketDetail.PackageId,
            SupplierSkuId = input.TicketDetail.SkuId,
            TicketsCombinationOrderId = input.TicketsCombinationOrderId,
            TimeSlotId = input.TimeSlotId,
            TimeSlot = input.TimeSlot,
            OpenSupplierType = input.OpenSupplierType,
            TravelDate = input.TravelDate,
            IsChannelTimeliness = input.TicketDetail.IsChannelTimeliness
        };
        if (input.TicketDetail.TimelinessChannelSettingInfos.Any())
        {
            ticketOrder.TimelinessChannelTypes = input.TicketDetail.TimelinessChannelSettingInfos
                .Select(x => x.TimelinessChannelType)
                .Aggregate(new PriceInventorySyncChannelType(), (current, item) => current | item);
        }
        await _dbContext.AddAsync(ticketOrder);

        OrderPrice orderPrice = new()
        {
            BaseOrderId = ticketOrder.BaseOrderId,
            SubOrderId = ticketOrder.Id,
            OrderType = OrderType.ScenicTicket,
            OrgCostPrice = orderMultPrice.OrgCostPrice,
            OrgPriceCurrencyCode = orderMultPrice.SaleCurrencyCode,
            OrgPrice = orderMultPrice.OrgPrice,
            PriceType = orderMultPrice.PriceType,
            PaymentCurrencyCode = orderMultPrice.PaymentCurrencyCode,
            Price = orderMultPrice.Price,
            CostPriceType = orderMultPrice.PriceType,
            CostCurrencyCode = orderMultPrice.CostCurrencyCode,
            CostPrice = orderMultPrice.CostPrice,
            Quantity = orderMultPrice.Quantity,
            CostExchangeRate = orderMultPrice.CostExchangeRate,
            ExchangeRate = orderMultPrice.ExchangeRate,
            CostDiscountRate = orderMultPrice.CostDiscountRate,
        };
        await _dbContext.AddAsync(orderPrice);

        //计算总折扣总额
        baseOrder.CostDiscountAmount =
            Math.Round(orderPrice.CostPrice * orderPrice.Quantity * orderPrice.CostDiscountRate / 100, 2);

        #endregion

        #region 订单字段
        var orderFieldTypes = new List<OrderFieldInformationType>();
        var fields = new List<OrderFieldInformation>();
        foreach (var orderField in input.OrderFields)
        {
            var typeInfo = _mapper.Map<OrderFieldInformationType>(orderField);
            typeInfo.BaseOrderId = ticketOrder.BaseOrderId;
            typeInfo.OrderId = ticketOrder.Id;
            typeInfo.OrderType = baseOrder.OrderType;
            var typeFields = _mapper.Map<List<OrderFieldInformation>>(orderField.Fields);
            typeFields.ForEach(x =>
            {
                x.OrderFieldInformationTypeId = typeInfo.Id;
                x.ProductTemplateType = typeInfo.ProductTemplateType;
            });
            fields.AddRange(typeFields);
            orderFieldTypes.Add(typeInfo);
        }

        await _dbContext.AddRangeAsync(orderFieldTypes);
        await _dbContext.AddRangeAsync(fields);

        #endregion

        #region 订单备注

        if (string.IsNullOrEmpty(input.Remark) is false)
        {
            var orderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = input.Remark,
                CreatorId = operationUserDto.UserId,
                CreatorName = operationUserDto.Name
            };

            await _dbContext.AddAsync(orderRemark);
        }

        if (string.IsNullOrEmpty(input.SupplierOrderRemark) is false)
        {
            var supplierOrderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = input.SupplierOrderRemark,
                CreatorId = operationUserDto.UserId,
                CreatorName = operationUserDto.Name,
                RemarkType = BaseOrderRemarkType.SupplierOrderRemark
            };

            await _dbContext.AddAsync(supplierOrderRemark);
        }

        #endregion

        #region 供应商订单附加信息


        // 记录供应商-订单附加信息
        if (input.OrderExtraInfos.Any() && input.OpenSupplierType.HasValue)
        {
            var extraInfos = input.OrderExtraInfos
                .Where(x => !string.IsNullOrEmpty(x.OptionValue))
                .Select(x => new OpenSupplierOrderExtraInfo
                {
                    BaseOrderId = baseOrder.Id,
                    OrderType = baseOrder.OrderType,
                    OpenSupplierType = input.OpenSupplierType!.Value,
                    DataType = x.DataType,
                    OptionKey = x.OptionKey,
                    OptionValue = x.OptionValue
                })
                .ToList();
            await _dbContext.AddRangeAsync(extraInfos);
        }

        #endregion

        #region 订单优惠项

        if (input.DiscountItems.Any())
        {
            await _dbContext.BaseOrderDiscounts.AddRangeAsync(input.DiscountItems.Select(x => new BaseOrderDiscount
            {
                BaseOrderId = baseOrder.Id,
                DiscountType = x.DiscountType,
                DiscountAmount = x.DiscountAmount,
                DiscountId = x.DiscountId,
                Title = x.Title
            }));
        }

        #endregion

        #region 订单分享信息

        if (input.TraceId is > 0)
        {
            var orderShareInfo = new OrderShareInfo
            {
                BaseOrderId = baseOrder.Id,
                OrderType = baseOrder.OrderType,
                BuyerId = baseOrder.UserId,
                TraceId = input.TraceId,
                CreateTime = baseOrder.CreateTime
            };
            await _dbContext.AddAsync(orderShareInfo);

            //上送跟踪日志
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = orderShareInfo.TraceId.Value,
                    CustomerId = orderShareInfo.BuyerId,
                    BehaviorType = TraceBehaviorType.CreateOrder,
                    OrderType = baseOrder.OrderType,
                    OrderId = baseOrder.Id,
                    VisitTargetName = baseOrder.ResourceName
                });
        }

        #endregion

        #region 期票/预订票库存冻结事件
        //分销渠道.价库同步类型的订单不扣库存
        if (!_scenicTicketOtaService.OtaSellingPlatforms.Contains(input.SellingPlatform) && input.TicketDetail.IsCompensation == false)
        {
            if (input.TicketDetail is
                { TicketsType: ScenicTicketsType.PromissoryNote, PriceInventorySource: PriceInventorySource.System })
            {
                var frozenGeneralInventoryCommand = new FrozenGeneralInventoryMessage
                {
                    TenantId = input.TenantId,
                    OrderId = ticketOrder.Id,
                    ProductId = input.ScenicSpotId,
                    ItemId = input.ScenicTicketId,
                    FrozenQuantity = input.Quantity
                };
                await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenGeneralInventory,
                    frozenGeneralInventoryCommand);
            }
            else
            {
                if (input.TimeSlotId.HasValue)
                {
                    //时段日历库存扣减
                    var frozenTimeSlotCalendarInventoryCommand = new FrozenTimeSlotInventoryMessage
                    {
                        TenantId = input.TenantId,
                        BeginDate = input.ValidityBegin,
                        EndDate = input.ValidityEnd,
                        ProductId = input.ScenicSpotId,
                        SkuId = input.ScenicTicketId,
                        TimeSlotId = input.TimeSlotId.Value,
                        OrderId = ticketOrder.Id,
                        FrozenQuantity = input.Quantity
                    };
                    await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenTimeSlotInventory,
                        frozenTimeSlotCalendarInventoryCommand);
                }
                else
                {
                    //非时段日历库存扣减
                    var frozenCalendarInventoryCommand = new FrozenCalendarInventoryMessage
                    {
                        TenantId = input.TenantId,
                        BeginDate = input.ValidityBegin,
                        EndDate = input.ValidityEnd,
                        ProductId = input.ScenicSpotId,
                        ItemId = input.ScenicTicketId,
                        OrderId = ticketOrder.Id,
                        FrozenQuantity = input.Quantity
                    };
                    await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenCalendarInventory,
                        frozenCalendarInventoryCommand);
                }
            }
        }

        #endregion

        #region 优惠券事件

        var discountItem = input.DiscountItems.FirstOrDefault(x => x.DiscountType == OrderDiscountType.UserCoupon);
        if (discountItem is not null)
        {
            var couponUsedCommand = new UserCouponUsedMessage
            {
                TenantId = input.TenantId,
                UserCouponId = discountItem.DiscountId,
                BaseOrderId = baseOrder.Id,
                OrderType = OrderType.ScenicTicket
            };
            await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponUsed, couponUsedCommand);
        }

        #endregion

        #region 手工单组合单替换 - 合单采购判断

        if (input is { TicketsCombinationOrderId: not null, TicketDetail.CredentialSourceType: CredentialSourceType.InterfaceDock })
        {
            await CombinationMergeOrderProcess(baseOrderId: baseOrder.Id,
                combinationOrderId: input.TicketsCombinationOrderId.Value,
                openSupplierType: ticketOrder.OpenSupplierType!.Value,
                activityId: ticketOrder.SupplierActivityId,
                packageId: ticketOrder.SupplierPackageId);
        }

        #endregion

        await _dbContext.SaveChangesAsync();


        //提交事务前 促销活动扣活动库存并记录订单信息
        if (input.FlashSaleInfo?.FlashSaleId > 0)
        {
            await _mediator.Send(new Requests.FlashSaleOrderRecordRequest
            {
                FlashSaleId = input.FlashSaleInfo.FlashSaleId,
                UserId = baseOrder.UserId,
                RecordItems = new List<Requests.FlashSaleRecordItem> {
                            new Requests.FlashSaleRecordItem {
                               FlashSaleItemId = input.FlashSaleInfo.FlashSaleItemId,
                               OrderId = ticketOrder.Id,
                               Quantity = orderPrice.Quantity
                            }
                        }
            });
        }

        //更新渠道同步失败订单状态
        if (input.SyncFailOrderId.HasValue)
        {
            await _capPublisher.PublishAsync(CapTopics.Order.OpenChannelSyncFailOrderStatusUpdate,
                new OpenChannelSyncFailOrderStatusUpdateMessage
                {
                    SyncFailOrderId = input.SyncFailOrderId.Value,
                    TenantId = input.TenantId,
                    OrderType = OrderType.ScenicTicket,
                    RecordSource = OpenChannelSyncFailOrderRecordSource.ManualCreate,
                    OperatorId = operationUserDto.UserId,
                    Operator = operationUserDto.Name
                });
        }

        await _capPublisher.PublishAsync(CapTopics.Product.BytePlusItemAttributesUpdate, new BytePlusItemAttributesUpdateMessage
        {
            TenantId = input.TenantId,
            OrderItems = new List<BytePlusOrderItemDto>() {
                new ()
                {
                    Id=$"{baseOrder.Id}_{ticketOrder.ScenicTicketId}",
                    BeginDate=(ticketOrder.TravelDate ?? ticketOrder.ValidityBegin).ToString("yyyy-MM-dd"),
                    EndDate=(ticketOrder.TravelDate ?? ticketOrder.ValidityEnd).ToString("yyyy-MM-dd"),
                    ItemCategory= BytePlusItemCategory.ScenicTicket,
                    ItemSubCategory= ticketOrder.ScenicTicketsType switch
                    {
                        ScenicTicketsType.Reservation=>BytePlusItemSubCategory.ReservationScenicTicket,
                        ScenicTicketsType.PromissoryNote=>BytePlusItemSubCategory.PromissoryNoteScenicTicket,
                    } ,
                    ItemId=ticketOrder.ScenicTicketId,
                    ItemName=input.TicketDetail.Name,
                    ItemVariant=$"{input.TicketDetail.Name}/{input.TicketDetail.TimeSlot}",
                    Quantity=orderPrice.Quantity,
                    Price=orderPrice.Price
                }
            }
        });

        //非分销渠道订单，超时关闭订单
        if (!_scenicTicketOtaService.OtaSellingPlatforms.Contains(input.SellingPlatform))
        {
            //超时关闭
            _backgroundJobClient.Schedule<HangfireClient.Jobs.IOrderJob>(
                s => s.CloseTimeoutOrder(baseOrder.Id, input.TenantId),
                TimeSpan.FromMinutes(30));
        }

        return new CreateScenicTicketOrderOutput
        {
            BaseOrderId = baseOrder.Id,
            ScenicTicketOrderId = ticketOrder.Id
        };
    }

    public async Task<CreateScenicTicketOrderOutput> CreateOtaOrder(CreateScenicTicketOrderInput input,
        OperationUserDto operationUserDto)
    {

        var contactFields = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Contacts)?.Fields;
        if (contactFields is not null)
        {
            var contactOrderField = _orderFieldInformationService.ChangeContact(contactFields);
            input.ContactsEmail = contactOrderField.ContactsEmail;
            input.ContactsName = contactOrderField.ContactsName;
            input.ContactsPhoneNumber = contactOrderField.ContactsPhoneNumber;
        }
        var productOperatorUser = input.TicketDetail.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == input.SellingPlatform);
        #region 主订单

        //创建主订单
        var baseOrder = new BaseOrder
        {
            UserId = input.UserId,
            UserNickName = input.UserName,
            VipLevelId = input.VipLevelId,
            VipLevelName = input.VipLevelName,
            AgencyId = input.AgencyId,
            AgencyName = input.AgencyName,
            ContactsName = input.ContactsName ?? "",
            ContactsPhoneNumber = input.ContactsPhoneNumber ?? "",
            ContactsEmail = input.ContactsEmail,
            ResourceName = input.ResourceName,
            ProductName = input.TicketDetail.Name,
            ProductSkuName = "",
            OrderType = OrderType.ScenicTicket,
            Status = BaseOrderStatus.WaitingForPay,//飞猪订单已支付.仍需要走授信额度支付流程
            SellingPlatform = input.SellingPlatform,
            SellingChannels = input.SellingChannels,
            TotalAmount = input.TotalAmount,
            DiscountAmount = input.DiscountAmount,
            PaymentAmount = input.PaymentAmount,
            PaymentCurrencyCode = input.PaymentCurrencyCode,
            Message = input.Message,
            ChannelOrderNo = input.ChannelOrderNo,
            PaymentExternalNo = input.PaymentExternalNo,
            DevelopUserId = input.TicketDetail.DevelopUserId,
            OperatorUserId = productOperatorUser?.OperatorUserId,
            OperatorAssistantUserId = productOperatorUser?.OperatorAssistantUserId,
            SalespersonId = input.SalespersonId,
            SalespersonName = input.SalespersonName,
            OrderCategory = OrderCategory.RegularOrder,
        };

        //订单分类处理
        if (input.TicketDetail.IsCompensation)
        {
            baseOrder.OrderCategory = OrderCategory.CompensationOrder;
        }

        if (input.TicketsCombinationOrderId.HasValue)
        {
            baseOrder.OrderCategory = OrderCategory.CombinationOrder;
        }

        baseOrder.SetTenantId(input.TenantId);
        await _dbContext.AddAsync(baseOrder);

        #endregion

        #region 订单日志

        //创建订单日志
        var createOrderLog = new OrderLogs
        {
            OrderId = baseOrder.Id,
            UserId = operationUserDto.UserId,
            UserName = operationUserDto.Name,
            OperationRole = operationUserDto.UserType,
            OperationType = input.OperationType,
            OrderLogType = OrderLogType.ScenicTicket,
        };
        createOrderLog.SetTenantId(input.TenantId);
        await _dbContext.AddAsync(createOrderLog);

        #endregion

        #region 景区门票子订单

        var ticketOrder = new ScenicTicketOrder
        {
            BaseOrderId = baseOrder.Id,
            Quantity = input.Quantity,
            ValidityBegin = input.ValidityBegin,
            ValidityEnd = input.ValidityEnd,
            SupplierId = input.TicketDetail.SupplierId,
            ScenicSpotId = input.ScenicSpotId,
            ScenicSpotPhotoPath = input.ScenicSpotPhotoPath,
            ScenicTicketId = input.ScenicTicketId,
            ScenicTicketsType = input.TicketDetail.TicketsType,
            LinePrice = input.ScenicTicketPriceInfo.LinePrice,
            FeeNote = input.TicketDetail.FeeNote,
            FeeNotNote = input.TicketDetail.FeeNotNote,
            OtherNote = input.TicketDetail.OtherNote,
            NeedToExchange = input.TicketDetail.NeedToExchange,
            ExchangeLocation = input.TicketDetail.ExchangeLocation,
            ExchangeProof = (int)input.TicketDetail.ExchangeProofs
                .Aggregate(new ExchangeProofType(), (current, item) => current | item),
            ExchangeNote = input.TicketDetail.ExchangeNote,
            IsSupportRefund = input.TicketDetail.IsSupportRefund,
            RefundDaysInAdvance = input.TicketDetail.RefundDaysInAdvance,
            RefundTimeInAdvance = TimeOnly.FromTimeSpan(input.TicketDetail.RefundTimeInAdvance),
            RefundRate = input.TicketDetail.RefundRate,
            AutoRefundAfterExpiration = input.TicketDetail.AutoRefundAfterExpiration,
            AutoRefundRate = input.TicketDetail.AutoRefundRate,
            CredentialSourceType = input.TicketDetail.CredentialSourceType,
            Status = ScenicTicketOrderStatus.WaitingForDeliver,
            OpeningTime = input.OpeningTime,
            ScenicSpotAddress = input.ScenicSpotAddress,
            SupplierActivityId = input.TicketDetail.ActivityId,
            SupplierPackageId = input.TicketDetail.PackageId,
            SupplierSkuId = input.TicketDetail.SkuId,
            TicketsCombinationOrderId = input.TicketsCombinationOrderId,
            TimeSlotId = input.TimeSlotId,
            TimeSlot = input.TimeSlot,
            OpenSupplierType = input.OpenSupplierType,
            TravelDate = input.TravelDate,
            IsChannelTimeliness = input.TicketDetail.IsChannelTimeliness
        };
        if (input.TicketDetail.TimelinessChannelSettingInfos.Any())
        {
            ticketOrder.TimelinessChannelTypes = input.TicketDetail.TimelinessChannelSettingInfos
                .Select(x => x.TimelinessChannelType)
                .Aggregate(new PriceInventorySyncChannelType(), (current, item) => current | item);
        }

        ticketOrder.SetTenantId(input.TenantId);
        await _dbContext.AddAsync(ticketOrder);

        #endregion

        #region 初始化订单价格

        OrderMultPriceDto orderMultPrice = input.ScenicTicketPriceInfo.OrderMultPrice;
        OrderPrice orderPrice = new()
        {
            BaseOrderId = ticketOrder.BaseOrderId,
            SubOrderId = ticketOrder.Id,
            OrderType = OrderType.ScenicTicket,
            OrgCostPrice = orderMultPrice.OrgCostPrice,
            OrgPriceCurrencyCode = orderMultPrice.SaleCurrencyCode,
            OrgPrice = orderMultPrice.OrgPrice,
            PriceType = orderMultPrice.PriceType,
            PaymentCurrencyCode = orderMultPrice.PaymentCurrencyCode,
            Price = orderMultPrice.Price,
            CostPriceType = orderMultPrice.PriceType,
            CostCurrencyCode = orderMultPrice.CostCurrencyCode,
            CostPrice = orderMultPrice.CostPrice,
            Quantity = orderMultPrice.Quantity,
            CostExchangeRate = orderMultPrice.CostExchangeRate,
            ExchangeRate = orderMultPrice.ExchangeRate,
            CostDiscountRate = orderMultPrice.CostDiscountRate
        };
        orderPrice.SetTenantId(input.TenantId);
        await _dbContext.AddAsync(orderPrice);

        //计算总折扣总额
        baseOrder.CostDiscountAmount =
            Math.Round(orderPrice.CostPrice * orderPrice.Quantity * orderPrice.CostDiscountRate / 100, 2);

        #endregion

        #region 订单字段
        var orderFieldTypes = new List<OrderFieldInformationType>();
        var fields = new List<OrderFieldInformation>();
        foreach (var orderField in input.OrderFields)
        {
            var typeInfo = _mapper.Map<OrderFieldInformationType>(orderField);
            typeInfo.BaseOrderId = ticketOrder.BaseOrderId;
            typeInfo.OrderId = ticketOrder.Id;
            typeInfo.OrderType = baseOrder.OrderType;
            typeInfo.SetTenantId(input.TenantId);
            var typeFields = _mapper.Map<List<OrderFieldInformation>>(orderField.Fields);
            typeFields.ForEach(x =>
            {
                x.OrderFieldInformationTypeId = typeInfo.Id;
                x.ProductTemplateType = typeInfo.ProductTemplateType;
                x.SetTenantId(input.TenantId);
            });
            fields.AddRange(typeFields);
            orderFieldTypes.Add(typeInfo);
        }

        await _dbContext.AddRangeAsync(orderFieldTypes);
        await _dbContext.AddRangeAsync(fields);

        #endregion

        #region 订单备注

        if (string.IsNullOrEmpty(input.Remark) is false)
        {
            var orderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = input.Remark,
                CreatorId = operationUserDto.UserId,
                CreatorName = operationUserDto.Name
            };
            await _dbContext.AddAsync(orderRemark);
        }

        if (string.IsNullOrEmpty(input.SupplierOrderRemark) is false)
        {
            var supplierOrderRemark = new BaseOrderRemark
            {
                BaseOrderId = baseOrder.Id,
                Remark = input.SupplierOrderRemark,
                CreatorId = operationUserDto.UserId,
                CreatorName = operationUserDto.Name,
                RemarkType = BaseOrderRemarkType.SupplierOrderRemark
            };
            await _dbContext.AddAsync(supplierOrderRemark);
        }

        #endregion

        #region 供应商订单附加信息


        // 记录供应商-订单附加信息
        if (input.OrderExtraInfos.Any() && input.OpenSupplierType.HasValue)
        {
            var extraInfos = input.OrderExtraInfos
                .Where(x => !string.IsNullOrEmpty(x.OptionValue))
                .Select(x => new OpenSupplierOrderExtraInfo
                {
                    BaseOrderId = baseOrder.Id,
                    OrderType = baseOrder.OrderType,
                    OpenSupplierType = input.OpenSupplierType!.Value,
                    DataType = x.DataType,
                    OptionKey = x.OptionKey,
                    OptionValue = x.OptionValue
                })
                .ToList();
            await _dbContext.AddRangeAsync(extraInfos);
        }

        #endregion

        #region 期票/预订票库存冻结事件

        //分销渠道.价库同步类型的订单不扣库存
        if (input.TicketDetail.PriceInventorySource == PriceInventorySource.System)
        {
            if (input.TicketDetail.TicketsType == ScenicTicketsType.PromissoryNote && input.OpenSupplierType is null)
            {
                var frozenGeneralInventoryCommand = new FrozenGeneralInventoryMessage
                {
                    TenantId = input.TenantId,
                    OrderId = ticketOrder.Id,
                    ProductId = input.ScenicSpotId,
                    ItemId = input.ScenicTicketId,
                    FrozenQuantity = input.Quantity
                };
                await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenGeneralInventory,
                    frozenGeneralInventoryCommand);
            }
            else
            {
                if (input.TimeSlotId.HasValue)
                {
                    //时段日历库存扣减
                    var frozenTimeSlotCalendarInventoryCommand = new FrozenTimeSlotInventoryMessage
                    {
                        TenantId = input.TenantId,
                        BeginDate = input.ValidityBegin,
                        EndDate = input.ValidityEnd,
                        ProductId = input.ScenicSpotId,
                        SkuId = input.ScenicTicketId,
                        TimeSlotId = input.TimeSlotId.Value,
                        OrderId = ticketOrder.Id,
                        FrozenQuantity = input.Quantity
                    };
                    await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenTimeSlotInventory,
                        frozenTimeSlotCalendarInventoryCommand);
                }
                else
                {
                    //非时段日历库存扣减
                    var frozenCalendarInventoryCommand = new FrozenCalendarInventoryMessage
                    {
                        TenantId = input.TenantId,
                        BeginDate = input.ValidityBegin,
                        EndDate = input.ValidityEnd,
                        ProductId = input.ScenicSpotId,
                        ItemId = input.ScenicTicketId,
                        OrderId = ticketOrder.Id,
                        FrozenQuantity = input.Quantity
                    };
                    await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenCalendarInventory,
                        frozenCalendarInventoryCommand);
                }
            }
        }

        #endregion

        await _dbContext.SaveChangesAsync();

        return new CreateScenicTicketOrderOutput
        {
            BaseOrderId = baseOrder.Id,
            ScenicTicketOrderId = ticketOrder.Id
        };
    }

    record MergeOrderData(long BaseOrderId, OpenSupplierType OpenSupplierType, string ActivityId, string? PackageId);

    [UnitOfWork]
    public async Task<CreateScenicOTACombinationOrderResult> CreateCombinationOtaOrder(
        List<CreateScenicTicketOrderInput> createOtaInputs,
        CreateScenicOTACombinationInfo combinationInfo,
        OperationUserDto operationUserDto)
    {
        var result = new CreateScenicOTACombinationOrderResult();

        TicketsCombinationOrder? combinationOrder = null;
        if (combinationInfo.AbnormalOrderId.HasValue)
        {
            //组合异常单处理
            combinationOrder = await _dbContext.TicketsCombinationOrders
                .IgnoreQueryFilters()
                .Where(x => x.Id == combinationInfo.AbnormalOrderId && x.TenantId == combinationInfo.TenantId)
                .FirstOrDefaultAsync();

            combinationOrder.AbnormalReason = string.Empty;
            combinationOrder.TicketsCombinationId = combinationInfo.TicketsCombinationId;
            combinationOrder.TicketsCombinationName = combinationInfo.TicketsCombinationName;
            combinationOrder.TicketsCombinationPackageId = combinationInfo.TicketsCombinationPackageId;
            combinationOrder.TicketsCombinationPackageName = combinationInfo.TicketsCombinationPackageName;
            combinationOrder.Quantity = combinationInfo.Quantity;
            combinationOrder.PaymentAmount = combinationInfo.PaymentAmount;
            combinationOrder.PaymentCurrencyCode = combinationInfo.PaymentCurrencyCode;
            combinationOrder.ChannelOrderNo = combinationInfo.ChannelOrderNo;
            combinationOrder.OrderStatus = TicketsCombinationOrderStatus.WaitingForDeliver;
            combinationOrder.SellingPlatform = combinationInfo.SellingPlatform;
            combinationOrder.SellingChannels = combinationInfo.SellingChannels;
            combinationOrder.AgencyId = combinationInfo.AgencyId;
            combinationOrder.TravelDate = createOtaInputs.FirstOrDefault()?.TravelDate;
            combinationOrder.UpdateTime = DateTime.Now;
            combinationOrder.CombinationType = combinationInfo.CombinationType;
        }

        //查无组合异常数据,新增组合订单
        if (combinationOrder == null)
        {
            //正常组合订单流程
            combinationOrder = new TicketsCombinationOrder()
            {
                TicketsCombinationId = combinationInfo.TicketsCombinationId,
                TicketsCombinationName = combinationInfo.TicketsCombinationName,
                TicketsCombinationPackageId = combinationInfo.TicketsCombinationPackageId,
                TicketsCombinationPackageName = combinationInfo.TicketsCombinationPackageName,
                Quantity = combinationInfo.Quantity,
                PaymentAmount = combinationInfo.PaymentAmount,
                PaymentCurrencyCode = combinationInfo.PaymentCurrencyCode,
                ChannelOrderNo = combinationInfo.ChannelOrderNo,
                OrderStatus = TicketsCombinationOrderStatus.WaitingForDeliver,
                SellingPlatform = combinationInfo.SellingPlatform,
                SellingChannels = combinationInfo.SellingChannels,
                AgencyId = combinationInfo.AgencyId,
                TravelDate = createOtaInputs.FirstOrDefault()?.TravelDate,
                CombinationType = combinationInfo.CombinationType
            };
            combinationOrder.SetTenantId(combinationInfo.TenantId);
            await _dbContext.AddAsync(combinationOrder);
        }

        var waitMergeData = new List<MergeOrderData>();//组合单合并采购关系绑定数据
        foreach (var createOtaInput in createOtaInputs)
        {
            createOtaInput.TicketsCombinationOrderId = combinationOrder.Id;
            var createOtaResult = await CreateOtaOrder(createOtaInput, operationUserDto);
            result.CombinationBaseOrderIds.Add(createOtaResult.BaseOrderId);

            //API对接订单
            if (createOtaInput.TicketDetail.CredentialSourceType == CredentialSourceType.InterfaceDock)
            {
                waitMergeData.Add(new MergeOrderData(createOtaResult.BaseOrderId,
                    createOtaInput.OpenSupplierType!.Value,
                    createOtaInput.TicketDetail.ActivityId!,
                    createOtaInput.TicketDetail.PackageId));
            }
        }

        //组合单合并采购关系绑定
        if (waitMergeData.Any())
        {
            var groupMergeData = waitMergeData.GroupBy(x => new
            {
                x.OpenSupplierType,
                x.ActivityId,
                x.PackageId
            })
                .ToList();
            foreach (var item in groupMergeData)
            {
                //超过2个子单则绑定合并采购关系.单个子单走正常的采购逻辑
                if (item.Count() <= 1) continue;

                var mergeOrder = new TicketsCombinationMergeOrder()
                {
                    CombinationOrderId = combinationOrder.Id
                };
                mergeOrder.SetTenantId(combinationInfo.TenantId);
                await _dbContext.AddAsync(mergeOrder);

                var mergeOrderItems = item.Select(x =>
                        new TicketsCombinationMergeOrderItem
                        {
                            BaseOrderId = x.BaseOrderId,
                            CombinationOrderId = combinationOrder.Id,
                            MergeOrderId = mergeOrder.Id
                        })
                    .ToList();
                await _dbContext.AddRangeAsync(mergeOrderItems);
            }
        }

        result.TicketsCombinationOrderId = combinationOrder.Id;
        result.TenantId = combinationInfo.TenantId;

        //定时同步OTA
        _backgroundJobClient.Schedule<HangfireClient.Jobs.IOrderJob>(
            s => s.ScenicTicketCombinationOrderDelivery(combinationOrder.Id, combinationInfo.TenantId),
            TimeSpan.FromSeconds(3));

        return result;
    }

    public async Task<GetScenicSpotDetailOutput> GetScenicDetail(long scenicSpotId)
    {
        var scenic = await _httpClientFactory.InternalGetAsync<GetScenicSpotDetailOutput>(
               _servicesAddress.Scenic_GetDetail(scenicSpotId));
        return scenic;
    }

    public async Task<ScenicTicketDetailDto> GetScenicTicketDetail(CreateOrderCheckScenicTicketInfoInput input)
    {
        input.TravelDate ??= DateTime.Today;
        var travelDate = input.TravelDate!.Value.Date;
        var searchResponse = await _httpClientFactory.InternalGetAsync<GetTicketDetailOutput>(
            _servicesAddress.Scenic_GetTicketDetail(input.ScenicTicketId));
        if (searchResponse == null)
            throw new BusinessException(ErrorTypes.Scenic.ScenicTicketsInvalid);
        var ticketDetail = _mapper.Map<ScenicTicketDetailDto>(searchResponse);
        if (ticketDetail.IsCompensation)
        {
            ticketDetail.ValidityBegin = travelDate;
            ticketDetail.ValidityEnd = travelDate;
            return ticketDetail;
        }
        if (ticketDetail.TicketsType == ScenicTicketsType.PromissoryNote && ticketDetail.PriceInventorySource == PriceInventorySource.System)
        {
            if (ticketDetail.ValidityType == ProductValidityType.None && input.CheckBookingTime)
            {
                PersonalizedBusinessEx(ErrorTypes.Scenic.ScenicTicketsInvalid, input.PersonalizedBusinessEx,
                    "产品编码无效", ticketDetail.Name);
            }

            if (ticketDetail.ValidityType == ProductValidityType.FromTheDateOfPurchase)
            {
                ticketDetail.ValidityBegin = input.TravelDate;
                ticketDetail.ValidityEnd = input.TravelDate!.Value.AddDays(ticketDetail.AfterPurchaseDays);
            }

            if (ticketDetail.ValidityType == ProductValidityType.DesignatedDate && input.CheckBookingTime)
            {
                if (ticketDetail.ValidityBegin!.Value > travelDate || ticketDetail.ValidityEnd!.Value < travelDate)
                {
                    PersonalizedBusinessEx(ErrorTypes.Order.SomeDatesAreUnavailable, input.PersonalizedBusinessEx,
                        "部分日期不可下单", ticketDetail.Name);
                }
            }
        }

        if (ticketDetail.TicketsType == ScenicTicketsType.Reservation ||
            (ticketDetail.TicketsType == ScenicTicketsType.PromissoryNote && ticketDetail.PriceInventorySource != PriceInventorySource.System))
        {
            //预订票是否提前购买
            if (ticketDetail.NeedToBuyInAdvance && input.CheckBookingTime)
            {
                //提前X天.在X时间前可购买
                var advanceTime = travelDate
                    .AddDays(-ticketDetail.BuyDaysInAdvance)
                    .Add(ticketDetail.BuyTimeInAdvance);
                var nowTime = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd HH:mm"));
                if (advanceTime < nowTime) //超过最晚可预订时间
                {
                    PersonalizedBusinessEx(ErrorTypes.Order.ExceededBookingTime, input.PersonalizedBusinessEx,
                        "超过最晚可预订时间", ticketDetail.Name);
                }
            }

            //价库同步产品 判断 价格渠道类型
            var priceChannelType = CalendarPriceChannelType.System;
            if (ticketDetail.PriceInventorySource != PriceInventorySource.System)
            {
                priceChannelType = _openPlatformBaseService.MapSellingPlatformToCalendarPriceChannelType(input.SellingPlatform);
            }

            //预订日期
            ticketDetail.ValidityBegin = travelDate;
            ticketDetail.ValidityEnd = travelDate;
            //查询日历价格
            var queryCalendarPriceRequest = new SearchTicketCalendarInput
            {
                ScenicSpotId = ticketDetail.ScenicSpotId,
                TicketIds = new long[] { input.ScenicTicketId },
                BeginDate = travelDate,
                EndDate = travelDate,
                PriceChannelType = priceChannelType
            };
            if (input.TimeSlotId.HasValue)
            {
                queryCalendarPriceRequest.TimeSlotIds = new[] { input.TimeSlotId.Value };
            }
            var calendarPrices = await GetScenicTicketCalendars(queryCalendarPriceRequest);
            var calendarPrice = calendarPrices.FirstOrDefault(x => x.TicketId == input.ScenicTicketId);
            var datePrice = calendarPrice?.Calendars.FirstOrDefault(x => x.Date.Date == travelDate);
            if (datePrice == null)
            {
                PersonalizedBusinessEx(ErrorTypes.Order.CalendarNotEnable, input.PersonalizedBusinessEx,
                    "日历价格不可售", ticketDetail.Name);
            }

            if (!datePrice.CostPrice.HasValue || !datePrice.SellingPrice.HasValue)
            {
                PersonalizedBusinessEx(ErrorTypes.Order.CalendarNotEnable, input.PersonalizedBusinessEx,
                    "日历价格不可售", ticketDetail.Name);
            }

            ticketDetail.CostPrice = datePrice.CostPrice!.Value;
            ticketDetail.LinePrice = datePrice.LinePrice ?? 0;
            ticketDetail.SellingPrice = datePrice.SellingPrice!.Value;
            ticketDetail.TimeSlotId = input.TimeSlotId;
            if (!string.IsNullOrEmpty(calendarPrice.TimeSlotName))
            {
                ticketDetail.TimeSlot = datePrice.Date.TimeOfDay;
            }
        }

        return ticketDetail;
    }

    public async Task CheckInventories(CheckInventoriesInput input)
    {
        var otaSellingPlatforms = _scenicTicketOtaService.OtaSellingPlatforms;
        switch (input.ScenicTicketsType)
        {
            case ScenicTicketsType.PromissoryNote when input.PriceInventorySource == PriceInventorySource.System:
                {
                    //查询普通库存
                    var inventories = await GetGeneralInventories(new GetGeneralInventoryInput
                    {
                        ProductId = input.ScenicSpotId,
                        ItemIds = new long[] { input.TicketId }
                    });
                    var inventory = inventories.FirstOrDefault(x => x.ItemId == input.TicketId);

                    if (input.SellingPlatform != SellingPlatform.System)
                    {
                        if (!(inventory?.Enabled ?? false))
                        {
                            PersonalizedBusinessEx(ErrorTypes.Product.ProductDisabled, input.PersonalizedBusinessEx,
                                "产品不可售", input.TicketName);
                        }
                    }

                    if (inventory?.AvailableQuantity < input.Quantity)
                    {
                        PersonalizedBusinessEx(ErrorTypes.Inventory.ProductInventoryNotEnough, input.PersonalizedBusinessEx,
                            "产品库存不足", input.TicketName);
                    }
                }
                break;
            case ScenicTicketsType.PromissoryNote when input.PriceInventorySource != PriceInventorySource.System:
            case ScenicTicketsType.Reservation:
                {
                    input.TravelDate ??= DateTime.Today;
                    var travelDate = input.TravelDate!.Value.Date;

                    if (input.TimeSlotId.HasValue)
                    {
                        //查询时段日历库存
                        var timeSlotCalendarInventories = await GetTimeSlotInventories(new GetTimeSlotInventoryInput
                        {
                            StartDate = travelDate,
                            EndDate = travelDate,
                            SkuIds = new List<long>() { input.TicketId },
                            TimeSlotIds = input.TimeSlotId.HasValue
                                ? new List<long> { input.TimeSlotId.Value }
                                : new List<long>(),
                            TimeSlots = input.TimeSlot.HasValue
                                ? new List<TimeSpan> { input.TimeSlot.Value }
                                : new List<TimeSpan>()
                        });
                        var inventories = timeSlotCalendarInventories.FirstOrDefault(x => x.TimeSlotId == input.TimeSlotId)
                            ?.Inventories;
                        var inventory = inventories?.FirstOrDefault(x => x.Date == travelDate);

                        if (input.PriceInventorySource != PriceInventorySource.System &&
                            otaSellingPlatforms.Contains(input.SellingPlatform))
                        {
                            //价库同步产品和并且是分销端.只判断是否为0
                            if (inventory?.AvailableQuantity == 0)
                            {
                                PersonalizedBusinessEx(ErrorTypes.Inventory.ProductInventoryNotEnough, input.PersonalizedBusinessEx,
                                    "产品库存不足", input.TicketName);
                            }
                        }
                        else
                        {
                            if (input.SellingPlatform != SellingPlatform.System)
                            {
                                if (!(inventory?.Enabled ?? false))
                                {
                                    PersonalizedBusinessEx(ErrorTypes.Product.ProductDisabled, input.PersonalizedBusinessEx,
                                        "产品不可售", input.TicketName);
                                }
                            }

                            if (inventory?.AvailableQuantity < input.Quantity)
                            {
                                PersonalizedBusinessEx(ErrorTypes.Inventory.ProductInventoryNotEnough, input.PersonalizedBusinessEx,
                                    "产品库存不足", input.TicketName);
                            }
                        }
                    }
                    else
                    {
                        //查询普通日历库存
                        var calendarInventories = await GetCalendarInventories(new GetCalendarInventoryInput
                        {
                            CalendarProducts =
                                new CalendarProduct[]
                                {
                                    new CalendarProduct
                                    {
                                        ProductId = input.ScenicSpotId,
                                        ItemIds = new long[] { input.TicketId }
                                    }
                                },
                            StartDate = travelDate,
                            EndDate = travelDate
                        });
                        var inventories = calendarInventories.FirstOrDefault(x => x.ItemId == input.TicketId)
                            ?.Inventories;
                        var inventory = inventories?.FirstOrDefault(x => x.Date == travelDate);

                        if (input.PriceInventorySource != PriceInventorySource.System &&
                            otaSellingPlatforms.Contains(input.SellingPlatform))
                        {
                            //价库同步产品和并且是分销端.只判断是否为0
                            if (inventory?.AvailableQuantity == 0)
                            {
                                PersonalizedBusinessEx(ErrorTypes.Inventory.ProductInventoryNotEnough, input.PersonalizedBusinessEx,
                                    "产品库存不足", input.TicketName);
                            }
                        }
                        else
                        {
                            if (input.SellingPlatform != SellingPlatform.System)
                            {
                                if (!(inventory?.Enabled ?? false))
                                {
                                    PersonalizedBusinessEx(ErrorTypes.Product.ProductDisabled, input.PersonalizedBusinessEx,
                                        "产品不可售", input.TicketName);
                                }
                            }

                            if (inventory?.AvailableQuantity < input.Quantity)
                            {
                                PersonalizedBusinessEx(ErrorTypes.Inventory.ProductInventoryNotEnough, input.PersonalizedBusinessEx,
                                    "产品库存不足", input.TicketName);
                            }
                        }
                    }
                }
                break;
        }
    }

    /// <summary>
    /// 分页查询景区门票订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>> Search(
        SearchScenicTicketOrderInput input)
    {
        var scenicTicketOrderTotalQueryable = BuildSearchTicketOrderQuery(input);

        var baseOrderTotalQueryable = BuildSearchBaseOrderQuery(input);

        var baseOrderQueryable = baseOrderTotalQueryable
            .WhereIF(input.Status is > 0, x => x.Status == input.Status!.Value)
            .WhereIF(input.TicketOrderStatus is ScenicTicketOrderStatus.WaitingForDeliver, x => x.Status == BaseOrderStatus.UnFinished);

        var scenicTicketOrderQueryable = scenicTicketOrderTotalQueryable
            .WhereIF(input.TicketOrderStatus.HasValue, x => x.Status == input.TicketOrderStatus)
            .WhereIF(input.Status is BaseOrderStatus.UnFinished, x => x.Status == ScenicTicketOrderStatus.Delivered);

        var pagingQueryable = from baseOrder in baseOrderQueryable
                              join scenicTicketOrder in scenicTicketOrderQueryable on baseOrder.Id equals scenicTicketOrder.BaseOrderId
                              select new SearchScenicTicketOrderOutput
                              {
                                  BaseOrderId = baseOrder.Id,
                                  ResourceName = baseOrder.ResourceName,
                                  ProductName = baseOrder.ProductName,
                                  ContactsName = baseOrder.ContactsName,
                                  ContactsPhoneNumber = baseOrder.ContactsPhoneNumber,
                                  ContactsEmail = baseOrder.ContactsEmail,
                                  SellingPlatform = baseOrder.SellingPlatform,
                                  SellingChannels = baseOrder.SellingChannels,
                                  Status = baseOrder.Status,
                                  CreateTime = baseOrder.CreateTime,
                                  TotalAmount = baseOrder.TotalAmount,
                                  DiscountAmount = baseOrder.DiscountAmount,
                                  PaymentAmount = baseOrder.PaymentAmount,
                                  PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
                                  ChannelOrderNo = baseOrder.ChannelOrderNo,
                                  ScenicTicketOrderId = scenicTicketOrder.Id,
                                  ScenicTicketsType = scenicTicketOrder.ScenicTicketsType,
                                  SupplierId = scenicTicketOrder.SupplierId,
                                  ValidityBegin = scenicTicketOrder.ValidityBegin,
                                  ValidityEnd = scenicTicketOrder.ValidityEnd,
                                  TicketOrderStatus = scenicTicketOrder.Status,
                                  CredentialSourceType = scenicTicketOrder.CredentialSourceType,
                                  TimeSlot = scenicTicketOrder.TimeSlot
                              };
        var pagingResult = await pagingQueryable.PagingAsync(input.PageIndex, input.PageSize);

        if (pagingResult.Data.Any())
        {

            #region 查询采购导入订单数据

            var purchaseOrderIds = pagingResult
                .Data
                .Where(x => x.CredentialSourceType == CredentialSourceType.PurchasingImport)
                .Select(x => x.BaseOrderId).Distinct().ToArray();

            var purchaseDeliveryResultMap = await GetPurchaseDeliveryResult(purchaseOrderIds);
            #endregion

            #region 查询供应端接口对接订单数据

            var dockOrderIds = pagingResult.Data
                .Where(x => x.CredentialSourceType == CredentialSourceType.InterfaceDock)
                .Select(x => x.BaseOrderId).Distinct().ToArray();

            var supplierDeliveryResultMap = await GetSupplierDeliveryResult(dockOrderIds);

            #endregion

            #region 手工发货订单数据

            var manualOrderIds = pagingResult
                .Data
                .Where(x => x.CredentialSourceType == CredentialSourceType.ManualDelivery)
                .Select(x => x.BaseOrderId).Distinct().ToArray();

            var manualDeliveryResultMap = await GetManualDeliveryResult(manualOrderIds);
            #endregion

            foreach (var pageItem in pagingResult.Data)
            {
                switch (pageItem.CredentialSourceType)
                {
                    case CredentialSourceType.PurchasingImport:

                        if (purchaseDeliveryResultMap.TryGetValue(pageItem.BaseOrderId, out var purchaseDeliveryResult))
                        {
                            pageItem.DeliverStatus = purchaseDeliveryResult.deliveryStatus;
                            pageItem.DeliveryErrorMsg = purchaseDeliveryResult.msg;
                            pageItem.DeliveryErrorCode = purchaseDeliveryResult.errorCode;
                        }

                        break;
                    case CredentialSourceType.InterfaceDock:

                        if (supplierDeliveryResultMap.TryGetValue(pageItem.BaseOrderId, out var supplierDeliveryResult))
                        {
                            pageItem.DeliverStatus = supplierDeliveryResult.deliveryStatus;
                            pageItem.DeliveryErrorMsg = supplierDeliveryResult.msg;
                            pageItem.DeliveryErrorCode = supplierDeliveryResult.errorCode;
                        }

                        break;

                    case CredentialSourceType.ManualDelivery:

                        if (manualDeliveryResultMap.TryGetValue(pageItem.BaseOrderId, out var manualDeliveryResult))
                        {
                            pageItem.DeliverStatus = manualDeliveryResult.deliveryStatus;
                            pageItem.DeliveryErrorMsg = manualDeliveryResult.msg;
                            pageItem.DeliveryErrorCode = manualDeliveryResult.errorCode;
                        }
                        break;
                }
            }
        }


        //查询订单状态数量
        var orderStatusCount = await SearchOrderStatusCount(input);

        return new PagingModel<SearchScenicTicketOrderOutput, ScenicTicketOrderStatusCount>
        {
            Data = pagingResult.Data,
            PageIndex = pagingResult.PageIndex,
            PageSize = pagingResult.PageSize,
            Total = pagingResult.Total,
            Supplement = orderStatusCount
        };
    }

    public async Task<ScenicTicketOrderStatusCount> SearchOrderStatusCount(SearchScenicTicketOrderInput input)
    {
        var scenicTicketOrderTotalQueryable = BuildSearchTicketOrderQuery(input);
        var baseOrderTotalQueryable = BuildSearchBaseOrderQuery(input);
        var statisticQuery = from baseOrder in baseOrderTotalQueryable
                             join ticketOrder in scenicTicketOrderTotalQueryable on baseOrder.Id equals ticketOrder.BaseOrderId
                             select new { baseOrder, ticketOrder };

        var statisticQueryResult = await statisticQuery
            .Select(x => new
            {
                BaseOrderId = x.baseOrder.Id,
                BaseOrderStatus = x.baseOrder.Status,
                TicketOrderStatus = x.ticketOrder.Status
            })
            .ToListAsync();

        //代付款订单数
        var waitingForPayOrderCount = statisticQueryResult
            .Count(x => x.BaseOrderStatus == BaseOrderStatus.WaitingForPay);

        //待发货订单数量
        var waitingForDeliverOrderCount = statisticQueryResult
            .Count(x => x.BaseOrderStatus == BaseOrderStatus.UnFinished &&
                        x.TicketOrderStatus == ScenicTicketOrderStatus.WaitingForDeliver);

        //待完成订单数量
        var unFinishedOrderCount = statisticQueryResult
            .Count(x => x.BaseOrderStatus == BaseOrderStatus.UnFinished
                        && x.TicketOrderStatus == ScenicTicketOrderStatus.Delivered);

        //已完成订单数量
        var finishedOrderCount = statisticQueryResult
            .Count(x => x.BaseOrderStatus == BaseOrderStatus.Finished);

        //已关闭订单数量
        var closedOrderCount = statisticQueryResult
            .Count(x => x.BaseOrderStatus == BaseOrderStatus.Closed);

        var result = new ScenicTicketOrderStatusCount
        {
            WaitingForPayOrderCount = waitingForPayOrderCount,
            WaitingForDeliverOrderCount = waitingForDeliverOrderCount,
            UnFinishedOrderCount = unFinishedOrderCount,
            FinishedOrderCount = finishedOrderCount,
            ClosedOrderCount = closedOrderCount
        };
        return result;
    }

    #region 订单详情

    public async Task<ScenicTicketOrderDetailOutput> Detail(ScenicTicketOrderDetailInput input)
    {
        var result = new ScenicTicketOrderDetailOutput();
        var orderGroup = await _dbContext.BaseOrders.IgnoreQueryFilters().AsNoTracking()
            .Join(_dbContext.ScenicTicketOrders.IgnoreQueryFilters().AsNoTracking(), x => x.Id, y => y.BaseOrderId,
                (b, s) => new
                {
                    baseOrder = b,
                    scenicTicketOrder = s
                })
            .Where(x => x.baseOrder.Id == input.BaseOrderId)
            .Where(x => x.scenicTicketOrder.BaseOrderId == input.BaseOrderId)
            .WhereIF(input.UserId.HasValue, x => x.baseOrder.UserId == input.UserId.Value)
            .WhereIF(input.AgencyId.HasValue, x => x.baseOrder.AgencyId == input.AgencyId.Value)
            .WhereIF(input.SupplierId.HasValue, x => x.scenicTicketOrder.SupplierId == input.SupplierId.Value)
            .FirstOrDefaultAsync();
        result.BaseOrderInfo = _mapper.Map<ScenicTicketBaseOrderInfo>(orderGroup.baseOrder);
        result.BaseOrderInfo.ChannelOrderNo =
            orderGroup.baseOrder.ChannelOrderNo?.Split(",", StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
            ?? Array.Empty<string>();
        result.ScenicTicketOrderInfo = _mapper.Map<ScenicTicketOrderInfo>(orderGroup.scenicTicketOrder);

        if (result.BaseOrderInfo.Status == BaseOrderStatus.Finished)
            result.BaseOrderInfo.FinishTime = orderGroup.baseOrder.UpdateTime;

        //处理换票凭证类型
        var dbEnum =
            (ExchangeProofType)Enum.ToObject(typeof(ExchangeProofType), orderGroup.scenicTicketOrder.ExchangeProof);
        result.ScenicTicketOrderInfo.ExchangeProofs = Enum.GetValues<ExchangeProofType>()
            .Where(x => (x & dbEnum) == x).ToList();

        //是否结算
        var existSettlement = await _dbContext.SettlementOrderDetails.AsNoTracking()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .AnyAsync();
        result.BaseOrderInfo.ExistSettlementOrder = existSettlement;
        result.BaseOrderInfo.SettlementPayed = existSettlement;


        var orderFieldTypes = await _dbContext.OrderFieldInformationType
                            .Where(x => x.BaseOrderId == result.BaseOrderInfo.Id)
                            .ToListAsync();
        var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
        var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
        var orderFields = await _dbContext.OrderFieldInformations
                           .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
                           .ToListAsync();
        var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
        orderFieldTypesOut.ForEach(type =>
        {
            var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort).ToList();
            type.Fields = fields;
        });
        result.OrderFields = orderFieldTypesOut;

        #region 基础供货信息

        var supplierInfo = new GetSupplierOutput();
        result.SupplyChannelInfo = new SupplyChannelInfo
        {
            SupplierId = result.ScenicTicketOrderInfo.SupplierId,
        };
        if (result.ScenicTicketOrderInfo.SupplierId > 0)
        {
            supplierInfo = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
                requestUri: _servicesAddress.Tenant_GetSupplier(result.ScenicTicketOrderInfo.SupplierId));

            result.SupplyChannelInfo.SupplierName = supplierInfo?.FullName;
            result.SupplyChannelInfo.ShortName = supplierInfo?.ShortName;
            result.SupplyChannelInfo.ChannelOrderNo = result.ScenicTicketOrderInfo.SupplierOrderId;
            result.SupplyChannelInfo.SupplierType = supplierInfo?.SupplierType;

            //baseOrder 采购币种赋值
            result.BaseOrderInfo.CostCurrencyCode = supplierInfo?.CurrencyCode;
        }

        #endregion

        #region 基础分销信息

        if (result.BaseOrderInfo.AgencyId is > 0)
        {
            result.DistributionChannelInfo = new DistributionChannelInfo
            {
                AgencyId = result.BaseOrderInfo.AgencyId!.Value,
                AgencyName = result.BaseOrderInfo.AgencyName,
                SellingChannels = result.BaseOrderInfo.SellingChannels,
                SellingPlatform = result.BaseOrderInfo.SellingPlatform,
                ChannelOrderNo = result.BaseOrderInfo.ChannelOrderNo
            };
        }
        else
        {
            result.DistributionChannelInfo = new DistributionChannelInfo
            {
                SellingChannels = result.BaseOrderInfo.SellingChannels,
                SellingPlatform = result.BaseOrderInfo.SellingPlatform
            };
        }

        #endregion

        //系统门票查询券码信息
        if (!result.ScenicTicketOrderInfo.CredentialSourceType.HasValue ||
            !CredentialSourceTypes.Contains(result.ScenicTicketOrderInfo.CredentialSourceType.Value))
        {
            result.TicketCodeInfos = await GetOrderCodeInfos(input.BaseOrderId, result.ScenicTicketOrderInfo.Id);
        }
        else
        {
            var deliveryResultMap = new Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>();
            switch (result.ScenicTicketOrderInfo.CredentialSourceType)
            {
                case CredentialSourceType.PurchasingImport:
                    deliveryResultMap = await GetPurchaseDeliveryResult(result.BaseOrderInfo.Id);
                    break;
                case CredentialSourceType.InterfaceDock:

                    deliveryResultMap = await GetSupplierDeliveryResult(result.BaseOrderInfo.Id);

                    #region 接口对接,查询客路供货信息补充

                    var supplierOrderInfo = await _dbContext.ScenicTicketSupplierOrders.AsNoTracking()
                        .Where(x => x.BaseOrderId == result.BaseOrderInfo.Id)
                        .Select(x => new { x.SupplierOrderId, x.OrderStatus })
                        .FirstOrDefaultAsync();
                    if (supplierOrderInfo == null)
                    {
                        //兼容旧客路.
                        supplierOrderInfo = await _dbContext.ScenicTicketKlookOrders.AsNoTracking()
                            .Where(x => x.BaseOrderId == result.BaseOrderInfo.Id)
                            .Select(x => new { SupplierOrderId = x.KlookOrderId, x.OrderStatus })
                            .FirstOrDefaultAsync();
                    }

                    result.SupplyChannelInfo = new SupplyChannelInfo
                    {
                        SupplierId = result.ScenicTicketOrderInfo.SupplierId,
                        SupplierName = supplierInfo?.FullName,
                        SupplierApiType = supplierInfo?.SupplierApiSetting?.SupplierApiType,
                        ChannelOrderNo = supplierOrderInfo?.SupplierOrderId,
                        ChannelOrderStatus = supplierOrderInfo?.OrderStatus
                    };

                    #endregion

                    break;
                case CredentialSourceType.ManualDelivery:
                    deliveryResultMap = await GetManualDeliveryResult(result.BaseOrderInfo.Id);
                    break;
            }

            //发货结果
            if (deliveryResultMap.TryGetValue(result.BaseOrderInfo.Id, out var deliveryResult))
            {
                result.DeliveryResult = new DeliveryResult
                {
                    DeliveryErrorMsg = deliveryResult.msg,
                    DeliverStatus = deliveryResult.deliveryStatus,
                    DeliveryErrorCode = deliveryResult.errorCode
                };
            }

            var otaPlatform = _scenicTicketOtaService.OtaSellingPlatforms;
            GetAgenciesByIdsOutput? agencyInfo = null;
            if (result.BaseOrderInfo.AgencyId.HasValue)
            {
                agencyInfo = await _agencyService.GetAgencyDetail(result.BaseOrderInfo.AgencyId!.Value, check: false);
            }
            //判断手工单是否对接分销商
            if (result.BaseOrderInfo.SellingPlatform == SellingPlatform.System)
            {
                if (agencyInfo != null)
                {
                    var systemPlatformOrderCheck = _openPlatformBaseService.CheckSystemPlatformOrder(agencyInfo.AgencyType,
                        agencyInfo.AgencyApiType,
                        otaPlatform);
                    otaPlatform = systemPlatformOrderCheck.otaPlatform;
                }
            }

            if (otaPlatform.Contains(result.BaseOrderInfo.SellingPlatform))
            {
                //OTA分销渠道信息补充
                result.DistributionChannelInfo = new DistributionChannelInfo
                {
                    AgencyId = result.BaseOrderInfo.AgencyId!.Value,
                    AgencyName = result.BaseOrderInfo.AgencyName,
                    AgencyApiType = agencyInfo?.AgencyApiType,
                    SellingChannels = result.BaseOrderInfo.SellingChannels,
                    SellingPlatform = result.BaseOrderInfo.SellingPlatform,
                    ChannelOrderNo = result.BaseOrderInfo.ChannelOrderNo,
                    ChannelOrderStatus = result?.DeliveryResult?.DeliverStatus ==
                                         ScenicTicketOrderDeliveryStatus.SyncFailed
                        ? ScenicTicketOrderStatus.WaitingForDeliver
                        : result.ScenicTicketOrderInfo.Status
                };
            }
        }



        return result;
    }

    public async Task<List<ScenicTicketOrderTravelerInfo>> GetTravelerInfos(long baseOrderId)
    {
        var travelers = await _dbContext.ScenicTicketOrderTravelers.IgnoreQueryFilters().AsNoTracking()
             .Where(x => x.BaseOrderId == baseOrderId).ToListAsync();
        var result = _mapper.Map<List<ScenicTicketOrderTravelerInfo>>(travelers);
        return result;
    }

    public async Task<List<ScenicTicketOrderCodeInfo>> GetOrderCodeInfos(long baseOrderId, long scenicTicketOrderId)
    {
        var ticketCodes = await _dbContext.TicketCodes.IgnoreQueryFilters().AsNoTracking()
                .Where(x => x.BaseOrderId == baseOrderId && x.SubOrderId == scenicTicketOrderId)
                .Select(x => x).ToListAsync();
        var infos = _mapper.Map<List<ScenicTicketOrderCodeInfo>>(ticketCodes);
        return infos;
    }

    public async Task<GetChannelOrderInfoOutput> GetChannelOrderInfo(string channelOrderNo)
    {
        var baseOrder = await _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.ChannelOrderNo == channelOrderNo)
            .OrderByDescending(x => x.Id)
            .FirstOrDefaultAsync();

        if (baseOrder is null)
        {
            return null;
        }

        var result = new GetChannelOrderInfoOutput
        {
            ChannelOrderNo = channelOrderNo,
            ContactsName = baseOrder.ContactsName,
            ContactsEmail = baseOrder.ContactsEmail,
            ContactsPhoneNumber = baseOrder.ContactsPhoneNumber
        };
        var orderPrices = await _dbContext.OrderPrices
            .Where(x => x.BaseOrderId == baseOrder.Id)
            .ToListAsync();
        if (orderPrices.Any())
        {
            result.Quantity = orderPrices.Sum(x => x.Quantity);
            result.OrderPrices = _mapper.Map<List<MultPriceOutput>>(orderPrices);
        }
        result.TravelerInfos = await GetTravelerInfos(baseOrder.Id);
        result.PaymentAmount = baseOrder.PaymentAmount;
        result.DiscountAmount = baseOrder.DiscountAmount;

        var orderFieldTypes = await _dbContext.OrderFieldInformationType
                          .Where(x => x.BaseOrderId == baseOrder.Id)
                          .ToListAsync();
        var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
        var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
        var orderFields = await _dbContext.OrderFieldInformations
                           .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
                           .ToListAsync();
        var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
        orderFieldTypesOut.ForEach(type =>
        {
            var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort).ToList();
            type.Fields = fields;
        });
        result.OrderFields = orderFieldTypesOut;

        return result;
    }

    public async Task<List<GetScenicOrderSimpleInfoOutput>> GetOrderSimpleInfo(params long[] baseOrderIds)
    {
        var scenicTicketOrders = await _dbContext.ScenicTicketOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        var result = scenicTicketOrders.Select(x => new GetScenicOrderSimpleInfoOutput
        {
            Id = x.Id,
            BaseOrderId = x.BaseOrderId,
            CredentialSourceType = x.CredentialSourceType,
            SupplierActivityId = x.SupplierActivityId,
            SupplierPackageId = x.SupplierPackageId,
            SupplierSkuId = x.SupplierSkuId,
            OpenSupplierType = x.OpenSupplierType
        })
            .ToList();
        return result;
    }
    #endregion

    /// <summary>
    /// 延长有效期
    /// 待完成的订单支持延长有效期
    /// </summary>
    /// <param name="input"></param>
    /// <param name="userDto"></param>
    public async Task UpdateValidity(UpdateScenicTicketOrderValidityTimeInput input, OperationUserDto user)
    {
        var baseOrderStatus = await _dbContext.BaseOrders.AsNoTracking()
            .Where(x => x.Id == input.BaseOrderId)
            .Select(x => x.Status)
            .FirstOrDefaultAsync();

        if (baseOrderStatus != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var scenicTicketOrder =
            await _dbContext.ScenicTicketOrders.FirstOrDefaultAsync(x => x.Id == input.ScenicTicketOrderId);
        if (scenicTicketOrder.ScenicTicketsType == ScenicTicketsType.Reservation)
            scenicTicketOrder.ValidityBegin = input.ValidityEnd;
        scenicTicketOrder.ValidityEnd = input.ValidityEnd;

        var orderLog = new OrderLogs
        {
            OrderId = input.BaseOrderId,
            OperationRole = user.UserType,
            OperationType = OrderOperationType.ValidityUpdated,
            OrderLogType = OrderLogType.ScenicTicket,
            UserId = user.UserId,
            UserName = user.Name,
        };

        await _dbContext.AddAsync(orderLog);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 发送使用短信
    /// </summary>
    /// <param name="baseOrderId"></param>
    public async Task SendSms(SendSmsInput input)
    {
        var baseOrder = await _dbContext.BaseOrders.FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        if (baseOrder is null) throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        var scenicTicketOrder =
            await _dbContext.ScenicTicketOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        if (scenicTicketOrder is null) throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        if (baseOrder.Status != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        #region 查询是否开启手工单的门票短信

        var notifyRequest = new GetCustomerNotifySwitchesInput
        {
            NotifyChannel = NotifyChannel.Manual
        };
        var httpContent =
            new StringContent(JsonConvert.SerializeObject(notifyRequest), Encoding.UTF8, "application/json");
        var notifyResult = await _httpClientFactory.InternalPostAsync<List<GetCustomerNotifySwitchesOutput>>(
            requestUri: _servicesAddress.CustomerNotify_GetSwitch(),
            httpContent: httpContent);
        if (notifyResult is null) throw new BusinessException(ErrorTypes.Notify.NotifyNotOpen);

        var scenicNotify = notifyResult
            .Where(x => x.NotifyEventType == NotifyEventType.ScenicTicket)
            .SelectMany(x => x.Items)
            .FirstOrDefault(x => x.NotifyEventSubType == NotifyEventSubType.ScenicTicket_CreateOrder);
        if (scenicNotify is null) throw new BusinessException(ErrorTypes.Notify.NotifyNotOpen);
        //判断是否开启短信和邮箱通知
        if (scenicNotify.SmsNotifyIsOpen is false && scenicNotify.EmailNotifyIsOpen is false)
            throw new BusinessException(ErrorTypes.Notify.NotifyNotOpen);

        #endregion

        var vouchers = new List<ScenicTicketOrderSendMessageVouchers>();

        if (scenicTicketOrder.CredentialSourceType.HasValue &&
            CredentialSourceTypes.Contains(scenicTicketOrder.CredentialSourceType.Value))
        {
            switch (scenicTicketOrder.CredentialSourceType)
            {
                case CredentialSourceType.PurchasingImport:

                    var voucherInfosQuery =
                        from detail in _dbContext.ScenicTicketPurchaseVoucherUsedDetails.AsQueryable()
                        join voucher in _dbContext.ScenicTicketPurchaseVouchers.AsQueryable()
                            on detail.TicketVoucherId equals voucher.Id
                        where detail.BaseOrderId == baseOrder.Id && detail.SubOrderId == scenicTicketOrder.Id
                        select new { detail, voucher };

                    var voucherInfos = await voucherInfosQuery.IgnoreQueryFilters()
                        .ToListAsync();

                    vouchers = voucherInfos.Select(x => new ScenicTicketOrderSendMessageVouchers
                    {
                        FilePath = x.voucher.FilePath,
                        ThumbnailPath = x.voucher.Thumbnail
                    })
                        .ToList();

                    break;
                case CredentialSourceType.InterfaceDock:

                    var supplierOrderVouchers = await _dbContext.ScenicTicketSupplierOrderVouchers
                        .AsNoTracking()
                        .Where(x => x.BaseOrderId == baseOrder.Id)
                        .Select(x => new ScenicTicketOrderSendMessageVouchers
                        {
                            FilePath = x.FilePath,
                            ThumbnailPath = x.Thumbnail
                        })
                        .ToListAsync();
                    vouchers.AddRange(supplierOrderVouchers);

                    break;
                case CredentialSourceType.ManualDelivery:

                    var manualOrderVouchers = await _dbContext.ScenicTicketSupplierOrderVouchers
                        .AsNoTracking()
                        .Where(x => x.BaseOrderId == baseOrder.Id)
                        .Select(x => new ScenicTicketOrderSendMessageVouchers
                        {
                            FilePath = x.FilePath,
                            ThumbnailPath = x.Thumbnail
                        })
                        .ToListAsync();
                    vouchers.AddRange(manualOrderVouchers);

                    break;
            }
        }

        await _scenicTicketOrderMessageService.SendMessageAsync(new ScenicTicketOrderSendMessageInput
        {
            BaseOrderId = baseOrder.Id,
            ScenicTicketOrderId = scenicTicketOrder.Id,
            Vouchers = vouchers
        });
    }

    /// <summary>
    /// 订单过期手动完成
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <param name="ChannelOrderNo">目前只支持单个</param>
    [UnitOfWork]
    public async Task FinishByManual(FinishByManualInput input, OperationUserDto user)
    {
        var baseOrders = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .WhereIF(input.BaseOrderId > 0, x => x.Id == input.BaseOrderId)
            .WhereIF(!string.IsNullOrEmpty(input.ChannelOrderNo), x => x.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .ToListAsync();

        //处理渠道单号
        if (!string.IsNullOrEmpty(input.ChannelOrderNo))
        {
            var processedOrders = baseOrders
                .SelectMany(x => x.ChannelOrderNo.Split(",")
                    .Where(y => !string.IsNullOrEmpty(y))
                    .Select(y => new
                    {
                        x.Id,
                        ChannelOrderNo = y
                    }))
                .Where(x => x.ChannelOrderNo == input.ChannelOrderNo)
                .ToList();
            var processedBaseOrderIds = processedOrders.Select(x => x.Id).Distinct().ToList();
            baseOrders = baseOrders.Where(x => processedBaseOrderIds.Contains(x.Id)).ToList();
        }

        if (baseOrders.Any() is false) throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        var baseOrderIds = baseOrders.Select(x => x.Id).ToList();
        var scenicTicketOrders = await _dbContext.ScenicTicketOrders
            .IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .WhereIF(user.UserType == UserType.Supplier, x => x.SupplierId == user.SupplierId)
            .ToListAsync();

        //判断是否组合订单的状态回显
        if (scenicTicketOrders.Any(x => x.TicketsCombinationOrderId.HasValue))
        {
            var combinationBaseOrderIds = scenicTicketOrders.Where(x => x.TicketsCombinationOrderId.HasValue)
                .Select(x => x.BaseOrderId)
                .ToList();
            baseOrders = baseOrders.Where(x => combinationBaseOrderIds.Contains(x.Id))
                .ToList();

            //更新组合订单状态
            var currentBaseOrderIds = baseOrders.Select(x => x.Id).ToList();
            var combinationOrderIds = scenicTicketOrders.Where(x => x.TicketsCombinationOrderId.HasValue)
                .Select(x => x.TicketsCombinationOrderId)
                .ToList();
            var ticketCombinationOrders = await _dbContext.TicketsCombinationOrders.IgnoreQueryFilters()
                .Where(x => combinationOrderIds.Contains(x.Id))
                .ToListAsync();
            var relatedScenicOrders = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
                .Where(x => x.TicketsCombinationOrderId.HasValue &&
                            combinationOrderIds.Contains(x.TicketsCombinationOrderId))
                .Select(x => new { x.Id, x.BaseOrderId, x.TicketsCombinationOrderId })
                .ToListAsync();
            //取出组合订单关联的其他baseOrderId
            var relatedBaseOrderIds = relatedScenicOrders.Select(x => x.BaseOrderId).ToList();
            var otherBaseOrderIds = relatedBaseOrderIds.Except(currentBaseOrderIds).ToList();
            var otherBaseOrders = await _dbContext.BaseOrders.IgnoreQueryFilters()
                .Where(x => otherBaseOrderIds.Contains(x.Id))
                .Select(x => new { x.Id, x.Status })
                .ToListAsync();

            foreach (var item in ticketCombinationOrders)
            {
                if (otherBaseOrderIds.Any())
                {
                    //查询
                    var relateBOrderIds =
                        relatedScenicOrders.Where(x => x.TicketsCombinationOrderId == item.Id)
                            .Select(x => x.BaseOrderId);
                    var allFinish = otherBaseOrders.Where(x => relateBOrderIds.Contains(x.Id))
                        .All(x => x.Status == BaseOrderStatus.Finished);
                    if (!allFinish) continue;
                }

                item.OrderStatus = TicketsCombinationOrderStatus.Success;
                item.UpdateTime = DateTime.Now;
            }
        }

        var orderPrices = await _dbContext.OrderPrices.IgnoreQueryFilters()
                   .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                   .Select(x => new { x.BaseOrderId, x.ExchangeRate })
                   .ToListAsync();

        var channelOrderModifyRequestList = new List<ChannelOrderModifyRequest>();//处理渠道插旗
        foreach (var baseOrder in baseOrders)
        {
            var scenicTicketOrder = scenicTicketOrders.FirstOrDefault(x => x.BaseOrderId == baseOrder.Id);
            if (scenicTicketOrder is null) throw new BusinessException(ErrorTypes.Order.OrderNotFind);

            if (!scenicTicketOrder.CredentialSourceType.HasValue ||
                !CredentialSourceTypes.Contains(scenicTicketOrder.CredentialSourceType.Value))
            {
                //查询未核销的券码
                var ticketCodeCount = await _dbContext.TicketCodes.AsNoTracking()
                    .Where(x => x.BaseOrderId == baseOrder.Id && x.SubOrderId == scenicTicketOrder.Id)
                    .Where(x => x.Status == TicketCodeStatus.WaitingForUse)
                    .CountAsync();

                //不自动退款且未核销(未发码)且过期的门票，支持手动完成订单
                if (scenicTicketOrder.AutoRefundAfterExpiration ||
                    scenicTicketOrder.ValidityEnd.AddDays(1) >= DateTime.Now ||
                    ticketCodeCount <= 0)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
            }
            else
            {
                if (baseOrder.Status != BaseOrderStatus.UnFinished)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                //门票-标记为已发货
                scenicTicketOrder.Status = ScenicTicketOrderStatus.Delivered;

                if (scenicTicketOrder.CredentialSourceType is CredentialSourceType.PurchasingImport)
                {
                    var purchaseRecord = await _dbContext.ScenicTicketPurchaseDeliveryRecords.IgnoreQueryFilters()
                        .Where(x => x.BaseOrderId == baseOrder.Id)
                        .OrderByDescending(x => x.Id)
                        .FirstOrDefaultAsync();

                    //同步分销渠道失败
                    if (purchaseRecord.Status != OrderVoucherDeliverStatus.SyncFailed)
                        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                    //采购导入门票
                    var voucherQuery = from order in _dbContext.ScenicTicketPurchaseOrders.AsQueryable()
                                       join voucher in _dbContext.ScenicTicketPurchaseVouchers.AsQueryable()
                                           on order.Id equals voucher.PurchaseOrderId
                                       join detail in _dbContext.ScenicTicketPurchaseVoucherUsedDetails on voucher.Id equals detail
                                           .TicketVoucherId
                                       where order.TicketId == scenicTicketOrder.ScenicTicketId
                                             && voucher.Status != ScenicTicketVoucherStatus.UnUsed
                                             && detail.BaseOrderId == baseOrder.Id
                                       select new { order, voucher };

                    var purchaseOrders = await voucherQuery.IgnoreQueryFilters()
                        .ToListAsync();
                    var purchaseOrderIds = purchaseOrders.Select(x => x.order.Id)
                        .Distinct()
                        .ToList();

                    //更新凭证使用状态
                    foreach (var item in purchaseOrders)
                    {
                        item.voucher.Status = ScenicTicketVoucherStatus.Used;
                        item.voucher.UsedTime = DateTime.Now;
                        item.voucher.UpdateTime = DateTime.Now;
                    }

                    //记录采购发货结果
                    var deliveryRecord = new ScenicTicketPurchaseDeliveryRecord
                    {
                        BaseOrderId = baseOrder.Id,
                        SubOrderId = scenicTicketOrder.Id,
                        Status = OrderVoucherDeliverStatus.Success
                    };
                    deliveryRecord.SetTenantId(baseOrder.TenantId);
                    await _dbContext.AddAsync(deliveryRecord);
                }
                else if (scenicTicketOrder.CredentialSourceType is CredentialSourceType.ManualDelivery)
                {
                    var supplierOrderRecord = new ScenicTicketSupplierOrderRecord
                    {
                        BaseOrderId = baseOrder.Id,
                        RecordType = ScenicTicketSupplierOrderRecordType.SyncDelivery,
                        IsSuccess = true
                    };
                    supplierOrderRecord.SetTenantId(baseOrder.TenantId);
                    await _dbContext.AddAsync(supplierOrderRecord);
                }
                else if (scenicTicketOrder.CredentialSourceType is CredentialSourceType.InterfaceDock)
                {
                    //开放平台同步凭证失败 或 同步分销渠道失败
                    var supplierRecords =
                        new List<(long RecordId, long SupplierSaasOrderId, ScenicTicketSupplierOrderStatus OrderStatus,
                            ScenicTicketSupplierOrderRecordType RecordType, bool IsSuccess)>();

                    #region 兼容旧客路

                    var klookDeliveryRecordsQuery =
                        from record in _dbContext.ScenicTicketKlookOrderRecords.AsQueryable()
                        join klookOrder in _dbContext.ScenicTicketKlookOrders.AsQueryable() on record
                                .ScenicTicketKlookOrderId
                            equals klookOrder.Id
                        where klookOrder.BaseOrderId == baseOrder.Id
                        select new { record, klookOrder.BaseOrderId, klookOrder.OrderStatus };
                    var klookDeliveryRecords = await klookDeliveryRecordsQuery.IgnoreQueryFilters()
                        .Select(x => new ValueTuple<long, long, ScenicTicketSupplierOrderStatus,
                            ScenicTicketSupplierOrderRecordType, bool>(x.record.Id, x.record.ScenicTicketKlookOrderId, x.OrderStatus,
                            x.record.RecordType, x.record.IsSuccess))
                        .ToListAsync();
                    supplierRecords.AddRange(klookDeliveryRecords);

                    #endregion

                    #region 供货方发货日志

                    var supplierDeliveryRecordQuery =
                        from supplierOrder in _dbContext.ScenicTicketSupplierOrders.AsQueryable()
                        join record in _dbContext.ScenicTicketSupplierOrderRecords.AsQueryable() on supplierOrder.Id
                            equals record.ScenicTicketSupplierOrderId
                        where supplierOrder.BaseOrderId == baseOrder.Id
                        select new { record, supplierOrder.BaseOrderId, supplierOrder.OrderStatus };

                    var supplierDeliveryRecords = await supplierDeliveryRecordQuery.IgnoreQueryFilters()
                        .Select(x => new ValueTuple<long, long, ScenicTicketSupplierOrderStatus,
                            ScenicTicketSupplierOrderRecordType, bool>(x.record.Id, x.record.ScenicTicketSupplierOrderId, x.OrderStatus,
                            x.record.RecordType, x.record.IsSuccess))
                        .ToListAsync();
                    supplierRecords.AddRange(supplierDeliveryRecords);

                    #endregion

                    var deliveryRecord = supplierRecords
                        .MaxBy(x => x.RecordId);
                    var syncDeliveryFail = deliveryRecord.OrderStatus == ScenicTicketSupplierOrderStatus.Delivered
                                           && deliveryRecord.RecordType is ScenicTicketSupplierOrderRecordType
                                                   .SyncDelivery
                                               or ScenicTicketSupplierOrderRecordType.Delivery
                                           && deliveryRecord.IsSuccess == false;

                    if (!syncDeliveryFail)
                        throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

                    //旧客路订单手动完成发货记录
                    if (klookDeliveryRecords.Any())
                    {
                        var scenicTicketKlookOrderRecord = new ScenicTicketKlookOrderRecord
                        {
                            ScenicTicketKlookOrderId = deliveryRecord.SupplierSaasOrderId,
                            RecordType = ScenicTicketSupplierOrderRecordType.SyncDelivery,
                            IsSuccess = true
                        };
                        scenicTicketKlookOrderRecord.SetTenantId(baseOrder.TenantId);
                        await _dbContext.AddAsync(scenicTicketKlookOrderRecord);
                    }

                    if (supplierDeliveryRecords.Any())
                    {
                        var supplierOrderRecord = new ScenicTicketSupplierOrderRecord
                        {
                            BaseOrderId = baseOrder.Id,
                            ScenicTicketSupplierOrderId = deliveryRecord.SupplierSaasOrderId,
                            RecordType = ScenicTicketSupplierOrderRecordType.SyncDelivery,
                            IsSuccess = true
                        };
                        supplierOrderRecord.SetTenantId(baseOrder.TenantId);
                        await _dbContext.AddAsync(supplierOrderRecord);
                    }
                }
            }


            #region 更改订单状态&插入订单日志

            baseOrder.Status = BaseOrderStatus.Finished;
            baseOrder.UpdateTime = DateTime.Now;

            var orderLog = new OrderLogs
            {
                OrderId = baseOrder.Id,
                OperationRole = user.UserType,
                OperationType = OrderOperationType.FinishedByManual,
                OrderLogType = OrderLogType.ScenicTicket,
                UserId = user.UserId,
                UserName = user.Name,
            };
            orderLog.SetTenantId(baseOrder.TenantId);
            await _dbContext.AddAsync(orderLog);

            //分销商订单转换成长值
            if (baseOrder.SellingPlatform == SellingPlatform.B2BWeb || baseOrder.SellingPlatform == SellingPlatform.B2BApplet)
            {
                var orderPrice = orderPrices.FirstOrDefault(x => x.BaseOrderId == baseOrder.Id);
                await _capPublisher.PublishAsync(CapTopics.Tenant.SyncOrderChangeGrowUpValue, new Contracts.Common.Tenant.DTOs.AgencyLevelDetail.SyncOrderChangeGrowUpValueInput
                {
                    AgencyId = baseOrder.AgencyId,
                    BusinessType = Contracts.Common.Tenant.Enums.AgencyLevelBusinessType.Ticket,
                    OrderAmout = baseOrder.PaymentAmount / orderPrice.ExchangeRate,
                    OrderNo = baseOrder.Id.ToString(),
                    Title = baseOrder.ProductName,
                });
            }

            //记录跟踪日志
            var shareInfo = await _dbContext.OrderShareInfos.IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);
            if (shareInfo?.TraceId is > 0)
            {
                await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                    new PushPromotionTraceRecordMessage
                    {
                        PromotionTraceId = shareInfo.TraceId.Value,
                        CustomerId = shareInfo.BuyerId,
                        BehaviorType = TraceBehaviorType.CompleteOrder,
                        OrderType = baseOrder.OrderType,
                        OrderId = baseOrder.Id,
                        VisitTargetName = baseOrder.ResourceName
                    });
            }

            #endregion

            #region 渠道插旗 (由于存在批量订单完成.直接更新插旗状态,不走事件)

            if (!string.IsNullOrEmpty(baseOrder.ChannelOrderNo))
            {
                //换算渠道类型
                var otaChannelType = await ChannelTypeProcess(
                    sellingPlatform: baseOrder.SellingPlatform,
                    agencyId: baseOrder.AgencyId,
                    tenantId: baseOrder.TenantId);

                //暂时只支持[飞猪]
                if (otaChannelType == OtaChannelType.AliTrip)
                {
                    var channelOrderNos = baseOrder.ChannelOrderNo.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();
                    if (channelOrderNos.Any())
                    {
                        //去重
                        channelOrderNos = channelOrderNos.Distinct().ToList();

                        // 每个渠道订单号，修改为红色插旗
                        foreach (var channelOrderNo in channelOrderNos)
                        {
                            //Saas订单状态为已确认/完成，提示飞猪渠道插旗为红色
                            channelOrderModifyRequestList.Add(new ChannelOrderModifyRequest
                            {
                                OtaType = otaChannelType.ToString().ToLowerInvariant(),
                                OtaOrderId = channelOrderNo,
                                Flag = (int)OpenChannelOrderFlagType.Red
                            });
                        }
                    }
                }
            }

            #endregion
        }

        //插旗修改处理
        if (channelOrderModifyRequestList.Any())
        {
            var tenantId = baseOrders.First().TenantId;
            foreach (var modifyRequest in channelOrderModifyRequestList)
            {
                _ = _openChannelService.OrderModify(modifyRequest, tenantId);
            }
        }
    }

    /// <summary>
    /// b2b 免登陆获取券码核销信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    public async Task<B2BGetByNoLoginOutput> GetTicketCodeByNoLogin(long baseOrderId, long orderId)
    {
        var result = new B2BGetByNoLoginOutput();
        var data = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .Join(_dbContext.ScenicTicketOrders.IgnoreQueryFilters(),
                b => b.Id,
                s => s.BaseOrderId,
                (b, s) => new { b, s })
            .Where(x => x.b.Id == baseOrderId && x.s.Id == orderId)
            .Select(x => new
            {
                x.b.ResourceName,
                x.b.ProductName,
                x.b.ProductSkuName,
                x.b.TenantId,
                x.s.Quantity,
                x.s.ValidityBegin,
                x.s.ValidityEnd,
                x.s.ScenicSpotId
            })
            .FirstOrDefaultAsync();

        if (data is null) return result;

        result.OrderDetail = new B2BGetByNoLoginScenicOrder
        {
            ProductName = data.ProductName,
            Quantity = data.Quantity,
            ValidityBegin = data.ValidityBegin,
            ValidityEnd = data.ValidityEnd
        };

        result.TicketCodes = await _dbContext.TicketCodes.IgnoreQueryFilters()
            .Where(x => x.BaseOrderId == baseOrderId)
            .GroupBy(x => x.Status)
            .Select(x => new B2BGetByNoLoginTicketCode
            {
                Status = x.Key,
                TicketCodes = x.Select(w => new TicketCodeDetail()
                {
                    Code = w.Code,
                    UpdateTime = w.UpdateTime
                })
            })
            .ToListAsync();

        //查询景点资源信息
        var headers = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("tenant", data.TenantId.ToString())
        };
        var scenic = await _httpClientFactory.InternalGetAsync<GetScenicSpotDetailOutput>(
            _servicesAddress.Scenic_GetDetail(data.ScenicSpotId),
            headers: headers);

        result.Resources = new List<B2BGetByNoLoginResources>
        {
            new B2BGetByNoLoginResources
            {
                CityName = scenic.CityName,
                ResourceInfos = new List<B2BGetByNoLoginResourceInfo>
                {
                    new B2BGetByNoLoginResourceInfo
                    {
                        Name = scenic.Name,
                        Address = scenic.Address,
                        Latitude = scenic.Latitude,
                        Longitude = scenic.Longitude
                    }
                }
            }
        };

        return result;
    }

    /// <summary>
    /// 时间提醒
    /// </summary>
    /// <returns></returns>
    public async Task<List<long>> AutoTimeReminder()
    {
        List<long> notifyScenicTicketOrderIds = new();

        var threeDaysAfterTheDate = DateTime.Now.AddDays(3).Date;
        var sevenDaysAfterTheDate = DateTime.Now.AddDays(7).Date;
        var scenicTicketOrderInfos = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
                   .Join(_dbContext.BaseOrders, s => s.BaseOrderId, b => b.Id, (s, b) => new { s, b })
                   .Where(x => x.b.Status.Equals(BaseOrderStatus.UnFinished)
                   && (x.s.ValidityEnd.Date.Equals(threeDaysAfterTheDate)
                   || x.s.ValidityEnd.Date.Equals(sevenDaysAfterTheDate)))
                   .Select(x => new
                   {
                       ProductName = x.b.ResourceName,
                       SkuName = x.b.ProductName,
                       x.s.ValidityBegin,
                       x.s.ValidityEnd,
                       x.b.ResourceName,
                       BaseOrder = new BaseOrderNotify
                       {
                           UserId = x.b.UserId,
                           Id = x.b.Id,
                           SellingPlatform = x.b.SellingPlatform,
                           TenantId = x.b.TenantId
                       },
                       ScenicTicketOrderId = x.s.Id
                   })
                   .ToListAsync();
        if (scenicTicketOrderInfos is null || !scenicTicketOrderInfos.Any()) return notifyScenicTicketOrderIds;

        //提交前判断队列是否已存在该条记录
        string _scenicticketTimeReminderKey = $"openapi:order:scenicticket_time_reminder_{DateTime.Now.Date:yyyy-MM-dd}";
        var cacheScenicTicketOrderIds = await _redisClient
            .ListRangeAsync<long>(_scenicticketTimeReminderKey);
        scenicTicketOrderInfos = scenicTicketOrderInfos
            .Where(x => !cacheScenicTicketOrderIds.Contains(x.ScenicTicketOrderId))
            .ToList();
        if (!scenicTicketOrderInfos.Any()) return notifyScenicTicketOrderIds;

        //消息通知
        foreach (var item in scenicTicketOrderInfos)
        {
            var orderNotifyDto = new OrderNotifyDto<ScenicTicketTimeReminderNotifyDto>()
            {
                BaseOrder = item.BaseOrder,
                NotifyDto = new ScenicTicketTimeReminderNotifyDto
                {
                    ProductName = item.ProductName,
                    SkuName = item.SkuName,
                    ValidityBegin = item.ValidityBegin,
                    ValidityEnd = item.ValidityEnd,
                    ResourceName = item.ResourceName
                }
            };
            await _messageNotifyService.ScenicTicketTimeReminderNotify(orderNotifyDto);
            notifyScenicTicketOrderIds.Add(item.ScenicTicketOrderId);
            await _redisClient.ListLeftPushAsync(_scenicticketTimeReminderKey, item.ScenicTicketOrderId);
        }
        await _redisClient.KeyExpireAsync(_scenicticketTimeReminderKey, TimeSpan.FromDays(1));
        return notifyScenicTicketOrderIds;
    }

    public async Task<IEnumerable<TicketTimeSlotInfo>> GetTimeSlots(GetTimeSlotsInput input)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8,
            "application/json");
        var result = await _httpClientFactory.InternalPostAsync<IEnumerable<TicketTimeSlotInfo>>(
            requestUri: _servicesAddress.Scenic_GetTimeSlots(),
            httpContent: httpContent);
        return result;
    }

    public async Task<OpenSupplierType?> CheckSupplierApiType(long supplierId, bool otaCheck = false)
    {
        var supplierInfo = await _supplierService.GetSupplierInfo(supplierId);

        OpenSupplierType? openSupplierApiType = null;
        if (otaCheck)
        {
            if (supplierInfo.SupplierType != SupplierType.Api)
            {
                throw new BusinessException("供应商无效");
            }

            if (supplierInfo.SupplierApiSetting.SupplierApiType == null)
            {
                throw new BusinessException("供应商无效");
            }

            if (!_scenicTicketOtaService.OpenSupplierApiTypes.Contains(supplierInfo.SupplierApiSetting.SupplierApiType.Value))
            {
                throw new BusinessException("API供应商未配置");
            }

        }

        var supplierApiType = supplierInfo.SupplierApiSetting?.SupplierApiType;
        if (supplierApiType != null)
        {
            openSupplierApiType = _openPlatformBaseService.MapSupplierApiTypeToOpenSupplierType(supplierApiType.Value);
        }

        return openSupplierApiType;
    }

    public async Task<GetAgenciesByIdsOutput?> CheckApiAgency(SellingPlatform sellingPlatform, long tenantId)
    {
        var agencyApiType = _openPlatformBaseService.MapSellingPlatformToAgencyApiType(sellingPlatform);
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        var agencyApiSettings =
            await _agencyService.GetAgencyApiSetting(agencyApiType, headers);
        if (agencyApiSettings == null)
        {
            throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);
        }

        var agency = await _agencyService.GetAgencyDetail(agencyApiSettings.AgencyId);
        if (agencyApiType != agency.AgencyApiType!.Value)
        {
            throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);
        }
        return agency;
    }

    /// <summary>
    /// 获取采购单发货结果
    /// </summary>
    /// <param name="baseOrderId"></param>
    public async Task<Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>>
        GetPurchaseDeliveryResult(params long[] baseOrderIds)
    {
        var result = new Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>();
        if (baseOrderIds.Any() is false)
            return result;

        var purchaseDeliveryRecords = await _dbContext.ScenicTicketPurchaseDeliveryRecords
            .AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        if (purchaseDeliveryRecords.Any() is false)
            return result;

        foreach (var baseOrderId in baseOrderIds)
        {
            var msg = string.Empty;
            int? errorCode = null;
            var deliveryStatus = new ScenicTicketOrderDeliveryStatus();
            var record = purchaseDeliveryRecords
                .Where(x => x.BaseOrderId == baseOrderId)
                .MaxBy(x => x.Id);

            if (record == null) continue;

            switch (record.Status)
            {
                case OrderVoucherDeliverStatus.InventoryNotEnough:

                    //1、若产品凭证来源=采购导入，若库存不足或其他原因导致的无法发货显示发货失败
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.DeliveryFail;
                    errorCode = (int)OrderBusinessErrorCodeType.InventoryNotEnough;

                    break;
                case OrderVoucherDeliverStatus.SyncFailed:

                    //2.同步API对接的分销商时，同步失败显示
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.SyncFailed;
                    errorCode = (int)OrderBusinessErrorCodeType.SyncFailed;

                    break;
                case OrderVoucherDeliverStatus.InCombination:

                    //组合套餐等待发货中
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.WaitingForDeliver;
                    errorCode = (int)OrderBusinessErrorCodeType.InCombination;

                    break;
            }
            result.Add(record.BaseOrderId, (deliveryStatus, msg, errorCode));
        }

        return result;
    }

    /// <summary>
    /// 获取接口对接订单发货结果
    /// </summary>
    /// <param name="baseOrderIds"></param>
    /// <returns></returns>
    record SupplierDeliveryRecords(
        long RecordId,
        bool IsSuccess, ScenicTicketSupplierOrderRecordType RecordType, string? ErrorMsg,
        int? ErrorCode, long BaseOrderId, ScenicTicketSupplierOrderStatus OrderStatus);
    public async Task<Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>>
        GetSupplierDeliveryResult(params long[] baseOrderIds)
    {
        var result = new Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>();
        if (baseOrderIds.Any() is false)
            return result;

        var deliveryRecords = new List<SupplierDeliveryRecords>();

        #region 供应商订单

        var supplierOrders = await _dbContext.ScenicTicketSupplierOrders.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .Select(x => new { x.BaseOrderId, x.OrderStatus })
            .ToListAsync();
        if (supplierOrders.Any())
        {
            var supplierOrderRecords = await _dbContext.ScenicTicketSupplierOrderRecords.AsNoTracking()
                .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                .Select(x => new
                {
                    x.Id,
                    x.BaseOrderId,
                    x.IsSuccess,
                    x.RecordType,
                    x.ErrorMsg,
                    x.ErrorCode,
                })
                .ToListAsync();

            var supplierDeliveryRecords = (from record in supplierOrderRecords
                                           let supplierOrder = supplierOrders.FirstOrDefault(x => x.BaseOrderId == record.BaseOrderId)
                                           select new SupplierDeliveryRecords(
                                               record.Id,
                                               record.IsSuccess,
                                               record.RecordType,
                                               record.ErrorMsg,
                                               record.ErrorCode,
                                               record.BaseOrderId,
                                               supplierOrder.OrderStatus))
                .ToList();

            deliveryRecords.AddRange(supplierDeliveryRecords);
        }

        #endregion

        #region 旧客路

        var oldKlookBaseOrderIds = baseOrderIds.Except(supplierOrders.Select(x => x.BaseOrderId))
            .ToList();
        if (oldKlookBaseOrderIds.Any())
        {
            //查询旧客路
            var oldKlookorders = await _dbContext.ScenicTicketKlookOrders.AsNoTracking()
                .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                .Select(x => new { x.Id, x.BaseOrderId, x.OrderStatus })
                .ToListAsync();
            var oldKlookOrderIds = oldKlookorders.Select(x => x.Id).ToList();
            //查询旧客路订单记录
            var oldKlookRecords = await _dbContext.ScenicTicketKlookOrderRecords.AsNoTracking()
                .Where(x => oldKlookOrderIds.Contains(x.ScenicTicketKlookOrderId))
                .Select(x => new
                {
                    x.Id,
                    x.ScenicTicketKlookOrderId,
                    x.IsSuccess,
                    x.RecordType,
                    x.ErrorMsg,
                    x.ErrorCode,
                })
                .ToListAsync();

            var oldKlookDeliveryRecords = (from record in oldKlookRecords
                                           let oldKlookOrder = oldKlookorders.FirstOrDefault(x => x.Id == record.ScenicTicketKlookOrderId)
                                           select new SupplierDeliveryRecords(
                                               record.Id,
                                               record.IsSuccess,
                                               record.RecordType,
                                               record.ErrorMsg,
                                               record.ErrorCode,
                                               oldKlookOrder.BaseOrderId,
                                               oldKlookOrder.OrderStatus))
                .ToList();

            deliveryRecords.AddRange(oldKlookDeliveryRecords);
        }

        #endregion

        if (deliveryRecords.Any() is false)
            return result;

        foreach (var baseOrderId in baseOrderIds)
        {
            var msg = string.Empty;
            int? errorCode = null;
            var deliveryStatus = new ScenicTicketOrderDeliveryStatus();
            var record = deliveryRecords
                .Where(x => x.BaseOrderId == baseOrderId)
                .MaxBy(x => x.RecordId);

            if (record == null) continue;

            switch (record.OrderStatus)
            {
                case ScenicTicketSupplierOrderStatus.WaitingForDeliver:

                    //1.发货中(支付成功后)
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.WaitingForDeliver;
                    errorCode = (int)OrderBusinessErrorCodeType.WaitingForDeliver;

                    break;
                case ScenicTicketSupplierOrderStatus.CreateFail:
                case ScenicTicketSupplierOrderStatus.WaitingForPay:
                case ScenicTicketSupplierOrderStatus.Refunded:
                case ScenicTicketSupplierOrderStatus.DeliveryFail:

                    //支付失败.订单退款,发货失败
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.DeliveryFail;
                    msg = record.ErrorMsg;
                    errorCode = record.ErrorCode;

                    break;
                case ScenicTicketSupplierOrderStatus.Delivered:

                    if (record.IsSuccess)
                    {
                        //发货成功
                        switch (record.RecordType)
                        {
                            case ScenicTicketSupplierOrderRecordType.InCombination:
                                deliveryStatus = ScenicTicketOrderDeliveryStatus.WaitingForDeliver;
                                errorCode = (int)OrderBusinessErrorCodeType.InCombination;
                                break;
                            default:
                                deliveryStatus = ScenicTicketOrderDeliveryStatus.Success;
                                break;
                        }
                    }
                    else
                    {
                        //2.发货失败
                        switch (record.RecordType)
                        {
                            //同步API对接的分销商时，同步失败显示
                            case ScenicTicketSupplierOrderRecordType.SyncDelivery:

                                deliveryStatus = ScenicTicketOrderDeliveryStatus.SyncFailed;
                                errorCode = (int)OrderBusinessErrorCodeType.SyncFailed;

                                break;
                            default:

                                //支付失败.订单退款,开放平台同步凭证失败,Saas处理图片失败===>发货失败,
                                deliveryStatus = ScenicTicketOrderDeliveryStatus.DeliveryFail;
                                msg = record.ErrorMsg;
                                errorCode = record.ErrorCode;

                                break;
                        }
                    }

                    break;
                case ScenicTicketSupplierOrderStatus.Reject:

                    //供应端拒单
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.Rejected;
                    errorCode = (int)OrderBusinessErrorCodeType.Rejected;

                    break;
                case ScenicTicketSupplierOrderStatus.Purchasing:

                    //等待供应端采购
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.Purchasing;
                    errorCode = (int)OrderBusinessErrorCodeType.TicketPurchasing;

                    break;
                case ScenicTicketSupplierOrderStatus.PayProcessing:

                    //支付处理中
                    deliveryStatus = ScenicTicketOrderDeliveryStatus.PayProcessing;
                    errorCode = (int)OrderBusinessErrorCodeType.PayProcessing;

                    break;
            }

            result.Add(record.BaseOrderId, (deliveryStatus, msg, errorCode));
        }

        return result;
    }

    public async Task<Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>>
        GetManualDeliveryResult(params long[] baseOrderIds)
    {
        var result = new Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>();
        if (baseOrderIds.Any() is false)
            return result;

        var supplierOrderRecords = await _dbContext.ScenicTicketSupplierOrderRecords.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .Where(x => x.ScenicTicketSupplierOrderId == 0)
            .ToListAsync();

        foreach (var baseOrderId in baseOrderIds)
        {
            var msg = string.Empty;
            int? errorCode = null;
            var deliveryStatus = new ScenicTicketOrderDeliveryStatus();
            var record = supplierOrderRecords
                .Where(x => x.BaseOrderId == baseOrderId)
                .MaxBy(x => x.Id);

            if (record == null)
            {
                errorCode = (int)OrderBusinessErrorCodeType.InManual;
                result.Add(baseOrderId, (ScenicTicketOrderDeliveryStatus.WaitingForDeliver, msg, errorCode));
                continue;
            }

            if (record.IsSuccess)
            {
                //发货成功
                switch (record.RecordType)
                {
                    case ScenicTicketSupplierOrderRecordType.InCombination:
                        deliveryStatus = ScenicTicketOrderDeliveryStatus.WaitingForDeliver;
                        errorCode = (int)OrderBusinessErrorCodeType.InCombination;
                        break;
                    default:
                        deliveryStatus = ScenicTicketOrderDeliveryStatus.Success;
                        break;
                }
            }
            else
            {
                //2.发货失败
                switch (record.RecordType)
                {
                    //同步API对接的分销商时，同步失败显示
                    case ScenicTicketSupplierOrderRecordType.SyncDelivery:

                        deliveryStatus = ScenicTicketOrderDeliveryStatus.SyncFailed;
                        errorCode = (int)OrderBusinessErrorCodeType.SyncFailed;

                        break;
                    default:

                        //支付失败.订单退款,开放平台同步凭证失败,Saas处理图片失败===>发货失败,
                        deliveryStatus = ScenicTicketOrderDeliveryStatus.DeliveryFail;
                        msg = record.ErrorMsg;
                        errorCode = record.ErrorCode;

                        break;
                }
            }

            result.Add(record.BaseOrderId, (deliveryStatus, msg, errorCode));
        }

        return result;
    }

    public async Task<bool> UpdateSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
           .Where(x => x.Id == input.BaseOrderId)
           .FirstOrDefaultAsync();

        if (baseOrder.Status == BaseOrderStatus.WaitingForPay || baseOrder.Status == BaseOrderStatus.Closed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var scenicTicketOrder =
          await _dbContext.ScenicTicketOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);


        //不是线下采购产品的订单不可以修改
        if (scenicTicketOrder.CredentialSourceType != CredentialSourceType.ManualDelivery)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        var settlementorderdetail = await _dbContext.SettlementOrderDetails
                               .Join(_dbContext.SettlementOrders, detail => detail.SettlementOrderId, settl => settl.Id, (detail, settl) => new { detail, settl })
                               .Where(x => x.detail.BaseOrderId == input.BaseOrderId)
                               .CountAsync();
        // 采购单财务核销后不能改
        if (settlementorderdetail > 0)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (scenicTicketOrder.SupplierId > 0)
        {
            var supplierInfo = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
              requestUri: _servicesAddress.Tenant_GetSupplier(scenicTicketOrder.SupplierId));
            if (supplierInfo.SupplierType == SupplierType.Api)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        scenicTicketOrder.SupplierOrderId = input.SupplierOrderId;


        var orderLog = new OrderLogs
        {
            OrderId = input.BaseOrderId,
            OperationRole = input.OpUser.UserType,
            OperationType = OrderOperationType.SupplierOrderIdUpdated,
            OrderLogType = OrderLogType.ScenicTicket,
            UserId = input.OpUser.UserId,
            UserName = input.OpUser.Name,
        };
        await _dbContext.AddAsync(orderLog);
        await _dbContext.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// 修改采购价
    /// </summary>
    /// <param name="input"></param>
    /// <param name="operationUser"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public async Task EditCost(UpdateCostInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
          .Where(a => a.Id == input.BaseOrderId)
          .FirstOrDefaultAsync();
        var order = await _dbContext.ScenicTicketOrders
            .Where(a => a.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        var price = await _dbContext.OrderPrices
            .FirstOrDefaultAsync(a => a.BaseOrderId == input.BaseOrderId);
        if (order is null || price is null) throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        if (order.CredentialSourceType == CredentialSourceType.PurchasingImport)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var statusArr = new List<ScenicTicketOrderStatus>() {
         ScenicTicketOrderStatus.WaitingForDeliver,
         ScenicTicketOrderStatus.Delivered
        };
        var baseStatusArr = new List<BaseOrderStatus>() {
         BaseOrderStatus.WaitingForPay,
         BaseOrderStatus.UnFinished
        };

        if (!(baseStatusArr.Contains(baseOrder.Status) && statusArr.Contains(order.Status)))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var settlementOrderDetail = await _dbContext.SettlementOrderDetails
           .Where(s => s.BaseOrderId == input.BaseOrderId)
           .FirstOrDefaultAsync();
        if (settlementOrderDetail != null)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var supplierInfo = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
          requestUri: _servicesAddress.Tenant_GetSupplier(order.SupplierId));
        if (supplierInfo.SupplierType == SupplierType.Api)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        price.CostPrice = input.Cost;

        //计算总折扣总额
        baseOrder.CostDiscountAmount =
            Math.Round(price.CostPrice * price.Quantity * price.CostDiscountRate / 100, 2);

        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.CostPriceUpdated,
            OrderLogType = OrderLogType.ScenicTicket,
            OrderId = order.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task UpdateCostDiscountRate(UpdateCostDiscountRateInput input)
    {
        var baseOrder = await _dbContext.BaseOrders
            .Where(a => a.Id == input.BaseOrderId)
            .FirstOrDefaultAsync();
        var order = await _dbContext.ScenicTicketOrders
            .Where(a => a.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        var price = await _dbContext.OrderPrices
            .Where(a => a.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        if (order is null || price is null) throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        //“手工发货”的门票订单，在订单已完成前，支持【采购折扣比例】的编辑
        if (order.CredentialSourceType != CredentialSourceType.ManualDelivery)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (baseOrder.Status != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        foreach (var item in price)
        {
            item.CostDiscountRate = input.CostDiscountRate;
        }

        //计算总折扣总额
        baseOrder.CostDiscountAmount =
            Math.Round(price.Sum(x => x.CostPrice * x.Quantity * x.CostDiscountRate / 100), 2);

        //增加操作日志
        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.CostDiscountRateUpdated,
            OrderLogType = OrderLogType.ScenicTicket,
            OrderId = order.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);
    }

    public async Task<List<GetOrderCountOutput>> GetOrderCount(GetOrderCountInput input)
    {
        var beginDate = input.BeginDate ?? DateTime.Today.AddDays(-30);
        var endDate = input.EndDate ?? DateTime.Today;
        var baseOrderStatus = new[] { BaseOrderStatus.UnFinished, BaseOrderStatus.Finished };

        var orderCount = await _dbContext.BaseOrders.AsNoTracking()
            .Join(_dbContext.ScenicTicketOrders.AsNoTracking(), b => b.Id,
                h => h.BaseOrderId,
                (b, h) => new { b, h })
            .Where(x => x.b.OrderType == OrderType.ScenicTicket
                        && baseOrderStatus.Contains(x.b.Status)
                        && x.b.CreateTime >= beginDate
                        && x.b.CreateTime <= endDate
                        && input.ProductIds.Contains(x.h.ScenicSpotId))
            .Select(x => new
            {
                x.h.ScenicSpotId,
            })
            .ToListAsync();

        var result = orderCount.GroupBy(x => x.ScenicSpotId)
            .Select(x => new GetOrderCountOutput
            {
                ProductId = x.Key,
                OrderCount = x.Count()
            })
        .ToList();
        return result;
    }

    public async Task<bool> UpdateTravelInfo(UpdateTravelInfoInput input)
    {
        var ticketOrder = await _dbContext.ScenicTicketOrders.Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        if (ticketOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var baseOrder = await _dbContext.BaseOrders.Where(x => x.Id == ticketOrder.BaseOrderId)
           .FirstOrDefaultAsync();

        if (baseOrder.Status == BaseOrderStatus.Finished || baseOrder.Status == BaseOrderStatus.Closed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var travelOrderFields = input.OrderFields.Where(x => x.TemplateType == TemplateType.Travel).ToList();
        List<long> travelerFieldIds = new List<long>();
        travelOrderFields.ForEach(orderField =>
        {
            orderField.Fields.ForEach(x =>
            {
                travelerFieldIds.Add(x.Id);
            });
        });

        var orderFields = await _dbContext.OrderFieldInformationType
               .Join(_dbContext.OrderFieldInformations, type => type.Id, field => field.OrderFieldInformationTypeId, (type, field) => new { field, type })
               .Where(x => travelerFieldIds.Contains(x.field.Id))
                .Where(x => x.type.BaseOrderId == baseOrder.Id)
               .Select(x => x.field)
               .ToListAsync();

        if (orderFields.Any() is false)
            return true;

        foreach (var item in orderFields)
        {
            var inputOrderFields = travelOrderFields.FirstOrDefault(x => x.Id == item.OrderFieldInformationTypeId);
            if (inputOrderFields != null)
            {
                var field = inputOrderFields.Fields.FirstOrDefault(x => x.Id == item.Id);
                item.FieldValue = field.FieldValue;
            }
        }

        baseOrder.UpdateTime = DateTime.Now;

        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.UpdateTravelInfo,
            OrderLogType = OrderLogType.ScenicTicket,
            OrderId = ticketOrder.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        await CompensationOrderDataUpdateCap(
            orderCategory: baseOrder.OrderCategory,
            baseOrderId: baseOrder.Id,
            tenantId: baseOrder.TenantId,
            orderType: baseOrder.OrderType);


        var count = await _dbContext.SaveChangesAsync();
        return count > 0 ? true : false;
    }

    public async Task<bool> UpdateOrderConfirmation(UpdateOrderConfirmationInput input)
    {
        var ticketOrder = await _dbContext.ScenicTicketOrders
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();

        if (ticketOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var baseOrder = await _dbContext.BaseOrders.AsNoTracking().Where(x => x.Id == ticketOrder.BaseOrderId)
            .FirstOrDefaultAsync();

        // 修改订单确认
        var commfields = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Order
                             && x.ProductTemplateType == ProductTemplateType.OrderCommon)?.Fields;
        if (commfields != null && commfields.Count > 0)
        {
            var orderFieldIds = commfields.Select(x => x.Id);
            var orderFields = await _dbContext.OrderFieldInformationType
                                .Join(_dbContext.OrderFieldInformations, type => type.Id, field => field.OrderFieldInformationTypeId, (type, field) => new { field, type })
                                .Where(x => orderFieldIds.Contains(x.field.Id))
                                .Where(x => x.type.BaseOrderId == ticketOrder.BaseOrderId)
                                .Select(x => x.field)
                                .ToListAsync();
            foreach (var item in orderFields)
            {
                var field = commfields.FirstOrDefault(x => x.Id == item.Id);
                if (field != null)
                {
                    item.FieldValue = field.FieldValue;
                }
            }
        }
        baseOrder.UpdateTime = DateTime.Now;

        OperationUserDto operationUser = input.OperationUser;

        OrderLogs orderLog = new()
        {
            OperationRole = operationUser.UserType,
            UserId = operationUser.UserId,
            UserName = operationUser.Name,
            OperationType = OrderOperationType.UpdateOrderConfirmation,
            OrderLogType = OrderLogType.ScenicTicket,
            OrderId = ticketOrder.BaseOrderId,
        };
        await _dbContext.AddAsync(orderLog);
        await CompensationOrderDataUpdateCap(
            orderCategory: baseOrder.OrderCategory,
            baseOrderId: baseOrder.Id,
            tenantId: baseOrder.TenantId,
            orderType: baseOrder.OrderType);


        var count = await _dbContext.SaveChangesAsync();

        return count > 0 ? true : false;
    }

    /// <summary>
    /// 修改联系人信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> UpdateContact(UpdateContactInput input)
    {
        var ticketOrder = await _dbContext.ScenicTicketOrders
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();

        if (ticketOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == ticketOrder.BaseOrderId)
            .FirstOrDefaultAsync();

        if (baseOrder.Status == BaseOrderStatus.Finished || baseOrder.Status == BaseOrderStatus.Closed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        OrderOperationType orderOperationType;

        var fields = input.OrderFields.FirstOrDefault(x => x.TemplateType == TemplateType.Contacts).Fields;
        var contactFields = _mapper.Map<List<OrderFieldInformationDto>>(fields);
        var contactOrderField = _orderFieldInformationService.ChangeContact(contactFields);

        if (!string.IsNullOrEmpty(contactOrderField.ContactsName))
            baseOrder.ContactsName = contactOrderField.ContactsName;
        if (!string.IsNullOrEmpty(contactOrderField.ContactsPhoneNumber))
            baseOrder.ContactsPhoneNumber = contactOrderField.ContactsPhoneNumber;
        if (!string.IsNullOrEmpty(contactOrderField.ContactsEmail))
            baseOrder.ContactsEmail = contactOrderField.ContactsEmail;

        var contactFieldIds = fields.Select(x => x.Id);
        var orderFields = await _dbContext.OrderFieldInformationType
               .Join(_dbContext.OrderFieldInformations, type => type.Id, field => field.OrderFieldInformationTypeId, (type, field) => new { field, type })
               .Where(x => contactFieldIds.Contains(x.field.Id))
               .Where(x => x.type.BaseOrderId == baseOrder.Id)
               .Select(x => x.field)
               .ToListAsync();

        foreach (var item in orderFields)
        {
            var field = fields.FirstOrDefault(x => x.Id == item.Id);
            if (field != null)
            {
                item.FieldValue = field.FieldValue;
            }
        }
        baseOrder.UpdateTime = DateTime.Now;

        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.UpdateContact,
            OrderLogType = OrderLogType.ScenicTicket,
            OrderId = baseOrder.Id,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);

        await CompensationOrderDataUpdateCap(
            orderCategory: baseOrder.OrderCategory,
            baseOrderId: baseOrder.Id,
            tenantId: baseOrder.TenantId,
            orderType: baseOrder.OrderType);


        var count = await _dbContext.SaveChangesAsync();

        return count > 0 ? true : false;
    }

    /// <summary>
    /// 出行人实名制校验
    /// </summary>
    /// <param name="touristInfoType"></param>
    /// <param name="orderFields"></param>
    /// <param name="quantity"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public bool CheckTouristInfo(TouristInfoType touristInfoType, List<SaveOrderFieldInformationTypeDto> orderFields,
        int quantity)
    {
        //门票出行人数量与模板对接校验
        var productTemplateType = touristInfoType switch
        {
            TouristInfoType.Every => ProductTemplateType.EachPerson,
            TouristInfoType.One => ProductTemplateType.JustOnePerson,
            _ => ProductTemplateType.EachPerson
        };
        var travelerFieldInfos = orderFields.Where(x =>
                x.TemplateType == TemplateType.Travel && x.ProductTemplateType == productTemplateType)
            .ToList();

        switch (touristInfoType)
        {
            case TouristInfoType.One:

                //判断出行人数量是否至少为1
                if (travelerFieldInfos.Count < 1)
                {
                    throw new BusinessException(ErrorTypes.Order.TravelerNotCompleted);
                }

                break;
            case TouristInfoType.Every:

                //判断出行人数量是否和购买数量匹配
                if (travelerFieldInfos.Count != quantity)
                {
                    throw new BusinessException(ErrorTypes.Order.TravelerNotCompleted);
                }

                //每一个出行人的信息（需要判断订单内的出行人信息是否重复，具体用身份证号码或护照号码进行校验重复）
                //暂时去掉

                break;
        }

        return true;
    }

    /// <summary>
    /// 修改出行时间
    /// </summary>
    /// <returns></returns>
    public async Task UpdateTravelTime(UpdateTravelTimeInput input)
    {
        var scenicTicketOrder = await _dbContext.ScenicTicketOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        if (scenicTicketOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        if (scenicTicketOrder.CredentialSourceType != CredentialSourceType.ManualDelivery)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        var lastDate = scenicTicketOrder.TravelDate!.Value.AddDays(-1);
        var lastTime = new DateTime(lastDate.Year, lastDate.Month, lastDate.Day, 23, 0, 0);
        if (DateTime.Now >= lastTime)
            throw new BusinessException(ErrorTypes.Order.ExceededUpdateTime);

        scenicTicketOrder.TravelDate = input.TravelDate;

        OperationUserDto operationUser = input.OperationUser;
        var logs = new OrderLogs
        {
            OperationRole = operationUser.UserType,
            OperationType = OrderOperationType.UpdateTravelTime,
            OrderLogType = OrderLogType.ScenicTicket,
            OrderId = scenicTicketOrder.BaseOrderId,
            UserId = operationUser.UserId,
            UserName = operationUser.Name
        };
        await _dbContext.AddAsync(logs);
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task ReplaceOrderProduct(ReplaceOrderProductInput input)
    {
        /*
         * 1.手工发货/采购录入订单，Saas订单状态为【待确认】时（即未发货前(不包括组合中状态)）支持更换产品
         * 2.接口对接订单，采购状态为【待支付】【供应商拒单】【已退款】【创单失败】时支持更换产品
         * 3.限制同一景区下更换产品
         * 4.不同类型产品切换.订单数据处理
         * 5.组合中的订单 不支持 替换
         */

        var baseOrder = await _dbContext.BaseOrders.FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var scenicTicketOrder = await _dbContext.ScenicTicketOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.ScenicTicketSupplierOrders.FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrderRecords = await _dbContext.ScenicTicketSupplierOrderRecords.Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        var purchaseDeliveryRecords = await _dbContext.ScenicTicketPurchaseDeliveryRecords.AsNoTracking()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        var orderPrices = await _dbContext.OrderPrices.Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();
        var combinationMergeOrderItem =
            await _dbContext.TicketsCombinationMergeOrderItems.FirstOrDefaultAsync(x =>
                x.BaseOrderId == input.BaseOrderId);

        if (baseOrder == null || scenicTicketOrder == null)
            throw new BusinessException(ErrorTypes.Order.NoMatchingData);

        if (scenicTicketOrder.ScenicSpotId != input.TicketDetail.ScenicSpotId) //限制同一景区下更换产品
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (input.TicketDetail.CredentialSourceType == CredentialSourceType.SystemGeneration)//限制系统发码产品
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //获取供应商类型
        var openSupplierType = input.SupplierApiType.HasValue
            ? _openPlatformBaseService.MapSupplierApiTypeToOpenSupplierType(input.SupplierApiType.Value)
            : null;

        //各类型订单状态校验
        if (scenicTicketOrder.CredentialSourceType == CredentialSourceType.ManualDelivery)//手工发货
        {
            if (scenicTicketOrder.Status != ScenicTicketOrderStatus.WaitingForDeliver) //限制未发货订单
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            var supplierOrderRecord = supplierOrderRecords
                .Where(x => x.BaseOrderId == input.BaseOrderId)
                .Where(x => x.ScenicTicketSupplierOrderId == 0)
                .MaxBy(x => x.Id);

            if (supplierOrderRecord is { RecordType: ScenicTicketSupplierOrderRecordType.InCombination }) //组合中不允许发货
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        else if (scenicTicketOrder.CredentialSourceType == CredentialSourceType.PurchasingImport)//采购导入
        {
            if (scenicTicketOrder.Status != ScenicTicketOrderStatus.WaitingForDeliver) //限制未发货订单
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            var purchaseDeliveryRecord = purchaseDeliveryRecords
                .Where(x => x.BaseOrderId == input.BaseOrderId)
                .MaxBy(x => x.Id);

            if (purchaseDeliveryRecord is { Status: OrderVoucherDeliverStatus.InCombination }) //组合中不允许发货
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        else if (scenicTicketOrder.CredentialSourceType == CredentialSourceType.InterfaceDock) //接口对接
        {
            if (baseOrder.Status != BaseOrderStatus.UnFinished) //限制为[待完成]订单
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            var supplierOrderRecord = supplierOrderRecords
                .Where(x => x.BaseOrderId == input.BaseOrderId)
                .Where(x => x.ScenicTicketSupplierOrderId == supplierOrder.Id)
                .MaxBy(x => x.Id);

            if (supplierOrderRecord is { RecordType: ScenicTicketSupplierOrderRecordType.InCombination }) //组合中不允许发货
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            var supplierOrderStatus = new[]
            {
                ScenicTicketSupplierOrderStatus.WaitingForPay,
                ScenicTicketSupplierOrderStatus.Reject,
                ScenicTicketSupplierOrderStatus.CreateFail,
                ScenicTicketSupplierOrderStatus.Refunded
            };

            //待支付状态只允许更换不同供应商产品
            if (supplierOrder.OrderStatus == ScenicTicketSupplierOrderStatus.WaitingForPay)
            {
                if (scenicTicketOrder.OpenSupplierType == openSupplierType)
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
            }

            if (!supplierOrderStatus.Contains(supplierOrder.OrderStatus)) //供应端订单状态不支持
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //记录变更日志扩展数据
        var extensionData = new OrderLogExtensionDataDto
        {
            OldProductId = scenicTicketOrder.ScenicSpotId,
            OldProductName = baseOrder.ResourceName,
            OldProductSkuId = scenicTicketOrder.ScenicTicketId,
            OldProductSkuName = baseOrder.ProductName,

            NewProductId = scenicTicketOrder.ScenicSpotId,
            NewProductName = baseOrder.ResourceName,
            NewProductSkuId = input.TicketId,
            NewProductSkuName = input.TicketDetail.Name
        };

        //baseOrder
        baseOrder.ProductName = input.TicketDetail.Name;
        baseOrder.TotalAmount = input.Price * scenicTicketOrder.Quantity;
        baseOrder.DevelopUserId = input.TicketDetail.DevelopUserId;
        var productOperatorUser = input.TicketDetail.ProductOperatorUser?.FirstOrDefault(x => x.SellingPlatform == baseOrder.SellingPlatform);
        baseOrder.OperatorUserId = productOperatorUser?.OperatorUserId;
        baseOrder.OperatorAssistantUserId = productOperatorUser?.OperatorAssistantUserId;

        //scenicTicketOrder
        scenicTicketOrder.ScenicTicketId = input.TicketId;
        scenicTicketOrder.SupplierId = input.TicketDetail.SupplierId;
        scenicTicketOrder.ScenicTicketsType = input.TicketDetail.TicketsType;
        scenicTicketOrder.CredentialSourceType = input.TicketDetail.CredentialSourceType;
        scenicTicketOrder.SupplierActivityId = input.TicketDetail.ActivityId;
        scenicTicketOrder.SupplierPackageId = input.TicketDetail.PackageId;
        scenicTicketOrder.SupplierSkuId = input.TicketDetail.SkuId;
        scenicTicketOrder.TimeSlotId = input.TimeSlotId;
        scenicTicketOrder.TimeSlot = input.TimeSlot;
        scenicTicketOrder.OpenSupplierType = openSupplierType;
        if (input.TravelDate.HasValue)
        {
            scenicTicketOrder.TravelDate = input.TravelDate.Value;
        }

        //orderprice
        foreach (var orderPrice in orderPrices)
        {
            orderPrice.CostPrice = input.CostPrice;
            orderPrice.Price = input.Price;
            orderPrice.CostCurrencyCode = input.CostCurrencyCode;
            orderPrice.CostExchangeRate = input.CostExchangeRate;
            orderPrice.CostDiscountRate = input.TicketDetail.CostDiscountRate;
        }

        //日志记录
        var orderLog = new OrderLogs
        {
            OrderId = baseOrder.Id,
            UserId = input.OperationUserDto.UserId,
            UserName = input.OperationUserDto.Name,
            OperationRole = input.OperationUserDto.UserType,
            OperationType = OrderOperationType.ReplaceOrderProduct,
            OrderLogType = OrderLogType.ScenicTicket,
            ExtensionData = JsonConvert.SerializeObject(extensionData)
        };
        await _dbContext.AddAsync(orderLog);

        //订单数据处理
        if (supplierOrder != null)
            _dbContext.Remove(supplierOrder);

        if (supplierOrderRecords.Any())
            _dbContext.RemoveRange(supplierOrderRecords);

        if (purchaseDeliveryRecords.Any())
            _dbContext.RemoveRange(purchaseDeliveryRecords);

        if (combinationMergeOrderItem != null)
            _dbContext.Remove(combinationMergeOrderItem);

        //新产品类型处理
        switch (input.TicketDetail.CredentialSourceType)
        {
            case CredentialSourceType.PurchasingImport: //采购订单

                var otaOrderDeliveryCommand = new ScenicOTAOrderDeliverMessage
                {
                    BaseOrderId = baseOrder.Id,
                    TenantId = baseOrder.TenantId
                };
                await _capPublisher.PublishAsync(CapTopics.Order.ScenicOTAOrderDeliver, otaOrderDeliveryCommand);

                break;
            case CredentialSourceType.InterfaceDock:

                if (scenicTicketOrder.TicketsCombinationOrderId.HasValue)
                {
                    #region 手工单组合单替换 - 合单采购判断

                    if (scenicTicketOrder.TicketsCombinationOrderId.HasValue && scenicTicketOrder.CredentialSourceType == CredentialSourceType.InterfaceDock)
                    {
                        await CombinationMergeOrderProcess(baseOrderId: baseOrder.Id,
                            combinationOrderId: scenicTicketOrder.TicketsCombinationOrderId.Value,
                            openSupplierType: scenicTicketOrder.OpenSupplierType!.Value,
                            activityId: scenicTicketOrder.SupplierActivityId,
                            packageId: scenicTicketOrder.SupplierPackageId);
                    }

                    #endregion

                    //组合合单处理
                    var combinationSupplierOrderCreateMessage = new CreateCombinationSupplierApiOrderMessage
                    {
                        BaseOrderId = baseOrder.Id,
                        CombinationOrderId = scenicTicketOrder.TicketsCombinationOrderId.Value,
                        OpenSupplierType = scenicTicketOrder.OpenSupplierType,
                        ActivityId = scenicTicketOrder.SupplierActivityId,
                        PackageId = scenicTicketOrder.SupplierPackageId,
                        TenantId = baseOrder.TenantId
                    };
                    await _capPublisher.PublishAsync(CapTopics.Order.ScenicTicketCombinationSupplierOrderCreate, combinationSupplierOrderCreateMessage);
                }
                else
                {
                    var supplierOrderCreateCommand = new CreateSupplierApiOrderMessage
                    {
                        BaseOrderId = baseOrder.Id,
                        TenantId = baseOrder.TenantId
                    };
                    await _capPublisher.PublishAsync(CapTopics.Order.ScenicTicketSupplierOrderCreate, supplierOrderCreateCommand);
                }

                break;
            case CredentialSourceType.ManualDelivery:

                //`手工发货`类型是手动上传凭证所以不走自动发货流程

                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        // 补差单信息补充更新
        await CompensationOrderDataUpdateCap(
            orderCategory: baseOrder.OrderCategory,
            baseOrderId: baseOrder.Id,
            tenantId: baseOrder.TenantId,
            orderType: baseOrder.OrderType);
    }

    public async Task<List<QueryOpenSupplierSkuExtraInfoOutput>> QueryOpenSupplierSkuExtraInfo(QueryOpenSupplierSkuExtraInfoInput input)
    {
        var productContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        var extraInfo = await _httpClientFactory.InternalPostAsync<List<QueryOpenSupplierSkuExtraInfoOutput>>(
            requestUri: _servicesAddress.Product_QueryOpenSupplierSkuExtraInfo(),
            httpContent: productContent);
        return extraInfo;
    }

    #region private

    private void PersonalizedBusinessEx(
        Enum businessErrorType,
        bool isPersonalized,
        string? message = null,
        string? personalizedPrefix = null)
    {
        if (isPersonalized && !string.IsNullOrEmpty(personalizedPrefix))
        {
            var individuationMessage = $"{personalizedPrefix} : {message}";
            throw new BusinessException(businessErrorType.ToString(), individuationMessage);
        }
        throw new BusinessException(businessErrorType);
    }

    /// <summary>
    /// 门票订单分页-BaseOrderTotalQuery
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>,
    private IQueryable<BaseOrder> BuildSearchBaseOrderQuery(SearchScenicTicketOrderInput input)
    {
        var baseOrderTotalQueryable = _dbContext.BaseOrders.AsNoTracking()
            .WhereIF(input.BaseOrderId is > 0, x => x.Id == input.BaseOrderId)
            .WhereIF(input.DateBegin.HasValue, x => x.CreateTime >= input.DateBegin!.Value)
            .WhereIF(input.DateEnd.HasValue,
                x => x.CreateTime < input.DateEnd!.Value.Date.AddDays(1)) //vebk前端传值是2024-08-05 23:59:59
            .WhereIF(!string.IsNullOrEmpty(input.ResourceName), x => x.ResourceName.Contains(input.ResourceName))
            .WhereIF(!string.IsNullOrEmpty(input.ProductName), x => x.ProductName.Contains(input.ProductName))
            .WhereIF(!string.IsNullOrEmpty(input.ContactsName), x => x.ContactsName.Contains(input.ContactsName))
            .WhereIF(!string.IsNullOrEmpty(input.ContactsPhoneNumber),
                x => x.ContactsPhoneNumber.Contains(input.ContactsPhoneNumber))
            .WhereIF(input.SellingPlatform > 0, x => x.SellingPlatform == input.SellingPlatform)
            .WhereIF(input.SellingChannels > 0, x => x.SellingChannels == input.SellingChannels)
            .WhereIF(input.AgencyId.HasValue, x => x.AgencyId == input.AgencyId!.Value)
            .WhereIF(!string.IsNullOrEmpty(input.ChannelOrderNo), x => x.ChannelOrderNo.Contains(input.ChannelOrderNo))
            .OrderByDescending(x => x.UpdateTime)
            .ThenByDescending(x => x.Id);
        return baseOrderTotalQueryable;
    }

    /// <summary>
    /// 门票订单分页-ScenicTicketOrderTotalQuery
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private IQueryable<ScenicTicketOrder> BuildSearchTicketOrderQuery(SearchScenicTicketOrderInput input)
    {
        var scenicTicketOrderTotalQueryable = _dbContext.ScenicTicketOrders.AsNoTracking()
            .WhereIF(input.SupplierId is > 0, x => x.SupplierId == input.SupplierId)
            .WhereIF(input.ScenicTicketOrderId is > 0, x => x.Id == input.ScenicTicketOrderId)
            .WhereIF(input.ExpiredOrder > 0, x => x.ValidityEnd.AddDays(1) < DateTime.Now)
            .WhereIF(input.ProductId.HasValue, x => x.ScenicTicketId.Equals(input.ProductId))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SupplierOrderId),
                x => x.SupplierOrderId.Contains(input.SupplierOrderId!))
            .WhereIF(input.ValidityBegin.HasValue,
                x => x.ScenicTicketsType != ScenicTicketsType.Reservation &&
                     x.ValidityBegin >= input.ValidityBegin!.Value)
            .WhereIF(input.ValidityEnd.HasValue,
                x => x.ScenicTicketsType != ScenicTicketsType.Reservation &&
                     x.ValidityEnd < input.ValidityEnd!.Value.AddDays(1));
        return scenicTicketOrderTotalQueryable;
    }

    /// <summary>
    /// 检查是否为组合订单
    /// </summary>
    /// <param name="channelOrderNos"></param>
    /// <value>传入的是渠道单号集合,虽然订单可以关联多个渠道单号,但目前组合订单只支持一个渠道单号</value>
    /// <returns>只会返回包含的第一个组合订单信息</returns>
    private async Task<(long? combinationOrderId, long? agencyId)> CheckIsCombinationOrder(
        params string[] channelOrderNos)
    {
        //传入数据为空
        if (!channelOrderNos.Any())
            return (null, null);

        Expression<Func<BaseOrder, bool>> predicate =
            channelOrderNos.Aggregate<string, Expression<Func<BaseOrder, bool>>>(null, (current, channelNoItem) =>
                current == null
                    ? x => x.ChannelOrderNo.Contains(channelNoItem)
                    : current.Or(x => x.ChannelOrderNo.Contains(channelNoItem)));

        //查询关联的渠道单号的订单数据
        var baseOrders = await _dbContext.BaseOrders
            .IgnoreQueryFilters()
            .Where(predicate)
            .Select(x => new { x.Id, x.AgencyId, x.ChannelOrderNo })
            .ToListAsync();

        //处理渠道单号
        var processedOrders = baseOrders
            .Select(x => new
            {
                x.Id,
                x.AgencyId,
                ChannelOrderNos = x.ChannelOrderNo.Split(',').Where(c => !string.IsNullOrEmpty(c)).ToList()
            })
            .Where(x => x.ChannelOrderNos.Intersect(channelOrderNos).Any())
            .ToList();

        //查询组合订单(目前只返回第一个组合订单,因为组合订单只有一个渠道单号)
        var baseOrderIds = processedOrders.Select(x => x.Id).Distinct().ToList();
        var ticketsCombinationOrder = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId) && x.TicketsCombinationOrderId != null)
            .Select(x => new { x.BaseOrderId, x.TicketsCombinationOrderId, })
            .FirstOrDefaultAsync();

        long? agencyId = null;
        if (ticketsCombinationOrder != null)
        {
            agencyId = baseOrders.FirstOrDefault(x => x.Id == ticketsCombinationOrder.BaseOrderId)?.AgencyId;
        }

        return (ticketsCombinationOrder?.TicketsCombinationOrderId, agencyId);
    }


    /// <summary>
    /// 查询景区预订票日历价格
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<IEnumerable<TicketCalendarOutput>> GetScenicTicketCalendars(
        SearchTicketCalendarInput request)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(request),
            Encoding.UTF8,
            "application/json");
        var result = await _httpClientFactory.InternalPostAsync<IEnumerable<TicketCalendarOutput>>(
            requestUri: _servicesAddress.Scenic_SearchTicketCalendarPrice(),
            httpContent: httpContent);
        return result;
    }

    /// <summary>
    /// 查询预订票日历价格库存
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<IEnumerable<CalendarInventoryOutput>> GetCalendarInventories(
        GetCalendarInventoryInput request)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(request),
            Encoding.UTF8,
            "application/json");
        var result = await _httpClientFactory.InternalPostAsync<IEnumerable<CalendarInventoryOutput>>(
            requestUri: _servicesAddress.Inventory_CalendarGetInventories(),
            httpContent: httpContent);
        return result;
    }

    /// <summary>
    /// 查询期票库存
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<IEnumerable<GeneralInventoryOutput>> GetGeneralInventories(
        GetGeneralInventoryInput request)
    {
        var httpContent =
            new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        return await _httpClientFactory.InternalPostAsync<IEnumerable<GeneralInventoryOutput>>(
            requestUri: _servicesAddress.Inventory_GeneralGetInventories(),
            httpContent: httpContent);
    }

    /// <summary>
    /// 查询时段日历库存
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<IEnumerable<GetTimeSlotInventoryOutput>> GetTimeSlotInventories(
        GetTimeSlotInventoryInput request)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(request),
            Encoding.UTF8,
            "application/json");
        var result = await _httpClientFactory.InternalPostAsync<IEnumerable<GetTimeSlotInventoryOutput>>(
            requestUri: _servicesAddress.Inventory_TimeSlotCalendarGetInventories(),
            httpContent: httpContent);
        return result;
    }


    /// <summary>
    /// 组合 - 合单关系绑定
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <param name="combinationOrderId"></param>
    /// <param name="openSupplierType"></param>
    /// <param name="activityId"></param>
    /// <param name="packageId"></param>
    private async Task CombinationMergeOrderProcess(long baseOrderId, long combinationOrderId, OpenSupplierType openSupplierType, string activityId, string? packageId)
    {
        var relatedTicketOrderIds = await _dbContext.ScenicTicketOrders.AsNoTracking()
            .Where(x => x.TicketsCombinationOrderId == combinationOrderId)
            .Where(x => x.OpenSupplierType == openSupplierType)
            .Where(x => x.SupplierActivityId == activityId && x.SupplierPackageId == packageId)
            .Select(x => x.BaseOrderId)
            .ToListAsync();

        //判断查出的订单状态.必须未正常状态的订单
        var relatedBaseOrders = await _dbContext.BaseOrders
            .AsNoTracking()
            .Where(x => relatedTicketOrderIds.Contains(x.Id))
            .ToListAsync();

        //判断查出的子单是否关联[supplierOrder]数据.存在数据状态必须是[创建失败]状态.
        var combinationSupplierOrders = await _dbContext.ScenicTicketSupplierOrders
            .AsNoTracking()
            .Where(x => relatedTicketOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        //得出[待处理]的子单id
        var baseOrderStatus = new[] { BaseOrderStatus.WaitingForPay, BaseOrderStatus.UnFinished };
        var pendingBaseOrderIds = new List<long> { baseOrderId };
        foreach (var itemId in relatedTicketOrderIds)
        {
            //订单状态判断
            var baseOrder = relatedBaseOrders.FirstOrDefault(x => x.Id == itemId);
            if (!baseOrderStatus.Contains(baseOrder.Status))
            {
                continue;
            }

            //采购状态判断
            var combinationSupplierOrder = combinationSupplierOrders.FirstOrDefault(x => x.BaseOrderId == itemId);
            if (combinationSupplierOrder == null)
            {
                //未存在采购数据
                pendingBaseOrderIds.Add(itemId);
            }
            else
            {
                //采购失败.支持重新采购
                if (combinationSupplierOrder.OrderStatus == ScenicTicketSupplierOrderStatus.CreateFail)
                {
                    pendingBaseOrderIds.Add(itemId);
                }
            }
        }

        //移除旧绑定关系
        var removeMergeOrderItems = await _dbContext.TicketsCombinationMergeOrderItems
            .Where(x => pendingBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        if (removeMergeOrderItems.Any())
            _dbContext.TicketsCombinationMergeOrderItems.RemoveRange(removeMergeOrderItems);

        pendingBaseOrderIds = pendingBaseOrderIds.Distinct().ToList();
        if (pendingBaseOrderIds.Count > 1)
        {
            //合单
            var mergeOrder = new TicketsCombinationMergeOrder
            {
                CombinationOrderId = combinationOrderId
            };
            await _dbContext.AddAsync(mergeOrder);

            //合单关系绑定
            var addMergeOrderItems = pendingBaseOrderIds.Select(itemId =>
                new TicketsCombinationMergeOrderItem
                {
                    BaseOrderId = itemId,
                    CombinationOrderId = combinationOrderId,
                    MergeOrderId = mergeOrder.Id
                }).ToList();

            await _dbContext.AddRangeAsync(addMergeOrderItems);
        }
    }

    /// <summary>
    /// 售卖平台换算渠道类型
    /// </summary>
    /// <param name="sellingPlatform"></param>
    /// <param name="agencyId"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    private async Task<OtaChannelType> ChannelTypeProcess(SellingPlatform sellingPlatform, long? agencyId, long tenantId)
    {
        var systemOtaChannelType = new OtaChannelType();
        if (sellingPlatform == SellingPlatform.System && agencyId is not null)
        {
            //判断手工单是否API对接分销商
            var otaSellingPlatform = new[] { SellingPlatform.Ctrip, SellingPlatform.Meituan, SellingPlatform.Fliggy, SellingPlatform.TikTok };
            var systemOrderCheckAgency = await _openPlatformBaseService.CheckSystemPlatformOrderByAgencyId(
                agencyId: agencyId!.Value,
                tenantId: tenantId,
                otaPlatform: otaSellingPlatform);
            systemOtaChannelType = systemOrderCheckAgency.otaChannelType;
        }

        OtaChannelType otaChannelType = sellingPlatform switch
        {
            SellingPlatform.Ctrip => OtaChannelType.Ctrip,
            SellingPlatform.Meituan => OtaChannelType.Meituan,
            SellingPlatform.Fliggy => OtaChannelType.AliTrip,
            SellingPlatform.TikTok => OtaChannelType.DouYin,
            _ => systemOtaChannelType
        };

        return otaChannelType;
    }


    /// <summary>
    ///  补差单数据更新事件
    /// </summary>
    /// <param name="orderCategory"></param>
    /// <param name="baseOrderId"></param>
    /// <param name="tenantId"></param>
    private async Task CompensationOrderDataUpdateCap(OrderCategory orderCategory, long baseOrderId, long tenantId, OrderType orderType)
    {
        if ((orderCategory & OrderCategory.CompensationMainOrder) != 0)
        {
            var capHeaders = new Dictionary<string, string?> { { "Tenant", tenantId.ToString() } };
            await _capPublisher.PublishAsync(CapTopics.Order.CompensationOrderDataUpdate,
                new CompensationOrderDataUpdateMessage
                {
                    CompensationMasterOrderId = baseOrderId,
                    OrderType = orderType
                }, capHeaders);
        }
    }
    #endregion
}