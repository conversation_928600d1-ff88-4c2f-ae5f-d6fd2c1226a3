using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Inventory.Messages;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.MailOrder;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Product.Messages;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.Api.Services;

public class MailOrderService : BaseOrderSeriesNumberService, IMailOrderService
{
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly CustomDbContext _dbContext;
    private readonly IRedisClient _redisClient;
    private readonly IUserCouponOrderService _userCouponOrderService;
    private readonly IMultPriceCalculateService _multPriceCalculateService;
    private readonly IRefundOrderService _refundOrderService;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IKuaidi100Service _kuaidi100Service;
    private readonly ServicesAddress _servicesAddress;

    public MailOrderService(IMapper mapper,
        ICapPublisher capPublisher,
        IHttpContextAccessor httpContextAccessor,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> optionsServicesAddress,
        CustomDbContext dbContext,
        IRedisClient redisClient,
        IUserCouponOrderService userCouponOrderService,
        IMultPriceCalculateService multPriceCalculateService,
        IRefundOrderService refundOrderService,
        IMessageNotifyService messageNotifyService,
        IKuaidi100Service kuaidi100Service) : base(dbContext)
    {
        _mapper = mapper;
        _capPublisher = capPublisher;
        _httpContextAccessor = httpContextAccessor;
        _httpClientFactory = httpClientFactory;
        _dbContext = dbContext;
        _redisClient = redisClient;
        _userCouponOrderService = userCouponOrderService;
        _multPriceCalculateService = multPriceCalculateService;
        _refundOrderService = refundOrderService;
        _messageNotifyService = messageNotifyService;
        _kuaidi100Service = kuaidi100Service;
        _servicesAddress = optionsServicesAddress?.Value;
    }

    #region 创建订单

    [UnitOfWork]
    public async Task<long> CreateOrder(CreateOrderInput input)
    {
        var user = _httpContextAccessor.HttpContext.GetCurrentUser();

        #region 验证产品的有效性
        var jsonParam = JsonConvert.SerializeObject(input.ProductSkus.Select(x => new
        {
            ProductSkuId = x.ProductSkuId,
            Quantity = x.Quantity
        }));
        var httpContent = new StringContent(jsonParam, Encoding.UTF8, "application/json");
        var productSkuValiditys = await _httpClientFactory.InternalPostAsync<List<IsMailProductValidity>>(
             requestUri: _servicesAddress.Product_ProductSkuIsMailProductValidity(),
             httpContent: httpContent);
        input.ProductSkus.ForEach(x =>
        {
            //库存不足
            var check = productSkuValiditys
                .Where(p => p.ProductSkuId == x.ProductSkuId)
                .Any(s => s.Quantity < x.Quantity);
            if (check)
                throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);
        });
        if (productSkuValiditys.Any(x => !x.IsValidity))
        {
            //var productSkuValidity = productSkuValiditys.First(x => !x.IsValidity);
            throw new BusinessException(ErrorTypes.Order.OrderVerifyFail);
        }
        #endregion

        #region 计算价格
        jsonParam = JsonConvert.SerializeObject(new
        {
            ProductSkuIds = input.ProductSkus.Select(x => x.ProductSkuId),
            SellingChannels = SellingChannels.WechatMall//微商城
        });
        httpContent = new StringContent(jsonParam, Encoding.UTF8, "application/json");
        var productSkuPrices = await _httpClientFactory.InternalPostAsync<List<GetProductSkuPrice>>(
             requestUri: _servicesAddress.Product_ProductSkuGetProductSkuPrice(),
             httpContent: httpContent);
        #endregion
        var productMultPriceInputs = productSkuPrices.Select(s => new GetProductMultPriceInput
        {
            ProductId = s.ProductId,
            ProductSkuId = s.ProductSkuId,
            Price = s.SellingPrice
        }).ToArray();
        var multPriceOutputs = await _multPriceCalculateService.GetProductMultPrices(ProductType.Mail,
            productMultPriceInputs);


        #region 计算邮费
        jsonParam = JsonConvert.SerializeObject(new
        {
            Province = input.ReceiverAddress.ProvinceCode,
            City = input.ReceiverAddress.CityCode,
            ProductInfos = productSkuValiditys.Select(x =>
            {
                var multPrice = multPriceOutputs.First(c => c.ProductSkuId == x.ProductSkuId).MultPrice;
                return new
                {
                    TemplateId = x.PostageTemplateId,
                    ProductNum = x.Quantity,
                    Amount = multPrice.RealPrice * x.Quantity
                };
            })
        });
        httpContent = new StringContent(jsonParam, Encoding.UTF8, "application/json"); ; ;
        var customerFreight = await _httpClientFactory.InternalPostAsync<CustomerFreight>(
             requestUri: _servicesAddress.Product_PostageTemplateCustomerFreight(),
             httpContent: httpContent);

        if (customerFreight.NoNonDeliveryTemplates.Any())
            throw new BusinessException(ErrorTypes.Product.DeliveryError);
        #endregion

        decimal totalAmount = 0, discountAmount = 0, paymentAmount = 0;
        productSkuPrices.ForEach(x =>
        {
            var multPrice = multPriceOutputs.FirstOrDefault(v => v.ProductSkuId == x.ProductSkuId).MultPrice;
            var quantity = input.ProductSkus.FirstOrDefault(v => v.ProductSkuId == x.ProductSkuId).Quantity;
            var sellingPrice = multPrice.RealPrice * quantity;
            totalAmount += sellingPrice;
        });
        var discountItems = new List<OrderDiscountItemDto>();
        if (input.UserCouponId > 0)
        {
            var request = new GetOrderUserCouponsInput
            {
                UserId = user.userid,
                ProductType = Contracts.Common.Marketing.Enums.LimitProductType.Mail,
                UserCouponId = input.UserCouponId,
                OrderProductInfos = productSkuPrices.Select(p =>
                {
                    var multPrice = multPriceOutputs.FirstOrDefault(v => v.ProductSkuId == p.ProductSkuId).MultPrice;
                    var quantity = input.ProductSkus.FirstOrDefault(v => v.ProductSkuId == p.ProductSkuId).Quantity;
                    var orderAmount = multPrice.RealPrice * quantity;
                    return new OrderProductInfo()
                    {
                        ProductId = p.ProductId,
                        ItemId = p.ProductSkuId,
                        OrderAmount = orderAmount
                    };
                })
            };
            var userCoupon = await _userCouponOrderService.GetOrderUserCoupon(request);
            if (userCoupon?.IsEnable is not true)
                throw new BusinessException(ErrorTypes.Marketing.UserCouponDisabled);
            discountItems.Add(new OrderDiscountItemDto
            {
                DiscountType = OrderDiscountType.UserCoupon,
                DiscountAmount = userCoupon.Discount,
                DiscountId = userCoupon.UserCouponId,
                Title = userCoupon.CouponName
            });

            discountAmount = userCoupon.Discount;//优惠券优惠金额

        }
        var freightAmount = customerFreight.FreightAmount.Sum(x => x.Value);//总邮费
        totalAmount += freightAmount;
        paymentAmount = totalAmount - discountAmount;//减去优惠金额
        var productName = productSkuValiditys.FirstOrDefault()?.ProductName ?? "";
        var productSkuName = productSkuValiditys.FirstOrDefault()?.ProductSkuName ?? "";
        var customerInfo = await _multPriceCalculateService.GetCustomerUserInfo();
        var baseOrder = new BaseOrder()
        {
            ProductName = productName,
            ProductSkuName = productSkuName,
            UserId = user.userid,
            UserNickName = customerInfo?.NickName ?? user.nickname,
            VipLevelId = customerInfo?.VipLevel?.VipLevelId ?? 0,
            VipLevelName = customerInfo?.VipLevel?.Name,
            ContactsName = input.ReceiverAddress.Name,
            ContactsPhoneNumber = input.ReceiverAddress.PhoneNumber,
            OrderType = OrderType.Mail,
            Status = BaseOrderStatus.WaitingForPay,
            SellingPlatform = input.SellingPlatform,
            SellingChannels = SellingChannels.WechatMall,//微商城
            TotalAmount = totalAmount,
            DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentType = PayType.None,
            Message = input.Message
        };

        var mailOrders = new List<MailOrder>();
        var orderPrices = new List<OrderPrice>();
        var mailOrderPostage = new List<MailOrderPostage>();
        productSkuValiditys.ForEach(x =>
        {
            var multPrice = multPriceOutputs.FirstOrDefault(v => v.ProductSkuId == x.ProductSkuId).MultPrice;
            var skuPrice = productSkuPrices.First(c => c.ProductSkuId == x.ProductSkuId);
            var mailOrder = new MailOrder()
            {
                BaseOrderId = baseOrder.Id,
                Quantity = x.Quantity,
                Status = MailOrderStatus.WaitingForPay,
                ProductId = x.ProductId,
                ProductName = x.ProductName,
                ProductSkuId = x.ProductSkuId,
                ProductSkuName = x.ProductSkuName,
                ProductIsSupportRefund = x.IsSupportRefund,//是否支持退款,
                ProductSupplierId = x.SupplierId,//供应商
                ProductImagePath = x.ImagePath,//图片路径
            };
            mailOrders.Add(mailOrder);
            OrderPrice orderPrice = new()
            {
                OrderType = OrderType.Mail,
                BaseOrderId = mailOrder.BaseOrderId,
                SubOrderId = mailOrder.Id,
                OrgPrice = multPrice.Price,
                OrgCostPrice = skuPrice.CostPrice,
                PriceType = multPrice.VipPrice.HasValue ? OrderPriceType.VipPrice : OrderPriceType.Default,
                Price = multPrice.RealPrice,
                CostPrice = skuPrice.CostPrice,
                Quantity = x.Quantity,
            };
            orderPrices.Add(orderPrice);

            decimal freightAmount = 0;
            if (customerFreight.FreightAmount.ContainsKey(x.PostageTemplateId))
                freightAmount = customerFreight.FreightAmount[x.PostageTemplateId];

            mailOrderPostage.Add(new MailOrderPostage()
            {
                BaseOrderId = baseOrder.Id,
                MailOrderId = mailOrder.Id,
                PostageTemplateId = x.PostageTemplateId,
                PostageTemplateName = x.PostageTemplateName,
                Amount = freightAmount
            });
        });
        //订单优惠
        if (discountItems.Any())
        {
            await _dbContext.BaseOrderDiscounts.AddRangeAsync(discountItems.Select(x => new BaseOrderDiscount
            {
                BaseOrderId = baseOrder.Id,
                DiscountType = x.DiscountType,
                DiscountAmount = x.DiscountAmount,
                DiscountId = x.DiscountId,
                Title = x.Title
            }));
        }
        //订单分享信息
        if (input.TraceId is > 0)
        {
            await _dbContext.OrderShareInfos.AddAsync(new OrderShareInfo
            {
                BaseOrderId = baseOrder.Id,
                OrderType = baseOrder.OrderType,
                BuyerId = baseOrder.UserId,
                TraceId = input.TraceId,
                CreateTime = baseOrder.CreateTime
            });
            
            //上送跟踪日志
            await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                new PushPromotionTraceRecordMessage
                {
                    PromotionTraceId = input.TraceId.Value,
                    CustomerId = baseOrder.UserId,
                    BehaviorType = TraceBehaviorType.CreateOrder,
                    OrderType = baseOrder.OrderType,
                    OrderId = baseOrder.Id,
                    VisitTargetName = baseOrder.ProductName
                });
        }
        await _dbContext.AddAsync(new OrderLogs()
        {
            OperationRole = UserType.Customer,
            OperationType = OrderOperationType.Created,
            OrderLogType = OrderLogType.Mail,
            OrderId = baseOrder.Id,
            UserId = user.userid,
            UserName = user.nickname
        });
        await _dbContext.AddAsync(baseOrder);
        await _dbContext.AddRangeAsync(mailOrders);
        await _dbContext.AddRangeAsync(orderPrices);
        await _dbContext.AddRangeAsync(mailOrderPostage);
        await _dbContext.AddAsync(new MailOrderReceiverAddress()
        {
            CityCode = input.ReceiverAddress.CityCode,
            ProvinceCode = input.ReceiverAddress.ProvinceCode,
            Address = input.ReceiverAddress.Address,
            BaseOrderId = baseOrder.Id,
            CityName = input.ReceiverAddress.CityName,
            DistrictCode = input.ReceiverAddress.DistrictCode,
            DistrictName = input.ReceiverAddress.DistrictName,
            Name = input.ReceiverAddress.Name,
            PhoneNumber = input.ReceiverAddress.PhoneNumber,
            ProvinceName = input.ReceiverAddress.ProvinceName
        });

        foreach (var mailOrder in mailOrders)
        {
            var orderPrice = orderPrices.FirstOrDefault(x => x.SubOrderId == mailOrder.Id);
            //冻结库存
            await _capPublisher.PublishAsync(CapTopics.Inventory.FrozenGeneralInventory, new FrozenGeneralInventoryMessage
            {
                TenantId = user.tenant.Value,
                OrderId = mailOrder.Id,
                ProductId = mailOrder.ProductId,
                ItemId = mailOrder.ProductSkuId,
                FrozenQuantity = orderPrice.Quantity
            });
        }

        //移除购物车
        await _capPublisher.PublishAsync(CapTopics.Product.ShoppingCartProductRemove, new RemoveShoppingCartProductMessage
        {
            TenantId = user.tenant.Value,
            UserId = user.userid,
            ProductSkuIds = mailOrders.Select(x => x.ProductSkuId).ToList()
        });
        //使用优惠券
        if (input.UserCouponId > 0 && baseOrder.DiscountAmount > 0)
        {
            await _capPublisher.PublishAsync(CapTopics.Marketing.UserCouponUsed, new UserCouponUsedMessage
            {
                TenantId = user.tenant.Value,
                UserCouponId = input.UserCouponId,
                BaseOrderId = baseOrder.Id,
                OrderType = OrderType.Mail
            });
        }
        await _dbContext.SaveChangesAsync();
        //超时自动关闭
        Hangfire.BackgroundJob.Schedule<HangfireClient.Jobs.IOrderJob>(s => s.CloseTimeoutOrder(baseOrder.Id, user.tenant.Value), TimeSpan.FromMinutes(30));
        return baseOrder.Id;
    }
    #endregion

    #region 修改订单地址
    public async Task<bool> UpdateBaseOrderReceiverAddress(UpdateBaseOrderReceiverAddressInput input, CurrentUser user)
    {
        if (!_dbContext.BaseOrders.Any(s => s.UserId == user.userid && input.BaseOrderId == input.BaseOrderId))
            throw new BusinessException("非法操作");
        var isMailOrderTrackingNumber = _dbContext.MailOrders
            .Any(x => x.BaseOrderId == input.BaseOrderId && x.Status == MailOrderStatus.WaitingForDeliver);
        if (isMailOrderTrackingNumber)
            throw new BusinessException("存在已发货订单，不支持修改地址");
        var mailOrderReceiverAddress = await _dbContext.MailOrderReceiverAddresses
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        mailOrderReceiverAddress.ReceiverAddressId = input.ReceiverAddressId;
        mailOrderReceiverAddress.Name = input.Name;
        mailOrderReceiverAddress.PhoneNumber = input.PhoneNumber;
        mailOrderReceiverAddress.ProvinceCode = input.ProvinceCode;
        mailOrderReceiverAddress.ProvinceName = input.ProvinceName;
        mailOrderReceiverAddress.CityCode = input.CityCode;
        mailOrderReceiverAddress.CityName = input.CityName;
        mailOrderReceiverAddress.DistrictCode = input.DistrictCode;
        mailOrderReceiverAddress.DistrictName = input.DistrictName;
        mailOrderReceiverAddress.Address = input.Address;
        var logs = new OrderLogs
        {
            OperationType = OrderOperationType.DeliverAddressUpdated,
            OrderLogType = OrderLogType.Mail,
            OrderId = mailOrderReceiverAddress.BaseOrderId,
            UserId = user.userid,
            UserName = user.nickname,
            OperationRole = UserType.Customer
        };
        await _dbContext.AddAsync(logs);
        var rows = await _dbContext.SaveChangesAsync();
        return rows > 0;
    }

    #endregion

    #region 分页查询订单
    public async Task<PagingModel<GetMailOrderOutput, IEnumerable<MailOrderStatusCount>>> GetMailOrders(GetMailOrderInput input)
    {
        var baseOrderQuery = _dbContext.BaseOrders.AsNoTracking()
            .WhereIF(input.BeginDate.HasValue, x => x.CreateTime >= input.BeginDate.Value)
            .WhereIF(input.EndDate.HasValue, x => x.CreateTime < input.EndDate.Value)
            .WhereIF(input.BaseOrderId.HasValue, x => x.Id.ToString().Contains(input.BaseOrderId.Value.ToString()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactsPhoneNumber), x => x.ContactsPhoneNumber.StartsWith(input.ContactsPhoneNumber))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactsName), x => x.ContactsName.Contains(input.ContactsName))
            .WhereIF(input.SellingPlatform.HasValue, x => x.SellingPlatform == input.SellingPlatform);

        var mailOrderBaseQuery = _dbContext.MailOrders.AsNoTracking()
            .WhereIF(!string.IsNullOrWhiteSpace(input.ProductName), x => x.ProductName.Contains(input.ProductName))
            .WhereIF(input.ProductSupplierId.HasValue, x => x.ProductSupplierId == input.ProductSupplierId);

        var mailOrderQuery = mailOrderBaseQuery.WhereIF(input.Status.HasValue, x => x.Status == input.Status.Value);

        var baseOrderIds = await baseOrderQuery
            .Join(mailOrderQuery
                , bo => bo.Id
                , mo => mo.BaseOrderId
                , (bo, mo) => new { bo, mo })
            .GroupBy(x => new { x.bo.Id, x.bo.CreateTime })
            .OrderByDescending(s => s.Key.CreateTime)//按创建时间倒序
            .PagingAsync(input.PageIndex, input.PageSize, x => x.Key.Id);

        //主单
        var baseOrders = await _dbContext.BaseOrders
            .Where(s => baseOrderIds.Data.Contains(s.Id))
            .Select(s => new GetMailOrderOutput
            {
                BaseOrderId = s.Id,
                ChannelOrderNo = s.ChannelOrderNo,
                ContactsName = s.ContactsName,
                ContactsPhoneNumber = s.ContactsPhoneNumber,
                CreateTime = s.CreateTime,
                PaymentAmount = s.PaymentAmount,
                SellingPlatform = s.SellingPlatform,
            })
            .OrderByDescending(s => s.CreateTime)
            .ToListAsync();
        //子单
        var mailOrders = await mailOrderQuery
            .Join(_dbContext.OrderPrices, x => x.Id, x => x.SubOrderId, (mailOrder, orderPrice) => new
            {
                mailOrder,
                orderPrice
            })
            .Where(s => baseOrderIds.Data.Contains(s.mailOrder.BaseOrderId))
            .Select(s => new GetMailOrder_MailOrder
            {
                BaseOrderId = s.mailOrder.BaseOrderId,
                MailOrderId = s.mailOrder.Id,
                ProductId = s.mailOrder.ProductId,
                ProductName = s.mailOrder.ProductName,
                ProductSellingPrice = s.orderPrice.Price,
                ProductSkuId = s.mailOrder.ProductSkuId,
                ProductSkuName = s.mailOrder.ProductSkuName,
                Quantity = s.orderPrice.Quantity,
                Status = s.mailOrder.Status
            })
            .ToListAsync();
        foreach (var order in baseOrders)
        {
            order.MailOrders = mailOrders
                .Where(s => s.BaseOrderId == order.BaseOrderId)
                .ToList();
        }

        var mailOrderStatusCount = await baseOrderQuery
             .Join(mailOrderBaseQuery, b => b.Id, m => m.BaseOrderId, (baseOrder, mailOrder) => new { mailOrder.Status })
             .GroupBy(s => s.Status)
             .Select(s => new MailOrderStatusCount
             {
                 Count = s.Count(),
                 OrderStatus = s.Key
             }).ToListAsync();

        var result = new PagingModel<GetMailOrderOutput, IEnumerable<MailOrderStatusCount>>()
        {
            PageIndex = baseOrderIds.PageIndex,
            PageSize = baseOrderIds.PageSize,
            Total = baseOrderIds.Total,
            Data = baseOrders,
            Supplement = mailOrderStatusCount
        };
        return result;
    }
    #endregion

    #region 订单发货信息
    public async Task<GetMailOrderShippedListOutput> GetMailOrderShippedList(long baseOrderId)
    {
        var baseOrder = await _dbContext.BaseOrders
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == baseOrderId);
        if (baseOrder == null || baseOrder.Status == BaseOrderStatus.Closed)
            throw new BusinessException("订单不存在或已关闭");

        var result = new GetMailOrderShippedListOutput();
        result.PostageTemplates = new List<GetMailOrderShippedList_PostageTemplate>();
        result.Message = baseOrder.Message;

        var mailOrderReceiverAddress = await _dbContext.MailOrderReceiverAddresses
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);
        if (mailOrderReceiverAddress == null)
            throw new BusinessException("订单收货地址有误");
        result.ReceiverAddress = new GetMailOrderShippedList_ReceiverAddress()
        {
            Address = mailOrderReceiverAddress.Address,
            CityName = mailOrderReceiverAddress.CityName,
            DistrictName = mailOrderReceiverAddress.DistrictName,
            Name = mailOrderReceiverAddress.Name,
            PhoneNumber = mailOrderReceiverAddress.PhoneNumber,
            ProvinceName = mailOrderReceiverAddress.ProvinceName
        };

        var mailOrder = await _dbContext.MailOrders
            .AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrder.Id && x.Status == MailOrderStatus.WaitingForDeliver)
            .ToListAsync();
        var mailOrderPostage = await _dbContext.MailOrderPostages
            .Where(x => mailOrder.Select(v => v.Id).Contains(x.MailOrderId))
            .ToListAsync();

        var data = from mo in mailOrder
                   join mop in mailOrderPostage on mo.Id equals mop.MailOrderId
                   select new
                   {
                       mo.Id,
                       mo.ProductName,
                       mo.ProductSkuName,
                       mop.PostageTemplateId,
                       mop.PostageTemplateName,
                       mop.Amount,
                   };
        foreach (var item in data.GroupBy(x => x.PostageTemplateId))
        {
            result.PostageTemplates.Add(new GetMailOrderShippedList_PostageTemplate()
            {
                PostageTemplateId = item.Key,
                PostageTemplateName = item.First().PostageTemplateName,
                MailOrders = item
                    .Select(x => new GetMailOrderShippedList_PostageTemplate_MailOrder()
                    {
                        MailOrderId = x.Id,
                        Amount = x.Amount,
                        ProductName = x.ProductName,
                        ProductSkuName = x.ProductSkuName
                    })
                    .ToList()
            });
        }
        return result;
    }
    #endregion

    #region 订单发货

    [UnitOfWork]
    public async Task SendOutGoods(SendOutGoodsInput input)
    {
        var user = _httpContextAccessor.HttpContext.GetCurrentUser();
        var logisticsCompany = await _dbContext.LogisticsCompanies
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Code == input.LogisticsCompanyCode);
        if (logisticsCompany == null)
            throw new BusinessException("物流信息有误");
        var mailOrders = await _dbContext.MailOrders
            .Where(x => input.MailOrderIds.Contains(x.Id))
            .ToListAsync();
        if (mailOrders.Count != input.MailOrderIds.Count
            || mailOrders.Any(s => s.Status != MailOrderStatus.WaitingForDeliver))
            throw new BusinessException("订单信息有误");
        foreach (var mailOrder in mailOrders)
        {
            mailOrder.TrackingNumber = input.TrackingNumber;
            mailOrder.Status = MailOrderStatus.WaitingForSign;
            mailOrder.UpdateTime = DateTime.Now;
        }

        await _dbContext.AddAsync(new OrderLogs()
        {
            OperationRole = UserType.Merchant,
            OperationType = OrderOperationType.Delivered,
            OrderLogType = OrderLogType.Mail,
            OrderId = mailOrders.First().BaseOrderId,
            UserId = user.userid,
            UserName = user.nickname
        });

        var existsTrackingNumber = await _dbContext.MailOrderLogistics
            .AnyAsync(x => x.TrackingNumber == input.TrackingNumber);
        if (existsTrackingNumber)
            throw new BusinessException("快递单号重复");

        var logistics = new MailOrderLogistics()
        {
            BaseOrderId = mailOrders.First().BaseOrderId,
            TrackingNumber = input.TrackingNumber,
            LogisticsCompanyName = logisticsCompany.Name,
            LogisticsCompanyCode = logisticsCompany.Code,
            Status = "卖家已发货"
        };
        await _dbContext.AddAsync(logistics);

        var baseOrderId = mailOrders.FirstOrDefault().BaseOrderId;
        var baseOrder = await _dbContext.BaseOrders.AsNoTracking()
            .Where(o => o.Id == baseOrderId)
            .FirstOrDefaultAsync();
        var orderPrices = await _dbContext.OrderPrices.AsNoTracking()
            .Where(x => input.MailOrderIds.Contains(x.SubOrderId))
            .ToListAsync();

        //消息通知
        await _messageNotifyService.MailDeliverNotify(new OrderNotifyDto<MailDeliverNotifyDto>
        {
            BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
            NotifyDto = new MailDeliverNotifyDto
            {
                ProductName = baseOrder.ProductName,
                SkuName = baseOrder.ProductSkuName,
                Quantity = orderPrices.Sum(m => m.Quantity),
                LogisticsCompanyName = logisticsCompany.Name,
                TrackingNumber = input.TrackingNumber,
                DeliverTime = DateTime.Now
            }
        });
    }
    #endregion

    #region 订单详情
    public async Task<OrderDetailsOutput> Details(long baseOrderId, long? customerId)
    {
        var baseOrder = await _dbContext.BaseOrders
            .Where(s => s.Id == baseOrderId)
            .WhereIF(customerId.HasValue, s => s.UserId == customerId)
            .FirstAsync();
        var result = _mapper.Map<OrderDetailsOutput>(baseOrder);

        //收件人
        result.ReceiverAddress = await _dbContext.MailOrderReceiverAddresses
            .Where(s => s.BaseOrderId == baseOrderId)
            .Select(s => new MailOrderReceiverAddressDto
            {
                ReceiverAddressId = s.ReceiverAddressId,
                Address = s.Address,
                CityCode = s.CityCode,
                CityName = s.CityName,
                DistrictCode = s.DistrictCode,
                DistrictName = s.DistrictName,
                Name = s.Name,
                PhoneNumber = s.PhoneNumber,
                ProvinceCode = s.ProvinceCode,
                ProvinceName = s.ProvinceName,
                BaseOrderId = s.BaseOrderId,

            })
            .FirstAsync();

        var mailOrderPostages = await _dbContext.MailOrderPostages
            .AsNoTracking()
            .Where(s => s.BaseOrderId == baseOrderId)
            .ToListAsync();
        var mailOrders = await _dbContext.MailOrders
            .AsNoTracking()
            .Where(s => s.BaseOrderId == baseOrderId)
            .ToListAsync();
        var mailOrderLogistiies = await _dbContext.MailOrderLogistics
            .AsNoTracking()
            .Where(s => s.BaseOrderId == baseOrderId)
            .ToListAsync();
        var orderPrices = await _dbContext.OrderPrices
            .Where(x => x.BaseOrderId == baseOrderId)
            .ToListAsync();

        //快递单号分组
        var orderPostages = mailOrders.Join(mailOrderPostages, m => m.Id, p => p.MailOrderId, (m, p) => new
        {
            MailOrder = m,
            m.TrackingNumber,
            p.MailOrderId,
            p.PostageTemplateId,
            p.Amount,
        }).ToList();
        result.SubOrders = orderPostages
            .GroupBy(x => new { x.TrackingNumber, x.PostageTemplateId, x.Amount })
            .Select(s =>
            {
                var mailOrderLogistics = mailOrderLogistiies
                    .Where(x => x.TrackingNumber == s.Key.TrackingNumber)
                    .FirstOrDefault();
                LogisticsInfo logistics = null;
                if (mailOrderLogistics != null)
                {
                    logistics = new LogisticsInfo
                    {
                        MailOrderLogisticsId = mailOrderLogistics.Id,
                        Status = mailOrderLogistics.Status,
                        CreateTime = mailOrderLogistics.CreateTime,
                        LogisticsCompanyName = mailOrderLogistics.LogisticsCompanyName,
                        TrackingNumber = mailOrderLogistics.TrackingNumber,
                        UpdateTime = $"{mailOrderLogistics.UpdateTime:yyyy-MM-dd HH:mm:ss}"
                    };
                }
                var mailOrderDetails = _mapper.Map<List<MailOrderDetail>>(s.Select(a => a.MailOrder));
                foreach (var mailOrderDetail in mailOrderDetails)
                {
                    var orderPrice = orderPrices.FirstOrDefault(x => x.SubOrderId == mailOrderDetail.Id);
                    mailOrderDetail.ProductSellingPrice = orderPrice.Price;
                    mailOrderDetail.ProductCostPrice = orderPrice.CostPrice;
                    mailOrderDetail.VipPrice = orderPrice.PriceType == OrderPriceType.VipPrice ? orderPrice.Price : null;
                    mailOrderDetail.PaymentCurrencyCode = orderPrice.PaymentCurrencyCode;
                    mailOrderDetail.CostCurrencyCode = orderPrice.CostCurrencyCode;
                }
                return new MailOrderGroup
                {
                    PostageTemplateId = s.Key.PostageTemplateId,
                    PostageAmount = s.Key.Amount,
                    Details = mailOrderDetails,
                    Logistics = logistics
                };
            })
            .ToList();

        return result;
    }
    #endregion

    #region 查询物流公司列表
    public async Task<List<GetLogisticsCompanyOutput>> GetLogisticsCompanys()
    {
        return await _dbContext.LogisticsCompanies
            .Select(x => new GetLogisticsCompanyOutput
            {
                Code = x.Code,
                Name = x.Name
            })
            .ToListAsync();
    }
    #endregion

    #region 查看物流轨迹 & 更新物流轨迹

    public async Task<IEnumerable<TrackData>> GetLogisticsInfo(long mailOrderLogisticsId)
    {
        var logistics = await _dbContext.MailOrderLogistics
            .FindAsync(mailOrderLogisticsId);
        //未发货 or 找不到物流
        if (logistics is null)
            return Enumerable.Empty<TrackData>();
        //5分钟内更新过
        var defaultTrackData = JsonConvert.DeserializeObject<IEnumerable<TrackData>>(logistics.TrackData ?? string.Empty);
        if (logistics.UpdateTime != null && logistics.UpdateTime.Value.AddMinutes(5) > DateTime.Now)
            return defaultTrackData;

        //从未查过物流 or 5分钟之前查过
        var lockName = $"logisticsTracking:{mailOrderLogisticsId}";
        var lockSecret = Guid.NewGuid().ToString();
        var hasLock = await _redisClient.LockTakeAsync(lockName, lockSecret);
        if (hasLock == false)
            return defaultTrackData;

        try
        {
            var response = await _kuaidi100Service
                .Tracking(new QueryTrackParam { num = logistics.TrackingNumber, com = logistics.LogisticsCompanyCode });
            var trackingData = JsonConvert.DeserializeObject<Kuaidi100TrackingDto>(response);
            if (trackingData.status == "200")
            {
                //更新轨迹
                logistics.TrackData = JsonConvert.SerializeObject(trackingData.data);
                if (trackingData.data.Any())
                {
                    var status = trackingData.data.First().status;
                    if (!string.IsNullOrWhiteSpace(status))
                        logistics.Status = status;
                    if (DateTime.TryParse(trackingData.data.First().ftime, out var ftime))
                        logistics.UpdateTime = ftime;
                }

                //更新待签收状态
                if (trackingData.state == "3")
                {
                    logistics.Status = "已签收";
                    var mailOrders = await _dbContext.MailOrders
                        .Where(s => s.BaseOrderId == logistics.BaseOrderId
                            && s.TrackingNumber == logistics.TrackingNumber
                            && s.Status == MailOrderStatus.WaitingForSign)
                        .ToListAsync();
                    if (DateTime.TryParse(trackingData.data.First().ftime, out DateTime signTime) == false)
                        signTime = DateTime.Now;
                    foreach (var order in mailOrders)
                    {
                        order.UpdateTime = DateTime.Now;
                        order.SignTime = signTime;
                        order.Status = MailOrderStatus.Signed;
                    }
                }
                await _dbContext.SaveChangesAsync();
            }
            return JsonConvert.DeserializeObject<IEnumerable<TrackData>>(logistics.TrackData ?? string.Empty);
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockName, lockSecret);
        }
    }

    #endregion

    #region 完结电商邮寄订单

    [UnitOfWork]
    public async Task<IEnumerable<long>> CompletedMailOrder()
    {
        var subOrderIds = new List<long>();
        /*1.查询存在已签收7天和已发货14天的电商订单的主单*/
        var signedEndDate = DateTime.Today.AddDays(-7).AddDays(1).AddSeconds(-1);//7天前
        var waitingForSignEndDate = DateTime.Today.AddDays(-14).AddDays(1).AddSeconds(-1);//14天前
        var baseOrderIds = await _dbContext.BaseOrders
            .Join(_dbContext.MailOrders, b => b.Id, m => m.BaseOrderId, (b, m) => new
            {
                BaseOrderId = b.Id,
                b.OrderType,
                m.Status,
                m.SignTime,
            })
            .IgnoreQueryFilters()
            .Where(x => x.OrderType == OrderType.Mail //邮寄订单
            && (
                (x.Status == MailOrderStatus.Signed && x.SignTime <= signedEndDate)
                || (x.Status == MailOrderStatus.WaitingForSign && x.SignTime <= waitingForSignEndDate)
            ))
            .GroupBy(x => x.BaseOrderId)
            .Select(x => x.Key)
            .ToListAsync();
        if (baseOrderIds.Any() is false) return subOrderIds;
        /*2.根据主单查询关联电商订单*/
        var baseOrders = await _dbContext.BaseOrders
            .IgnoreQueryFilters()
            .Where(b => baseOrderIds.Contains(b.Id))
            .ToListAsync();

        var mailOrders = await _dbContext.MailOrders
            .IgnoreQueryFilters()
            .Where(m => baseOrderIds.Contains(m.BaseOrderId))
            .ToListAsync();

        var shareInfoList = await _dbContext.OrderShareInfos
            .IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        
        foreach (var baseOrder in baseOrders)
        {
            var orders = mailOrders.Where(m => m.BaseOrderId.Equals(baseOrder.Id)).ToList();
            if (!orders.Any()) continue;
            foreach (var order in orders)
            {
                /*3.电商订单 已签收7天的子单改为 已完成 已发货14天但是未签收状态的子单改为 已完成*/
                if ((order.Status == MailOrderStatus.Signed && order.SignTime <= signedEndDate)
                    || (order.Status == MailOrderStatus.WaitingForSign && order.SignTime <= waitingForSignEndDate))
                {
                    order.Status = MailOrderStatus.Finished;
                    order.UpdateTime = DateTime.Now;
                    subOrderIds.Add(order.Id);
                }
            }
            /*4.如果一个主单下所有子单状态"已完成" or "已退款"，将主单改为 已完成*/
            var hasFinished = orders.Any(o => o.Status == MailOrderStatus.Finished)
                && orders.All(o => o.Status == MailOrderStatus.Finished || o.Status == MailOrderStatus.Refunded);
            if (hasFinished && baseOrder.Status == BaseOrderStatus.UnFinished)//待完成
            {
                baseOrder.Status = BaseOrderStatus.Finished;
                baseOrder.UpdateTime = DateTime.Now;
                //达人奖金待提现
                await _capPublisher.PublishAsync(CapTopics.User.BonusRelease,
                   new BonusReleaseMessage
                   {
                       TenantId = baseOrder.TenantId,
                       BaseOrderId = baseOrder.Id,
                       UserNickName = baseOrder.UserNickName,
                       Status = 1//1-完成
                   });
                
                //记录跟踪日志
                var shareInfo = shareInfoList.FirstOrDefault(x => x.BaseOrderId == baseOrder.Id);
                if (shareInfo?.TraceId is >0)
                {
                    await _capPublisher.PublishAsync(CapTopics.Marketing.PushPromotionTraceRecord,
                        new PushPromotionTraceRecordMessage
                        {
                            PromotionTraceId = shareInfo.TraceId.Value,
                            CustomerId = shareInfo.BuyerId,
                            BehaviorType = TraceBehaviorType.CompleteOrder,
                            OrderType = baseOrder.OrderType,
                            OrderId = baseOrder.Id,
                            VisitTargetName = baseOrder.ProductName
                        });
                }
            }
        }
        return subOrderIds;
    }

    #endregion

    #region 客户发起退款申请

    [UnitOfWork]
    public async Task Refund(MailOrderRefundInput input, CurrentUser user)
    {
        var refundInfoOutputs = await GetMailOrdersByRefundable(input.BaseOrderId, input.MailOrderId);
        var refundInfo = refundInfoOutputs.FirstOrDefault();
        if (refundInfo is null)
            throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);

        var hasOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == input.BaseOrderId && x.UserId == user.userid)
            .AnyAsync();
        if (hasOrder is false) throw new BusinessException("订单不存在");

        var mailOrder = (await _dbContext.MailOrders
            .Where(o => o.Id == input.MailOrderId && o.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync()) ?? throw new BusinessException("订单不存在");

        if (mailOrder.Status == MailOrderStatus.Refunding || mailOrder.Status == MailOrderStatus.Refunded)
            throw new BusinessException(ErrorTypes.Order.OrderCannotRefund);

        var orderPrice = await _dbContext.OrderPrices
            .AsNoTracking()
            .Where(x => x.SubOrderId == mailOrder.Id)
            .FirstOrDefaultAsync();

        mailOrder.Status = MailOrderStatus.Refunding;
        mailOrder.UpdateTime = DateTime.Now;
        var logs = new OrderLogs
        {
            OperationRole = UserType.Customer,
            OperationType = OrderOperationType.RefundApplied,
            OrderLogType = OrderLogType.Refund,
            OrderId = mailOrder.BaseOrderId,
            UserId = user.userid,
            UserName = user.nickname
        };
        await _dbContext.AddAsync(logs);

        var refundOrderApplyCommand = new RefundOrderApplyMessage()
        {
            BaseOrderId = mailOrder.BaseOrderId,
            HasReviewed = false,
            Quantity = refundInfo.Quantity,
            RefundOrderType = RefundOrderType.Mail,
            SubOrdeId = mailOrder.Id,
            ReviewTime = DateTime.Now,
            TenantId = mailOrder.TenantId,
            PaymentCurrencyCode = orderPrice.PaymentCurrencyCode,
            TotalAmount = refundInfo.MaxAmount,//最多可退
            PostageAmount = refundInfo.PostageAmount,//邮费
            UserType = UserType.Customer,
            UserId = user.userid,
            UserName = user.nickname,
            Reason = input.Reason,
            ProofImgs = input.ProofImgs != null ? string.Join(",", input.ProofImgs) : "",
            SupplierId = mailOrder.ProductSupplierId,
            Cost = orderPrice.CostPrice * orderPrice.Quantity,
            CostCurrencyCode = orderPrice.CostCurrencyCode,
        };
        await _capPublisher.PublishAsync(CapTopics.Order.OrderRefundApply, refundOrderApplyCommand);
    }

    #endregion

    #region 主动退款

    public async Task<IList<GetMailOrderRefundInfoOutput>> GetMailOrdersByRefundable(long baseOrderId, long? orderId)
    {
        var baseOrder = await _dbContext.BaseOrders
            .Where(x => x.Id == baseOrderId)
            .Select(x => new
            {
                x.Id,
                x.TotalAmount,
                x.DiscountAmount,
                x.PaymentAmount
            })
            .FirstOrDefaultAsync();

        var existsOffsetOrder = await _dbContext.OffsetOrders
           .AnyAsync(x => x.BaseOrderId == baseOrderId);
        if (existsOffsetOrder)
            throw new BusinessException(ErrorTypes.Order.RefundRefunseByOffsetOrder);

        var enableStatus = new MailOrderStatus[]
        {
            MailOrderStatus.WaitingForDeliver,
            MailOrderStatus.WaitingForSign,
            MailOrderStatus.Signed
        };
        var query = from mailOrder in _dbContext.MailOrders
                    join orderPrice in _dbContext.OrderPrices
                    on mailOrder.Id equals orderPrice.SubOrderId
                    where mailOrder.BaseOrderId == baseOrderId
                    select new GetMailOrderRefundInfoOutput
                    {
                        Id = mailOrder.Id,
                        ProductSellingPrice = orderPrice.Price,
                        Quantity = orderPrice.Quantity,
                        ProductName = mailOrder.ProductName,
                        ProductSkuName = mailOrder.ProductSkuName,
                        Status = mailOrder.Status,
                    };
        var orders = await query.ToListAsync();

        var mailOrderPostages = await _dbContext.MailOrderPostages
            .Where(x => x.BaseOrderId == baseOrderId)
            .ToListAsync();
        var orderIds = orders
           .Where(x => enableStatus.Contains(x.Status))
           .Select(x => x.Id);
        var orderPostageTemplates = mailOrderPostages
            .Where(x => orderIds.Contains(x.MailOrderId))
            .GroupBy(x => x.PostageTemplateId)
            .Select(x => new
            {
                PostageTemplateId = x.Key,
                x.First().Amount,
                Count = x.Count()
            });

        var totalAmount = baseOrder.TotalAmount;
        var discountAmount = baseOrder.DiscountAmount;
        var leftDiscountAmount = discountAmount;
        for (int i = 0; i < orders.Count; i++)
        {
            var order = orders[i];
            order.TotalAmount = order.ProductSellingPrice * order.Quantity;
            order.Rate = totalAmount == 0 ? 0 : Math.Round(order.TotalAmount / totalAmount, 2);

            var mailOrderPostage = mailOrderPostages
                .FirstOrDefault(x => x.MailOrderId == order.Id);
            if (mailOrderPostage is not null)
            {
                order.PostageAmount = mailOrderPostage.Amount;
                var orderPostageTemplate = orderPostageTemplates
                    .First(x => x.PostageTemplateId == mailOrderPostage.PostageTemplateId);
                order.IncludePostageAmount = orderPostageTemplate.Count <= 1;
            }

            if (discountAmount > 0)
            {
                if ((i + 1) == orders.Count)
                {
                    order.DiscountAmount = leftDiscountAmount;
                    continue;
                }
                order.DiscountAmount = Math.Round(order.Rate * discountAmount, 2);
                leftDiscountAmount -= order.DiscountAmount;
            }
        }

        var outputs = orders
            .Where(x => enableStatus.Contains(x.Status))
            .Where(x => !orderId.HasValue || x.Id == orderId)
            .ToList();

        return outputs;
    }

    [UnitOfWork]
    public async Task RefundMailOrder(RefundMailOrderInput input, CurrentUser user)
    {
        var refundInfoOutputs = await GetMailOrdersByRefundable(input.BaseOrderId, input.MailOrderId);
        var refundInfo = refundInfoOutputs.FirstOrDefault();
        if (refundInfo is null)
            throw new BusinessException("订单当前状态不能退款");

        var mailOrder = (await _dbContext.MailOrders
            .Where(o => o.Id == input.MailOrderId && o.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync()) ?? throw new BusinessException("订单不存在");
        var orderPrice = await _dbContext.OrderPrices
            .AsNoTracking()
            .Where(x => x.SubOrderId == mailOrder.Id)
            .FirstOrDefaultAsync();
        if (input.Quantity > orderPrice.Quantity)
            throw new BusinessException("退款数量不正确");

        if (input.RefundAmount > refundInfo.MaxAmount)
            throw new BusinessException("退款金额不能大于订单金额");

        mailOrder.Status = MailOrderStatus.Refunding;
        mailOrder.UpdateTime = DateTime.Now;
        var logs = new OrderLogs
        {
            OperationRole = UserType.Merchant,
            OperationType = OrderOperationType.RefundApplied,
            OrderLogType = OrderLogType.Refund,
            OrderId = mailOrder.BaseOrderId,
            UserId = user.userid,
            UserName = user.nickname
        };
        await _dbContext.AddAsync(logs);

        var refundOrderApplyCommand = new RefundOrderApplyMessage
        {
            BaseOrderId = mailOrder.BaseOrderId,
            HasReviewed = true,
            Quantity = input.Quantity,
            Reason = input.Reason,
            RefundOrderType = RefundOrderType.Mail,
            SubOrdeId = mailOrder.Id,
            ReviewTime = DateTime.Now,
            TenantId = mailOrder.TenantId,
            PaymentCurrencyCode = orderPrice.PaymentCurrencyCode,
            TotalAmount = input.RefundAmount,
            PostageAmount = 0,
            UserType = UserType.Merchant,
            UserId = user.userid,
            UserName = user.nickname,
            SupplierId = mailOrder.ProductSupplierId,
            Cost = orderPrice.CostPrice * input.Quantity,
            CostCurrencyCode = orderPrice.CostCurrencyCode,
        };
        await _capPublisher.PublishAsync(CapTopics.Order.OrderRefundApply, refundOrderApplyCommand);
    }

    #endregion
}
