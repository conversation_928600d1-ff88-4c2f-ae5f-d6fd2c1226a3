using Aliyun.OSS;
using AutoMapper;
using Cit.Storage.Aliyun.Oss;
using Cit.Storage.Redis;
using Common.GlobalException;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.ScenicTicketOrder.OTA;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.DTOs.TicketsCombinationOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Scenic.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hangfire;
using MathNet.Numerics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Order.Api.ConfigModel;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Interfaces;
using System.Collections.Concurrent;

namespace Order.Api.Services;

public class TicketsCombinationOrderService : ITicketsCombinationOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IScenicTicketOrderService _scenicTicketOrderService;
    private readonly IScenicTicketOTAService _scenicTicketOtaService;
    private readonly ILogger<TicketsCombinationOrderService> _logger;
    private readonly IAgencyService _agencyService;
    private readonly IRedisClient _redisClient;
    private readonly IScenicTicketOrderMessageService _scenicTicketOrderMessageService;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IScenicTicketPurchaseService _scenicTicketPurchaseService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IOrderOssService _orderOssService;
    private readonly ICapPublisher _capPublisher;

    private const string _lockKey = "combinationscenicorder:delivery:{0}";
    private readonly string _timeSlotFormat = @"hh\:mm";

    public TicketsCombinationOrderService(CustomDbContext dbContext,
        IMapper mapper,
        IScenicTicketOrderService scenicTicketOrderService,
        IScenicTicketOTAService scenicTicketOtaService,
        ILogger<TicketsCombinationOrderService> logger,
        IAgencyService agencyService,
        IRedisClient redisClient,
        IScenicTicketOrderMessageService scenicTicketOrderMessageService,
        IBackgroundJobClient backgroundJobClient,
        IScenicTicketPurchaseService scenicTicketPurchaseService,
        IOpenPlatformBaseService openPlatformBaseService,
        IOrderOssService orderOssService,
        ICapPublisher capPublisher)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _scenicTicketOrderService = scenicTicketOrderService;
        _scenicTicketOtaService = scenicTicketOtaService;
        _logger = logger;
        _agencyService = agencyService;
        _redisClient = redisClient;
        _scenicTicketOrderMessageService = scenicTicketOrderMessageService;
        _backgroundJobClient = backgroundJobClient;
        _scenicTicketPurchaseService = scenicTicketPurchaseService;
        _openPlatformBaseService = openPlatformBaseService;
        _orderOssService = orderOssService;
        _capPublisher = capPublisher;
    }

    public async Task<PagingModel<SearchTicketsCombinationOrderOutput, CombinationOrderStatistics>> Search(SearchTicketsCombinationOrderInput input)
    {
        //组合订单Query
        var ticketsCombinationQuery = _dbContext.TicketsCombinationOrders.AsNoTracking()
            .WhereIF(input.TicketsCombinationId is not null,
                x => x.TicketsCombinationId.Equals(input.TicketsCombinationId))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TicketsCombinationName),
                x => x.TicketsCombinationName.Contains(input.TicketsCombinationName!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ChannelOrderNo), x => x.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .WhereIF(input.TicketsCombinationPackageId is not null,
                x => x.TicketsCombinationPackageId == input.TicketsCombinationPackageId)
            .WhereIF(!string.IsNullOrEmpty(input.TicketsCombinationPackageName),
                x => x.TicketsCombinationPackageName.Contains(input.TicketsCombinationPackageName!))
            ;

        var combinationOrderStatistics = new CombinationOrderStatistics();

        #region 统计

        var totalData = await ticketsCombinationQuery
            .GroupBy(x => x.OrderStatus)
            .Select(x=> new
            {
                OrderStatus = x.Key,
                Count = x.Count()
            })
            .ToListAsync();
        combinationOrderStatistics.DeliveryFailCount = totalData
            .Where(x => x.OrderStatus == TicketsCombinationOrderStatus.DeliveryFail)
            .Sum(x => x.Count);
        combinationOrderStatistics.SyncFailedFailCount = totalData
            .Where(x => x.OrderStatus == TicketsCombinationOrderStatus.DeliverySyncFailed)
            .Sum(x => x.Count);
        combinationOrderStatistics.WaitingForDeliverCount = totalData
            .Where(x => x.OrderStatus == TicketsCombinationOrderStatus.WaitingForDeliver)
            .Sum(x => x.Count); ;
        combinationOrderStatistics.SuccessCount = totalData
            .Where(x => x.OrderStatus == TicketsCombinationOrderStatus.Success)
            .Sum(x => x.Count);
        combinationOrderStatistics.AbnormalCount = totalData
            .Where(x => x.OrderStatus == TicketsCombinationOrderStatus.ChannelAbnormal)
            .Sum(x => x.Count);
        combinationOrderStatistics.ClosedCount = totalData
            .Where(x => x.OrderStatus == TicketsCombinationOrderStatus.Closed)
            .Sum(x => x.Count);

        #endregion

        var ticketsCombinationOrders = await ticketsCombinationQuery
            .WhereIF(input.OrderStatus.HasValue, x => x.OrderStatus == input.OrderStatus)
            .OrderByDescending(x => x.Id)
            .PagingAsync(input.PageIndex, input.PageSize);

        //查无组合数据返回
        if (ticketsCombinationOrders.Data.Any() is false)
        {
            return new PagingModel<SearchTicketsCombinationOrderOutput, CombinationOrderStatistics>
            {
                PageIndex = input.PageIndex,
                PageSize = input.PageSize,
                Supplement = combinationOrderStatistics
            };
        }

        var pageData = _mapper.Map<PagingModel<SearchTicketsCombinationOrderOutput>>(ticketsCombinationOrders);

        var ticketsCombinationOrderIds = pageData.Data.Select(x => x.Id).ToList();
        //获取对应的门票订单
        var scenicTicketOrders = await _dbContext.ScenicTicketOrders.AsNoTracking()
            .WhereIF(ticketsCombinationOrderIds.Any(),
                x => x.TicketsCombinationOrderId != null &&
                     ticketsCombinationOrderIds.Contains(x.TicketsCombinationOrderId.Value))
            .ToListAsync();
        
        //获取baseOrder信息
        var baseOrderIds = scenicTicketOrders.Select(x => x.BaseOrderId).Distinct().ToArray();
        var baseOrders = await _dbContext.BaseOrders.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.Id))
            .ToListAsync();

        //获取发货结果
        var combinedDeliveryResultMap = await GetCombinedDeliveryResult(scenicTicketOrders);
        
        //查询供货端供货信息补充
        var supplierOrderInfos = await GetSupplierOrderInfos(baseOrderIds);

        foreach (var item in pageData.Data)
        {
            var combinationScenicTicketOrders = scenicTicketOrders
                .Where(x => x.TicketsCombinationOrderId == item.Id).ToList();
            if (combinationScenicTicketOrders.Any() is false)
            {
                //组合异常单渠道信息补充
                if (item.OrderStatus == TicketsCombinationOrderStatus.ChannelAbnormal //异常单
                    || item is {OrderStatus: TicketsCombinationOrderStatus.Closed, AbnormalOrderSourceType: not null})//关闭的异常单
                {
                    item.DistributionChannelInfo = new DistributionChannelInfo
                    {
                        AgencyId = item.AgencyId!.Value,
                        SellingPlatform = item.SellingPlatform!.Value,
                        SellingChannels = item.SellingChannels!.Value,
                        ChannelOrderNo = item.ChannelOrderNo.Split(","),
                        ChannelOrderStatus = ScenicTicketOrderStatus.WaitingForDeliver
                    };
                }
                
                continue;
            }
            var firstCombinationScenicTicketOrder = combinationScenicTicketOrders.First();
            var firstCombinationBaseOrder = baseOrders.First(x => x.Id == firstCombinationScenicTicketOrder.BaseOrderId);
            _ = combinedDeliveryResultMap.TryGetValue(firstCombinationScenicTicketOrder.BaseOrderId,
                    out var firstDeliveryResult);

            item.TravelDate = firstCombinationScenicTicketOrder.TravelDate;
            
            //默认展示第一个门票订单的分销渠道发货信息
            item.DistributionChannelInfo = new DistributionChannelInfo
            {
                AgencyId = firstCombinationBaseOrder.AgencyId,
                AgencyName = firstCombinationBaseOrder.AgencyName,
                SellingPlatform = firstCombinationBaseOrder.SellingPlatform,
                SellingChannels = firstCombinationBaseOrder.SellingChannels,
                ChannelOrderNo = item.ChannelOrderNo?.Split(",",StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries),
                ChannelOrderStatus = firstDeliveryResult.deliveryStatus is ScenicTicketOrderDeliveryStatus.SyncFailed
                        ? ScenicTicketOrderStatus.WaitingForDeliver
                        : firstCombinationScenicTicketOrder.Status
            };

            //处理组合关联门票订单信息
            var scenicTicketOrderInfos = new List<Contracts.Common.Order.DTOs.TicketsCombinationOrder.ScenicTicketOrderInfo>();
            foreach (var combinationScenicTicketOrder in combinationScenicTicketOrders)
            {
                var baseOrder = baseOrders.FirstOrDefault(x => x.Id == combinationScenicTicketOrder.BaseOrderId);
                var supplierOrderInfo = supplierOrderInfos.FirstOrDefault(x => x.BaseOrderId == baseOrder.Id);
                ScenicTicketSupplierOrderStatus? channelOrderStatus = null;
                if (supplierOrderInfo != default)
                {
                    //默认供应端订单状态
                    channelOrderStatus = supplierOrderInfo.OrderStatus;
                }

                var scenicTicketOrderInfo = new Contracts.Common.Order.DTOs.TicketsCombinationOrder.ScenicTicketOrderInfo
                {
                    Id = combinationScenicTicketOrder.Id,
                    ScenicSpotId = combinationScenicTicketOrder.ScenicSpotId,
                    ScenicSpotName = baseOrder.ResourceName,
                    TicketId = combinationScenicTicketOrder.ScenicTicketId,
                    TicketsName = baseOrder.ProductName,
                    TimeSlotId = combinationScenicTicketOrder.TimeSlotId,
                    TimeSlotName = combinationScenicTicketOrder.TimeSlot.HasValue
                    ? combinationScenicTicketOrder.TimeSlot.Value.ToString(_timeSlotFormat)
                    : null,
                    Quantity = combinationScenicTicketOrder.Quantity,
                    BaseOrderStatus = baseOrder.Status,
                    ChannelOrderStatus = channelOrderStatus,
                    BaseOrderId = baseOrder.Id,
                    ValidityBegin = combinationScenicTicketOrder.ValidityBegin,
                    ValidityEnd = combinationScenicTicketOrder.ValidityEnd,
                    SupplierId = combinationScenicTicketOrder.SupplierId,
                    PaymentAmount = baseOrder.PaymentAmount,
                    PaymentCurrencyCode = baseOrder.PaymentCurrencyCode,
                    ContactsName = baseOrder.ContactsName,
                    CredentialSourceType = combinationScenicTicketOrder.CredentialSourceType,
                    Status = combinationScenicTicketOrder.Status,
                    PaymentType = baseOrder.PaymentType
                };
                
                #region 获取发货结果
                var credentialSourceTypes = new[] { CredentialSourceType.PurchasingImport, CredentialSourceType.InterfaceDock,CredentialSourceType.ManualDelivery };
                if (combinationScenicTicketOrder.CredentialSourceType.HasValue && credentialSourceTypes.Contains(combinationScenicTicketOrder.CredentialSourceType.Value))
                {
                    //发货结果
                    if (combinedDeliveryResultMap.TryGetValue(combinationScenicTicketOrder.BaseOrderId, out var deliveryResult))
                    {
                        scenicTicketOrderInfo.DeliveryResult = new DeliveryResult
                        {
                            DeliveryErrorMsg = deliveryResult.msg,
                            DeliverStatus =
                                deliveryResult == default
                                    ? ScenicTicketOrderDeliveryStatus.WaitingForDeliver //组合有延迟.所以默认待发货状态
                                    : deliveryResult.deliveryStatus,
                            DeliveryErrorCode = deliveryResult.errorCode
                        };
                    }
                }
                #endregion
                
                scenicTicketOrderInfos.Add(scenicTicketOrderInfo);

            }

            item.ScenicTicketOrderInfos = scenicTicketOrderInfos;
        }

        return new PagingModel<SearchTicketsCombinationOrderOutput, CombinationOrderStatistics>
        {
            Data = pageData.Data,
            PageIndex = pageData.PageIndex,
            PageSize = pageData.PageSize,
            Total = pageData.Total,
            Supplement = combinationOrderStatistics
        };
    }

    /// <summary>
    /// 获取门票组合订单的发货结果
    /// </summary>
    /// <param name="scenicTicketOrders"></param>
    /// <returns></returns>
    private async Task<Dictionary<long, (ScenicTicketOrderDeliveryStatus deliveryStatus, string msg, int? errorCode)>>
        GetCombinedDeliveryResult(List<ScenicTicketOrder> scenicTicketOrders)
    {
        var purchaseOrderIds = scenicTicketOrders.Where(x => x.CredentialSourceType == CredentialSourceType.PurchasingImport)
            .Select(x => x.BaseOrderId).Distinct().ToArray();
        
        var supplierOrderIds = scenicTicketOrders.Where(x => x.CredentialSourceType == CredentialSourceType.InterfaceDock)
            .Select(x => x.BaseOrderId).Distinct().ToArray();
        
        var manualOrderIds = scenicTicketOrders.Where(x => x.CredentialSourceType == CredentialSourceType.ManualDelivery)
            .Select(x => x.BaseOrderId).Distinct().ToArray();
        
        //获取发货结果
        var purchaseDeliveryResultMap = await _scenicTicketOrderService.GetPurchaseDeliveryResult(purchaseOrderIds);
        var supplierDeliveryResultMap = await _scenicTicketOrderService.GetSupplierDeliveryResult(supplierOrderIds);
        var manualDeliveryResultMap = await _scenicTicketOrderService.GetManualDeliveryResult(manualOrderIds);
        var combinedDeliveryResultMap = purchaseDeliveryResultMap
            .Concat(supplierDeliveryResultMap)
            .Concat(manualDeliveryResultMap)
            .ToDictionary(x => x.Key, x => x.Value);

        return combinedDeliveryResultMap;
    }

    /// <summary>
    /// 查询供应端订单信息
    /// </summary>
    /// <param name="baseOrderIds"></param>
    /// <returns></returns>
    private async Task<List<(long BaseOrderId, ScenicTicketSupplierOrderStatus OrderStatus)>>
        GetSupplierOrderInfos(params long[] baseOrderIds)
    {
        var supplierOrderInfos = new List<(long BaseOrderId, ScenicTicketSupplierOrderStatus OrderStatus)>();
        if(baseOrderIds.Any() is false) return supplierOrderInfos;
        
        var supplierOrders = await _dbContext.ScenicTicketSupplierOrders.AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .Select(x => new ValueTuple<long, ScenicTicketSupplierOrderStatus>(x.BaseOrderId, x.OrderStatus))
            .ToListAsync();
        supplierOrderInfos.AddRange(supplierOrders);
        
        //兼容旧客路
        var supplierBaseOrderIds = supplierOrders.Select(x => x.Item1).Distinct().ToList();
        var klookBaseOrderIds = baseOrderIds.Except(supplierBaseOrderIds).ToList();
        if (klookBaseOrderIds.Any())
        {
            var klookOrders = await _dbContext.ScenicTicketKlookOrders.AsNoTracking()
                .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                .Select(x => new ValueTuple<long, ScenicTicketSupplierOrderStatus>(x.BaseOrderId, x.OrderStatus))
                .ToListAsync();
            supplierOrderInfos.AddRange(klookOrders);
        }
        return supplierOrderInfos;
    }

    [UnitOfWork]
    public async Task AddAbnormalOrder(AddAbnormalCombinationOrderInput input)
    {
        var abnormalOrder = await _dbContext.TicketsCombinationOrders
            .FirstOrDefaultAsync(x => x.ChannelOrderNo == input.ChannelOrderNo);

        //不存在
        if (abnormalOrder == null)
        {
            abnormalOrder = new TicketsCombinationOrder
            {
                Quantity = input.Quantity,
                TicketsCombinationId = input.TicketsCombinationId,
                TicketsCombinationName = input.TicketsCombinationName,
                TicketsCombinationPackageId = input.TicketsCombinationPackageId,
                TicketsCombinationPackageName = input.TicketsCombinationPackageName,
                PaymentAmount = input.PaymentAmount,
                PaymentCurrencyCode = input.PaymentCurrencyCode,
                ChannelOrderNo = input.ChannelOrderNo,
                OrderStatus = TicketsCombinationOrderStatus.ChannelAbnormal,
                DataContentJson = input.DataContentJson,
                AbnormalOrderSourceType = AbnormalOrderSourceType.OpenChannelFail,
                AbnormalReason = input.AbnormalReason,
                SellingPlatform = input.SellingPlatform,
                SellingChannels = input.SellingChannels,
                AgencyId = input.AgencyId,
                TravelDate = input.TravelDate,
                CombinationType = input.CombinationType
            };
            await _dbContext.AddAsync(abnormalOrder);
        }
        else
        {
            if (abnormalOrder.OrderStatus != TicketsCombinationOrderStatus.ChannelAbnormal)
            {
                return;
            }
            
            abnormalOrder.AbnormalReason = input.AbnormalReason;
            abnormalOrder.UpdateTime = DateTime.Now;
        }
    }
    
    [UnitOfWork]
    public async Task UpdateAbnormalOrder(UpdateAbnormalCombinationOrderInput input)
    {
        var abnormalOrder = await _dbContext.TicketsCombinationOrders
            .FirstOrDefaultAsync(x => x.Id == input.Id);
        
        abnormalOrder.AbnormalReason = input.AbnormalReason;
        abnormalOrder.UpdateTime = DateTime.Now;
    }
    
    [UnitOfWork]
    public async Task CloseAbnormalOrder(CloseAbnormalCombinationOrderInput input)
    {
        var abnormalOrder = await _dbContext.TicketsCombinationOrders
            .FirstOrDefaultAsync(x => x.Id == input.Id);
        
        if (abnormalOrder.OrderStatus != TicketsCombinationOrderStatus.ChannelAbnormal)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        
        abnormalOrder.OrderStatus = TicketsCombinationOrderStatus.Closed;
        abnormalOrder.ClosedTime = DateTime.Now;
        abnormalOrder.UpdateTime = abnormalOrder.ClosedTime;
    }

    [UnitOfWork]
    public async Task ReplaceTicketCombination(ReplaceTicketCombinationInput input)
    {
        var abnormalOrder = await _dbContext.TicketsCombinationOrders
            .FirstOrDefaultAsync(x => x.Id == input.Id);

        if (abnormalOrder.OrderStatus != TicketsCombinationOrderStatus.ChannelAbnormal)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        
        abnormalOrder.TicketsCombinationId = input.TicketsCombinationId;
        abnormalOrder.TicketsCombinationName = input.TicketsCombinationName;
        abnormalOrder.TicketsCombinationPackageId = input.TicketsCombinationPackageId;
        abnormalOrder.TicketsCombinationPackageName = input.TicketsCombinationPackageName;
        abnormalOrder.UpdateTime = DateTime.Now;
    }

    /// <summary>
    /// 查询组合异常订单详情
    /// </summary>
    /// <remarks>不包括关联的子单信息</remarks>
    /// <param name="orderId"></param>
    /// <returns></returns>
    public async Task<GetAbnormalCombinationOrderDetailOutPut> GetAbnormalOrderDetail(long orderId)
    {
        var abnormalOrder = await _dbContext.TicketsCombinationOrders
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == orderId);
        
        if(abnormalOrder == null) return null;

        return new GetAbnormalCombinationOrderDetailOutPut
        {
            Id = abnormalOrder.Id,
            Quantity = abnormalOrder.Quantity,
            TicketsCombinationId = abnormalOrder.TicketsCombinationId,
            TicketsCombinationName = abnormalOrder.TicketsCombinationName,
            TicketsCombinationPackageId = abnormalOrder.TicketsCombinationPackageId,
            TicketsCombinationPackageName = abnormalOrder.TicketsCombinationPackageName,
            PaymentAmount = abnormalOrder.PaymentAmount,
            PaymentCurrencyCode = abnormalOrder.PaymentCurrencyCode,
            ChannelOrderNo = abnormalOrder.ChannelOrderNo,
            OrderStatus = abnormalOrder.OrderStatus,
            DataContentJson = abnormalOrder.DataContentJson,
            AbnormalOrderSourceType = abnormalOrder.AbnormalOrderSourceType,
            AbnormalReason = abnormalOrder.AbnormalReason,
            SellingPlatform = abnormalOrder.SellingPlatform!.Value,
            SellingChannels = abnormalOrder.SellingChannels!.Value,
            AgencyId = abnormalOrder.AgencyId!.Value,
            TravelDate = abnormalOrder.TravelDate ?? DateTime.Today,
            CombinationType = abnormalOrder.CombinationType
        };
    }
    
    [UnitOfWork]
    public async Task ReplaceTicketsOrder(ReplaceTicketsOrderInput input)
    {
        var oldScenicTicketOrders = await _dbContext.ScenicTicketOrders
                                        .FirstOrDefaultAsync(x => x.Id.Equals(input.OldTicketsOrderId))
                                    ?? throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        var newScenicTicketOrders = await _dbContext.ScenicTicketOrders
                                        .FirstOrDefaultAsync(x => x.Id.Equals(input.NewTicketsOrderId))
                                    ?? throw new BusinessException(ErrorTypes.Order.OrderNotFind);

        //替换订单需要是非系统生成的产品类型
        var credentialSourceTypes = new[]
        {
            CredentialSourceType.PurchasingImport, CredentialSourceType.InterfaceDock,
            CredentialSourceType.ManualDelivery
        };
        if (!(newScenicTicketOrders.CredentialSourceType != null &&
              credentialSourceTypes.Contains(newScenicTicketOrders.CredentialSourceType.Value)))
        {
            throw new BusinessException(ErrorTypes.Order.NotSupportOrderReplace);
        }

        oldScenicTicketOrders.TicketsCombinationOrderId = null;
        newScenicTicketOrders.TicketsCombinationOrderId = input.TicketsCombinationOrderId;

        //定时同步OTA
        _backgroundJobClient.Schedule<HangfireClient.Jobs.IOrderJob>(
            s => s.ScenicTicketCombinationOrderDelivery(input.TicketsCombinationOrderId,
                newScenicTicketOrders.TenantId),
            TimeSpan.FromSeconds(3));
    }
    
    public async Task<bool> PaymentAmountCheck(long orderId)
    {
        //查询组合单信息
        var combinationOrder = await _dbContext.TicketsCombinationOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == orderId);
        
        //查询组合单关联的门票信息
        var combinationBaseOrderIds = await _dbContext.ScenicTicketOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => x.TicketsCombinationOrderId == orderId)
            .Select(x=> x.BaseOrderId)
            .ToListAsync();
        
        var baseOrders = await _dbContext.BaseOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => combinationBaseOrderIds.Contains(x.Id))
            .ToListAsync();
        
        //查询未完成订单
        var normalBaseOrders = baseOrders
            .Where(x => x.Status == BaseOrderStatus.UnFinished)
            .ToList();
        
        //查询退款订单(部分退款的订单金额需要处理)
        var closeBaseOrders = baseOrders
            .Where(x => x.Status == BaseOrderStatus.Closed)
            .Select(x => x)
            .ToList();
        var closeBaseOrderIds = closeBaseOrders.Select(x => x.Id).ToList();
        var refundOrders = await _dbContext.RefundOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => closeBaseOrderIds.Contains(x.BaseOrderId) && x.Status == RefundOrderStatus.Refunded)
            .ToListAsync();

        var closeOrderTotalPaymentAmount = closeBaseOrders.Sum(x => x.PaymentAmount);//关闭订单的总金额
        var totalRefundAmount = refundOrders.Sum(x => x.TotalAmount);//退款总金额
        var diffRefundAmount = closeOrderTotalPaymentAmount - totalRefundAmount;//部分退款差值
        
        
        //查询抵冲单
        var normalBaseOrderIds = normalBaseOrders.Select(x => x.Id).ToList();
        var offsetOrders = await _dbContext.OffsetOrders
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => normalBaseOrderIds.Contains(x.BaseOrderId))
            .Where(x => x.OffsetType == OffsetOrderType.Receipt)
            .ToListAsync();

        //金额计算 (子单支付金额 +抵冲金额 = 渠道单金额）)
        var channelPaymentAmount = combinationOrder.PaymentAmount; //渠道金额
        var orderPaymentAmount = normalBaseOrders.Sum(x => x.PaymentAmount); //子单支付金额 
        var offsetPaymentAmount = offsetOrders.Sum(x => x.Amount); //抵冲金额
        return channelPaymentAmount == (orderPaymentAmount + offsetPaymentAmount + diffRefundAmount); //子单支付金额 + 抵冲金额 + 部分退款差值
    }
    
    #region 组合订单发货

    public async Task<CombinationOrderDeliveryOutput> Delivery(CombinationOrderDeliveryInput input)
    {
        var result = new CombinationOrderDeliveryOutput();
        var combinationOrderPurchaseVoucherIds = new List<long>();
        var combinationBaseOrderIds = new List<long>();
        var combinationKlookOrderSaasIds = new List<long>();
        var combinationSupplierSaasOrder = new List<(long baseOrderId, long supplierOrderSaasId)>();
        var combinationScenicOrders = new List<ScenicTicketOrder>();

        var lockKey = string.Format(_lockKey, input.TicketsCombinationOrderId);
        var lockSecret = Guid.NewGuid().ToString();

        try
        {
            await _redisClient.LockTakeWaitingAsync(lockKey, lockSecret, TimeSpan.FromSeconds(10));

            //查询组合订单-待发货门票订单
            combinationScenicOrders = await _dbContext.ScenicTicketOrders
                .IgnoreQueryFilters()
                .Where(x => x.TicketsCombinationOrderId == input.TicketsCombinationOrderId)
                .ToListAsync();
            
            //查出已关闭的订单
            var relatedBaseOrderIds = combinationScenicOrders.Select(x => x.BaseOrderId).ToList();
            var closedBaseOrderIds = await _dbContext.BaseOrders.AsNoTracking().IgnoreQueryFilters()
                .Where(x => relatedBaseOrderIds.Contains(x.Id) && x.Status == BaseOrderStatus.Closed)
                .Select(x => x.Id)
                .ToListAsync();
            //过滤掉已经关闭的门票订单
            combinationScenicOrders = combinationScenicOrders
                .Where(x => !closedBaseOrderIds.Contains(x.BaseOrderId))
                .ToList();
            
            if (combinationScenicOrders.Any() is false)
            {
                throw new BusinessException("查无组合订单");
            }

            //存在非未发货状态的订单
            if (combinationScenicOrders.Any(x => x.Status != ScenicTicketOrderStatus.WaitingForDeliver))
            {
                throw new BusinessException(ErrorTypes.Order.OTAOrderDelivered);
            }

            var tenantId = combinationScenicOrders.FirstOrDefault().TenantId;
            var checkCombination = await CheckCombinationOrder(combinationScenicOrders);
            if (checkCombination.isAllCheckPass)
            {
                _logger.LogInformation("[OTA]门票组合订单发货,{@Input},{@Message}", checkCombination, "同步中");

                //有凭证.同步OTA
                combinationOrderPurchaseVoucherIds.AddRange(checkCombination.purchaseVoucherIds);
                combinationBaseOrderIds.AddRange(checkCombination.baseOrderIds);
                combinationKlookOrderSaasIds.AddRange(checkCombination.klookOrderSaasIds);
                combinationSupplierSaasOrder.AddRange(checkCombination.supplierSassOrders);

                //采购导入订单
                var purchaseScenicOrders = combinationScenicOrders
                    .Where(x => x.CredentialSourceType == CredentialSourceType.PurchasingImport)
                    .ToList();

                //判断凭证数量是否少于订单数(忽略手工发货订单和供应端的虚拟凭证订单)
                var ignoreMissingVoucherOrderCount = combinationScenicOrders.Count - checkCombination.missingVoucherOrderIds.Count;
                if (checkCombination.vouchers.Count < ignoreMissingVoucherOrderCount)
                {
                    throw new BusinessException("凭证数量少于订单数");
                }

                //查询关联主订单
                var combinationBaseOrders = await _dbContext.BaseOrders
                    .IgnoreQueryFilters()
                    .Where(x => combinationBaseOrderIds.Contains(x.Id))
                    .ToListAsync();
                //校验主订单信息
                if (combinationBaseOrders.Any(x => x.Status != BaseOrderStatus.UnFinished))
                    throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);


                #region 订单发送邮件

                foreach (var item in combinationScenicOrders)
                {
                    var orderVouchers = checkCombination.vouchers.Where(x => x.BaseOrderId == item.BaseOrderId)
                        .ToList();

                    if (orderVouchers.Any())
                    {
                        //发送邮件
                        await _scenicTicketOrderMessageService.SendMessageAsync(new ScenicTicketOrderSendMessageInput
                        {
                            BaseOrderId = item.BaseOrderId,
                            ScenicTicketOrderId = item.Id,
                            Vouchers = orderVouchers.Select(x => new ScenicTicketOrderSendMessageVouchers
                            {
                                FilePath = x.FilePath,
                                ThumbnailPath = x.ThumbnailPath
                            }).ToList()
                        });
                    }
                }

                #endregion

                #region pdf链接加签

                var voucherBag = new ConcurrentBag<SupplierOrderVoucherItem>();
                if (checkCombination.vouchers.Any())
                {
                    var parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = 3 };
                    Parallel.ForEach(checkCombination.vouchers, parallelOptions, path =>
                    {
                        if (!string.IsNullOrEmpty(path.FilePath))
                        {
                            var voucherBagItem = new SupplierOrderVoucherItem();
                            voucherBagItem.FilePath = _orderOssService.SetObjectAcl(path.FilePath);
                            voucherBagItem.PdfSourcePath = path.PdfSourcePath;
                            voucherBagItem.ImageSourcePath = path.ImageSourcePath;
                            voucherBagItem.ThumbnailPath = path.ThumbnailPath;
                            
                            // acl
                            if (!string.IsNullOrEmpty(path.ImageSourcePath))
                            {
                                voucherBagItem.ImageSourcePath = _orderOssService.SetObjectAcl(path.ImageSourcePath);
                            }

                            if (!string.IsNullOrEmpty(path.PdfSourcePath))
                            {
                                voucherBagItem.PdfSourcePath = _orderOssService.SetObjectAcl(path.PdfSourcePath);
                            }

                            voucherBag.Add(voucherBagItem);
                        }
                    });
                    if (voucherBag.Any() is false || voucherBag.Count != checkCombination.vouchers.Count)
                    {
                        _logger.LogWarning("[OTA]门票组合订单发货,{@Input},{@Message}", input, "pdf链接配置访问权限失败");
                        result.Message = "pdf链接配置访问权限失败";
                        return result;
                    }
                }

                #endregion

                #region OTA发货

                //OTA发货
                var sellingPlatform = combinationBaseOrders.First().SellingPlatform;
                var agencyId = combinationBaseOrders.First().AgencyId;
                var channelOrderNoList = new List<string>();
                foreach (var baseOrder in combinationBaseOrders)
                {
                    var baseOrderChannelOrderNoList = baseOrder.ChannelOrderNo.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                    channelOrderNoList.AddRange(baseOrderChannelOrderNoList);
                }
                channelOrderNoList = channelOrderNoList.Distinct().ToList();

                var otaPlatform = _scenicTicketOtaService.OtaSellingPlatforms;
                var otaSellingChannels = _scenicTicketOtaService.OtaSellingChannels;
                var systemOtaChannelType = new OtaChannelType();
                if (sellingPlatform == SellingPlatform.System)
                {
                    var agency = await _agencyService.GetAgencyDetail(agencyId);
                    var systemPlatformOrderCheck = _openPlatformBaseService.CheckSystemPlatformOrder(agency.AgencyType,
                        agency.AgencyApiType,
                        otaPlatform);
                    otaPlatform = systemPlatformOrderCheck.otaPlatform;
                    systemOtaChannelType = systemPlatformOrderCheck.otaChannelType;
                }

                ScenicTicketOTAOrderDeliveryOutput? otaDeliveryResult = new();
                if (checkCombination.vouchers.Any())
                {
                    var otaDeliveryRequest = new ScenicTicketOTAOrderDeliveryInput
                    {
                        OtaOrderIds = channelOrderNoList,
                        Vouchers = voucherBag.ToList(),
                        SellingPlatform = sellingPlatform,
                        SystemOtaChannelType = systemOtaChannelType,
                        TenantId = tenantId
                    };
                    otaDeliveryResult = await _scenicTicketOtaService.Delivery(otaDeliveryRequest);
                }
                else
                {
                    otaDeliveryResult.Code = 200;
                }
                var otaDeliveryIsSuccess = otaDeliveryResult.Code == 200;
                if (otaDeliveryIsSuccess)
                {
                    //ota组合订单同步成功,需要更新组合订单的采购产品的凭证使用状态
                    if (combinationOrderPurchaseVoucherIds.Any())
                    {
                        var combinationPurchaseVouchers = await _dbContext.ScenicTicketPurchaseVouchers
                            .IgnoreQueryFilters()
                            .Where(x => combinationOrderPurchaseVoucherIds.Contains(x.Id))
                            .ToListAsync();
                        foreach (var voucher in combinationPurchaseVouchers)
                        {
                            voucher.Status = ScenicTicketVoucherStatus.Used;
                            voucher.UsedTime = DateTime.Now;
                            voucher.UpdateTime = DateTime.Now;
                        }
                    }

                    //ota组合订单同步成功.需要更新组合订单的主订单状态
                    foreach (var combinationBaseOrder in combinationBaseOrders)
                    {
                        /*
                         * 1. 手工发货产品不需要推进到已完成状态.进入已确认状态即可(主订单未完成,子订单已发货)
                         */
                        var scenicTicketOrder =
                            combinationScenicOrders.FirstOrDefault(x => x.BaseOrderId == combinationBaseOrder.Id);

                        if (scenicTicketOrder.CredentialSourceType != CredentialSourceType.ManualDelivery)
                        {
                            //成功-订单自动完成(修改主订单状态)
                            combinationBaseOrder.Status = BaseOrderStatus.Finished;
                            combinationBaseOrder.UpdateTime = DateTime.Now;

                            //完成订单日志
                            var combinationOrderLog = new OrderLogs
                            {
                                OrderId = combinationBaseOrder.Id,
                                OperationType = OrderOperationType.Finished,
                                OrderLogType = OrderLogType.ScenicTicket,
                            };
                            combinationOrderLog.SetTenantId(combinationBaseOrder.TenantId);
                            await _dbContext.AddAsync(combinationOrderLog);
                        }
                    }

                }

                #region 更新ScenicOrder状态.不关注OTA同步结果

                foreach (var item in combinationScenicOrders)
                {
                    //门票子订单状态更新为已发货
                    item.Status = ScenicTicketOrderStatus.Delivered;
                }

                #endregion

                #region 采购单发货结果日志.不关注OTA同步结果

                foreach (var order in purchaseScenicOrders)
                {
                    var deliveryRecord = new ScenicTicketPurchaseDeliveryRecord
                    {
                        BaseOrderId = order.BaseOrderId,
                        SubOrderId = order.Id,
                        Status = otaDeliveryIsSuccess
                            ? OrderVoucherDeliverStatus.Success
                            : OrderVoucherDeliverStatus.SyncFailed
                    };
                    deliveryRecord.SetTenantId(order.TenantId);
                    await _dbContext.AddAsync(deliveryRecord);
                }

                #endregion

                #region 接口对接/手工发货 单发货结果日志.不关注OTA同步结果

                foreach (var supplierOrderSaasId in combinationKlookOrderSaasIds)
                {
                    #region 兼容旧klook

                    var saasKlookOrderDeliveryRecord = new ScenicTicketKlookOrderRecord
                    {
                        ScenicTicketKlookOrderId = supplierOrderSaasId,
                        RecordType = ScenicTicketSupplierOrderRecordType.SyncDelivery,
                        IsSuccess = otaDeliveryIsSuccess,
                        ErrorMsg = otaDeliveryResult.Msg
                    };
                    saasKlookOrderDeliveryRecord.SetTenantId(tenantId);
                    await _dbContext.AddAsync(saasKlookOrderDeliveryRecord);

                    #endregion
                }

                foreach (var item in combinationSupplierSaasOrder)
                {
                    var supplierOrderDeliveryRecord = new ScenicTicketSupplierOrderRecord()
                    {
                        ScenicTicketSupplierOrderId = item.supplierOrderSaasId,
                        BaseOrderId = item.baseOrderId,
                        RecordType = ScenicTicketSupplierOrderRecordType.SyncDelivery,
                        IsSuccess = otaDeliveryIsSuccess,
                        ErrorMsg = otaDeliveryResult.Msg
                    };
                    supplierOrderDeliveryRecord.SetTenantId(tenantId);
                    await _dbContext.AddAsync(supplierOrderDeliveryRecord);
                }

                #endregion

                #region 渠道插旗更改

                if (systemOtaChannelType == OtaChannelType.AliTrip || sellingPlatform == SellingPlatform.Fliggy)
                {
                    //Saas订单状态为已确认/完成，提示飞猪渠道插旗为红色
                    await _capPublisher.PublishAsync(CapTopics.Order.ChannelOrderFlagModify, new ChannelOrderFlagModifyMessage
                    {
                        ChannelOrderNo = string.Join(',',channelOrderNoList),
                        SellingPlatform = SellingPlatform.Fliggy,
                        FlagType = OpenChannelOrderFlagType.Red,
                        AgencyId = null,
                        TenantId = tenantId
                    });

                }
                #endregion
                
                result.Message = otaDeliveryResult.Msg;
                result.IsSuccess = otaDeliveryIsSuccess;

                #endregion

                await _dbContext.SaveChangesAsync();
            }
            else
            {
                throw new BusinessException("组合订单查无可同步凭证");
            }
        }
        catch (BusinessException businessException)
        {
            _logger.LogWarning("[OTA]门票组合订单发货,{@Input},{@Message}", input, businessException.Message);
            result.Message = businessException.Message;
            result.IsSuccess = false;
        }
        catch (Exception e)
        {
            _logger.LogError("[OTA]门票组合订单发货,{@Input},{@Message}", input, e);
            result.Message = "发货异常";
            result.IsSuccess = false;
        }
        finally
        {
            //更新组合订单状态
            await UpdateTicketCombinationOrderStatus(input.TicketsCombinationOrderId, combinationScenicOrders);
            await _redisClient.LockReleaseAsync(lockKey, lockSecret);
        }

        return result;
    }

    private async Task<(
        List<CheckCombinationOrderVoucherDto> vouchers,
        List<long> purchaseVoucherIds,
        List<long> klookOrderSaasIds,
        List<(long baseOrderId, long supplierOrderSaasId)> supplierSassOrders,
        List<long> baseOrderIds,
        List<long> missingVoucherOrderIds,
        bool isAllCheckPass)
    > CheckCombinationOrder(List<ScenicTicketOrder> combinationScenicOrders)
    {
        //组合套餐的订单需要判断所有组合的订单的状态
        var vouchers = new List<CheckCombinationOrderVoucherDto>();
        var purchaseVoucherIds = new List<long>();
        var baseOrderIds = new List<long>();
        var klookOrderSaasIds = new List<long>();
        var supplierSassOrders = new List<(long baseOrderId, long supplierOrderSaasId)>();
        var missingVoucherOrderIds = new List<long>();

        //采购门票订单
        var purchaseScenicOrders = combinationScenicOrders
            .Where(x => x.CredentialSourceType == CredentialSourceType.PurchasingImport)
            .ToList();
        if (purchaseScenicOrders.Any())
        {
            var purchaseBaseOrderIds = purchaseScenicOrders.Select(x => x.BaseOrderId).ToList();

            //只有全都是InCombination状态的订单才可以执行同步OTA
            var records = await _dbContext.ScenicTicketPurchaseDeliveryRecords
                .IgnoreQueryFilters()
                .Where(x => purchaseBaseOrderIds.Contains(x.BaseOrderId))
                .ToListAsync();
            if (purchaseBaseOrderIds.Select(item => records
                    .Where(x => x.BaseOrderId == item)
                    .MaxBy(x => x.Id))
                .Any(itemRecord => itemRecord.Status != OrderVoucherDeliverStatus.InCombination))
            {
                return (new(), new(), new(), new(), new(), new(), false);
            }

            var frozenQuery = from voucher in _dbContext.ScenicTicketPurchaseVouchers.AsQueryable()
                              join detail in _dbContext.ScenicTicketPurchaseVoucherUsedDetails on voucher.Id equals detail
                                  .TicketVoucherId
                              where voucher.Status == ScenicTicketVoucherStatus.Frozen
                                    && purchaseBaseOrderIds.Contains(detail.BaseOrderId)
                              select new { detail, voucher };

            var purchaseOrders = await frozenQuery
                .IgnoreQueryFilters()
                .ToListAsync();

            var purchaseVouchers = purchaseOrders.Select(x =>
                new CheckCombinationOrderVoucherDto
                {
                    BaseOrderId = x.detail.BaseOrderId,
                    PurchaseOrderId = x.voucher.PurchaseOrderId,
                    FilePath = x.voucher.FilePath,
                    ThumbnailPath = x.voucher.Thumbnail,
                    ImageSourcePath = x.voucher.CompositeImagePath,
                    CredentialSourceType = CredentialSourceType.PurchasingImport
                })
                .ToList();
            vouchers.AddRange(purchaseVouchers);
            purchaseVoucherIds.AddRange(purchaseOrders.Select(x => x.voucher.Id));
            baseOrderIds.AddRange(purchaseBaseOrderIds);
        }

        //供货端门票订单
        var supplierScenicOrders = combinationScenicOrders
            .Where(x => x.CredentialSourceType == CredentialSourceType.InterfaceDock)
            .ToList();

        if (supplierScenicOrders.Any())
        {
            var supplierBaseOrderIds = supplierScenicOrders.Select(x => x.BaseOrderId).ToList();

            #region 查询供货端数据

            var orderAndVouchers = new List<CombinationOrderSupplierOrderVouchers>();
            var supplierQuery = from supplierOrder in _dbContext.ScenicTicketSupplierOrders
                                join voucher in _dbContext.ScenicTicketSupplierOrderVouchers on supplierOrder.Id equals voucher.ScenicTicketSupplierOrderId
                                    into temp
                                from voucher in temp.DefaultIfEmpty()
                                where supplierBaseOrderIds.Contains(supplierOrder.BaseOrderId)
                                select new { supplierOrder, voucher };

            var supplierOrderAndVouchers = await supplierQuery.IgnoreQueryFilters().ToListAsync();
            var containsSupplierOrderSaasIds = supplierOrderAndVouchers.Select(x => x.supplierOrder.Id).Distinct().ToList();
            // 由于供应商创单流程存在耗时，所以这里需要校验判断对应的供应商订单数量是否一致
            if (containsSupplierOrderSaasIds.Count != supplierBaseOrderIds.Count)
            {
                return (new(), new(), new(), new(), new(), new(), false);
            }
            
            //处理供应商虚拟凭证订单
            var isVirtualVoucherBaseOrderIds = supplierOrderAndVouchers.Where(x => x.supplierOrder.IsVirtualVoucher).Select(x => x.supplierOrder.BaseOrderId).ToList();
            missingVoucherOrderIds.AddRange(isVirtualVoucherBaseOrderIds);
            
            //非虚拟凭证订单
            var noVirtualVouchers = supplierOrderAndVouchers.Where(x => x.supplierOrder.IsVirtualVoucher == false).ToList();
            if (noVirtualVouchers.Any(x => x.voucher is null))
            {
                return (new(), new(), new(), new(), new(), new(), false);
            }
            orderAndVouchers.AddRange(noVirtualVouchers.Select(item => new CombinationOrderSupplierOrderVouchers
            {
                SupplierOrderSaasId = item.supplierOrder.Id,
                BaseOrderId = item.supplierOrder.BaseOrderId,
                OrderStatus = item.supplierOrder.OrderStatus,
                VoucherItem = new CombinationOrderSupplierOrderVoucherItem
                {
                    VoucherId = item.voucher.Id,
                    Thumbnail = item.voucher.Thumbnail,
                    FilePath = item.voucher.FilePath,
                    ImageSourcePath = item.voucher.ImageSourcePath,
                    PdfSourcePath = item.voucher.PdfSourcePath
                }
            }));
            
            //记录saas订单信息
            supplierSassOrders.AddRange(supplierOrderAndVouchers
                .Select(x => (x.supplierOrder.BaseOrderId, x.supplierOrder.Id))
                .ToList());

            #endregion

            //需要所有订单都是已发货.才可以同步
            if (orderAndVouchers.Any(x => x.OrderStatus != ScenicTicketSupplierOrderStatus.Delivered))
            {
                return (new(), new(), new(), new(), new(), new(), false);
            }

            var supplierVouchers = orderAndVouchers.Select(x =>
                new CheckCombinationOrderVoucherDto
                {
                    BaseOrderId = x.BaseOrderId,
                    PurchaseOrderId = null,
                    FilePath = x.VoucherItem.FilePath,
                    ThumbnailPath = x.VoucherItem.Thumbnail,
                    ImageSourcePath = x.VoucherItem.ImageSourcePath,
                    PdfSourcePath = x.VoucherItem.PdfSourcePath,
                    CredentialSourceType = CredentialSourceType.InterfaceDock
                });
            vouchers.AddRange(supplierVouchers);
            baseOrderIds.AddRange(supplierBaseOrderIds);

            //只有全都是InCombination状态的订单才可以执行同步OTA
            #region 查询供货端订单记录

            var supplierRecordsQuery = _dbContext.ScenicTicketSupplierOrderRecords
                .IgnoreQueryFilters()
                .Where(x => containsSupplierOrderSaasIds.Contains(x.ScenicTicketSupplierOrderId))
                .Select(x => new
                {
                    x.Id,
                    supplierOrderSaasId = x.ScenicTicketSupplierOrderId,
                    x.RecordType
                });

            #endregion

            var records = await supplierRecordsQuery.ToListAsync();
            if (records.Any() is false)
                return (new(), new(), new(), new(), new(), new(), false);

            if (containsSupplierOrderSaasIds.Select(item => records
                    .Where(x => x.supplierOrderSaasId == item)
                    .MaxBy(x => x.Id))
                .Any(itemRecord => itemRecord.RecordType != ScenicTicketSupplierOrderRecordType.InCombination))
            {
                return (new(), new(), new(), new(), new(), new(), false);
            }
        }

        //手工发货门票订单
        var manualOrders =
            combinationScenicOrders.Where(x => x.CredentialSourceType == CredentialSourceType.ManualDelivery).ToList();
        if (manualOrders.Any())
        {
            var manualBaseOrderIds = manualOrders.Select(x => x.BaseOrderId).ToList();

            //查询手工发货单是否已经上传凭证,支持无凭证发货
            var manualVouchers = await _dbContext.ScenicTicketSupplierOrderVouchers.IgnoreQueryFilters()
                .Where(x => manualBaseOrderIds.Contains(x.BaseOrderId))
                .Select(x =>
                    new CheckCombinationOrderVoucherDto
                    {
                        BaseOrderId = x.BaseOrderId,
                        PurchaseOrderId = null,
                        FilePath = x.FilePath,
                        ThumbnailPath = x.Thumbnail,
                        ImageSourcePath = x.ImageSourcePath,
                        PdfSourcePath = x.PdfSourcePath,
                        CredentialSourceType = CredentialSourceType.ManualDelivery
                    })
                .ToListAsync();

            //存在无凭证的手工发货订单
            var missingVoucherManualOrderIds = manualBaseOrderIds
                .Where(x => manualVouchers.All(v => v.BaseOrderId != x))
                .ToList();

            //查询手工发货订单发货日志
            var manualOrderRecords = await _dbContext.ScenicTicketSupplierOrderRecords
                .IgnoreQueryFilters()
                .Where(x => manualBaseOrderIds.Contains(x.BaseOrderId))
                .ToListAsync();

            if (manualOrderRecords.Any() is false)
                return (new(), new(), new(), new(), new(), new(), false);

            if (manualBaseOrderIds.Select(item => manualOrderRecords
                    .Where(x => x.BaseOrderId == item)
                    .MaxBy(x => x.Id))
                .Any(itemRecord => itemRecord.RecordType != ScenicTicketSupplierOrderRecordType.InCombination))
            {
                return (new(), new(), new(), new(), new(), new(), false);
            }

            vouchers.AddRange(manualVouchers);
            baseOrderIds.AddRange(manualBaseOrderIds);
            missingVoucherOrderIds.AddRange(missingVoucherManualOrderIds);
            long defaultSupplierSaasId = 0;
            supplierSassOrders.AddRange(manualBaseOrderIds.Select(x => (x, defaultSupplierSaasId)).ToList());
        }

        return (vouchers, purchaseVoucherIds, klookOrderSaasIds, supplierSassOrders, baseOrderIds, missingVoucherOrderIds, true);
    }

    private async Task UpdateTicketCombinationOrderStatus(long ticketCombinationOrderId,
        List<ScenicTicketOrder> combinationScenicOrders)
    {
        if (combinationScenicOrders.Any() is false) return;

        //查询组合订单
        var ticketsCombinationOrders = await _dbContext.TicketsCombinationOrders
            .IgnoreQueryFilters()
            .Where(x => x.Id == ticketCombinationOrderId)
            .ToListAsync();

        //查询关联订单
        var baseOrderIds = combinationScenicOrders.Select(x => x.BaseOrderId).ToList();
        var baseOrders = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.Id))
            .Select(x => new { x.Id, x.Status })
            .ToListAsync();

        #region 采购导入订单/日志

        var pOrders = combinationScenicOrders
            .Where(x => x.CredentialSourceType == CredentialSourceType.PurchasingImport)
            .ToList();
        var pOrderBaseIds = pOrders.Select(x => x.BaseOrderId).ToList();
        var pRecords = await _dbContext.ScenicTicketPurchaseDeliveryRecords.IgnoreQueryFilters()
            .Where(x => pOrderBaseIds.Contains(x.BaseOrderId))
            .ToListAsync();

        #endregion

        #region 接口对接订单/日志

        var iOrders = combinationScenicOrders.Where(x => x.CredentialSourceType == CredentialSourceType.InterfaceDock)
            .ToList();
        var iBaseOrderIds = iOrders.Select(x => x.BaseOrderId).ToList();
        var kRecordsQuery = from klookOrder in _dbContext.ScenicTicketKlookOrders.AsQueryable()
                            join record in _dbContext.ScenicTicketKlookOrderRecords.AsQueryable() on klookOrder.Id equals record
                                .ScenicTicketKlookOrderId
                            where iBaseOrderIds.Contains(klookOrder.BaseOrderId)
                            select new
                            {
                                recordId = record.Id,
                                record.IsSuccess,
                                record.RecordType,
                                record.ErrorMsg,
                                record.ErrorCode,
                                klookOrder.BaseOrderId,
                                klookOrder.OrderStatus
                            };
        var kRecords = await kRecordsQuery.IgnoreQueryFilters().ToListAsync();

        var sRecordsQuery = from supplierOrder in _dbContext.ScenicTicketSupplierOrders.AsQueryable()
                            join record in _dbContext.ScenicTicketSupplierOrderRecords.AsQueryable() on supplierOrder.Id equals record
                                .ScenicTicketSupplierOrderId
                            where iBaseOrderIds.Contains(supplierOrder.BaseOrderId)
                            select new
                            {
                                recordId = record.Id,
                                record.IsSuccess,
                                record.RecordType,
                                record.ErrorMsg,
                                record.ErrorCode,
                                supplierOrder.BaseOrderId,
                                supplierOrder.OrderStatus
                            };
        var sRecords = await sRecordsQuery.IgnoreQueryFilters().ToListAsync();

        #endregion

        #region 手工发货订单/日志

        var mOrders = combinationScenicOrders
            .Where(x => x.CredentialSourceType == CredentialSourceType.ManualDelivery)
            .ToList();
        var mOrderBaseIds = mOrders.Select(x => x.BaseOrderId).ToList();
        var mRecords = await _dbContext.ScenicTicketSupplierOrderRecords.IgnoreQueryFilters()
            .Where(x => mOrderBaseIds.Contains(x.BaseOrderId))
            .ToListAsync();


        #endregion

        foreach (var combinationOrder in ticketsCombinationOrders)
        {
            //相关的订单
            var relatedOrders = combinationScenicOrders.Where(x => x.TicketsCombinationOrderId == combinationOrder.Id);
            //相关订单发货状态
            var relatedDeliveryStatus = new List<ScenicTicketOrderDeliveryStatus>();
            foreach (var relatedOrder in relatedOrders)
            {
                //判断主订单是否已完成
                var relateBaseOrder = baseOrders.FirstOrDefault(x => x.Id == relatedOrder.BaseOrderId);
                if (relateBaseOrder.Status == BaseOrderStatus.Finished)
                {
                    relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.Success);
                    continue;
                }

                switch (relatedOrder.CredentialSourceType)
                {
                    case CredentialSourceType.PurchasingImport:
                        //采购导入数据
                        var lastRecord = pRecords.Where(x => x.BaseOrderId == relatedOrder.BaseOrderId)
                            .MaxBy(x => x.Id);

                        if (lastRecord is null)
                        {
                            relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.WaitingForDeliver);
                            continue;
                        }

                        switch (lastRecord.Status)
                        {
                            case OrderVoucherDeliverStatus.InventoryNotEnough:

                                //1、若产品凭证来源=采购导入，若库存不足或其他原因导致的无法发货显示发货失败
                                relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.DeliveryFail);

                                break;
                            case OrderVoucherDeliverStatus.SyncFailed:

                                //2.同步API对接的分销商时，同步失败显示
                                relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.SyncFailed);

                                break;
                            case OrderVoucherDeliverStatus.InCombination:

                                //组合套餐等待发货中
                                relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.WaitingForDeliver);

                                break;
                        }
                        break;
                    case CredentialSourceType.InterfaceDock:
                        var iLastRecord = kRecords.Where(x => x.BaseOrderId == relatedOrder.BaseOrderId)
                                              .MaxBy(x => x.recordId)
                                          ?? sRecords.Where(x => x.BaseOrderId == relatedOrder.BaseOrderId)
                                              .MaxBy(x => x.recordId);

                        if (iLastRecord is null)
                        {
                            relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.WaitingForDeliver);
                            continue;
                        }

                        switch (iLastRecord.OrderStatus)
                        {
                            case ScenicTicketSupplierOrderStatus.WaitingForDeliver:

                                //1.发货中
                                relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.WaitingForDeliver);

                                break;
                            case ScenicTicketSupplierOrderStatus.CreateFail:
                            case ScenicTicketSupplierOrderStatus.WaitingForPay:
                            case ScenicTicketSupplierOrderStatus.Refunded:
                            case ScenicTicketSupplierOrderStatus.DeliveryFail:
                            case ScenicTicketSupplierOrderStatus.Purchasing:
                            case ScenicTicketSupplierOrderStatus.PayProcessing:

                                //支付失败.订单退款,发货失败
                                relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.DeliveryFail);

                                break;
                            case ScenicTicketSupplierOrderStatus.Delivered:

                                if (iLastRecord.IsSuccess)
                                {
                                    //发货成功
                                    switch (iLastRecord.RecordType)
                                    {
                                        case ScenicTicketSupplierOrderRecordType.InCombination:
                                            relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.WaitingForDeliver);
                                            break;
                                        default:
                                            relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.Success);
                                            break;
                                    }
                                }
                                else
                                {
                                    //2.发货失败
                                    switch (iLastRecord.RecordType)
                                    {
                                        //同步API对接的分销商时，同步失败显示
                                        case ScenicTicketSupplierOrderRecordType.SyncDelivery:
                                            relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.SyncFailed);

                                            break;
                                        default:

                                            //支付失败.订单退款,开放平台同步凭证失败,Saas处理图片失败===>发货失败,
                                            relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.DeliveryFail);

                                            break;
                                    }
                                }

                                break;
                        }
                        break;
                    case CredentialSourceType.ManualDelivery:

                        //手工发货
                        var mLastRecord = mRecords.Where(x => x.BaseOrderId == relatedOrder.BaseOrderId)
                            .MaxBy(x => x.Id);

                        if (mLastRecord is null)
                        {
                            relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.WaitingForDeliver);
                            continue;
                        }

                        if (mLastRecord.IsSuccess)
                        {
                            //发货成功
                            switch (mLastRecord.RecordType)
                            {
                                case ScenicTicketSupplierOrderRecordType.InCombination:

                                    relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.WaitingForDeliver);

                                    break;
                                default:

                                    relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.Success);

                                    break;
                            }
                        }
                        else
                        {
                            //2.发货失败
                            switch (mLastRecord.RecordType)
                            {
                                //同步API对接的分销商时，同步失败显示
                                case ScenicTicketSupplierOrderRecordType.SyncDelivery:

                                    relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.SyncFailed);

                                    break;
                                default:

                                    //支付失败.订单退款,开放平台同步凭证失败,Saas处理图片失败===>发货失败,
                                    relatedDeliveryStatus.Add(ScenicTicketOrderDeliveryStatus.DeliveryFail);

                                    break;
                            }
                        }

                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }

            if (relatedDeliveryStatus.Any(x => x == ScenicTicketOrderDeliveryStatus.SyncFailed))
            {
                combinationOrder.OrderStatus = TicketsCombinationOrderStatus.DeliverySyncFailed;
                combinationOrder.UpdateTime = DateTime.Now;
                continue;
            }

            if (relatedDeliveryStatus.Any(x => x == ScenicTicketOrderDeliveryStatus.DeliveryFail))
            {
                combinationOrder.OrderStatus = TicketsCombinationOrderStatus.DeliveryFail;
                combinationOrder.UpdateTime = DateTime.Now;
                continue;
            }

            if (relatedDeliveryStatus.All(x => x == ScenicTicketOrderDeliveryStatus.WaitingForDeliver))
            {
                combinationOrder.OrderStatus = TicketsCombinationOrderStatus.WaitingForDeliver;
                combinationOrder.UpdateTime = DateTime.Now;
                continue;
            }

            if (relatedDeliveryStatus.All(x => x == ScenicTicketOrderDeliveryStatus.Success))
            {
                combinationOrder.OrderStatus = TicketsCombinationOrderStatus.Success;
                combinationOrder.UpdateTime = DateTime.Now;
                continue;
            }
        }

        foreach (var combinationOrder in ticketsCombinationOrders)
        {
            if (combinationOrder.SellingPlatform == SellingPlatform.TikTok && 
                combinationOrder.OrderStatus is TicketsCombinationOrderStatus.DeliverySyncFailed or TicketsCombinationOrderStatus.Success)
            {
                // 抖音发送退款审核消息
                await _capPublisher.PublishAsync(CapTopics.Order.OpenChannelRefundApplyAudit,
                    new ProcessingChannelRefundApplyOrderMessage
                    {
                        ChannelOrderNo = combinationOrder.ChannelOrderNo,
                        SellingPlatform = combinationOrder.SellingPlatform!.Value,
                        OrderType = OrderType.ScenicTicket,
                        OrderCategory = OrderCategory.CombinationOrder,
                    });
            }
        }
        
        await _dbContext.SaveChangesAsync();
    }

    #endregion
}
