using AutoMapper;
using Common.Jwt;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.DTOs.InvoiceTitle;
using Contracts.Common.Reflection;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.Enums;
using DotNetCore.CAP;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Order.Api.Services.Interfaces;

namespace Order.Api.Services;

public class InvoiceTitleService : IInvoiceTitleService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ICapPublisher _capPublisher;
    public InvoiceTitleService(CustomDbContext customeDbContext,
        IMapper mapper,
        IHttpContextAccessor httpContextAccessor, ICapPublisher capPublisher)
    {
        _dbContext = customeDbContext;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
        _capPublisher = capPublisher;
    }

    public async Task<List<GetInvoiceTitleOutput>> GetList(long userId)
    {
        var invoiceTitles = await _dbContext.InvoiceTitles.AsNoTracking()
            .Where(x => x.UserId.Equals(userId))
            .ToListAsync();

        var result = _mapper.Map<List<GetInvoiceTitleOutput>>(invoiceTitles);
        var invoiceConfigIds = invoiceTitles.Where(x => x.InvoiceConfigId.HasValue).Select(x => x.InvoiceConfigId).ToList();
        var invoiceConfigs = await _dbContext.InvoiceConfigs.AsNoTracking().Where(x => invoiceConfigIds.Contains(x.Id)).ToListAsync();
        //获取默认的发票接口配置
        var defaultInvoiceConfig = await _dbContext.InvoiceConfigs.AsNoTracking().FirstOrDefaultAsync(x => x.IsDefault.Equals(true));
        foreach (var item in result)
        {
            var invoiceConfig = invoiceConfigs.FirstOrDefault(x => x.Id.Equals(item.InvoiceConfigId));
            item.InvoiceConfigId = invoiceConfig is null ? defaultInvoiceConfig.Id : invoiceConfig.Id;
            item.InvoiceConfigName = invoiceConfig is null ? defaultInvoiceConfig.Name : invoiceConfig.Name;
            item.InvoiceConfigContentPrefix = invoiceConfig is null ? defaultInvoiceConfig.ContentPrefix : invoiceConfig.ContentPrefix;
        }
        return result;
    }

    public async Task<long> Add(AddInvoiceTitleInput input)
    {
        var invoiceTitle = _mapper.Map<AddInvoiceTitleInput, InvoiceTitle>(input);

        await _dbContext.AddAsync(invoiceTitle);
        await _dbContext.SaveChangesAsync();
        var log = await CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = input.UserId;
        log.TenantId = invoiceTitle.TenantId;
        log.Content = $"新增设置发票抬头：" + PropertyInfoHelper.PropertyInfoMsg<AddInvoiceTitleInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        return invoiceTitle.Id;
    }

    public async Task Update(UpdateInvoiceTitleInput input)
    {
        var invoiceTitle = await _dbContext.InvoiceTitles.FindAsync(input.Id);
        if (invoiceTitle is null) return;

        var oldData = new InvoiceTitleDto();
        _mapper.Map(invoiceTitle, oldData);

        _mapper.Map(input, invoiceTitle);
        invoiceTitle.UpdateTime = DateTime.Now;

        var log = await CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = invoiceTitle.UserId;
        log.TenantId = invoiceTitle.TenantId;
        log.Content = $"设置发票抬头：" + PropertyInfoHelper.PropertyInfoMsg<UpdateInvoiceTitleInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        await _dbContext.SaveChangesAsync();
    }

    public async Task Delete(DeleteInput input)
    {
        var invoiceTitle = await _dbContext.InvoiceTitles.FindAsync(input.Id);
        if (invoiceTitle is null) return;
        _dbContext.Remove(invoiceTitle);

        var log = await CreateLog(input);
        log.OperationType = OperationType.Delete;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = invoiceTitle.UserId;
        log.TenantId = invoiceTitle.TenantId;
        log.Content = $"删除发票抬头：" + PropertyInfoHelper.PropertyInfoMsg<DeleteInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = invoiceTitle
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<OperationLogDto> CreateLog(object input)
    {
        CurrentUser currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
        var request = _httpContextAccessor.HttpContext.Request;
        var ip = _httpContextAccessor.HttpContext.GetRemotingIp();
        var log = new OperationLogDto()
        {
            OperationType = OperationType.Add,
            System = SystemType.Vebk,
            Host = request.Host.ToString(),
            Url = request.Path.ToString(),
            Agent = request.Headers.UserAgent,
            Ip = ip,
            Query = JsonConvert.SerializeObject(request.Query),
            Body = JsonConvert.SerializeObject(input),
            OperationUserId = currentUser?.userid,
            OperationUserName = currentUser?.nickname,
            TenantId = currentUser?.tenant
        };
        return log;
    }
}
