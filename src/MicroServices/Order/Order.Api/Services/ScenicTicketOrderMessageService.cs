using AutoMapper;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Notify.DTOs;
using Contracts.Common.Notify.DTOs.EmailNotify;
using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs.MessageNotify;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.OrderLog;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.UOW;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.Api.Services;

public class ScenicTicketOrderMessageService : IScenicTicketOrderMessageService
{
    /// <summary>
    /// 发送邮件的售卖平台
    /// <list type="bullet">
    ///<item>手工单</item>
    ///<item>B2BWeb</item>
    ///<item>飞猪</item>
    ///<item>美团</item>
    ///<item>携程</item>
    ///<item>抖音</item>
    /// </list>
    /// </summary>
    private static SellingPlatform[] SendVoucherEmailSellingPlatforms => new[]
    {
        SellingPlatform.System,
        SellingPlatform.B2BWeb,
        SellingPlatform.Fliggy,
        SellingPlatform.Meituan,
        SellingPlatform.Ctrip,
        SellingPlatform.TikTok
    };

    /// <summary>
    /// 凭证来源类型
    /// <list type="bullet">
    ///<item>采购导入</item>
    ///<item>接口对接 </item>
    ///<item>手工发货</item>
    /// </list>
    /// </summary>
    private static CredentialSourceType[] CredentialSourceTypes => new[]
    {
        CredentialSourceType.PurchasingImport, 
        CredentialSourceType.InterfaceDock,
        CredentialSourceType.ManualDelivery
    };
    
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;
    private readonly IAgencyService _agencyService;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMessageNotifyService _messageNotifyService;
    private readonly IOrderFieldInformationService _orderFieldInformationService;

    private static readonly FileExtensionContentTypeProvider _typeProvider = new();

    public ScenicTicketOrderMessageService(
        IAgencyService agencyService,
        IMapper mapper,
        CustomDbContext dbContext,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddressOptions,
        IMessageNotifyService messageNotifyService,
        IOrderFieldInformationService orderFieldInformationService)
    {
        _mapper = mapper;
        _agencyService = agencyService;
        _messageNotifyService = messageNotifyService;
        _dbContext = dbContext;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddressOptions.Value;
        _orderFieldInformationService = orderFieldInformationService;
    }

    public async Task SendMessageAsync(ScenicTicketOrderSendMessageInput input)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var scenicTicketOrder = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.ScenicTicketOrderId);
        
        var messageNotifyDto = await SupplementMessageNotify(baseOrder,scenicTicketOrder,input.Vouchers);
        await _messageNotifyService.ScenicTicketCreateOrderNotify(messageNotifyDto);

        await AddSendEmailLog(baseOrder: baseOrder,
            userType: UserType.None,
            userId: 0,
            userName: string.Empty,
            receivingEmailAddress: baseOrder.ContactsEmail);
    }

    [UnitOfWork]
    public async Task<ScenicTicketOrderSendEmailSyncOutput> SendEmailSync(ScenicTicketOrderSendEmailSyncInput input)
    {
        var output = new ScenicTicketOrderSendEmailSyncOutput();
        var baseOrder = await _dbContext.BaseOrders.AsNoTracking().FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var scenicTicketOrder = await _dbContext.ScenicTicketOrders.AsNoTracking()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        if (scenicTicketOrder.Status != ScenicTicketOrderStatus.Delivered)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (string.IsNullOrEmpty(input.EmailAddress))
        {
            if (string.IsNullOrEmpty(baseOrder.ContactsEmail))
            {
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
            }

            input.EmailAddress = baseOrder.ContactsEmail; //默认接收邮箱
        }

        // 售卖平台判断
        if (!SendVoucherEmailSellingPlatforms.Contains(baseOrder.SellingPlatform))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        // 获取凭证数据
        var vouchers = new List<ScenicTicketOrderSendMessageVouchers>();
        switch (scenicTicketOrder.CredentialSourceType)
        {
            case CredentialSourceType.PurchasingImport:

                var purchaseOrderIds = await _dbContext.ScenicTicketPurchaseVoucherUsedDetails.AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => x.PurchaseOrderId)
                    .ToListAsync();
                if (purchaseOrderIds.Any())
                {
                    vouchers = await _dbContext.ScenicTicketPurchaseVouchers.AsNoTracking()
                        .Where(x => purchaseOrderIds.Contains(x.PurchaseOrderId))
                        .Select(x => new ScenicTicketOrderSendMessageVouchers
                        {
                            FilePath = x.FilePath, ThumbnailPath = x.Thumbnail
                        })
                        .ToListAsync();
                }
                
                break;
            case CredentialSourceType.InterfaceDock:
            case CredentialSourceType.ManualDelivery:

                vouchers = await _dbContext.ScenicTicketSupplierOrderVouchers
                    .AsNoTracking()
                    .Where(x => x.BaseOrderId == input.BaseOrderId)
                    .Select(x => new ScenicTicketOrderSendMessageVouchers
                    {
                        FilePath = x.FilePath, ThumbnailPath = x.Thumbnail
                    })
                    .ToListAsync();
                
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
        
        var messageNotifyDto = await SupplementMessageNotify(baseOrder: baseOrder,
            scenicTicketOrder: scenicTicketOrder,
            vouchers: vouchers);
        var sendEmailRequest = new SendEmailSynchronouslyInput
        {
            TenantId = baseOrder.TenantId,
            SendToTheRole = SendToTheRole.Customer,
            NotifyMode = NotifyMode.Email,
            NotifyEventSubType = NotifyEventSubType.ScenicTicket_CreateOrder,
            NotifyChannel = baseOrder.SellingPlatform switch
            {
                SellingPlatform.System => NotifyChannel.Manual,
                SellingPlatform.B2BWeb => NotifyChannel.B2b,
                SellingPlatform.Fliggy => NotifyChannel.B2b,
                SellingPlatform.Meituan => NotifyChannel.B2b,
                SellingPlatform.Ctrip => NotifyChannel.B2b,
                SellingPlatform.TikTok => NotifyChannel.B2b,
                _ => 0
            },
            Variables = new
            {
                Addressee = input.EmailAddress,
                OrderId = messageNotifyDto.NotifyDto.Id,
                messageNotifyDto.NotifyDto.ScenicSpotName,
                messageNotifyDto.NotifyDto.TicketName,
                messageNotifyDto.NotifyDto.ValidityBegin,
                messageNotifyDto.NotifyDto.ValidityEnd,
                TicketCodeUrl = $"/verification?baseOrderId={baseOrder.Id}&orderId={scenicTicketOrder.Id}&type=1",
                OrderContact = baseOrder.ContactsName,
                Remark = baseOrder.Message,
                messageNotifyDto.NotifyDto.Travelers,
                messageNotifyDto.NotifyDto.Quantity,
                baseOrder.PaymentAmount,
                CreateOrderTime = baseOrder.CreateTime,
                messageNotifyDto.NotifyDto.CredentialSourceType,
                messageNotifyDto.NotifyDto.AttachmentFilePaths,
                messageNotifyDto.NotifyDto.OpeningTime,
                messageNotifyDto.NotifyDto.ScenicSpotAddress,
                baseOrder.SellingPlatform,
                messageNotifyDto.NotifyDto.OrderFields
            }
        };
        var sendEmailResponse = await SendEmailSynchronously(sendEmailRequest);
        output.IsSuccessed = sendEmailResponse.IsSuccessed;
        
        // 订单发送日志记录
        await AddSendEmailLog(baseOrder: baseOrder,
            userType: input.OperationUserDto.UserType,
            userId: input.OperationUserDto.UserId,
            userName: input.OperationUserDto.Name,
            receivingEmailAddress: input.EmailAddress);
        
        return output;
    }

    #region private

    private async Task AddSendEmailLog(BaseOrder baseOrder,UserType userType,long userId,string? userName, string receivingEmailAddress)
    {
        if (SendVoucherEmailSellingPlatforms.Contains(baseOrder.SellingPlatform) &&
            !string.IsNullOrEmpty(receivingEmailAddress))
        {
            var orderNotifyEmail = await GetTenantOrderNotifyEmail(baseOrder.TenantId);
            var orderLog = new OrderLogs
            {
                OrderId = baseOrder.Id,
                OperationRole = userType,
                OperationType = OrderOperationType.OrderSendEmail,
                UserId = userId,
                UserName = userName,
                OrderLogType = OrderLogType.ScenicTicket,
                ExtensionData = JsonConvert.SerializeObject(new OrderLogExtensionDataDto
                {
                    SendEmailAddress = orderNotifyEmail?.Address,
                    ReceivingEmailAddress = receivingEmailAddress
                })
            };
            orderLog.SetTenantId(baseOrder.TenantId);
            await _dbContext.AddAsync(orderLog);
        }
    }
    
    private async Task<OrderNotifyDto<ScenicTicketCreateOrderNotifyDto>> SupplementMessageNotify(
        BaseOrder baseOrder,
        ScenicTicketOrder scenicTicketOrder,
        List<ScenicTicketOrderSendMessageVouchers> vouchers)
    {
        var messageNotifyDto = new OrderNotifyDto<ScenicTicketCreateOrderNotifyDto>();
        
        //获取模板信息
        var orderFields = await GetOrderFieldInfo(baseOrder.Id, scenicTicketOrder.Id);
        if (!scenicTicketOrder.CredentialSourceType.HasValue ||
            !CredentialSourceTypes.Contains(scenicTicketOrder.CredentialSourceType.Value))
        {
            //普通产品
            var ticketCodes = await _dbContext.TicketCodes
                .Where(x => x.BaseOrderId == scenicTicketOrder.BaseOrderId && x.SubOrderId == scenicTicketOrder.Id)
                .Select(x => x.Code)
                .ToListAsync();

            messageNotifyDto = new OrderNotifyDto<ScenicTicketCreateOrderNotifyDto>
            {
                BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                NotifyDto = new ScenicTicketCreateOrderNotifyDto
                {
                    ScenicSpotName = baseOrder.ResourceName,
                    TicketName = baseOrder.ProductName,
                    Payment = baseOrder.PaymentAmount,
                    Quantity = scenicTicketOrder.Quantity,
                    ValidityBegin = scenicTicketOrder.ValidityBegin,
                    ValidityEnd = scenicTicketOrder.ValidityEnd,
                    TicketCodes = ticketCodes.Select(s => s.ToString()),
                    CredentialSourceType = scenicTicketOrder.CredentialSourceType,
                    Id = baseOrder.Id.ToString(),
                    OpeningTime = scenicTicketOrder.OpeningTime,
                    ScenicSpotAddress = scenicTicketOrder.ScenicSpotAddress,
                    Status = scenicTicketOrder.Status,
                    OrderFields = orderFields
                }
            };
        }
        else
        {
            var attachmentFilePaths = new List<AttachmentFilePath>();
            var notifyOrderNo = baseOrder.Id.ToString();
            if (baseOrder.AgencyId is > 0)
            {
                var agency = await _agencyService.GetAgencyDetail(baseOrder.AgencyId);
                notifyOrderNo = agency.AgencyType == AgencyType.Api
                    ? baseOrder.ChannelOrderNo ?? baseOrder.Id.ToString()
                    : baseOrder.Id.ToString();
            }
            messageNotifyDto = new OrderNotifyDto<ScenicTicketCreateOrderNotifyDto>
            {
                BaseOrder = _mapper.Map<BaseOrderNotify>(baseOrder),
                NotifyDto = new ScenicTicketCreateOrderNotifyDto
                {
                    ScenicSpotName = baseOrder.ResourceName,
                    TicketName = baseOrder.ProductName,
                    Payment = baseOrder.PaymentAmount,
                    ValidityBegin = scenicTicketOrder.ValidityBegin,
                    ValidityEnd = scenicTicketOrder.ValidityEnd,
                    Quantity = scenicTicketOrder.Quantity,
                    CredentialSourceType = scenicTicketOrder.CredentialSourceType,
                    Id = notifyOrderNo,
                    OpeningTime = scenicTicketOrder.OpeningTime,
                    AttachmentFilePaths = attachmentFilePaths,
                    ScenicSpotAddress = scenicTicketOrder.ScenicSpotAddress,
                    Status = scenicTicketOrder.Status,
                    OrderFields = orderFields
                }
            };
            
            //判断是否需要发凭证邮件通知
            if (SendVoucherEmailSellingPlatforms.Contains(baseOrder.SellingPlatform))
            {
                //验证邮箱有效性
                var isValidEmail = _messageNotifyService.IsValidEmail(baseOrder.ContactsEmail);
                if (isValidEmail)
                {
                    //发邮件通知 需要下载附件
                    foreach (var item in vouchers)
                    {
                        var supplementFileInfo = SupplementFileInfo(item.FilePath);
                        attachmentFilePaths.Add(new AttachmentFilePath
                        {
                            ContentType = supplementFileInfo.contentType,
                            FileName = supplementFileInfo.fileName,
                            FilePath = item.FilePath
                        });
                    }

                    messageNotifyDto.NotifyDto.AttachmentFilePaths = attachmentFilePaths;
                }
                else
                {
                    messageNotifyDto.BaseOrder.ContactsEmail = string.Empty;
                }
            }
        }

        return messageNotifyDto;
    }
    
    /// <summary>
    /// 补充文件信息
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    private (string contentType, string fileName, FileType fileType) SupplementFileInfo(string filePath)
    {
        var result = ("unknown", "unknown", FileType.Unknown);
        var guid = Guid.NewGuid().ToString();
        var suffix = Path.GetExtension(filePath);
        if (string.IsNullOrEmpty(suffix))
            return result;

        if (_typeProvider.Mappings.TryGetValue(suffix, out var mime))
        {
            var fileName = $"{guid}{suffix}";
            var fileType = suffix.Equals(".pdf", StringComparison.OrdinalIgnoreCase)
                ? FileType.Pdf
                : FileType.Image;
            return (mime, fileName, fileType);
        }

        return result;
    }

    /// <summary>
    /// 查询订单的字段信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    private async Task<List<OrderFieldInformationSmsTypeDto>> GetOrderFieldInfo(long baseOrderId, long scenicOrderId)
    {
        var templateTypes = new List<TemplateType>() { TemplateType.Travel, TemplateType.Order };
        var orderFields = await _dbContext.OrderFieldInformationType
            .Join(_dbContext.OrderFieldInformations, type => type.Id, field => field.OrderFieldInformationTypeId,
                (type, field) => new { type, field })
            .Where(x => x.type.BaseOrderId == baseOrderId)
            .Where(x => x.type.OrderId == scenicOrderId)
            .Where(x => templateTypes.Contains(x.type.TemplateType))
            .Select(x => new { x.type.TemplateType, x.field })
            .ToListAsync();
        // 出行人订单公用信息、出人信息
        var orderTravelFields =
            orderFields.Where(x => x.TemplateType == TemplateType.Travel).Select(x => x.field).ToList();
        var orderFieldsOut = _mapper.Map<List<OrderFieldInformationDto>>(orderTravelFields);
        var travelerList = _orderFieldInformationService.ChangeOrderSmsField(orderFieldsOut, TemplateType.Travel);
        return travelerList;
    }

    /// <summary>
    /// 发送邮箱
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<SendEmailSynchronouslyOutput> SendEmailSynchronously(SendEmailSynchronouslyInput request)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(request),
            Encoding.UTF8,
            "application/json");
        var result = await _httpClientFactory.InternalPostAsync<SendEmailSynchronouslyOutput>(
            requestUri: _servicesAddress.SendEmail_Synchronously(),
            httpContent: httpContent);
        return result;
    }

    /// <summary>
    /// 查询租户配置的订单通知邮箱
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    private async Task<TenantEmailBoxDto> GetTenantOrderNotifyEmail(long tenantId)
    {
        var header = new List<KeyValuePair<string, string>> {new("tenant", tenantId.ToString())};
        var emailBoxList = await _httpClientFactory.InternalGetAsync<List<TenantEmailBoxDto>>(
            requestUri: _servicesAddress.TenantEmailBox_Get(),
            headers: header);
        return emailBoxList.FirstOrDefault(x=>x.TenantEmailBoxType == TenantEmailBoxType.OrderNotify);
    }
    
    #endregion
}