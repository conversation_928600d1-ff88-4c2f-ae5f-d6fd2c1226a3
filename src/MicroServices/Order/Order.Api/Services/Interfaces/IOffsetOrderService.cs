using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.OffsetOrder;
using Contracts.Common.Order.Messages;
using EfCoreExtensions.Abstract;

namespace Order.Api.Services.Interfaces;

public interface IOffsetOrderService
{
    Task<long> Add(AddOffsetOrderInput input);
    Task<GetOffsetOrderDetailOutput> Detail(long id);
    Task Edit(EditOffsetOrderInput input,OperationUserDto operationUser,long tenantId);
    Task Delete(long id,long tenantId);
    Task<PagingModel<SearchOffsetOrderOutput>> Search(SearchOffsetOrderInput input);
    Task<IEnumerable<GetOffsetOrderListOutput>> GetList(GetOffsetOrderListInput input);

    Task RefundResult(RefundResultMessage receive);

    Task SyncOffsetOrderDingtalk(SyncOffsetOrderDingtalkMessage message);
}