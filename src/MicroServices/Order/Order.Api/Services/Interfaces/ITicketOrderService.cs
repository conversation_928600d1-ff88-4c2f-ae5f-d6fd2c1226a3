using Common.Jwt;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.TicketCodeUsed;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Product.DTOs.ProductSku;
using EfCoreExtensions.Abstract;

namespace Order.Api.Services.Interfaces
{
    public interface ITicketOrderService
    {
        /// <summary>
        /// 延长有效期
        /// </summary>
        Task UpdateValidityTime(OperationUserDto operationUser, UpdateTicketOrderValidityTimeInput input);

        /// <summary>
        /// 修改采购价
        /// </summary>
        Task UpdateCostPrice(OperationUserDto user, UpdateTicketOrderCostPriceInput input);

        /// <summary>
        /// 搜索
        /// </summary>
        Task<PagingModel<SearchTicketOrderInfo, SearchTicketOrderStatusStat>> Search(SearchTicketOrderInput input);

        /// <summary>
        /// 获取产品信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<TicketOrderGetProductOutput> GetProduct(GetTicketProductInput input);

        /// <summary>
        /// 通用创建订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<long> Create(CreateTicketOrderInput input);

        /// <summary>
        /// 创建手工单
        /// </summary>
        Task<long> ManualAdd(CurrentUser user, ManualAddTicketOrderInput input);

        /// <summary>
        /// 创建订单
        /// </summary>
        Task<long> MallAdd(CurrentUser buyer, MallAddTicketOrderInput input);

        /// <summary>
        /// 获取使用明细
        /// </summary>
        Task<GetUsedDetailOutput> GetUsingDetail(long baseOrderId);

        Task<GetRefundabledOutput> GetRefundabled(long baseOrderId, OperationUserDto operationUser);

        Task<GetReservationabledOutput> GetReservationabled(long baseOrderId, long? customerId);

        Task Refund(RefundTicketOrderInput input, OperationUserDto operationUser);
        /// <summary>
        /// 券类订单过期自动完结
        /// </summary>
        Task<List<FinishExpirationTicketOrderOutput>> FinishExpirationTicketOrder();
        /// <summary>
        /// 获取订单
        /// </summary>
        Task<GetTicketOrderInfo> Get(GetTicketOrderInfoInput input);

        /// <summary>
        /// 卡券订单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<TicketOrderInfoDto> GetTicketOrderInfo(GetTicketOrderInfoInput input);

        /// <summary>
        /// 卡券类券码信息
        /// </summary>
        /// <param name="ticketOrderId"></param>
        /// <returns></returns>
        Task<List<TicketCodeDto>> GetTicketCodes(long ticketOrderId);

        /// <summary>
        /// 修改订单
        /// </summary>
        Task Update(OperationUserDto optUser, UpdateTicketOrderInput input);

        /// <summary>
        /// b2b免登陆获取核销信息
        /// </summary>
        /// <param name="baseOrderId"></param>
        /// <returns></returns>
        Task<B2BGetByNoLoginOutput> B2BGetTicketCodeByNoLogin(long baseOrderId, long orderId);

        /// <summary>
        /// 订单时间提醒
        /// </summary>
        /// <returns></returns>
        Task<List<long>> AutoTimeReminder();

        Task<bool> UpdateSupplierOrderId(UpdateSupplierOrderIdInput input);

    }
}
