using Contracts.Common.Order.DTOs.GroupBookingAgencyShare;

namespace Order.Api.Services.Interfaces;

public interface IGroupBookingAgencyShareService
{
    /// <summary>
    /// 获取或创建分销商账号分享码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GroupBookingAgencyShareOutput> GetOrCreateAsync(GetOrCreateInput input);

    /// <summary>
    /// 根据code获取分销商账号分享码信息
    /// </summary>
    /// <param name="code"></param>
    /// <returns></returns>
    Task<GroupBookingAgencyShareOutput?> GetByCodeAsync(string code);
}
