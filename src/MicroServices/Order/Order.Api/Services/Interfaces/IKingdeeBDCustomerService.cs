using Contracts.Common.Order.DTOs.Kingdee;
using Contracts.Common.Order.Messages;

namespace Order.Api.Services.Interfaces;

public interface IKingdeeBDCustomerService
{
    /// <summary>
    /// 根据分销商id查询客户信息
    /// </summary>
    /// <param name="agencyIds"></param>
    /// <returns></returns>
    Task<List<KingdeeBDCustomer>> GetCustomers(GetCustomersInput input);

    /// <summary>
    /// 添加客户信息
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task AddCustomer(KingdeeCustomerAddMessage receive);
}
