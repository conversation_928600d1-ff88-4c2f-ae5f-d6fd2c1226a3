using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.Messages;
using EfCoreExtensions.Abstract;

namespace Order.Api.Services.Interfaces;

public interface IOpenChannelSyncFailOrderService
{
    /// <summary>
    /// 添加失败同步订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Add(OpenChannelSyncFailOrderAddInput input);

    /// <summary>
    /// 更新失败同步订单状态
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task Update(OpenChannelSyncFailOrderStatusUpdateMessage receive);

    /// <summary>
    /// 查询列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchOpenChannelSyncFailOrderOutput>> Search(SearchOpenChannelSyncFailOrderInput input);

    /// <summary>
    /// 查询分页数据状态数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetSyncFailOrderCountOutput>> SearchStatusCount(SearchOpenChannelSyncFailOrderInput input);

    /// <summary>
    /// 查询同步失败订单数据
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    Task<GetOpenChannelSyncFailOrderOutput> Detail(GetOpenChannelSyncFailOrderInput input);

    /// <summary>
    /// 作废同步失败订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Invalid(InvalidSyncFailOrderInput input);

    /// <summary>
    /// 更新订单类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateOrderType(UpdateChannelSyncFailOrderTypeInput input);
}