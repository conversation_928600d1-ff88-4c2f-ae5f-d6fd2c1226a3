using Contracts.Common.Order.DTOs.Kingdee;
using Contracts.Common.Order.DTOs.Kingdee.KingdeeAPPayable;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using DotNetCore.CAP;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Order.Api.ConfigModel;
using Order.Api.Services.Interfaces;

namespace Order.Api.Services;

public class KingdeeAPPayableService : IKingdeeAPPayableService
{
    private readonly CustomDbContext _dbContext;
    private readonly IKingdeeBDSupplierService _kingdeeBDSupplierService;
    private readonly IKingdeeService _kingdeeService;
    private readonly ICurrencyExchangeRateService _currencyExchangeRateService;
    private readonly ICapPublisher _capPublisher;
    private readonly ILogger<KingdeeAPPayableService> _logger;
    private readonly ISupplierService _supplierService;
    private readonly IAgencyService _agencyService;
    private readonly KingdeeDataConfig _kingdeeDataConfig;

    public KingdeeAPPayableService(CustomDbContext dbContext,
        IKingdeeBDSupplierService kingdeeBDSupplierService,
        IKingdeeService kingdeeService,
        ICurrencyExchangeRateService currencyExchangeRateService,
        ICapPublisher capPublisher,
        IOptions<KingdeeDataConfig> kingdeeDataConfig,
        ILogger<KingdeeAPPayableService> logger,
        ISupplierService supplierService,
        IAgencyService agencyService)
    {
        _dbContext = dbContext;
        _kingdeeBDSupplierService = kingdeeBDSupplierService;
        _kingdeeService = kingdeeService;
        _currencyExchangeRateService = currencyExchangeRateService;
        _capPublisher = capPublisher;
        _logger = logger;
        _supplierService = supplierService;
        _agencyService = agencyService;
        _kingdeeDataConfig = kingdeeDataConfig.Value;
    }

    public async Task<List<KingdeeAPPayableDto>> Generate(GenerateInput input)
    {
        var kingdeeAPPayables = await Search(input);
        if (kingdeeAPPayables.Count == 0)
            return kingdeeAPPayables;
        //供应商数据
        var supplierIds = kingdeeAPPayables.Select(x => x.SupplierId)
            .Distinct()
            .ToArray();
        var BDSuppliers = await _kingdeeBDSupplierService.GetSuppliers(new GetSuppliersInput
        {
            SupplierIds = supplierIds,
            Status = KingdeeFormStatus.Audited
        });
        var agencyIds = kingdeeAPPayables
            .Where(x => x.AgencyId.HasValue)
            .Select(x => x.AgencyId!.Value)
            .Distinct()
            .ToList();
        var agencies = await _agencyService.AgencyGetByIds(new Contracts.Common.Tenant.DTOs.Agency.GetAgenciesByIdsInput
        {
            AgencyIds = agencyIds
        });
        //币种
        var currencies = await _kingdeeService.GetBDCurrencies();
        foreach (var bill in kingdeeAPPayables)
        {
            var model = bill.Model;
            bill.Model.FsubHeadFinc.FExchangeRate = 1;//当前本位币跟币别一致，汇率默认设置为1，后续产品出需求再调整
            var currency = currencies.FirstOrDefault(x => x.FCODE == bill.CurrencyCode);
            if (currency is not null)
            {
                bill.Model.FCURRENCYID = new(currency.FNumber);
                bill.Model.FsubHeadFinc.FMAINBOOKSTDCURRID = new(currency.FNumber);
            }
            var FNumber = BDSuppliers
                .FirstOrDefault(x => x.SupplierId == bill.SupplierId)?.BDSupplier.FNumber;
            bill.FNumber = FNumber;
            bill.Model.FSUPPLIERID = new(FNumber ?? string.Empty);
            //组织机构
            var organization = _kingdeeService.GetSupplierORGOrganization(bill.SupplierId, bill.BaseCurrencyCode);
            model.FSETTLEORGID = new() { FNumber = organization.FSETTLEORGID };
            model.FPAYORGID = new() { FNumber = organization.FPAYORGID };
            model.FPURCHASEORGID = new(FNumber = organization.FPURCHASEORGID);
            model.FPURCHASERGROUPID = new() { FNumber = organization.FPURCHASEORGID };
            foreach (var item in model.FEntityDetail)
            {
                item.FEntryTaxRate = organization.FEntryTaxRate;//税率
            }
            //本位币
            bill.TargetCurrencyCode = organization.MainBookCurrencyCode;
            //分销商 部门 产品线
            var agency = agencies.FirstOrDefault(s => s.Id == bill.AgencyId);
            if (agency?.TenantDepartmentName is not null)
            {
                var depline = _kingdeeService.GetSupplierDepartmentLine(agency.TenantDepartmentName);
                if (!string.IsNullOrWhiteSpace(depline.department))
                    model.FPURCHASEDEPTID = new FNumberModel { FNumber = depline.department };
                if (!string.IsNullOrWhiteSpace(depline.line))
                {
                    foreach (var item in model.FEntityDetail)
                    {
                        item.F_qwe_Assistant = new FNumberModel { FNumber = depline.line };
                    }
                }
            }
        }

        var getExchangeRatesInputs = kingdeeAPPayables
           .Where(x => x.BaseCurrencyCode != x.TargetCurrencyCode)
           .Select(x => new { x.BaseCurrencyCode, x.TargetCurrencyCode })
           .Distinct()
           .Select(x => new GetExchangeRatesInput
           {
               BaseCurrencyCode = x.BaseCurrencyCode,
               TargetCurrencyCode = x.TargetCurrencyCode,
           });
        var exchangeRates = await _currencyExchangeRateService.GetExchangeRates(getExchangeRatesInputs);
        foreach (var bill in kingdeeAPPayables)
        {
            var targetCurrency = currencies.FirstOrDefault(x => x.FCODE == bill.TargetCurrencyCode);
            if (targetCurrency is not null)
            {
                bill.Model.FsubHeadFinc.FMAINBOOKSTDCURRID = new(targetCurrency.FNumber);
            }
            decimal? exchangeRate = bill.BaseCurrencyCode != bill.TargetCurrencyCode ?
               exchangeRates.FirstOrDefault(x => x.BaseCurrencyCode == bill.BaseCurrencyCode
                       && x.TargetCurrencyCode == bill.TargetCurrencyCode)?.ExchangeRate : 1;
            bill.Model.FsubHeadFinc.FExchangeRate = exchangeRate;
            bill.ExchangeRate = exchangeRate;
            bill.TargetExchangeRate = exchangeRate;

            //限制业务日期
            if (input.LimitFDate.HasValue && bill.Model.FDATE > input.LimitFDate.Value)
            {
                bill.Model.FDATE = input.LimitFDate.Value;
            }
        }

        //应付单 表体明细的单价必录且必须大于0!
        return kingdeeAPPayables.Where(x => x.SupplierId > 0 && x.Amount > 0).ToList();
    }

    private async Task<List<KingdeeAPPayableDto>> Search(GenerateInput input)
    {
        DateTime beginDate = input.BeginDate; DateTime endDate = input.EndDate;
        AggregateOrderStatus[] aggregateOrderStatuses = new AggregateOrderStatus[] 
        {
            AggregateOrderStatus.Confirmed,
            AggregateOrderStatus.WaitingForConfirm,
            AggregateOrderStatus.Finished,
        };
        //玩乐订单
        var baseOrderQuery = _dbContext.AggregateOrders
            .Where(x => x.OrderType != OrderType.Hotel && aggregateOrderStatuses.Contains(x.OrderStatus))
            .Where(x => x.TravelDate >= beginDate && x.TravelDate < endDate.AddDays(1))
            .Select(x => new
            {
                x.BaseOrderId,
                x.OrderType,
                x.TravelDate,
                x.CreateTime,
                x.AgencyId,
            });
        var baseOrders = await baseOrderQuery
            .WhereIF(input.BaseOrderIds?.Length is > 0, x => input.BaseOrderIds!.Contains(x.BaseOrderId))
            .ToListAsync();
        List<SubOrderModel> subOrders = new();
        var groups = baseOrders.GroupBy(x => x.OrderType);
        foreach (var g in groups)
        {
            var ids = g.Select(x => x.BaseOrderId);
            if (ids.Any() is not true)
                continue;
            switch (g.Key)
            {
                case OrderType.Ticket:
                    var ticketOrders = await _dbContext.TicketOrders
                        .Where(x => ids.Contains(x.BaseOrderId))
                        .Select(x => new SubOrderModel
                        {
                            SubOrderId = x.Id,
                            BaseOrderId = x.BaseOrderId,
                            OrderType = g.Key,
                            SupplierId = x.ProductSupplierId
                        })
                        .ToListAsync();
                    subOrders.AddRange(ticketOrders);
                    break;
                case OrderType.ScenicTicket:
                    var scenicTicketOrders = await _dbContext.ScenicTicketOrders
                        .Where(x => ids.Contains(x.BaseOrderId))
                        .Select(x => new SubOrderModel
                        {
                            SubOrderId = x.Id,
                            BaseOrderId = x.BaseOrderId,
                            OrderType = g.Key,
                            SupplierId = x.SupplierId
                        })
                        .ToListAsync();
                    subOrders.AddRange(scenicTicketOrders);
                    break;
                case OrderType.TravelLineOrder:
                    var travelLineOrders = await _dbContext.TravelLineOrder
                      .Where(x => ids.Contains(x.BaseOrderId))
                      .Select(x => new SubOrderModel
                      {
                          SubOrderId = x.Id,
                          BaseOrderId = x.BaseOrderId,
                          OrderType = g.Key,
                          SupplierId = x.SupplierId
                      })
                      .ToListAsync();
                    subOrders.AddRange(travelLineOrders);
                    break;
                case OrderType.CarHailing:
                    var carHailingOrders = await _dbContext.CarHailingOrders
                    .Where(x => ids.Contains(x.BaseOrderId))
                    .Select(x => new SubOrderModel
                    {
                        SubOrderId = x.Id,
                        BaseOrderId = x.BaseOrderId,
                        OrderType = g.Key,
                        SupplierId = x.SupplierId
                    })
                    .ToListAsync();
                    subOrders.AddRange(carHailingOrders);
                    break;
                case OrderType.Mail:
                    var mailOrders = await _dbContext.MailOrders
                   .Where(x => ids.Contains(x.BaseOrderId))
                   .Select(x => new SubOrderModel
                   {
                       SubOrderId = x.Id,
                       BaseOrderId = x.BaseOrderId,
                       OrderType = g.Key,
                       SupplierId = x.ProductSupplierId
                   })
                   .ToListAsync();
                    subOrders.AddRange(mailOrders);
                    break;
                case OrderType.CarProduct:
                    var carProductOrders = await _dbContext.CarProductOrders
                   .Where(x => ids.Contains(x.BaseOrderId))
                   .Select(x => new SubOrderModel
                   {
                       SubOrderId = x.Id,
                       BaseOrderId = x.BaseOrderId,
                       OrderType = g.Key,
                       SupplierId = x.SupplierId
                   })
                   .ToListAsync();
                    subOrders.AddRange(carProductOrders);
                    break;
            }
        }

        ////2.订单退款单
        //var refundOrderQuery = from refundOrder in _dbContext.RefundOrders
        //                       join baseOrder in _dbContext.BaseOrders
        //                       on refundOrder.BaseOrderId equals baseOrder.Id
        //                       where refundOrder.Status == RefundOrderStatus.Refunded
        //                       && refundOrder.UpdateTime >= beginDate
        //                       && refundOrder.UpdateTime < endDate.AddDays(1)
        //                       select new
        //                       {
        //                           refundOrder.Id,
        //                           refundOrder.SupplierId,
        //                           refundOrder.UpdateTime,
        //                           refundOrder.CreateTime,
        //                           refundOrder.Cost,
        //                           refundOrder.CostCurrencyCode,
        //                           refundOrder.BaseOrderId,
        //                           refundOrder.OrderType
        //                       };
        //var refundOrders = await refundOrderQuery.ToListAsync();

        //3.抵充单
        /*酒店抵冲单
            1.在推送周期内离店的订单，离店日期前创建的抵冲单，业务日期按照离店日期推送；
            2.在推送周期内创建，已过关联订单离店日期的抵冲单，业务日期按照创建日期推送；
            玩乐抵冲单
            1.在推送周期内出行的订单，出行日期前创建的抵冲单，业务日期按照出行日期推送；
            2.在推送周期内创建，已过关联订单出行日期的抵冲单，业务日期按照创建日期推送；
        */
        var aggregateOffsetOrderQuery = _dbContext.AggregateOrders
                .Join(_dbContext.OffsetOrders.AsNoTracking(), a => a.BaseOrderId, o => o.BaseOrderId, (a, o) => new
                {
                    AggregateOrder = a,
                    OffsetOrder = o
                })
                .IgnoreQueryFilters()
                .Where(x => x.AggregateOrder.TenantId == input.TenantId)
                .Where(x => x.AggregateOrder.AbnormalOrderSourceType == null && x.AggregateOrder.OrderStatus != AggregateOrderStatus.ChannelAbnormal)
                .Where(x => x.OffsetOrder.OffsetType == OffsetOrderType.Payable
                    && (x.OffsetOrder.ProcessStatus == null || x.OffsetOrder.ProcessStatus == OffsetOrderProcessingStatus.Success))
                .WhereIF(input.OffsetOrderIds?.Length is > 0, x => input.OffsetOrderIds!.Contains(x.OffsetOrder.Id))
                .Select(s => new TravelDateOffsetOrderDto
                {
                    TravelDate = s.AggregateOrder.OrderType == OrderType.Hotel ? s.AggregateOrder.FinishDate : s.AggregateOrder.TravelDate,
                    OffsetOrder = s.OffsetOrder,
                    AgencyId = s.AggregateOrder.AgencyId,
                });
        var aggregateOffsetOrders = await aggregateOffsetOrderQuery
            .Where(x => x.TravelDate >= x.OffsetOrder.CreatTime)
            .Where(x => x.TravelDate >= beginDate && x.TravelDate < endDate.AddDays(1))
            .ToListAsync();

        var aggregateOffsetOrders2 = await aggregateOffsetOrderQuery
            .Where(x => x.TravelDate < x.OffsetOrder.CreatTime)
            .Where(x => x.OffsetOrder.CreatTime >= beginDate && x.OffsetOrder.CreatTime < endDate.AddDays(1))
            .ToListAsync();

        var offsetOrders = aggregateOffsetOrders.Union(aggregateOffsetOrders2).ToList();

        //玩乐价格信息
        var baseOrderIds = baseOrders.Select(x => x.BaseOrderId)
            .Distinct();
        var orderPrices = await _dbContext.OrderPrices
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        List<KingdeeAPPayableDto> kingdeeAPPayables = new();

        foreach (var order in subOrders)
        {
            var prices = orderPrices.Where(x => x.SubOrderId == order.SubOrderId);
            if (prices.Any() is not true) continue;
            var baseOrder = baseOrders.FirstOrDefault(x => x.BaseOrderId == order.BaseOrderId);
            var amount = prices.Sum(s => s.CostPrice * s.Quantity);
            var costCurrencyCode = prices.First().CostCurrencyCode;
            var exchangeRate = 1;
            var date = baseOrder.TravelDate ?? baseOrder.CreateTime;
            KingdeeAPPayableDto kingdeeAPPayable = new()
            {
                BusinessOrderId = order.BaseOrderId,
                BusinessType = KingdeeBusinessType.Order,
                Amount = amount,
                CurrencyCode = costCurrencyCode,
                TotalAmount = amount,
                BaseCurrencyCode = costCurrencyCode,
                ExchangeRate = exchangeRate,
                SupplierId = order.SupplierId,
                AgencyId = baseOrders.FirstOrDefault(x => x.BaseOrderId == order.BaseOrderId)?.AgencyId,
                Date = date,
                Model = new()
                {
                    FBillTypeID = new(_kingdeeDataConfig.FBillTypeID.Payable),
                    FBillNo = order.BaseOrderId.ToString(),
                    FDATE = date,
                    FSETTLEORGID = new(_kingdeeDataConfig.FSETTLEORGID),
                    FPAYORGID = new(_kingdeeDataConfig.FPAYORGID),
                    FPURCHASERGROUPID = new(_kingdeeDataConfig.FPURCHASERGROUPID),
                    FPURCHASEDEPTID = new(_kingdeeDataConfig.FPURCHASEDEPTID.Other),
                    FSUPPLIERID = new(""),//供应商
                    FCURRENCYID = new(costCurrencyCode),
                    FsubHeadFinc = new()
                    {
                        FMAINBOOKSTDCURRID = new(costCurrencyCode),
                        FACCNTTIMEJUDGETIME = date.ToShortDateString(),
                        FExchangeRate = exchangeRate,
                        FEXCHANGETYPE = new(_kingdeeDataConfig.FEXCHANGETYPE),
                    },
                    FEntityDetail = new FentitydetailModel[] {
                          new()
                          {
                                F_qwe_Assistant = new(_kingdeeDataConfig.F_qwe_Assistant.Other),
                                FMATERIALID = new(_kingdeeDataConfig.FMATERIALID),
                                FPRICEUNITID = new(_kingdeeDataConfig.FPRICEUNITID),
                                FPriceQty = 1,
                                FTaxPrice = amount,
                                FEntryTaxRate = _kingdeeDataConfig.FEntryTaxRate,//税率
                          }
                     }
                }
            };
            kingdeeAPPayables.Add(kingdeeAPPayable);
        }

        //foreach (var order in refundOrders)
        //{
        //    var amount = order.Cost;
        //    var costCurrencyCode = order.CostCurrencyCode;
        //    var exchangeRate = 1;
        //    var date = order.UpdateTime ?? order.CreateTime;
        //    KingdeeAPPayableDto kingdeeAPPayable = new()
        //    {
        //        BusinessType = KingdeeBusinessType.RefundOrder,
        //        BusinessOrderId = order.Id,
        //        Amount = amount,
        //        CurrencyCode = costCurrencyCode,
        //        TotalAmount = amount,
        //        BaseCurrencyCode = costCurrencyCode,
        //        ExchangeRate = exchangeRate,
        //        SupplierId = order.SupplierId,
        //        Date = date,
        //        Model = new()
        //        {
        //            FBillTypeID = new(_kingdeeDataConfig.FBillTypeID.Payable),
        //            FBillNo = order.Id.ToString(),
        //            FDATE = date,
        //            FSETTLEORGID = new(_kingdeeDataConfig.FSETTLEORGID),
        //            FPAYORGID = new(_kingdeeDataConfig.FPAYORGID),
        //            FPURCHASERGROUPID = new(_kingdeeDataConfig.FPURCHASERGROUPID),
        //            FPURCHASEDEPTID = new(_kingdeeDataConfig.FPURCHASEDEPTID.Other),
        //            FSUPPLIERID = new(""),//供应商
        //            FCURRENCYID = new(costCurrencyCode),
        //            FsubHeadFinc = new()
        //            {
        //                FACCNTTIMEJUDGETIME = date.ToShortDateString(),
        //                FMAINBOOKSTDCURRID = new(costCurrencyCode),
        //                FExchangeRate = exchangeRate,
        //                FEXCHANGETYPE = new(_kingdeeDataConfig.FEXCHANGETYPE),
        //            },
        //            FEntityDetail = new FentitydetailModel[] {
        //                new()
        //                {
        //                    F_qwe_Assistant = new(_kingdeeDataConfig.F_qwe_Assistant.Other),
        //                    FMATERIALID = new(_kingdeeDataConfig.FMATERIALID),
        //                    FPRICEUNITID = new(_kingdeeDataConfig.FPRICEUNITID),
        //                    FPriceQty = -1,
        //                    FTaxPrice = amount,
        //                    FEntryTaxRate = _kingdeeDataConfig.FEntryTaxRate,//税率
        //                }
        //            }
        //        }
        //    };
        //    kingdeeAPPayables.Add(kingdeeAPPayable);
        //}

        foreach (var offsetOrderDto in offsetOrders)
        {
            var order = offsetOrderDto.OffsetOrder;
            var amount = Math.Abs(order.Amount);
            var costCurrencyCode = order.CurrencyCode;
            var exchangeRate = 1;
            var date = order.CreatTime;
            if (offsetOrderDto.TravelDate.HasValue && offsetOrderDto.TravelDate > date)
                date = offsetOrderDto.TravelDate!.Value;

            KingdeeAPPayableDto kingdeeAPPayable = new()
            {
                BusinessOrderId = order.Id,
                BusinessType = KingdeeBusinessType.OffSetOrder,
                Amount = amount,
                CurrencyCode = costCurrencyCode,
                TotalAmount = amount,
                BaseCurrencyCode = costCurrencyCode,
                ExchangeRate = exchangeRate,
                SupplierId = order.SupplierId,
                AgencyId = order.AgencyId,
                Date = date,
                Model = new()
                {
                    FBillTypeID = new(_kingdeeDataConfig.FBillTypeID.PayableOffset),
                    FBillNo = order.Id.ToString(),
                    FDATE = date,

                    FSETTLEORGID = new(_kingdeeDataConfig.FSETTLEORGID),
                    FPAYORGID = new(_kingdeeDataConfig.FPAYORGID),
                    FPURCHASERGROUPID = new(_kingdeeDataConfig.FPURCHASERGROUPID),
                    FPURCHASEDEPTID = new(order.BusinessType == OffsetOrderBusinessType.HotelOrder ?
                    _kingdeeDataConfig.FPURCHASEDEPTID.Hotel : _kingdeeDataConfig.FPURCHASEDEPTID.Other),
                    FSUPPLIERID = new(""),//供应商
                    FCURRENCYID = new(costCurrencyCode),
                    FsubHeadFinc = new()
                    {
                        FACCNTTIMEJUDGETIME = date.ToShortDateString(),
                        FMAINBOOKSTDCURRID = new(costCurrencyCode),
                        FExchangeRate = exchangeRate,
                        FEXCHANGETYPE = new(_kingdeeDataConfig.FEXCHANGETYPE),
                    },
                    FEntityDetail = new FentitydetailModel[] {
                        new(){
                        F_qwe_Assistant = new(order.BusinessType == OffsetOrderBusinessType.HotelOrder ?
                            _kingdeeDataConfig.F_qwe_Assistant.Hotel : _kingdeeDataConfig.F_qwe_Assistant.Other),
                        F_qwe_Assistant1 = new(_kingdeeDataConfig.F_qwe_Assistant1.PayableOffset),
                        FMATERIALID = new(_kingdeeDataConfig.FMATERIALID),
                        FPRICEUNITID = new(_kingdeeDataConfig.FPRICEUNITID),
                        FPriceQty = order.Amount > 0 ? 1 : -1,
                        FTaxPrice = amount,
                        FEntryTaxRate = _kingdeeDataConfig.FEntryTaxRate,//税率
                        }
                     }
                }
            };
            kingdeeAPPayables.Add(kingdeeAPPayable);
        }
        return kingdeeAPPayables.Where(x => x.SupplierId > 0).ToList();
    }
    record TravelDateOffsetOrderDto
    {
        public DateTime? TravelDate { get; set; }
        public OffsetOrder OffsetOrder { get; set; }

        public long? AgencyId { get; set; }
    }

}