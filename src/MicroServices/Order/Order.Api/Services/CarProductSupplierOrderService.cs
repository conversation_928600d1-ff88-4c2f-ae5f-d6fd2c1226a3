using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Notify.Messages;
using Contracts.Common.Order.DTOs.CarProductSupplierOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.Fields.Group;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using DotNetCore.CAP;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Contracts;
using Order.Api.Services.OpenPlatform.Contracts.Supplier;
using Order.Api.Services.OpenPlatform.Interfaces;
using Order.Api.Services.Validator.CarProductSupplierOrder;

namespace Order.Api.Services;

/// <summary>
/// 开放平台-用车供应端下单服务
/// </summary>
public class CarProductSupplierOrderService : ICarProductSupplierOrderService
{
    private readonly IMapper _mapper;
    private readonly IRedisClient _redisClient;
    private readonly CustomDbContext _dbContext;
    private readonly ICapPublisher _capPublisher;
    private readonly ServicesAddress _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly ILogger<CarProductSupplierOrderService> _logger;
    
    private const int _successCode = 200;
    
    public CarProductSupplierOrderService(
        IMapper mapper,
        IRedisClient redisClient,
        CustomDbContext dbContext,
        ICapPublisher capPublisher,
        IHttpClientFactory httpClientFactory,
        IOpenSupplierService openSupplierService,
        ILogger<CarProductSupplierOrderService>  logger,
        IOptions<ServicesAddress> servicesAddressOptions
    )
    {
        _mapper = mapper;
        _logger = logger;
        _dbContext = dbContext;
        _redisClient = redisClient;
        _capPublisher = capPublisher;
        _httpClientFactory = httpClientFactory;
        _openSupplierService = openSupplierService;
        _servicesAddress = servicesAddressOptions.Value;
    }

    public async Task<QueryCarOrderByNotifyIdOutput> QueryByNotifyId(QueryCarOrderByNotifyIdInput input)
    {
        var carProductSupplierOrder = await _dbContext.CarProductSupplierOrderRecords
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.NotifyId == input.NotifyId)
            .Select(x=> new QueryCarOrderByNotifyIdOutput
            {
                BaseOrderId = x.BaseOrderId,
                TenantId = x.TenantId
            })
            .FirstOrDefaultAsync();
        
        return carProductSupplierOrder;
    }
    public async Task<List<CarSupplierOrderFuzzySearchOutput>> FuzzySearch(CarSupplierOrderFuzzySearchInput input,long tenantId)
    {
        var searchResult = await _openSupplierService.CarAddressFuzzySearch(
            new SupplierCarAddressFuzzySearchRequest
            {
                SupplierType = input.SupplierType,
                Address = input.Address,
            },tenantId);

        var result = searchResult.Data.Items.Select(x =>
                new CarSupplierOrderFuzzySearchOutput
                {
                    Address = x.Address, 
                    DetailAddress = x.DetailAddress,
                })
            .ToList();

        return result;
    }

    #region 询价流程 1.发起询价,2.询价回调通知处理 - 目前已移除这个流程,不在订单业务流中处理

    [UnitOfWork]
    public async Task InitiateQuote(CarProductSupplierOrderQuoteMessage receive)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == receive.BaseOrderId);
        var carProductOrder = await _dbContext.CarProductOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
        var supplierOrder = await _dbContext.CarProductSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);

        //目前支持mozio供应商
        if (carProductOrder.OpenSupplierType != OpenSupplierType.Mozio)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //saas用车订单状态检查
        if (carProductOrder.Status != CarProductOrderStatus.WaitingForConfirm)
        {
            _logger.LogError("[OpenSupplier]InitiateQuote:{@Input},{@Message}", receive, "订单已确认");
            return;
        }

        //api订单状态询价检查
        var supplierOrderIsExist = supplierOrder != null;
        if (supplierOrder != null)
        {
            //允许重新询价,需要判断是否支持重新询价(1.订单状态为待询价,2.订单状态为询价失败,3.订单状态为预约失败)
            if (supplierOrder.OrderStatus != CarProductSupplierOrderStatus.PendingQuote &&
                supplierOrder.OrderStatus != CarProductSupplierOrderStatus.QuoteFailed &&
                supplierOrder.OrderStatus != CarProductSupplierOrderStatus.ReservationFailed)
            {
                _logger.LogError("[OpenSupplier]InitiateQuote:{@Input},{@Message}", receive, "订单已询价,不允许重新询价");
                return;
            }

            supplierOrder.UpdateTime = DateTime.Now;
        }
        else
        {
            //init
            supplierOrder = new CarProductSupplierOrder
            {
                BaseOrderId = baseOrder.Id,
                CarProductOrderId = carProductOrder.Id,
                SupplierId = carProductOrder.SupplierId,
                Amount = 0,
                SupplierType = carProductOrder.OpenSupplierType,
            };
            supplierOrder.SetTenantId(carProductOrder.TenantId);
        }

        //查询租户,供应信息
        var tenantId = carProductOrder.TenantId;
        var supplier = await GetSupplier(carProductOrder.SupplierId, tenantId);

        #region initialize

        //币种赋值
        supplierOrder.Currency = supplier.CurrencyCode;

        //操作记录
        var supplierOrderRecord = new CarProductSupplierOrderRecord
        {
            BaseOrderId = baseOrder.Id, RecordType = CarProductSupplierOrderRecordType.Quote
        };
        supplierOrderRecord.SetTenantId(tenantId);

        var quoteRequest = new SupplierCarOrderQuoteRequest
        {
            SupplierType = carProductOrder.OpenSupplierType.ToString().ToLowerInvariant(),
            UseCarType = carProductOrder.CarProductType.ToString().ToLowerInvariant(),
            UseTime = carProductOrder.TravelDate.ToString("yyyy-MM-dd HH:mm:ss"),
            PassengerCount = carProductOrder.Passengers,
            Currency = supplier.CurrencyCode,
            BookingDuration = 0,
            DepartLocation = new SupplierCarOrderDepartLocationInfo(),
            ArrivalLocation = new SupplierCarOrderArrivalLocationInfo(),
        };

        //询价数据补充
        var orderFieldTypeInfos = await GetOrderFieldInfo(baseOrder.Id);
        var travelOrderFieldInfos = orderFieldTypeInfos.TravelFieldInfo
            .SelectMany(x => x.Fields)
            .ToList();
        var travelSupplementData = await TravelDataSupplement(
            carProductOrder,
            travelOrderFieldInfos);

        //订单确认信息数据
        var orderConfirmFieldInfos = orderFieldTypeInfos.OrderConfirmFieldInfo
            .SelectMany(x => x.Fields)
            .ToList();
        var orderConfirmSupplementData = OrderConfirmDataSupplement(orderConfirmFieldInfos);

        //出发地,目的地信息补充
        quoteRequest.DepartLocation = travelSupplementData.Departure;
        quoteRequest.ArrivalLocation = travelSupplementData.Arrival;

        //用车时长信息补充
        if (carProductOrder.CarProductType == CarProductType.CarChartered)
        {
            //用车类型是 包车 时必填
            if (travelSupplementData.CharterCarDays > 0)
            {
                quoteRequest.BookingDuration = travelSupplementData.CharterCarDays * 24;
            }

            if (quoteRequest.BookingDuration <= 0 && orderConfirmSupplementData.CharterCarDays > 0)
            {
                quoteRequest.BookingDuration = orderConfirmSupplementData.CharterCarDays * 24;
            }
        }

        var quoteRequestValidationResultMsg = ValidateQuoteRequest(quoteRequest, carProductOrder.CarProductType);

        #endregion

        //发起询价
        var quoteResponse = await _openSupplierService.CarOrderQuote(quoteRequest, tenantId);
        if (quoteResponse.Code != _successCode)
        {
            //发起询价失败
            supplierOrder.OrderStatus = CarProductSupplierOrderStatus.QuoteFailed;
            supplierOrderRecord.ResultMessage = quoteResponse.Msg;
            supplierOrderRecord.ResultCode = quoteResponse.Code.ToString();

            if (quoteResponse.Code == (int)OrderBusinessErrorCodeType.ParameterError)
            {
                //参数错误,处理下提示
                supplierOrderRecord.ResultMessage = quoteRequestValidationResultMsg;
            }
        }
        else
        {
            //发起询价成功,保存通知id,等待回调
            supplierOrder.OrderStatus = CarProductSupplierOrderStatus.Quoting;
            supplierOrderRecord.NotifyId = quoteResponse.Data.NotifyId;
        }

        if (!supplierOrderIsExist)
            await _dbContext.AddAsync(supplierOrder);

        await _dbContext.AddAsync(supplierOrderRecord);
    }

    [UnitOfWork]
    public async Task QuoteNotifyProcess(CarProductSupplierOrderQuoteNotifyInput input)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var carProductOrder = await _dbContext.CarProductOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.CarProductSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrderRecord = await _dbContext.CarProductSupplierOrderRecords.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId && x.NotifyId == input.NotifyId);

        //目前支持mozio供应商
        if (carProductOrder.OpenSupplierType != OpenSupplierType.Mozio)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //saas用车订单状态检查
        if (carProductOrder.Status != CarProductOrderStatus.WaitingForConfirm)
        {
            _logger.LogError("[OpenSupplier]QuoteNotifyProcess:{@Input},{@Message}", input, "订单已确认");
            return;
        }

        //api订单状态询价检查
        if (supplierOrder.OrderStatus != CarProductSupplierOrderStatus.Quoting)
        {
            _logger.LogError("[OpenSupplier]QuoteNotifyProcess:{@Input},{@Message}", input, "订单状态不是询价中");
            return;
        }

        //notifyId检查
        if (supplierOrderRecord is null)
        {
            _logger.LogError("[OpenSupplier]QuoteNotifyProcess:{@Input},{@Message}", input, "查无询价通知记录");
            return;
        }

        //询价数据校验
        //1.查询符合最大乘客数和最大行李数 2.取价格最低的一项产品
        var passengers = carProductOrder.Passengers;
        var baggages = carProductOrder.Baggages;
        var quoteResult = input.Results
            .Where(x => x.Vehicle.MaxPassengerCount >= passengers && x.Vehicle.MaxLuggageCount >= baggages)
            .MinBy(x => x.Price);

        if (quoteResult is null)
        {
            //查无符合数据.更新最近一条询价通知结果
            supplierOrderRecord.ResultMessage = "查无符合条件的用车信息";
            supplierOrderRecord.UpdateTime = DateTime.Now;

            supplierOrder.OrderStatus = CarProductSupplierOrderStatus.QuoteFailed;
            supplierOrder.UpdateTime = DateTime.Now;
        }
        else
        {
            #region initialize

            var reserveRequest = new SupplierCarOrderReservationsRequest
            {
                SupplierType = carProductOrder.OpenSupplierType.ToString().ToLowerInvariant(),
                SearchId = input.SearchId,
                OrderId = baseOrder.Id.ToString(),
                ResultId = quoteResult.ResultId,
                Price = quoteResult.Price,
                Currency = supplierOrder.Currency,
                UseTime = carProductOrder.TravelDate.ToString("yyyy-MM-dd HH:mm:ss"),
                OrderRemark = baseOrder.Message,
                DepartLocation = new SupplierCarOrderDepartLocationInfo(),
                ArrivalLocation = new SupplierCarOrderArrivalLocationInfo(),
                PassengerCount = new SupplierCarOrderPassengerCountInfo(),
                ContactPerson = new SupplierCarOrderContactPersonInfo(),
                FlightDetail = new SupplierCarOrderFlightDetailInfo(),
            };

            //乘客数量信息补充
            reserveRequest.PassengerCount.Adult = passengers;
            reserveRequest.PassengerCount.Luggage = baggages;

            var orderFieldTypeInfos = await GetOrderFieldInfo(baseOrder.Id);
            var travelOrderFieldInfos = orderFieldTypeInfos.TravelFieldInfo
                .SelectMany(x => x.Fields)
                .ToList();
            //地点信息补充
            var travelSupplementData = await TravelDataSupplement(
                carProductOrder,
                travelOrderFieldInfos);
            reserveRequest.DepartLocation = travelSupplementData.Departure;
            reserveRequest.ArrivalLocation = travelSupplementData.Arrival;

            //航班信息补充
            reserveRequest.FlightDetail = travelSupplementData.FlightDetail;

            //联系人信息补充
            var contactsOrderFieldInfos = orderFieldTypeInfos.ContactsFieldInfo
                .SelectMany(x => x.Fields)
                .ToList();
            var personSupplementData = PersonDataSupplement(contactsOrderFieldInfos);
            reserveRequest.ContactPerson = personSupplementData;

            //订单备注信息补充
            if (string.IsNullOrEmpty(reserveRequest.OrderRemark))
            {
                //客户备注为空,使用最新一条订单备注
                var orderRemark = await _dbContext.BaseOrderRemarks.IgnoreQueryFilters()
                    .Where(x => x.BaseOrderId == baseOrder.Id)
                    .OrderByDescending(x => x.CreateTime)
                    .Select(x => x.Remark)
                    .FirstOrDefaultAsync();

                reserveRequest.OrderRemark = orderRemark;
            }

            var reserveRequestValidationResultMsg =
                ValidateReservationRequest(reserveRequest, travelSupplementData.IncludeAirport);

            //预订操作记录
            var reserveRecord = new CarProductSupplierOrderRecord
            {
                BaseOrderId = baseOrder.Id,
                RecordType = CarProductSupplierOrderRecordType.Reservations
            };
            reserveRecord.SetTenantId(baseOrder.TenantId);

            #endregion

            //发起预订
            var reserveResponse =
                await _openSupplierService.CarOrderReservations(reserveRequest, supplierOrder.TenantId);
            if (reserveResponse.Code != _successCode)
            {
                //预订失败
                reserveRecord.ResultMessage = reserveResponse.Msg;
                reserveRecord.ResultCode = reserveResponse.Code.ToString();
                reserveRecord.UpdateTime = DateTime.Now;

                supplierOrder.OrderStatus = CarProductSupplierOrderStatus.ReservationFailed;
                supplierOrder.UpdateTime = DateTime.Now;

                if (reserveResponse.Code == (int)OrderBusinessErrorCodeType.ParameterError)
                {
                    //参数错误,处理下提示
                    reserveRecord.ResultMessage = reserveRequestValidationResultMsg;
                }
            }
            else
            {
                //预订成功,保存notifyId等信息,等待回调
                reserveRecord.NotifyId = reserveResponse.Data.NotifyId;
                supplierOrder.Amount = quoteResult.Price;
                supplierOrder.SearchId = input.SearchId;
                supplierOrder.ResultId = quoteResult.ResultId;
                supplierOrder.OrderStatus = CarProductSupplierOrderStatus.Reserving;
                supplierOrder.UpdateTime = DateTime.Now;
            }

            await _dbContext.AddAsync(reserveRecord);
        }
    }

    #endregion

    #region 预订流程 1.发起预订,2.预订回调通知处理

    [UnitOfWork]
    public async Task InitiateReservation(CarSupplierOrderReservationInput input)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var carProductOrder = await _dbContext.CarProductOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.CarProductSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierQuote = await _dbContext.CarProductOrderSupplierQuote.IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == carProductOrder.BaseOrderId)
            .FirstOrDefaultAsync();
        
        //查询租户,供应信息
        var tenantId = carProductOrder.TenantId;
        var supplier = await GetSupplier(carProductOrder.SupplierId, tenantId);

        //目前支持mozio供应商
        if (carProductOrder.OpenSupplierType != OpenSupplierType.Mozio)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //saas用车订单状态检查
        if (carProductOrder.Status != CarProductOrderStatus.WaitingForConfirm)
        {
            _logger.LogError("[OpenSupplier]InitiateReservation:{@Input},{@Message}", input, "订单已确认");
            return;
        }
        
        //api订单状态预订检查
        var supplierOrderIsExist = supplierOrder != null;
        if (supplierOrder != null)
        {
            //允许重新预订,需要判断是否支持重新预订(1.订单状态为预订失败)
            if (supplierOrder.OrderStatus != CarProductSupplierOrderStatus.ReservationFailed)
            {
                _logger.LogError("[OpenSupplier]InitiateReservation:{@Input},{@Message}", input, "已关联预定单,不允许重复预订");
                return;
            }

            supplierOrder.UpdateTime = DateTime.Now;
        }
        else
        {
            //init
            supplierOrder = new CarProductSupplierOrder
            {
                BaseOrderId = baseOrder.Id,
                CarProductOrderId = carProductOrder.Id,
                SupplierId = carProductOrder.SupplierId,
                Amount = 0,
                Currency = supplier.CurrencyCode,
                SupplierType = carProductOrder.OpenSupplierType
            };
            supplierOrder.SetTenantId(carProductOrder.TenantId);
        }

        #region initialize
        
        //预订请求数据初始化
        var reserveRequest = new SupplierCarOrderReservationsRequest
        {
            SupplierType = carProductOrder.OpenSupplierType.ToString().ToLowerInvariant(),
            OrderId = baseOrder.Id.ToString(),
            Currency = supplierOrder.Currency,
            UseTime = carProductOrder.TravelDate.ToString("yyyy-MM-dd HH:mm:ss"),
            OrderRemark = baseOrder.Message,
            DepartLocation = new SupplierCarOrderDepartLocationInfo(),
            ArrivalLocation = new SupplierCarOrderArrivalLocationInfo(),
            PassengerCount = new SupplierCarOrderPassengerCountInfo(),
            ContactPerson = new SupplierCarOrderContactPersonInfo(),
            FlightDetail = new SupplierCarOrderFlightDetailInfo(),
            AdditionalServices = new List<SupplierCarOrderAdditionalServicesInfo>()
        };
        var validationResultMsg = string.Empty;
        await ReservationRequestSupplement(
            carProductOrder: carProductOrder,
            supplierQuote: supplierQuote,
            reserveRequest: reserveRequest,
            onValidationResultMsgSet: m => validationResultMsg = m);

        #endregion

        //发起预订
        var reserveResponse = new ApiBaseResponse<SupplierCarOrderReservationsResponse>();
        if (string.IsNullOrEmpty(validationResultMsg))
        {
            //saas验证通过,发起预订
            reserveResponse = await _openSupplierService.CarOrderReservations(reserveRequest, tenantId);
        }
        else
        {
            //saas验证不通过,发起预订失败
            reserveResponse.Code = (int)OrderBusinessErrorCodeType.ParameterError;
            reserveResponse.Msg = validationResultMsg;
        }
        
        //预订操作记录
        var reserveRecord = new CarProductSupplierOrderRecord
        {
            BaseOrderId = baseOrder.Id,
            RecordType = CarProductSupplierOrderRecordType.Reservations,
            ResultCode = reserveResponse.Code.ToString(),
            UpdateTime = DateTime.Now
        };
        reserveRecord.SetTenantId(baseOrder.TenantId);
        
        //发起预订结果处理
        if (reserveResponse.Code != _successCode)
        {
            //预订失败
            //记录失败消息
            reserveRecord.ResultMessage = reserveResponse.Msg;
            
            //更新api订单状态
            supplierOrder.OrderStatus = CarProductSupplierOrderStatus.ReservationFailed;
            supplierOrder.UpdateTime = DateTime.Now;
        }
        else
        {
            //预订发起成功
            //保存notifyId等信息,等待回调
            reserveRecord.NotifyId = reserveResponse.Data.NotifyId;
            
            //更新api订单状态
            supplierOrder.Amount = reserveRequest.Price;
            supplierOrder.SearchId = reserveRequest.SearchId;
            supplierOrder.ResultId = reserveRequest.ResultId;
            supplierOrder.OrderStatus = CarProductSupplierOrderStatus.Reserving;
            supplierOrder.UpdateTime = DateTime.Now;
        }

        await _dbContext.AddAsync(reserveRecord);
        
        if (!supplierOrderIsExist)
            await _dbContext.AddAsync(supplierOrder);
    }

    [UnitOfWork]
    public async Task ReservationNotifyProcess(CarProductSupplierOrderReservationNotifyInput input)
    {
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var carProductOrder = await _dbContext.CarProductOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.CarProductSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrderRecord = await _dbContext.CarProductSupplierOrderRecords.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId && x.NotifyId == input.NotifyId);

        //目前支持mozio供应商
        if (carProductOrder.OpenSupplierType != OpenSupplierType.Mozio)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //saas用车订单状态检查
        if (carProductOrder.Status != CarProductOrderStatus.WaitingForConfirm)
        {
            _logger.LogError("[OpenSupplier]ReservationNotifyProcess:{@Input},{@Message}", input, "订单已确认");
            return;
        }

        //api订单状态询价检查
        if (supplierOrder.OrderStatus != CarProductSupplierOrderStatus.Reserving)
        {
            _logger.LogError("[OpenSupplier]ReservationNotifyProcess:{@Input},{@Message}", input, "订单状态不是预订中");
            return;
        }

        //notifyId检查
        if (supplierOrderRecord is null)
        {
            _logger.LogError("[OpenSupplier]ReservationNotifyProcess:{@Input},{@Message}", input, "查无询价通知记录");
            return;
        }

        //操作记录更新
        supplierOrderRecord.ResultMessage = input.ResultMessage;
        supplierOrderRecord.UpdateTime = DateTime.Now;

        switch (input.OrderStatus)
        {
            case CarProductSupplierOrderReservationNotifyStatus.Pending:
                supplierOrder.OrderStatus = CarProductSupplierOrderStatus.Reserving;
                supplierOrder.UpdateTime = DateTime.Now;
                break;
            case CarProductSupplierOrderReservationNotifyStatus.Failed:
                supplierOrder.OrderStatus = CarProductSupplierOrderStatus.ReservationFailed;
                supplierOrder.UpdateTime = DateTime.Now;
                break;
            case CarProductSupplierOrderReservationNotifyStatus.Completed:

                //更新api订单状态
                supplierOrder.ConfirmCode = input.ConfirmationNumber;
                supplierOrder.SupplierOrderId = input.OutOrderId;
                supplierOrder.OrderStatus = CarProductSupplierOrderStatus.Completed;
                supplierOrder.UpdateTime = DateTime.Now;

                //更新saas用车订单状态
                carProductOrder.ConfirmCode = input.ConfirmationNumber;
                carProductOrder.SupplierOrderId = supplierOrder.SupplierOrderId;
                carProductOrder.Status = CarProductOrderStatus.Confirmed;
                carProductOrder.ConfirmTime = DateTime.Now;
                carProductOrder.UpdateTime = DateTime.Now;
                var confirmedLog = new OrderLogs
                {
                    OperationType = OrderOperationType.Confirmed,
                    OrderLogType = OrderLogType.CarProduct,
                    OrderId = baseOrder.Id,
                    UserId = 0,
                    UserName = "System",
                    OperationRole = UserType.None,
                };
                confirmedLog.SetTenantId(baseOrder.TenantId);
                await _dbContext.AddAsync(confirmedLog);
                
                //ota渠道发货
                await _capPublisher.PublishAsync(CapTopics.Order.CarProductOtaSyncDelivery,
                    new CarProductOtaSyncDeliveryMessage
                    {
                        BaseOrderId = baseOrder.Id,
                        CarProductOrderId = carProductOrder.Id,
                        ChannelOrderNo = baseOrder.ChannelOrderNo,
                        SellingPlatform = baseOrder.SellingPlatform,
                        TenantId = baseOrder.TenantId,
                        AgencyId = baseOrder.AgencyId
                    });
                
                
                /*
                 * Saas订单状态为待确认，提示飞猪渠道插旗为蓝色
                 * Saas订单状态为已确认/完成，提示飞猪渠道插旗为红色
                 */
                await _capPublisher.PublishAsync(CapTopics.Order.ChannelOrderFlagModify, new ChannelOrderFlagModifyMessage
                {
                    ChannelOrderNo = baseOrder.ChannelOrderNo,
                    SellingPlatform = baseOrder.SellingPlatform,
                    FlagType = OpenChannelOrderFlagType.Red,
                    AgencyId = baseOrder.AgencyId,
                    TenantId = baseOrder.TenantId
                });

                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        if (carProductOrder.Status == CarProductOrderStatus.Confirmed)
        {
            //B2B系统通知
            await _capPublisher.PublishAsync(CapTopics.Notify.MessageProcess,
                new NotifyMessageProcessMessage
                {
                    NotifyEventSubType =
                        Contracts.Common.Notify.Enums.NotifyEventSubType.CarProduct_OrderConfirmed,
                    NotifyMode = Contracts.Common.Notify.Enums.NotifyMode.SiteMessage,
                    SendToTheRole = Contracts.Common.Notify.Enums.SendToTheRole.AgencyStaff,
                    TenantId = baseOrder.TenantId,
                    Variables = new {BaseOrderId = baseOrder.Id, baseOrder.AgencyId, baseOrder.UserId}
                });
        }
    }

    [UnitOfWork]
    public async Task<CancelCarProductSupplierOrderOutput> Cancel(CancelCarProductSupplierOrderInput input)
    {
        var result = new CancelCarProductSupplierOrderOutput();
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == input.BaseOrderId);
        var carProductOrder = await _dbContext.CarProductOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var supplierOrder = await _dbContext.CarProductSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);

        //目前支持mozio供应商
        if (carProductOrder.OpenSupplierType != OpenSupplierType.Mozio)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        //api订单状态检查
        if (supplierOrder.OrderStatus != CarProductSupplierOrderStatus.Completed)
        {
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }

        #region initialize

        var cancelRequest = new SupplierCarOrderCancelRequest
        {
            SupplierType = carProductOrder.OpenSupplierType.ToString().ToLowerInvariant(),
            OutOrderId = supplierOrder.SupplierOrderId
        };

        //操作记录
        var cancelRecord = new CarProductSupplierOrderRecord
        {
            BaseOrderId = baseOrder.Id, RecordType = CarProductSupplierOrderRecordType.Cancel,
        };
        cancelRecord.SetTenantId(baseOrder.TenantId);

        #endregion

        //执行api订单取消
        var cancelResponse = await _openSupplierService.CarOrderCancel(cancelRequest, baseOrder.TenantId);
        if (cancelResponse.Code != _successCode)
        {
            //取消操作失败
            cancelRecord.ResultMessage = cancelResponse.Msg;
            cancelRecord.UpdateTime = DateTime.Now;

            //返回结果失败信息
            result.ResultMessage = cancelResponse.Msg;
        }
        else
        {
            //取消操作成功,判断处理结果,更新api订单状态
            cancelRecord.ResultCode = cancelResponse.Data.ResultCode;
            cancelRecord.ResultMessage = cancelResponse.Data.ResultMessage;
            cancelRecord.UpdateTime = DateTime.Now;

            var successCode = CarProductSupplierOrderCancelResultCodeType.SUCCESSED.ToString().ToLowerInvariant();
            if (cancelResponse.Data.ResultCode.Equals(successCode, StringComparison.OrdinalIgnoreCase))
            {
                //返回结果成功信息
                result.IsSuccess = true;

                //api订单取消成功,更新saas用车订单状态
                supplierOrder.OrderStatus = CarProductSupplierOrderStatus.Canceled;
                supplierOrder.UpdateTime = DateTime.Now;
            }

            //返回结果信息
            result.ResultMessage = cancelResponse.Data.ResultMessage;
        }

        await _dbContext.AddAsync(cancelRecord);


        return result;
    }

    #endregion
    
    record TravelDataSupplementData(
        SupplierCarOrderDepartLocationInfo? Departure,
        SupplierCarOrderArrivalLocationInfo? Arrival,
        SupplierCarOrderFlightDetailInfo? FlightDetail,
        bool IncludeAirport,
        int CharterCarDays);
    /// <summary>
    /// 地点信息补充
    /// </summary>
    /// <returns></returns>
    private async Task<TravelDataSupplementData> TravelDataSupplement(
        CarProductOrder carProductOrder,
        List<OrderFieldInformationOutput> orderFieldInfos)
    {
        SupplierCarOrderDepartLocationInfo? departure = null;
        SupplierCarOrderArrivalLocationInfo? arrival = null;
        SupplierCarOrderFlightDetailInfo? flightDetail = null;
        
        ProductTemplateType? productTemplateType = carProductOrder.CarProductType switch
        {
            CarProductType.AirportTransfer => carProductOrder.AirportTransferType switch
            {
                AirportTransferType.PickUp => ProductTemplateType.AirportTransferPickUp,
                AirportTransferType.DropOff => ProductTemplateType.AirportTransferDropOff,
                _ => null
            },
            CarProductType.PointToPointTransfer => ProductTemplateType.PointToPointTransfer,
            CarProductType.CarChartered => ProductTemplateType.CarChartered,
            _ => null
        };

        if (productTemplateType == null)
        {
            return new TravelDataSupplementData(null, null, null, true, 0);
        }
        
        var orderFields = orderFieldInfos
            .Where(x => x.ProductTemplateType == productTemplateType)
            .ToList();

        //上车地点和送达地点是否包含机场资源
        var includeAirport = false;
        //上车地点
        var departureJson = orderFields.FirstOrDefault(x => x.FieldCode == "DepartureJson")
            ?.FieldValue;
        if (!string.IsNullOrEmpty(departureJson))
        {
            var departureData = JsonConvert.DeserializeObject<AddressDto>(departureJson);
            if (departureData.Detail is {Latitude: not null, Longitude: not null})
            {
                departure = new SupplierCarOrderDepartLocationInfo
                {
                    Latitude = departureData.Detail.Latitude?.ToString(),
                    Longitude = departureData.Detail.Longitude?.ToString()
                };
            }
            
            //检查是否包含机场资源
            if(departureData.Detail.ResourceType == EsAggregateResourceParentType.Airport)
                includeAirport = true;
        }

        //送达地点
        var destinationJson = orderFields.FirstOrDefault(x => x.FieldCode == "DestinationJson")
            ?.FieldValue;
        if (!string.IsNullOrEmpty(destinationJson))
        {
            var destinationData = JsonConvert.DeserializeObject<AddressDto>(destinationJson);
            if (destinationData.Detail is {Latitude: not null, Longitude: not null})
            {
                arrival = new SupplierCarOrderArrivalLocationInfo
                {
                    Latitude = destinationData.Detail.Latitude?.ToString(),
                    Longitude = destinationData.Detail.Longitude?.ToString()
                };
            }
            
            //检查是否包含机场资源
            if(destinationData.Detail.ResourceType == EsAggregateResourceParentType.Airport)
                includeAirport = true;
        }
        
        //航班信息
        //如果出发地和目的地存在机场.则需要航班信息.如果都不是机场,则不需要航班信息
        if (includeAirport)
        {
            var flightNumberFieldValue = orderFields.FirstOrDefault(x => x.FieldCode == "FlightNumber")
                ?.FieldValue;
            if (!string.IsNullOrEmpty(flightNumberFieldValue))
            {
                flightDetail = new SupplierCarOrderFlightDetailInfo
                {
                    FlightNumber = flightNumberFieldValue, 
                    //航线取值的航班号的前两位,需要校验航班号是否有2位
                    Airline = flightNumberFieldValue.Length >= 2 
                        ? flightNumberFieldValue[..2]
                        : flightNumberFieldValue
                };
            }
        }

        //包车天数
        var charterCarDays = 0;
        var charterCarDaysFieldValue = orderFields.FirstOrDefault(x => x.FieldCode == "CharterCarDays")
            ?.FieldValue;
        if (!string.IsNullOrEmpty(charterCarDaysFieldValue))
        {
            charterCarDays = Convert.ToInt32(charterCarDaysFieldValue);
        }

        return new TravelDataSupplementData(departure,arrival,flightDetail,includeAirport,charterCarDays);

    }

    record OrderConfirmDataSupplementData(int CharterCarDays);
    /// <summary>
    /// 订单确认信息补充
    /// </summary>
    private OrderConfirmDataSupplementData OrderConfirmDataSupplement(List<OrderFieldInformationOutput> orderFieldInfos)
    {
        //包车天数
        var charterCarDays = 0;
        var charterCarDaysFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "CharterCarDays"
                                 && !string.IsNullOrEmpty(x.FieldValue))
            ?.FieldValue;
        if (!string.IsNullOrEmpty(charterCarDaysFieldValue))
        {
            charterCarDays = Convert.ToInt32(charterCarDaysFieldValue);
        }
        
        return new OrderConfirmDataSupplementData(charterCarDays);
    }
    
    /// <summary>
    /// 联系人信息补充
    /// </summary>
    private SupplierCarOrderContactPersonInfo PersonDataSupplement(List<OrderFieldInformationOutput> orderFieldInfos)
    {
        var result = new SupplierCarOrderContactPersonInfo();
        
        //联系人姓名
        var contactNameFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "ContactName")?.FieldValue;
        if (!string.IsNullOrEmpty(contactNameFieldValue))
        {
            //由于可能出现`名`和`姓`的字段模板没配置.这里默认取`联系人姓名`
            //默认取第一字当姓.
            result.LastName = contactNameFieldValue[..1];
            result.FirstName = contactNameFieldValue[1..];
        }
        
        var contactPhoneFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "ContactPhone")?.FieldValue;
        if (!string.IsNullOrEmpty(contactPhoneFieldValue))
        {
            var contactPhoneData = JsonConvert.DeserializeObject<PhoneDto>(contactPhoneFieldValue);
            result.Phone = contactPhoneData.Value;
            //区号处理 `[+86]` => `+86`
            result.AreaCode = contactPhoneData.Type.TrimStart('[').TrimEnd(']');
        }

        var contactEmailFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "ContactEmail")?.FieldValue;
        if (!string.IsNullOrEmpty(contactEmailFieldValue))
        {
            result.Email = contactEmailFieldValue;
        }
        
        //英文名
        var firstNameFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "FirstName")?.FieldValue;
        if (!string.IsNullOrEmpty(firstNameFieldValue))
        {
            result.FirstName = firstNameFieldValue;
        }
        
        
        //英文姓
        var lastNameFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "LastName")?.FieldValue;
        if (!string.IsNullOrEmpty(lastNameFieldValue))
        {
            result.LastName = lastNameFieldValue;
        }

        return result;
    }

    record OrderFieldData(
        List<OrderFieldInformationTypeOutput> TravelFieldInfo,
        List<OrderFieldInformationTypeOutput> ContactsFieldInfo,
        List<OrderFieldInformationTypeOutput> OrderConfirmFieldInfo
        );
    /// <summary>
    /// 查询订单的字段信息
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    private async Task<OrderFieldData> GetOrderFieldInfo(long baseOrderId)
    {
        var orderFieldTypes = await _dbContext.OrderFieldInformationType
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrderId)
            .ToListAsync();
        var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
        var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
        var orderFields = await _dbContext.OrderFieldInformations
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
            .ToListAsync();
        var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
        orderFieldTypesOut.ForEach(type =>
        {
            var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort).ToList();
            type.Fields = fields;
        });

        var travelFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Travel)
            .ToList();
        var contactsFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Contacts)
            .ToList();
        var orderConfirmFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Order)
            .ToList();

        return new OrderFieldData(
            TravelFieldInfo: travelFieldInfo,
            ContactsFieldInfo: contactsFieldInfo,
            OrderConfirmFieldInfo: orderConfirmFieldInfo);
    }
    private async Task<GetSupplierOutput> GetSupplier(long supplierId,long tenantId)
    {
        var header = new  List<KeyValuePair<string,string>>
        {
            new("tenant",tenantId.ToString())
        };
        var supplier = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
            requestUri: _servicesAddress.Tenant_GetSupplier(supplierId),
            headers: header);
        return supplier;
    }

    
    /// <summary>
    /// 计算金额是否除得尽(2位小数),如果可除得尽,返回原始单价,如果不除不尽,返回差价单价
    /// </summary>
    /// <param name="AvgCostPrice">平均成本价</param>
    /// <param name="DiffCostPrice">加上差额后的成本价</param>
    record CalculateDivisibleAmountData(decimal AvgCostPrice, decimal? DiffCostPrice);
    private CalculateDivisibleAmountData CalculateDivisibleAmount(decimal amount,int count)
    {
        // 计算原始的单价
        var rawSupplierSkuPrice = amount / count;
                    
        // 对原始单价进行四舍五入(保留2位小数)
        var openSupplierSkuPrice = Math.Round(rawSupplierSkuPrice, 2);
        
        //判断是否除不尽
        var threshold = 0;
        bool isDivisible = Math.Abs(rawSupplierSkuPrice - openSupplierSkuPrice) == threshold;

        decimal? diffCostPrice= null;
        if (!isDivisible)
        {
            //除不尽,处理差值
            var diffPrice = amount - (count * openSupplierSkuPrice);
            diffCostPrice = openSupplierSkuPrice + diffPrice;
        }
        
        return new CalculateDivisibleAmountData(openSupplierSkuPrice,diffCostPrice);
    }
    
    //询价入参校验,返回确实参数错误提示
    private string ValidateQuoteRequest(SupplierCarOrderQuoteRequest quoteRequest, CarProductType carProductType)
    {
        var validator = new SupplierCarOrderQuoteRequestValidator(carProductType);
        var result = validator.Validate(quoteRequest);
        if (!result.IsValid)
        {
            return result.Errors.First().ErrorMessage;
        }
        return string.Empty;
    }

    private string ValidateReservationRequest(SupplierCarOrderReservationsRequest reservationRequest,
        bool includeAirport)
    {
        var validator = new SupplierCarOrderReservationsRequestValidator(includeAirport);
        var result = validator.Validate(reservationRequest);
        if (!result.IsValid)
        {
            return result.Errors.First().ErrorMessage;
        }
        return string.Empty;
    }
    
    /// <summary>
    /// 比价
    /// <remarks>支付金额 - 采购金额后的值>=阈值，才可以预定;如不设置则不判断</remarks>
    /// </summary>
    /// <param name="BaseOrderId">主订单id</param>
    /// <param name="CarProductOrder">用车订单id</param>
    /// <param name="MinProfit">最低阈值</param>
    /// <param name="QuotePrice">供应商询价结果</param>
    /// <returns></returns>
    record ComparePriceInput(long BaseOrderId,
        long CarProductOrderId,
        decimal? MinProfit,
        decimal QuotePrice);
    private async Task<bool> ComparePrice(ComparePriceInput input)
    {
        var compareResult = true;
        if (input.MinProfit.HasValue is false)
            return compareResult;
        
        //查询价格
        var orderPrice = await _dbContext.OrderPrices
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => x.BaseOrderId == input.BaseOrderId && x.SubOrderId == input.CarProductOrderId)
            .FirstOrDefaultAsync();

        //阈值范围币种是商户币种.需要换算
        var costAmount = (input.QuotePrice * orderPrice.CostExchangeRate) * orderPrice.Quantity;
        var paymentAmount = (orderPrice.Price / orderPrice.ExchangeRate) * orderPrice.Quantity;
        
        //支付金额 - 采购金额后的值>=阈值，
        compareResult = (paymentAmount - costAmount) >= input.MinProfit;
        return compareResult;
    }

    /// <summary>
    /// 预定请求数据补充
    /// </summary>
    /// <param name="carProductOrder"></param>
    /// <param name="supplierQuote"></param>
    /// <param name="reserveRequest"></param>
    /// <param name="validationResultMsg"></param>
    private async Task ReservationRequestSupplement(
        CarProductOrder carProductOrder,
        CarProductOrderSupplierQuote? supplierQuote,
        SupplierCarOrderReservationsRequest reserveRequest,
        Action<string> onValidationResultMsgSet)
    {
        var validationResultMsg = string.Empty;
        //询价数据
        if (supplierQuote is null || string.IsNullOrEmpty(supplierQuote.Results))
        {
            //查无询价数据,预订失败
            validationResultMsg = "未找到询价信息,预订失败";
            onValidationResultMsgSet(validationResultMsg);
            return;
        }
        var quoteJObject = JsonConvert.DeserializeObject<SupplierCarOrderQuoteResultItem>(supplierQuote.Results);
        reserveRequest.SearchId = supplierQuote.SearchId;
        reserveRequest.ResultId = supplierQuote.ResultId;
        reserveRequest.Price = quoteJObject.Price;
        
        //附加服务项目查询
        var additionalServicesRemarks = new List<string>();
        var carServiceItems = await _dbContext.CarServiceItemOrders.IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == carProductOrder.BaseOrderId)
            .ToListAsync();
        foreach (var carServiceItem in carServiceItems)
        {
            reserveRequest.AdditionalServices.Add(new SupplierCarOrderAdditionalServicesInfo
            {
                Code = carServiceItem.PlatformMapKey,
                Count = carServiceItem.Quantity
            });

            if (carServiceItem.Quantity > 1)
            {
                //附加服务项目count>1时，需要在用户订单备注里面填写这个要求，如：orderRemark="Please provide 2 child seats."
                additionalServicesRemarks.Add($"Please provide {carServiceItem.Quantity} {carServiceItem.PlatformMapKey}.");
            }
        }
        
        //订单备注信息补充
        reserveRequest.OrderRemark += string.Join(Environment.NewLine, additionalServicesRemarks);
        
        //乘客数量信息补充
        reserveRequest.PassengerCount.Adult = carProductOrder.Passengers; //乘客数量
        reserveRequest.PassengerCount.Luggage = carProductOrder.Baggages; //行李数
        
        //字段模板信息查询
        var orderFieldTypeInfos = await GetOrderFieldInfo(carProductOrder.BaseOrderId);
        var travelOrderFieldInfos = orderFieldTypeInfos.TravelFieldInfo
            .SelectMany(x => x.Fields)
            .ToList();
        //地点信息补充
        var travelSupplementData = await TravelDataSupplement(
            carProductOrder,
            travelOrderFieldInfos);
        reserveRequest.DepartLocation = travelSupplementData.Departure;
        reserveRequest.ArrivalLocation = travelSupplementData.Arrival;

        //航班信息补充
        reserveRequest.FlightDetail = travelSupplementData.FlightDetail;

        //联系人信息补充
        var contactsOrderFieldInfos = orderFieldTypeInfos.ContactsFieldInfo
            .SelectMany(x => x.Fields)
            .ToList();
        var personSupplementData = PersonDataSupplement(contactsOrderFieldInfos);
        reserveRequest.ContactPerson = personSupplementData;
        
        //预定前入参数据验证,返回错误信息
        validationResultMsg =
            ValidateReservationRequest(reserveRequest, travelSupplementData.IncludeAirport);
        onValidationResultMsgSet(validationResultMsg);
    }
}