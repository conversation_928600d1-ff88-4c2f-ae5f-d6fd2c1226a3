using Contracts.Common.Order.DTOs.TicketOrder;
using FluentValidation;

namespace Order.Api.Services.Validator.TicketOrder
{
    public class RefundTicketOrderInputValidator : AbstractValidator<RefundTicketOrderInput>
    {
        public RefundTicketOrderInputValidator()
        {
            RuleFor(x => x.BaseOrderId).NotNull().GreaterThan(0);
            RuleFor(x => x.Quantity).NotNull().GreaterThan(0);
            RuleFor(x => x.Amount).NotNull().GreaterThanOrEqualTo(0);
            RuleFor(x => x.Message).NotNull().Length(1, 200);
        }
    }
}
