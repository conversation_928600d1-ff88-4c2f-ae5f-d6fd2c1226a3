using Contracts.Common.Order.DTOs;
using FluentValidation;

namespace Order.Api.Services.Validator
{
    public class SearchOrderByUserInputValidator : AbstractValidator<SearchOrderByUserInput>
    {
        public SearchOrderByUserInputValidator()
        {
            RuleFor(s => s.PageIndex).GreaterThan(0);
            RuleFor(s => s.PageSize).GreaterThan(0).LessThanOrEqualTo(200);
            RuleFor(s => s.Status).IsInEnum();
        }
    }
}
