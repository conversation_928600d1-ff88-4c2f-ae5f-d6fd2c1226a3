using Contracts.Common.Order.DTOs.MailOrder;
using FluentValidation;

namespace Order.Api.Services.Validator.MailOrder
{
    public class SendOutGoodsInputValidator : AbstractValidator<SendOutGoodsInput>
    {
        public SendOutGoodsInputValidator()
        {
            RuleFor(x => x.MailOrderIds).NotNull();
            RuleFor(x => x.MailOrderIds).Must((x, i) =>
            {
                if (i.Any(v => v > 0)) return true;
                else return false;
            });
            RuleFor(x => x.LogisticsCompanyCode).NotEmpty();
            RuleFor(x => x.TrackingNumber).NotEmpty();
        }
    }
}
