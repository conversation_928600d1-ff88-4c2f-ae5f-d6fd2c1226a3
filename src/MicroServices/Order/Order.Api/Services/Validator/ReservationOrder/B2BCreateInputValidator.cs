using Contracts.Common.Order.DTOs.ReservationOrder;
using FluentValidation;

namespace Order.Api.Services.Validator.ReservationOrder;

public class B2BCreateInputValidator : AbstractValidator<B2BCreateInput>
{
    public B2BCreateInputValidator()
    {
        RuleFor(x => x.BaseOrderId).NotNull().GreaterThan(0);
        RuleFor(x => x.TravelDateBegin).NotNull().GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.TravelDateEnd).NotNull().GreaterThanOrEqualTo(x => x.TravelDateBegin);
        RuleFor(x => x.Quantity).NotNull().GreaterThan(0);
    }
}
