using Common.GlobalException;
using Contracts.Common.Order.DTOs.OrderManualVoucher;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.DTOs.TravelLineOrder.OTA;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Interfaces;
using System.Collections.Concurrent;

namespace Order.Api.Services;

public class TravelLineVoucherService : ITravelLineVoucherService
{
    private readonly CustomDbContext _dbContext;
    private readonly ITravelLineOTAService _lineOtaService;
    private readonly IAgencyService _agencyService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly ICapPublisher _capPublisher;
    private readonly ITravelLineOrderMessageService _travelLineOrderMessageService;
    private readonly IOrderOssService _orderOssService;
    
    public TravelLineVoucherService(
        CustomDbContext dbContext,
        ITravelLineOTAService lineOtaService,
        IAgencyService agencyService,
        IOpenPlatformBaseService openPlatformBaseService,
        ICapPublisher capPublisher,
        ITravelLineOrderMessageService travelLineOrderMessageService,
        IOrderOssService orderOssService)
    {
        _dbContext = dbContext;
        _lineOtaService = lineOtaService;
        _agencyService = agencyService;
        _openPlatformBaseService = openPlatformBaseService;
        _capPublisher = capPublisher;
        _travelLineOrderMessageService = travelLineOrderMessageService;
        _orderOssService = orderOssService;
    }
    
    [UnitOfWork]
    public async Task ManualAdd(ManualAddVoucherInput input)
    {
        var lineOrder = await _dbContext.TravelLineOrder.AsNoTracking()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .FirstOrDefaultAsync();
        
        //已确认订单才能手工上传凭证发货
        if(lineOrder.Status != TravelLineOrderStatus.Confirmed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        //只支持线下采购产品类型 和 供应商发货失败的API对接订单(errorCode =10009)
        var allowableCredentialSourceTypes = new List<LineProductPurchaseSourceType>
        {
            LineProductPurchaseSourceType.OfflinePurchase,
            LineProductPurchaseSourceType.InterfaceDock
        };
        if (!allowableCredentialSourceTypes.Contains(lineOrder.PurchaseSourceType))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        if (lineOrder.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            // 供应商发货失败的API对接订单(errorCode =10009)
            var supplierRecord = await _dbContext.TravelLineSupplierOrderRecords
                .Where(x => x.BaseOrderId == input.BaseOrderId)
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync();
            if (supplierRecord == null) throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

            if (supplierRecord is not { RecordType: LineProductSupplierOrderRecordType.Delivery, IsSuccess: false })
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
            
            if(supplierRecord.ErrorCode != (int)OrderBusinessErrorCodeType.DeliverFailed)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
            
            //至少一张凭证
            if (input.Vouchers.Count <= 0) throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        
        //移除已上传的.以最后一次上传的为准
        var dbVouchers = await _dbContext.TravelLineSupplierOrderVouchers
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .ToListAsync();        
        _dbContext.RemoveRange(dbVouchers);
        
        var vouchers = input.Vouchers.Select(item => new TravelLineSupplierOrderVoucher
        {
            BaseOrderId = input.BaseOrderId,
            FilePath = item.FilePath,
            Thumbnail = item.Thumbnail,
            ImageSourcePath = item.CompositeImagePath,//pdf转图片的合成路径
            PdfSourcePath = item.CompositePdfPath,// 图片转pdf的合成路径 
            VoucherSourceType = LineProductVoucherSourceType.ManualUpload
        }).ToList();

        await _dbContext.AddRangeAsync(vouchers);
        
    }

    [UnitOfWork]
    public async Task<ManualDeliveryVoucherOutput> ManualDelivery(ManualDeliveryVoucherInput input)
    {
        var result = new ManualDeliveryVoucherOutput();

        //线路订单
        var lineOrder = await _dbContext.TravelLineOrder
            .FirstOrDefaultAsync(x => x.BaseOrderId == input.BaseOrderId);
        var baseOrder = await _dbContext.BaseOrders
            .FirstOrDefaultAsync(x => x.Id == lineOrder.BaseOrderId);
        
        //只允许在待完成订单才允许手动发货
        if (baseOrder.Status != BaseOrderStatus.UnFinished)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        //已确认订单才能手工上传凭证发货
        if(lineOrder.Status != TravelLineOrderStatus.Confirmed)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        //同步失败 - 重发凭证
        if (input.SyncFailedReSend)
        {
            var isSyncFailed = await _dbContext.TravelLineSupplierOrderRecords.AsNoTracking()
                .Where(x => x.BaseOrderId == input.BaseOrderId)
                .Where(x => x.RecordType == LineProductSupplierOrderRecordType.SyncDelivery)
                .Where(x => x.IsSuccess == false)
                .AnyAsync();
            
            if (!isSyncFailed)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        
        //查询凭证信息(支持不上传凭证发货)
        var vouchers = await _dbContext.TravelLineSupplierOrderVouchers.AsNoTracking()
            .Where(x => x.BaseOrderId == input.BaseOrderId)
            .Select(x => new SupplierOrderVoucherItem
            {
                FilePath = x.FilePath,
                ThumbnailPath = x.Thumbnail,
                PdfSourcePath = x.PdfSourcePath,
                ImageSourcePath = x.ImageSourcePath
            })
            .ToListAsync();
        
        //发货记录
        TravelLineSupplierOrderRecord deliveryRecord = await BaseDelivery(baseOrder, lineOrder, vouchers,input.SyncFailedReSend);
        deliveryRecord.SetTenantId(baseOrder.TenantId);
        await _dbContext.AddAsync(deliveryRecord);
        
        result.DeliverySuccess = deliveryRecord.IsSuccess;
        result.ErrorMessage = deliveryRecord.ErrorMsg;
        switch (deliveryRecord.RecordType)
        {
            case LineProductSupplierOrderRecordType.Delivery:
                result.DeliverStatus = OrderVoucherDeliverStatus.Success;
                break;
            case LineProductSupplierOrderRecordType.SyncDelivery:
                if (deliveryRecord.IsSuccess)
                {
                    result.DeliverStatus = OrderVoucherDeliverStatus.Success;
                }
                else
                {
                    result.DeliverStatus = OrderVoucherDeliverStatus.SyncFailed;
                }

                break;
            case LineProductSupplierOrderRecordType.InCombination:
                result.DeliverStatus = OrderVoucherDeliverStatus.InCombination;
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
        
        return result;
    }
    
    public async Task<List<GetSupplierVoucherDeliveryInfoOutput>> GetVouchers(params long[] baseOrderIds)
    {
        if (baseOrderIds.Any() is false) return new List<GetSupplierVoucherDeliveryInfoOutput>();

        var vouchers = await _dbContext.TravelLineSupplierOrderVouchers.IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .Select(x => new {x.BaseOrderId, x.FilePath, x.Thumbnail, x.CreateTime})
            .ToListAsync();

        if (vouchers.Any() is false)
        {
            return new List<GetSupplierVoucherDeliveryInfoOutput>();
        }

        var result = new List<GetSupplierVoucherDeliveryInfoOutput>();
        foreach (var voucher in vouchers)
        {
            var item = new GetSupplierVoucherDeliveryInfoOutput
            {
                BaseOrderId = voucher.BaseOrderId,
                FilePath = voucher.FilePath,
                Thumbnail = voucher.Thumbnail,
                CreateTime = voucher.CreateTime
            };
            result.Add(item);
        }

        return result.ToList();
    }


    public async Task<TravelLineSupplierOrderRecord> BaseDelivery(BaseOrder baseOrder, TravelLineOrder travelLineOrder,
        List<SupplierOrderVoucherItem> vouchers,
        bool ignoreEmailSend = false)
    {
        var isDelivery = true;
        var deliveryRecord = new TravelLineSupplierOrderRecord();
        var otaPlatform = _lineOtaService.OtaSellingPlatforms;
        var otaSellingChannels = _lineOtaService.OtaSellingChannels;
        var systemOtaChannelType = new OtaChannelType();
        if (baseOrder.SellingPlatform == SellingPlatform.System)
        {
            //手工单也可提下OTA的订单.所以也会需要发货
            var agency = await _agencyService.GetAgencyDetail(baseOrder.AgencyId);
            var systemPlatformOrderCheck = _openPlatformBaseService.CheckSystemPlatformOrder(agency.AgencyType,
                agency.AgencyApiType,
                otaPlatform);

            //判断是否支持OTA发货
            otaPlatform = systemPlatformOrderCheck.otaPlatform;
            systemOtaChannelType = systemPlatformOrderCheck.otaChannelType;
        }
        
        //非待完成订单不走OTA同步渠道
        if (baseOrder.Status != BaseOrderStatus.UnFinished)
        {
            isDelivery = false;
            deliveryRecord = new TravelLineSupplierOrderRecord
            {
                BaseOrderId = baseOrder.Id,
                SupplierOrderId = travelLineOrder.SupplierOrderId,
                RecordType = LineProductSupplierOrderRecordType.Delivery,
                IsSuccess = true
            };
        }

        if (isDelivery)
        {
            //发送邮件,此处凭证只发送当前订单关联的凭证
            if (vouchers.Any() && ignoreEmailSend == false)
            {
                await _travelLineOrderMessageService.SendConfirmedMessageAsync(
                    new TravelLineOrderSendConfirmedMessageInput()
                    {
                        BaseOrderId = baseOrder.Id,
                        Vouchers = vouchers.Select(x=> new TravelLineOrderSendConfirmedMessageVouchers
                        {
                            FilePath = x.FilePath,
                            ThumbnailPath = x.ThumbnailPath
                        })
                        .ToList()
                    });
            }

            if (otaPlatform.Contains(baseOrder.SellingPlatform))
            {
                //多渠道单号判断
                var channelOrderNos = new List<string>();
                if (!string.IsNullOrEmpty(baseOrder.ChannelOrderNo))
                {
                    channelOrderNos = baseOrder.ChannelOrderNo.Split(',').Where(x => !string.IsNullOrEmpty(x)).ToList();
                }

                await ChannelConfirm(
                    baseOrder: baseOrder,
                    travelLineOrder: travelLineOrder,
                    channelOrderNos: channelOrderNos,
                    systemOtaChannelType: systemOtaChannelType);
                
                var otaDeliveryResponse = await ChannelOrderPreDelivery(new
                (
                    PurchaseSourceType: travelLineOrder.PurchaseSourceType,
                    SellingPlatform : baseOrder.SellingPlatform,
                    ChannelOrderNos : channelOrderNos,
                    Vouchers : vouchers,
                    SystemOtaChannelType : systemOtaChannelType,
                    OtaPlatform : otaPlatform,
                    TenantId : baseOrder.TenantId
                ));
                var otaDeliveryIsSuccess = otaDeliveryResponse.Code == 200;

                await UploadLineOtaChannelOrder(baseOrder.Id,otaDeliveryIsSuccess,otaDeliveryResponse.Msg);
                
                deliveryRecord = new TravelLineSupplierOrderRecord
                {
                    BaseOrderId = baseOrder.Id,
                    SupplierOrderId = travelLineOrder.SupplierOrderId,
                    RecordType = LineProductSupplierOrderRecordType.SyncDelivery,
                    IsSuccess = otaDeliveryIsSuccess,
                    ErrorMsg = otaDeliveryResponse.Msg
                };
            }
            else
            {
                //非OTA售卖平台
                deliveryRecord = new TravelLineSupplierOrderRecord
                {
                    BaseOrderId = baseOrder.Id,
                    SupplierOrderId = travelLineOrder.SupplierOrderId,
                    RecordType = LineProductSupplierOrderRecordType.Delivery,
                    IsSuccess = true
                };
            }
        }
        
        
        return deliveryRecord;
    }


    #region private
    
    private async Task UploadLineOtaChannelOrder(long baseOrderId,bool isSuccess,string errorMsg)
    {
        var otaOrder = await _dbContext.TravelLineOtaOrders
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrderId);
        if(otaOrder == null) return;

        if (isSuccess)
        {
            otaOrder.Status = TravelLineOtaOrderStatus.Delivered;
            otaOrder.UpdateTime = DateTime.Now;
        }
        
        var otaOrderRecord = new TravelLineOtaOrderRecord
        {
            BaseOrderId = otaOrder.BaseOrderId,
            SubOrderId = otaOrder.SubOrderId,
            TravelLineOtaOrderId = otaOrder.Id,
            RecordType = TravelLineOtaOrderRecordType.SyncDeliver,
            IsSuccess = isSuccess,
            ErrorMsg = errorMsg
        };
        otaOrderRecord.SetTenantId(otaOrder.TenantId);
        await _dbContext.AddAsync(otaOrderRecord);
    }

    record ChannelOrderPreDeliveryResult(int Code,string Msg);
    record ChannelOrderPreDeliveryInput(
        LineProductPurchaseSourceType PurchaseSourceType,
        SellingPlatform SellingPlatform,
        SellingPlatform[] OtaPlatform,
        OtaChannelType SystemOtaChannelType,
        List<string> ChannelOrderNos,
        List<SupplierOrderVoucherItem> Vouchers,
        long TenantId);
    private async Task<ChannelOrderPreDeliveryResult> ChannelOrderPreDelivery(ChannelOrderPreDeliveryInput input)
    {
        var result = new ChannelOrderPreDeliveryResult(200,string.Empty);
        if (input.OtaPlatform.Contains(input.SellingPlatform) && input.Vouchers.Any())
        {
            //设置文件权限
            var voucherBag = new ConcurrentBag<SupplierOrderVoucherItem>();
            foreach (var item in input.Vouchers)
            {
                var voucherBagItem = new SupplierOrderVoucherItem
                {
                    ThumbnailPath = item.ThumbnailPath,
                    SourcePath = item.SourcePath,
                    PdfSourcePath = item.PdfSourcePath,
                    ImageSourcePath = item.ImageSourcePath
                };
                voucherBagItem.FilePath = _orderOssService.SetObjectAcl(item.FilePath!);
                if (!string.IsNullOrEmpty(item.ImageSourcePath))
                {
                    voucherBagItem.ImageSourcePath = _orderOssService.SetObjectAcl(item.ImageSourcePath);
                }
                                
                if (!string.IsNullOrEmpty(item.PdfSourcePath))
                {
                    voucherBagItem.PdfSourcePath = _orderOssService.SetObjectAcl(item.PdfSourcePath);
                }
                voucherBag.Add(voucherBagItem);
            }
            
            var channelDeliveryRequest = new TravelLineChannelDeliveryInput
            {
                OtaOrderIds = input.ChannelOrderNos,
                SellingPlatform = input.SellingPlatform,
                SystemOtaChannelType = input.SystemOtaChannelType,
                Vouchers = voucherBag.ToList(),
                TenantId = input.TenantId
            };
            var channelDeliveryResult = await _lineOtaService.Delivery(channelDeliveryRequest);
            result = new ChannelOrderPreDeliveryResult(channelDeliveryResult.Code,channelDeliveryResult.Msg);
        }

        return result;
    }

    private async Task ChannelConfirm(BaseOrder baseOrder, TravelLineOrder travelLineOrder,
        OtaChannelType systemOtaChannelType,
        List<string> channelOrderNos)
    {
        // [api对接]产品渠道确认.[手工发货]产品再确认场景下就已经发起
        if (travelLineOrder.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
        {
            try
            {
                // 判断是否需要发起渠道确认
                var channelConfirmSuccess = await _dbContext.TravelLineOtaOrderRecords
                    .IgnoreQueryFilters()
                    .AsNoTracking()
                    .Where(x => x.BaseOrderId == baseOrder.Id)
                    .Where(x => x.RecordType == TravelLineOtaOrderRecordType.SyncConfirm && x.IsSuccess == true)
                    .AnyAsync();
                if (!channelConfirmSuccess)
                {
                    var confirmResult = await _lineOtaService.Confirm(new TravelLineOTAOrderConfirmInput
                    {
                        OtaOrderIds = channelOrderNos,
                        SellingPlatform = baseOrder.SellingPlatform,
                        SystemOtaChannelType = systemOtaChannelType,
                        TenantId = baseOrder.TenantId
                    });
                    var confirmSuccess = confirmResult.Code is 200 or (int)TravelLineOtaOrderErrorCode.FliggyAlreadyConfirm;
                    var otaOrder = await _dbContext.TravelLineOtaOrders.IgnoreQueryFilters()
                        .FirstOrDefaultAsync(x => x.BaseOrderId == baseOrder.Id);
                    if (otaOrder is not null)
                    {
                        otaOrder.Status = confirmSuccess
                            ? TravelLineOtaOrderStatus.WaitingForDeliver //已确认直接变成待发货
                            : TravelLineOtaOrderStatus.WaitingForConfirm;
                        otaOrder.UpdateTime = DateTime.Now;

                        var otaOrderRecord = new TravelLineOtaOrderRecord
                        {
                            BaseOrderId = baseOrder.Id,
                            SubOrderId = travelLineOrder.Id,
                            TravelLineOtaOrderId = otaOrder.Id,
                            RecordType = TravelLineOtaOrderRecordType.SyncConfirm,
                            IsSuccess = confirmSuccess,
                            ErrorMsg = confirmResult.Msg,
                            ErrorCode = confirmSuccess
                                ? null
                                : (int)OrderBusinessErrorCodeType.LineSyncConfirmFailed
                        };
                        otaOrderRecord.SetTenantId(baseOrder.TenantId);
                        await _dbContext.AddAsync(otaOrderRecord);
                    }
                }
            }
            catch (Exception e)
            {
            }
        }
    }
    
    #endregion
}