using Contracts.Common.Order.DTOs.CarProductOrder.OTA;
using Contracts.Common.Order.Enums;
using Order.Api.Services.OpenPlatform.Contracts;
using Order.Api.Services.OpenPlatform.Contracts.Channel;
using Order.Api.Services.OpenPlatform.Interfaces;

namespace Order.Api.Services.OpenPlatform;

public class CarProductOtaService : ICarProductOTAService
{
    private readonly IOpenChannelService _openChannelService;

    /// <summary>
    /// 渠道响应成功码
    /// </summary>
    private readonly int _channelResponseSuccessCode = 200;

    public CarProductOtaService(
        IOpenChannelService openChannelService)
    {
        _openChannelService = openChannelService;
    }

    public SellingPlatform[] OtaSellingPlatforms => new[] {SellingPlatform.Fliggy, SellingPlatform.Ctrip};
    public SellingChannels[] OtaSellingChannels => new[] {SellingChannels.Fliggy, SellingChannels.Ctrip};

    public async Task<CarProductOrderOtaSyncDeliveryProcessOutput> Delivery(CarProductOrderOtaSyncDeliveryProcessInput input)
    {
        var result = new CarProductOrderOtaSyncDeliveryProcessOutput();
        if (input.OtaOrderIds.Any() is false)
        {
            result.Code = -1;
            result.Msg = "OTA订单号不能为空";
            return result;
        }
        
        //去重
        input.OtaOrderIds = input.OtaOrderIds.Distinct().ToList();
        
        var otaChannelType = input.SellingPlatform switch {
            SellingPlatform.System => input.SystemOtaChannelType,
            SellingPlatform.Meituan => OtaChannelType.Meituan,
            SellingPlatform.Fliggy => OtaChannelType.AliTrip,
            SellingPlatform.Ctrip => OtaChannelType.Ctrip,
            SellingPlatform.TikTok => OtaChannelType.DouYin,
            _ => throw new ArgumentOutOfRangeException()
        };
        var request = new ChannelOrderDeliveryAllRequest<ChannelOrderDriverServiceInfo>
        {
            OtaType = otaChannelType.ToString().ToLowerInvariant(),
            Category = OpenChannelCategoryType.Driver.ToString().ToLowerInvariant(),
            ServiceInfo = new ChannelOrderDriverServiceInfo()
        };

        var responseList = new List<ApiBaseResponse<ChannelOrderDeliveryAllResponse>>();
        foreach (var otaOrderId in input.OtaOrderIds)
        {
            request.OtaOrderId = otaOrderId;
            var response = await _openChannelService.DeliverAll(request,input.TenantId);
            responseList.Add(response);
        }
        
        //tod: 目前不关注结果
        result.Code = _channelResponseSuccessCode;  
        return result;
    }
}