using Contracts.Common.Order.Enums;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.ConfigModel;
using Order.Api.Services.OpenPlatform.Contracts;
using Order.Api.Services.OpenPlatform.Contracts.Supplier;
using Order.Api.Services.OpenPlatform.Interfaces;

namespace Order.Api.Services.OpenPlatform;

public class OpenSupplierService : IOpenSupplierService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<OpenSupplierService> _logger;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IOptionsMonitor<OpenPlatformOrderConfig> _openPlatformOrderConfigMonitor;

    private const int _openSupplierUniversalErrorCode = 1000;// 供应端通用默认错误码

    public OpenSupplierService(
        IHttpClientFactory httpClientFactory,
        ILogger<OpenSupplierService> logger,
        IOpenPlatformBaseService openPlatformBaseService,
        IOptionsMonitor<OpenPlatformOrderConfig> openPlatformOrderConfigMonitor)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _openPlatformBaseService = openPlatformBaseService;
        _openPlatformOrderConfigMonitor = openPlatformOrderConfigMonitor;
    }

    public async Task<ApiBaseResponse<SupplierOrderCreateOrderResponse>> CreateOrder(
        SupplierOrderCreateOrderRequest request, long tenantId)
    {
        var relativeUrl = $"{_openPlatformOrderConfigMonitor.CurrentValue.SupplierCommonOrderCreateUrl}";
        var response =  await ExecuteRequest<SupplierOrderCreateOrderRequest, SupplierOrderCreateOrderResponse>(request,
            tenantId,
            relativeUrl);
        if (response.Code == _openSupplierUniversalErrorCode) // 替换错误码
            response.Code = (int)OrderBusinessErrorCodeType.OperationFailure;
        return response;
    }

    public async Task<ApiBaseResponse<SupplierOrderPayBalanceResponse>> PayOrder(SupplierOrderPayBalanceRequest request,
        long tenantId)
    {
        var relativeUrl = $"{_openPlatformOrderConfigMonitor.CurrentValue.SupplierCommonOrderPayUrl}";
        var response =  await ExecuteRequest<SupplierOrderPayBalanceRequest, SupplierOrderPayBalanceResponse>(request, tenantId,
            relativeUrl);
        if (response.Code == _openSupplierUniversalErrorCode) // 替换错误码
            response.Code = (int)OrderBusinessErrorCodeType.OperationFailure;
        return response;
    }

    public async Task<ApiBaseResponse<SupplierOrderDetailResponse>> GetOrderDetail(SupplierOrderDetailRequest request,
        long tenantId)
    {
        var relativeUrl = $"{_openPlatformOrderConfigMonitor.CurrentValue.SupplierCommonOrderDetailUrl}";
        return await ExecuteRequest<SupplierOrderDetailRequest, SupplierOrderDetailResponse>(request, tenantId,
            relativeUrl);
    }

    public async Task<ApiBaseResponse<SupplierOrderApplyCancelResponse>> CancelOrder(
        SupplierOrderApplyCancelRequest request, long tenantId)
    {
        var relativeUrl = $"{_openPlatformOrderConfigMonitor.CurrentValue.SupplierCommonOrderCancelUrl}";
        return await ExecuteRequest<SupplierOrderApplyCancelRequest, SupplierOrderApplyCancelResponse>(request,
            tenantId,
            relativeUrl);
    }


    #region 用车供应商业务接口

    public async Task<ApiBaseResponse<SupplierCarAddressFuzzySearchResponse>> CarAddressFuzzySearch(
        SupplierCarAddressFuzzySearchRequest request,
        long tenantId)
    {
        var relativeUrl = _openPlatformOrderConfigMonitor.CurrentValue.SupplierCarAddressSearchUrl;
        return await ExecuteRequest<SupplierCarAddressFuzzySearchRequest, SupplierCarAddressFuzzySearchResponse>(
            request, tenantId,
            relativeUrl,
            OpenSupplierType.Mozio);
    }

    public async Task<ApiBaseResponse<SupplierCarOrderQuoteResponse>> CarOrderQuote(
        SupplierCarOrderQuoteRequest request, long tenantId)
    {
        var relativeUrl = _openPlatformOrderConfigMonitor.CurrentValue.SupplierCarOrderQuoteUrl;
        return await ExecuteRequest<SupplierCarOrderQuoteRequest, SupplierCarOrderQuoteResponse>(request, tenantId,
            relativeUrl,
            OpenSupplierType.Mozio);
    }

    public async Task<ApiBaseResponse<SupplierCarOrderReservationsResponse>> CarOrderReservations(
        SupplierCarOrderReservationsRequest request, long tenantId)
    {
        var relativeUrl = $"{_openPlatformOrderConfigMonitor.CurrentValue.SupplierCarOrderReservationsUrl}";
        return await ExecuteRequest<SupplierCarOrderReservationsRequest, SupplierCarOrderReservationsResponse>(request,
            tenantId,
            relativeUrl,
            OpenSupplierType.Mozio);
    }

    public async Task<ApiBaseResponse<SupplierCarOrderDetailResponse>> GetCarOrderDetail(
        SupplierCarOrderDetailRequest request, long tenantId)
    {
        var relativeUrl = $"{_openPlatformOrderConfigMonitor.CurrentValue.SupplierCarOrderDetailUrl}";
        return await ExecuteRequest<SupplierCarOrderDetailRequest, SupplierCarOrderDetailResponse>(request, tenantId,
            relativeUrl,
            OpenSupplierType.Mozio);
    }

    public async Task<ApiBaseResponse<SupplierCarOrderCancelResponse>> CarOrderCancel(
        SupplierCarOrderCancelRequest request, long tenantId)
    {
        var relativeUrl = $"{_openPlatformOrderConfigMonitor.CurrentValue.SupplierCarOrderCancelUrl}";
        return await ExecuteRequest<SupplierCarOrderCancelRequest, SupplierCarOrderCancelResponse>(request, tenantId,
            relativeUrl,
            OpenSupplierType.Mozio);
    }

    #endregion

    #region 通用产品详情查询

    public async Task<ApiBaseResponse<SupplierProductDetailResponse>> SupplierProductDetail(
        SupplierProductDetailRequest request, long tenantId)
    {
        var relativeUrl = _openPlatformOrderConfigMonitor.CurrentValue.SupplierCommonProductDetailUrl;
        return await ExecuteRequest<SupplierProductDetailRequest, SupplierProductDetailResponse>(
            request, tenantId, relativeUrl);
    }

    #endregion

    /// <summary>
    /// 执行请求
    /// </summary>
    /// <param name="request">请求data</param>
    /// <param name="tenantId">租户id</param>
    /// <param name="relativeUrl">url</param>
    /// <typeparam name="TRequest">TRequest</typeparam>
    /// <typeparam name="TResponse">TResponse</typeparam>
    /// <returns></returns>
    private async Task<ApiBaseResponse<TResponse>> ExecuteRequest<TRequest, TResponse>(TRequest request, long tenantId,
        string relativeUrl,
        OpenSupplierType? openSupplierType = null)
    {
        // 生成签名
        var requestBody = await _openPlatformBaseService.CreateRequestBody(request, tenantId);
        var baseUrl = openSupplierType switch
        {
            OpenSupplierType.Mozio => _openPlatformOrderConfigMonitor.CurrentValue.SupplierCarBaseUrl,
            _ => _openPlatformOrderConfigMonitor.CurrentValue.SupplierBaseUrl
        };
        var uriString = baseUrl + relativeUrl;
        var response =  await SendRequest<ApiBaseRequest<TRequest>, TResponse>(uriString, requestBody);
        if (response.Code != 200)
        {
            if (response.Data is ApiBodyErrorResponse errorResponse && errorResponse.Error != null)
            {
                var errorMessage = errorResponse.Error.Message;
                response.Msg = string.IsNullOrWhiteSpace(response.Msg)
                    ? errorMessage
                    : $"{response.Msg}; {errorMessage}";
            }
        }

        return response;
    }


    private async Task<ApiBaseResponse<TResponse>> SendRequest<TRequest, TResponse>(string uriString,
        TRequest requestBody)
    {
        var result = new ApiBaseResponse<TResponse>() {Code = -1};
        try
        {
            var client = _httpClientFactory.CreateClient();
            var requestUri = new Uri(uriString);
            var serializeObject = JsonConvert.SerializeObject(requestBody);
            client.Timeout = TimeSpan.FromSeconds(150); //开放平台设置请求超时时间 150s
            var jsonContent = new StringContent(serializeObject, Encoding.UTF8, "application/json");
            using HttpResponseMessage response = await client.PostAsync(requestUri, jsonContent);

            if (response.StatusCode == HttpStatusCode.GatewayTimeout)
            {
                // 网关超时
                result.Code = -1;
                result.Msg = "请求超时,等待供应方通知";
                result.IsGatewayTimeOut = true;
                _logger.LogError("[OpenSupplier]接口请求结果:{@Message},{@Url},{@Input}", result, uriString, requestBody);
            }
            else
            {
                response.EnsureSuccessStatusCode();
                var responseStr = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("[OpenSupplier]接口请求结果:{@Message},{@Url},{@Input}", responseStr, uriString, requestBody);
                result = JsonConvert.DeserializeObject<ApiBaseResponse<TResponse>>(responseStr);
            }
        }
        catch (Exception e)
        {
            result.Code = -1;
            result.Msg = "请求异常";
            _logger.LogError(e, "[OpenSupplier]接口请求结果:{@Message},{@Url},{@Input}", result, uriString, requestBody);
        }

        return result;
    }
}