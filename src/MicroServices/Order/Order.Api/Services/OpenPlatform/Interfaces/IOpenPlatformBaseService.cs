using Contracts.Common.Order.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.CitOpenTenantConfig;
using Contracts.Common.Tenant.Enums;
using Order.Api.Services.OpenPlatform.Contracts;

namespace Order.Api.Services.OpenPlatform.Interfaces;

public interface IOpenPlatformBaseService
{
    /// <summary>
    /// 查询cit open tenant配置信息
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<GetCitOpenTenantConfigOutput> GetCitOpenTenantConfig(long tenantId);

    /// <summary>
    /// 创建开放平台请求体
    /// </summary>
    Task<ApiBaseRequest<T>> CreateRequestBody<T>(T data, long tenantId);
    
    /// <summary>
    /// 开放平台签名
    /// </summary>
    /// <param name="appKey"></param>
    /// <param name="appSecret"></param>
    /// <returns></returns>
    (string requestTime, string sign) GenerateSign(string appKey, string appSecret);


    /// <summary>
    /// 年龄计算
    /// </summary>
    /// <param name="birthDate"></param>
    /// <returns></returns>
    int CalculateAge(DateTime birthDate);

    
    /// <summary>
    /// 映射供应商API类型到开放平台供应商类型
    /// </summary>
    /// <param name="supplierApiType"></param>
    /// <returns></returns>
    OpenSupplierType? MapSupplierApiTypeToOpenSupplierType(SupplierApiType supplierApiType);

    /// <summary>
    /// 映射开放平台供应商类型到供应商API类型
    /// </summary>
    /// <param name="openSupplierType"></param>
    /// <returns></returns>
    SupplierApiType MapOpenSupplierTypeToSupplierApiType(OpenSupplierType? openSupplierType);

    /// <summary>
    /// 映射价格库存来源到供应商API类型
    /// </summary>
    /// <param name="priceInventorySource"></param>
    /// <returns></returns>
    SupplierApiType MapPriceInventorySourceToSupplierApiType(PriceInventorySource? priceInventorySource);

    /// <summary>
    /// 通过售卖平台获取售卖渠道
    /// </summary>
    /// <param name="sellingPlatform"></param>
    /// <returns></returns>
    SellingChannels MapSellingPlatformToSellingChannels(SellingPlatform sellingPlatform);

    /// <summary>
    /// 通过售卖平台获取分销商API类型
    /// </summary>
    /// <param name="sellingPlatform"></param>
    /// <returns></returns>
    AgencyApiType MapSellingPlatformToAgencyApiType(SellingPlatform sellingPlatform);

    /// <summary>
    /// 通过售卖平台获取日历价格渠道类型
    /// </summary>
    /// <param name="sellingPlatform"></param>
    /// <returns></returns>
    CalendarPriceChannelType MapSellingPlatformToCalendarPriceChannelType(SellingPlatform sellingPlatform);

    /// <summary>
    ///  通过售卖平台获取开放平台渠道类型
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    OtaChannelType MapSellingPlatformToOtaChannelType(SellingPlatform value);

    /// <summary>
    /// 系统手工单-根据分销商id判断是否是API分销商,如果是API分销商,则根据分销商api类型进行分销渠道的同步
    /// </summary>
    /// <param name="agencyId">分销商id</param>
    /// <param name="tenantId">租户od</param>
    /// <param name="otaPlatform">ota平台类型</param>
    /// <returns></returns>
    Task<(SellingPlatform[] otaPlatform, OtaChannelType otaChannelType)> CheckSystemPlatformOrderByAgencyId(
        long agencyId,
        long tenantId,
        SellingPlatform[] otaPlatform);
    
    /// <summary>
    /// 系统手工单-根据分销商类型判断是否是API分销商,如果是API分销商,则根据分销商api类型进行分销渠道的同步
    /// </summary>
    /// <param name="agencyType"></param>
    /// <param name="agencyApiType"></param>
    /// <param name="otaPlatform"></param>
    /// <returns></returns>
    (SellingPlatform[] otaPlatform, OtaChannelType otaChannelType) CheckSystemPlatformOrder(
        AgencyType agencyType,AgencyApiType? agencyApiType, SellingPlatform[] otaPlatform);

    /// <summary>
    /// 获取saas枚举值
    /// </summary>
    /// <param name="value"></param>
    /// <typeparam name="TEnum"></typeparam>
    /// <returns></returns>
    TEnum GetSaasEnumValue<TEnum>(string value) where TEnum : struct, Enum;
}