namespace Order.Api.Services.OpenPlatform.Contracts.Supplier;

public class SupplierProductResponse
{
    
}

#region 查询产品详情

/// <summary>
/// 查询产品详情
/// </summary>
public class SupplierProductDetailResponse : ApiBodyErrorResponse
{
    /// <summary>
    /// 开放平台供应商配置-productId
    /// </summary>
    public string OutProductId { get; set; }
    
    /// <summary>
    /// 商品名
    /// </summary>
    public string ProductName { get; set; }
    
    /// <summary>
    /// 商品描述
    /// </summary>
    public string ProductDesc { get; set; }
    
    /// <summary>
    /// sku列表
    /// </summary>
    public List<SupplierProductDetailSkuItem> SkuList { get; set; } = new();
}
public class SupplierProductDetailSkuItem
{
    /// <summary>
    /// 开放平台供应商配置-optionId
    /// </summary>
    public string OutProductOptionId { get; set; }
    
    /// <summary>
    /// 开放平台供应商配置-skuId
    /// </summary>
    public string OutSkuId { get; set; }
    
    /// <summary>
    /// 常规价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 成人价
    /// </summary>
    public decimal? AdultPrice { get; set; }
    
    /// <summary>
    /// 儿童价
    /// </summary>
    public decimal? ChildPrice { get; set; }
    
    /// <summary>
    /// 婴儿价
    /// </summary>
    public decimal? BabyPrice { get; set; }

    /// <summary>
    /// 币种
    /// <value>渠道采购币种默认门票配置的供应商币种</value>
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// 1-期票，2-预定票
    /// </summary>
    public int TicketType { get; set; }

    /// <summary>
    /// 年龄段票种类型
    /// <value>
    ///<para>ADULT: 成人；</para>
    ///<para>CHILD: 儿童；</para>
    ///<para>BABY: 婴儿；</para>
    ///<para>PERSON：按每个人计费，价格只返回 price</para>
    ///<para>ALL：多票种；返回多个价格 ；</para>
    /// </value>
    /// <remarks></remarks>
    /// </summary>
    public string SkuType { get; set; }
    
    /// <summary>
    /// 是否时段
    /// </summary>
    public bool IsTimeSlot { get; set; }
    
    /// <summary>
    /// 时段信息
    /// </summary>
    public List<string> TimeSlot { get; set; } = new();
    
    /// <summary>
    /// 供应商给到产品折扣（佣金），如15表示为15%的折扣
    /// </summary>
    public decimal CommissionRate { get; set; }

    /// <summary>
    /// 附加信息列表
    /// </summary>
    public List<SupplierProductDetailExtraInfoItem> ExtraInfos { get; set; } = new();
}

public class SupplierProductDetailExtraInfoItem
{
    /// <summary>
    /// 附加信息标识符
    /// <value>
    /// <para>ApiSkuExtraInfoDataType</para>
    /// <para>BoardingLocation-上车地点</para>
    /// <para>DeliveryLocation-送达地点</para>
    /// <para>DepartureHotel-出发酒店</para>
    /// </value>
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    /// 附加信息名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 值类型
    /// <value>
    /// <para>ApiSkuExtraInfoValueType</para>
    /// <para>text-输入文本</para>
    /// <para>select-下拉选择</para>
    /// </value>
    /// </summary>
    public string ValueType { get; set; }
    
    /// <summary>
    /// 是否为必填项
    /// </summary>
    public bool Required { get; set; }

    /// <summary>
    /// 值选项列表
    /// </summary>
    public List<SupplierProductDetailsExtraInfoValueOptionItem> ValueOptions { get; set; } = new();
}

public class SupplierProductDetailsExtraInfoValueOptionItem
{
    /// <summary>
    /// 选项key
    /// </summary>
    public string Key { get; set; }
    
    /// <summary>
    /// 选项值
    /// </summary>
    public string Value { get; set; }
}

#endregion