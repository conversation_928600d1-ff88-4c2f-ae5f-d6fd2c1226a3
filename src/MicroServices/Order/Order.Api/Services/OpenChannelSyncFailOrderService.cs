using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using EfCoreExtensions;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Contracts.Channel;
using Order.Api.Services.OpenPlatform.Interfaces;
using System.Linq.Expressions;

namespace Order.Api.Services;

public class OpenChannelSyncFailOrderService : IOpenChannelSyncFailOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    public OpenChannelSyncFailOrderService(
        CustomDbContext dbContext,
        IMapper mapper,
        ICapPublisher capPublisher)
    {
        _mapper = mapper;
        _dbContext = dbContext;
        _capPublisher = capPublisher;
    }

    [UnitOfWork]
    public async Task Add(OpenChannelSyncFailOrderAddInput input)
    {
        // 添加同步失败的渠道订单
        var syncFailOrder = await _dbContext.OpenChannelSyncFailOrders
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ChannelOrderNo == input.ChannelOrderNo);
        if (syncFailOrder == null)
        {
            //插入订单数据
            syncFailOrder = new OpenChannelSyncFailOrder
            {
                ChannelOrderNo = input.ChannelOrderNo,
                ChannelMasterOrderNo = input.ChannelMasterOrderNo,
                ProductId = input.ProductId,
                ProductName = input.ProductName,
                SkuId = input.SkuId,
                SkuName = input.SkuName,
                TimeSlotId = input.TimeSlotId,
                TimeSlot = input.TimeSlot,
                IsCombination = input.IsCombination,
                AgencyId = input.AgencyId,
                SupplierId = input.SupplierId,
                TravelDate = input.TravelDate,
                ContactsName = input.ContactsName,
                ContactsEmail = input.ContactsEmail,
                ContactsPhoneNumber = input.ContactsPhoneNumber,
                PaymentAmount = input.PaymentAmount,
                CurrencyCode = input.CurrencyCode,
                DataContentJson = input.DataContentJson,
                OrderType = input.OrderType,
                SellingChannels = input.SellingChannels,
                SellingPlatform = input.SellingPlatform,
                SyncStatus = OpenChannelFailOrderSyncStatus.SyncFail
            };
            await _dbContext.AddAsync(syncFailOrder);
        }

        //新增同步失败订单记录
        var syncFailOrderRecord = new OpenChannelSyncFailOrderRecord
        {
            OpenChannelSyncFailOrderId = syncFailOrder.Id,
            ChannelOrderNo = input.ChannelOrderNo,
            Reason = input.Reason,
            RecordSource = input.RecordSource,
            RecordType = OpenChannelSyncFailOrderRecordType.SyncFail,
            Operator = input.Operator,
            OperatorId = input.OperatorId
        };
        await _dbContext.AddAsync(syncFailOrderRecord);

        await CreatedSuccessfullyProcess(syncFailOrder, new CreatedSuccessfullyProcessDto
        (
            input.ChannelOrderNo,
            input.RecordSource,
            input.Operator,
            input.OperatorId,
            input.TenantId,
            input.OrderType
        ));
        
        //插旗处理
        if (syncFailOrder is {SyncStatus: OpenChannelFailOrderSyncStatus.SyncFail, SellingPlatform: SellingPlatform.Fliggy})
        {
            //Saas订单状态为异常，提示飞猪渠道插旗为灰色
            await _capPublisher.PublishAsync(CapTopics.Order.ChannelOrderFlagModify, new ChannelOrderFlagModifyMessage
            {
                ChannelOrderNo = input.ChannelOrderNo,
                SellingPlatform = input.SellingPlatform,
                FlagType = OpenChannelOrderFlagType.Grey,
                TenantId = input.TenantId
            });
        }
    }

    [UnitOfWork]
    public async Task Update(OpenChannelSyncFailOrderStatusUpdateMessage receive)
    {
        //更新订单状态,不需要做状态的校验
        var syncFailOrders = await _dbContext.OpenChannelSyncFailOrders
            .Where(x => x.Id == receive.SyncFailOrderId
                                      && x.OrderType == receive.OrderType
                                      && x.TenantId == receive.TenantId)
            .ToListAsync();
        if (syncFailOrders.Any() is false)
            return;

        foreach (var failOrder in syncFailOrders)
        {
            //更新订单状态
            await CreatedSuccessfullyProcess(failOrder, new CreatedSuccessfullyProcessDto
            (
                failOrder.ChannelOrderNo,
                receive.RecordSource,
                receive.Operator,
                receive.OperatorId,
                receive.TenantId,
                receive.OrderType
            ));
            
            //新增操作日志
            var log = new OrderLogs
            {
                OperationRole = UserType.None,
                OperationType = OrderOperationType.AbnormalOrderProcess,
                OrderId = failOrder.Id,
                UserId = receive.OperatorId ?? 0,
                UserName = receive.Operator,
                OrderLogType = OrderLogType.ChannelAbnormal
            };
            log.SetTenantId(receive.TenantId);
            await _dbContext.AddAsync(log);
        }
    }

    [UnitOfWork]
    public async Task Invalid(InvalidSyncFailOrderInput input)
    {
        //更新订单状态
        var syncFailOrder = await _dbContext.OpenChannelSyncFailOrders
            .FirstOrDefaultAsync(x => x.Id == input.Id);

        if (syncFailOrder == null)
            return;

        //状态判断.已同步成功的订单不允许作废
        if (syncFailOrder.SyncStatus == OpenChannelFailOrderSyncStatus.SyncSuccess)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //订单已作废不允许再次作废
        if (syncFailOrder.SyncStatus == OpenChannelFailOrderSyncStatus.SyncInvalid)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //作废订单
        syncFailOrder.SyncStatus = OpenChannelFailOrderSyncStatus.SyncInvalid;
        syncFailOrder.UpdateTime = DateTime.Now;

        //新增作废订单记录
        var invalidOrderRecord = new OpenChannelSyncFailOrderRecord
        {
            OpenChannelSyncFailOrderId = syncFailOrder.Id,
            ChannelOrderNo = syncFailOrder.ChannelOrderNo,
            Reason = string.Empty,
            RecordSource = OpenChannelSyncFailOrderRecordSource.Other,
            RecordType = OpenChannelSyncFailOrderRecordType.Invalid,
            Operator = input.Operator,
            OperatorId = input.OperatorId
        };
        invalidOrderRecord.SetTenantId(syncFailOrder.TenantId);
        await _dbContext.AddAsync(invalidOrderRecord);

        //作废订单时,立即更新关联的通用订单状态
        await UpdateAggregateRelatedOrder(syncFailOrder.Id,syncFailOrder.SyncStatus);
        
                    
        //新增操作日志
        var log = new OrderLogs
        {
            OperationRole = UserType.None,
            OperationType = OrderOperationType.AbnormalOrderClosed,
            OrderId = syncFailOrder.Id,
            UserId = input.OperatorId ?? 0,
            UserName = input.Operator,
            OrderLogType = OrderLogType.ChannelAbnormal
        };
        await _dbContext.AddAsync(log);
    }

    record CreatedSuccessfullyProcessDto(string ChannelOrderNo, OpenChannelSyncFailOrderRecordSource RecordSource, string? Operator, long? OperatorId, long TenantId, OrderType OrderType);
    /// <summary>
    /// 分销渠道单号匹配saas订单成功处理
    /// </summary>
    private async Task CreatedSuccessfullyProcess(OpenChannelSyncFailOrder syncFailOrder, CreatedSuccessfullyProcessDto dto)
    {
        //查询是否已经存在当前渠道单号的订单数据
        //多渠道单号以逗号分割,需要处理成数组
        var channelOrderNos = dto.ChannelOrderNo.Split(',').ToList();
        Expression<Func<BaseOrder, bool>> predicate = x => false;
        predicate = channelOrderNos.Aggregate(predicate,
            (current, channelOrderNo) => current.Or(x => x.ChannelOrderNo.Contains(channelOrderNo)));

        //查询订单
        var saasOrders = await _dbContext.BaseOrders
            .AsNoTracking()
            .Where(predicate)
            .Select(x=> new
            {
                x.Id,
                x.ChannelOrderNo
            })
            .ToListAsync();
        
        if (saasOrders.Any() is false)
            return;

        /*
         * 多渠道单号全匹配
         * saas多渠道单号需要拆解
         * 传入渠道单号列表与saas多渠道单号进行全匹配
         */
        var saasChannelOrderNos = saasOrders
            .SelectMany(x =>
                x.ChannelOrderNo.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries))
            .ToList();
        if (!channelOrderNos.All(x => saasChannelOrderNos.Contains(x)))
        {
            return;
        }

        //更新订单状态
        syncFailOrder.SyncStatus = OpenChannelFailOrderSyncStatus.SyncSuccess;
        syncFailOrder.UpdateTime = DateTime.Now;
        syncFailOrder.FinishTime = DateTime.Now;

        //新增创建成功操作记录
        var syncFailOrderRecord = new OpenChannelSyncFailOrderRecord
        {
            OpenChannelSyncFailOrderId = syncFailOrder.Id,
            ChannelOrderNo = dto.ChannelOrderNo,
            Reason = $"{dto.RecordSource.GetDescription()}-重新创建订单成功",
            RecordSource = dto.RecordSource,
            RecordType = OpenChannelSyncFailOrderRecordType.CreatedSuccessfully,
            Operator = dto.Operator,
            OperatorId = dto.OperatorId
        };
        syncFailOrderRecord.SetTenantId(dto.TenantId);
        await _dbContext.AddAsync(syncFailOrderRecord);
    }

    public async Task<PagingModel<SearchOpenChannelSyncFailOrderOutput>> Search(SearchOpenChannelSyncFailOrderInput input)
    {
        var query = SearchQuery(input);
        var pageData = await query
            .WhereIF(input.SyncStatus.Any(), x => input.SyncStatus.Contains(x.SyncStatus))
            .OrderByDescending(x => x.Id)
            .PagingAsync(input.PageIndex, input.PageSize,
                x => new SearchOpenChannelSyncFailOrderOutput
                {
                    Id = x.Id,
                    ChannelOrderNo = x.ChannelOrderNo,
                    ProductId = x.ProductId,
                    ProductName = x.ProductName,
                    SkuId = x.SkuId,
                    SkuName = x.SkuName,
                    TimeSlotId = x.TimeSlotId,
                    TimeSlot = x.TimeSlot,
                    IsCombination = x.IsCombination,
                    AgencyId = x.AgencyId,
                    SupplierId = x.SupplierId,
                    TravelDate = x.TravelDate,
                    ContactsEmail = x.ContactsEmail,
                    ContactsName = x.ContactsName,
                    ContactsPhoneNumber = x.ContactsPhoneNumber,
                    PaymentAmount = x.PaymentAmount,
                    CurrencyCode = x.CurrencyCode,
                    OrderType = x.OrderType,
                    SellingChannels = x.SellingChannels,
                    SellingPlatform = x.SellingPlatform,
                    DataContentJson = x.DataContentJson,
                    SyncStatus = x.SyncStatus,
                    CreateTime = x.CreateTime,
                    UpdateTime = x.UpdateTime
                });
        
        if (pageData.Data.Any())
        {
            var channelOrderNoList = pageData.Data.Select(x => x.ChannelOrderNo).Distinct().ToList();
            var orderTypes = new[]
            {
                OrderType.ScenicTicket,
                OrderType.TravelLineOrder,
                OrderType.CarProduct
            };
            
            //处理渠道单号查询
            Expression<Func<BaseOrder, bool>> predicate =
                channelOrderNoList.Aggregate<string, Expression<Func<BaseOrder, bool>>>(null,
                    (current, channelOrderNoItem) =>
                        current == null
                            ? x => x.ChannelOrderNo.Contains(channelOrderNoItem)
                            : current.Or(x => x.ChannelOrderNo.Contains(channelOrderNoItem)));
            
            //like查询包含的saas订单
            var relatedSaasOrderInfo = await _dbContext.BaseOrders.AsNoTracking()
                .Where(x => orderTypes.Contains(x.OrderType))
                .Where(predicate)
                .Select(x => new { x.Id, x.ChannelOrderNo })
                .ToListAsync();

            //只展示失败的最新一条原因
            var relatedRecords = await _dbContext.OpenChannelSyncFailOrderRecords.AsNoTracking()
                .Where(x => channelOrderNoList.Contains(x.ChannelOrderNo))
                .Where(x => x.RecordType == OpenChannelSyncFailOrderRecordType.SyncFail)
                .ToListAsync();
            foreach (var data in pageData.Data)
            {
                //获取渠道单号全等于的订单
                data.RelatedBaseOrderIds = relatedSaasOrderInfo
                    .SelectMany(x=> x.ChannelOrderNo.Split(',')
                        .Where(y => y == data.ChannelOrderNo)
                        .Select(_ => x.Id)
                        .Distinct())
                    .ToList();

                data.Reason = relatedRecords
                    .Where(x => x.ChannelOrderNo == data.ChannelOrderNo
                                && x.OpenChannelSyncFailOrderId == data.Id)
                    .MaxBy(x => x.Id)?.Reason;
            }
        }

        return pageData;
    }

    public async Task<IEnumerable<GetSyncFailOrderCountOutput>> SearchStatusCount(SearchOpenChannelSyncFailOrderInput input)
    {
        //获取订单状态统计 目前只统计异常未处理的的订单
        var query = SearchQuery(input);
        var queryResult = await query
            .Where(x => x.SyncStatus == OpenChannelFailOrderSyncStatus.SyncFail)
            .Select(x => new {x.Id, x.ChannelOrderNo, x.SyncStatus, x.OrderType})
            .ToListAsync();
        var result = queryResult
            .GroupBy(x => x.OrderType)
            .Select(x => new GetSyncFailOrderCountOutput
            {
                OrderType = x.Key,
                SyncFailCount = x.Count()
            });
        return result;
    }

    /// <summary>
    /// 分页查询Query
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private IQueryable<OpenChannelSyncFailOrder> SearchQuery(SearchOpenChannelSyncFailOrderInput input)
    {
        var query = _dbContext.OpenChannelSyncFailOrders.AsNoTracking()
            .WhereIF(input.Id is >0,x=>x.Id == input.Id)
            .WhereIF(!string.IsNullOrEmpty(input.ChannelOrderNo), x => x.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .WhereIF(input.OrderTypes.Any(), x => input.OrderTypes.Contains(x.OrderType))
            .WhereIF(input.SellingPlatform is > 0, x => x.SellingPlatform == input.SellingPlatform)
            .WhereIF(input.SellingChannels is > 0, x => x.SellingChannels == input.SellingChannels)
            .WhereIF(input.AgencyId is >0,x=>x.AgencyId == input.AgencyId)
            .WhereIF(input.SupplierId is >0,x=>x.SupplierId == input.SupplierId)
            .WhereIF(!string.IsNullOrEmpty(input.ProductName), x => x.ProductName.Contains(input.ProductName!))
            .WhereIF(!string.IsNullOrEmpty(input.SkuName), x => x.SkuName.Contains(input.SkuName!))
            .WhereIF(input.SkuId is >0,x=>x.SkuId == input.SkuId)
            .WhereIF(!string.IsNullOrEmpty(input.ContactsName), x => x.ContactsName.Contains(input.ContactsName!))
            .WhereIF(!string.IsNullOrEmpty(input.ContactsPhoneNumber), x => x.ContactsPhoneNumber.Contains(input.ContactsPhoneNumber!))
            .WhereIF(input.TravelDateBegin.HasValue,x=>x.TravelDate >= input.TravelDateBegin)
            .WhereIF(input.TravelDateEnd.HasValue,x=>x.TravelDate < input.TravelDateEnd!.Value.Date.AddDays(1))
            .WhereIF(input.CreateDateBegin.HasValue,x=>x.CreateTime >= input.CreateDateBegin)
            .WhereIF(input.CreateDateEnd.HasValue,x=>x.CreateTime < input.CreateDateEnd!.Value.Date.AddDays(1));
        
        return query;
    }

    public async Task<GetOpenChannelSyncFailOrderOutput> Detail(GetOpenChannelSyncFailOrderInput input)
    {
        if (!input.SyncFailOrderId.HasValue && string.IsNullOrEmpty(input.ChannelOrderNo))
        {
            return null!;
        }
        
        var syncFailOrder = await _dbContext.OpenChannelSyncFailOrders.AsNoTracking()
            .WhereIF(input.SyncFailOrderId.HasValue, x => x.Id == input.SyncFailOrderId)
            .WhereIF(!string.IsNullOrEmpty(input.ChannelOrderNo),x => x.ChannelOrderNo.Contains(input.ChannelOrderNo!))
            .WhereIF(input.OrderType.HasValue,x=>x.OrderType == input.OrderType)
            .FirstOrDefaultAsync();
        var result = _mapper.Map<GetOpenChannelSyncFailOrderOutput>(syncFailOrder);

        if (result != null)
        {
            //只展示失败的最新一条原因
            var relatedRecords = await _dbContext.OpenChannelSyncFailOrderRecords.AsNoTracking()
                .Where(x => x.OpenChannelSyncFailOrderId == result.SyncFailOrderId
                            && x.RecordType == OpenChannelSyncFailOrderRecordType.SyncFail)
                .OrderByDescending(x => x.CreateTime)
                .FirstOrDefaultAsync();
            result.Reason = relatedRecords?.Reason;
        }
        
        return result;
    }

    [UnitOfWork]
    public async Task UpdateOrderType(UpdateChannelSyncFailOrderTypeInput input)
    {
        var syncFailOrder = await _dbContext.OpenChannelSyncFailOrders
            .FirstOrDefaultAsync(x => x.Id == input.Id);
        if (syncFailOrder == null)
            return;

        syncFailOrder.OrderType = input.OrderType;
        syncFailOrder.UpdateTime = DateTime.Now;
        
        //新增订单类型更新记录
        var invalidOrderRecord = new OpenChannelSyncFailOrderRecord
        {
            OpenChannelSyncFailOrderId = syncFailOrder.Id,
            ChannelOrderNo = syncFailOrder.ChannelOrderNo,
            Reason = string.Empty,
            RecordSource = OpenChannelSyncFailOrderRecordSource.Other,
            RecordType = OpenChannelSyncFailOrderRecordType.ChangeOrderType,
            Operator = input.Operator,
            OperatorId = input.OperatorId
        };
        await _dbContext.AddAsync(invalidOrderRecord);
        
        //新增操作日志
        var log = new OrderLogs
        {
            OperationRole = UserType.None,
            OperationType = OrderOperationType.AbnormalOrderTypeUpdated,
            OrderId = syncFailOrder.Id,
            UserId = input.OperatorId ?? 0,
            UserName = input.Operator,
            OrderLogType = OrderLogType.ChannelAbnormal
        };
        await _dbContext.AddAsync(log);
    }

    private async Task UpdateAggregateRelatedOrder(long syncFailOrderId,OpenChannelFailOrderSyncStatus syncStatus)
    {
        //更新aggregateOrder表的状态.
        var aggregateOrders = await _dbContext.AggregateOrders
            .Where(x => x.BaseOrderId == syncFailOrderId)
            .Where(x => x.OrderStatus == AggregateOrderStatus.ChannelAbnormal
                        ||
                        //作废(已关闭)的订单.OrderStatus不是ChannelAbnormal,而是Closed.
                        (x.AbnormalOrderSourceType == AbnormalOrderSourceType.OpenChannelFail))
            .FirstOrDefaultAsync();
        if (aggregateOrders != null)
        {
            aggregateOrders.OrderStatus = syncStatus switch
            {
                OpenChannelFailOrderSyncStatus.SyncFail =>  AggregateOrderStatus.ChannelAbnormal,
                OpenChannelFailOrderSyncStatus.SyncSuccess =>  AggregateOrderStatus.ChannelAbnormal,
                OpenChannelFailOrderSyncStatus.SyncInvalid =>  AggregateOrderStatus.Closed // 订单已作废关闭
            };
            aggregateOrders.ConfirmStatus =  syncStatus switch
            {
                OpenChannelFailOrderSyncStatus.SyncFail => AggregateOrderConfirmStatus.UnConfirm,
                OpenChannelFailOrderSyncStatus.SyncSuccess => AggregateOrderConfirmStatus.Confirmed,
                OpenChannelFailOrderSyncStatus.SyncInvalid => AggregateOrderConfirmStatus.Confirmed
            };
        }
    }
}