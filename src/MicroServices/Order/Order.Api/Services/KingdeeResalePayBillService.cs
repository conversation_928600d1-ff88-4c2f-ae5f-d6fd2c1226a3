using Contracts.Common.Order.DTOs.Kingdee;
using Contracts.Common.Order.DTOs.Kingdee.KingdeeResalePayBill;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Order.Api.ConfigModel;
using Order.Api.Services.Interfaces;

namespace Order.Api.Services;

public class KingdeeResalePayBillService : IKingdeeResalePayBillService
{
    private readonly CustomDbContext _dbContext;
    private readonly IKingdeeBDSupplierService _kingdeeBDSupplierService;
    private readonly IKingdeeService _kingdeeService;
    private readonly ICurrencyExchangeRateService _currencyExchangeRateService;
    private readonly KingdeeDataConfig _kingdeeDataConfig;

    public KingdeeResalePayBillService(CustomDbContext dbContext,
        IOptions<KingdeeDataConfig> kingdeeDataConfig,
        IKingdeeBDSupplierService kingdeeBDSupplierService,
        IKingdeeService kingdeeService,
        ICurrencyExchangeRateService currencyExchangeRateService)
    {
        _dbContext = dbContext;
        _kingdeeBDSupplierService = kingdeeBDSupplierService;
        _kingdeeService = kingdeeService;
        _currencyExchangeRateService = currencyExchangeRateService;
        _kingdeeDataConfig = kingdeeDataConfig.Value;
    }

    public async Task<List<KingdeeResalePayBillDto>> Generate(GenerateInput input)
    {
        List<KingdeeResalePayBillDto> result = new();
        //额度预付款转销 “财务中心 - 应收应付 - 付款结算单”功能的导出表，状态 == 已付款 and 付款方式 == 预付款 and 实收金额 >= 0，按付款结算单号推，金额取“实付金额”
        DateTime beginDate = input.BeginDate;
        DateTime endDate = input.EndDate.AddDays(1);
        var settlementOrders = await _dbContext.SettlementOrders.AsNoTracking()
            .IgnoreQueryFilters()
            .Join(_dbContext.SettlementOrderTransferRecords, s => s.Id, r => r.SettlementOrderId, (s, r) => new
            {
                SettlementOrder = s,
                SettlementOrderTransferRecord = r
            })
            .Where(x => x.SettlementOrder.TenantId == input.TenantId)
            .Where(x => x.SettlementOrder.Status == SettlementOrderStatus.PaymentCompleted
                && x.SettlementOrder.PaymentAmount >= 0
                && x.SettlementOrderTransferRecord.TransferType == SettlementTransferType.Prepayment
                && x.SettlementOrderTransferRecord.Status == SettlementTransferStatus.Succeeded
                && x.SettlementOrder.PayTime >= beginDate && x.SettlementOrder.PayTime < endDate)
            .Select(x => x.SettlementOrder)
            .ToListAsync();

        if (!settlementOrders.Any())
            return result;
        foreach (var settlementOrder in settlementOrders)
        {
            var currencyCode = settlementOrder.PaymentAmountCurrencyCode;
            var amount = Math.Abs(settlementOrder.PaymentAmount!.Value);
            var baseCurrencyCode = settlementOrder.TotalAmountCurrencyCode;
            var totalAmount = Math.Abs(settlementOrder.TotalAmount);
            var exchangeRate = decimal.Round(amount / totalAmount, 10);
            KingdeeResalePayBillDto bill = new()
            {
                BusinessType = KingdeeBusinessType.SettlementOrder,
                BusinessOrderId = settlementOrder.Id,
                CurrencyCode = currencyCode,
                Amount = amount,
                BaseCurrencyCode = baseCurrencyCode,
                TotalAmount = totalAmount,
                ExchangeRate = exchangeRate,
                Date = settlementOrder.PayTime!.Value,
                SupplierId = settlementOrder.SupplierId,
                Model = new()
                {
                    //金蝶转销付款单信息
                    FBillNo = settlementOrder.Id.ToString(),
                    FBillTypeID = new() { FNumber = _kingdeeDataConfig.FBillTypeID.ResalePayBill },
                    FDATE = settlementOrder.PayTime.Value.ToString("yyyy-MM-dd"),
                    FSETTLEORGID = new() { FNumber = _kingdeeDataConfig.FSETTLEORGID },
                    FPAYORGID = new() { FNumber = _kingdeeDataConfig.FPAYORGID },
                    FPURCHASEORGID = new() { FNumber = _kingdeeDataConfig.FPURCHASERGROUPID },
                    FPURCHASEDEPTID = new() { FNumber = _kingdeeDataConfig.FPURCHASEDEPTID.Other },
                    FCONTACTUNITTYPE = "BD_Supplier",
                    FCONTACTUNIT = new() { FNumber = "" },//供应商
                    FPAYUNITTYPE = "BD_Supplier",
                    FPAYUNIT = new() { FNumber = "" },//供应商
                    FCURRENCYID = new() { FNumber = "" },//币种转换
                    FMAINBOOKID = new() { FNumber = "" },//币种转换
                    FEXCHANGETYPE = new() { FNumber = _kingdeeDataConfig.FEXCHANGETYPE },
                    FEXCHANGERATE = exchangeRate,
                    FSupplierID = new() { FNumber = "" },//供应商
                    FSETTLECUR = new() { FNumber = "" },//币种转换
                    FSETTLEMAINBOOKID = new() { FNumber = "" },//币种转换
                    FSETTLEEXCHANGETYPE = new() { FNumber = _kingdeeDataConfig.FEXCHANGETYPE },
                    FSETTLERATE = 1,
                    FREMARK = settlementOrder.Remark,
                    FEntity = new Fentity[]
                    {
                        new()
                        {
                            FSETTLETYPEID=new (){ FNumber=_kingdeeDataConfig.FSETTLETYPEID.JSFS04_SYS },
                            FPURPOSEID=new (){ FNumber=_kingdeeDataConfig.FPURPOSEID.SFKYT102,},//付款用途 SFKYT102扣预付款
                            FPAYTOTALAMOUNTFOR = totalAmount,//应付金额
                            FPAYAMOUNTFOR_E = amount,
                            FREALPAYAMOUNTFOR_D = amount,
                            FACCOUNTID = new(_kingdeeService.GetFAccountId(settlementOrder.BankAccount,currencyCode)),
                            FProductLine= new(){ FNumber=_kingdeeDataConfig.F_qwe_Assistant.Other },//产品线
                            FHOPNumber=settlementOrder.Id.ToString(),
                        }
                    },
                },
            };
            result.Add(bill);
        }

        var supplierIds = result.Select(x => x.SupplierId).Distinct().ToArray();
        var suppliers = await _kingdeeBDSupplierService.GetSuppliers(new GetSuppliersInput
        {
            SupplierIds = supplierIds,
            Status = KingdeeFormStatus.Audited
        });
        var currencies = await _kingdeeService.GetBDCurrencies();
        result.ForEach(
            bill =>
            {
                var model = bill.Model;
                var supplier = suppliers.FirstOrDefault(x => x.SupplierId == bill.SupplierId);
                bill.FNumber = supplier?.BDSupplier?.FNumber;
                if (supplier is not null)
                {
                    model.FCONTACTUNIT = new(supplier.BDSupplier.FNumber);
                    model.FPAYUNIT = new(supplier.BDSupplier.FNumber);
                    model.FSupplierID = new(supplier.BDSupplier.FNumber);
                }
                var currency = currencies.FirstOrDefault(x => x.FCODE == bill.CurrencyCode);
                if (currency is not null)
                {
                    model.FCURRENCYID = new(currency.FNumber);
                }
                var baseCurrency = currencies.FirstOrDefault(x => x.FCODE == bill.BaseCurrencyCode);
                if (baseCurrency is not null)
                {
                    model.FSETTLECUR = new(baseCurrency.FNumber);
                    model.FMAINBOOKID = new(baseCurrency.FNumber);
                    model.FSETTLEMAINBOOKID = new(baseCurrency.FNumber);
                }
                //组织机构
                var organization = _kingdeeService.GetSupplierORGOrganization(bill.SupplierId, bill.BaseCurrencyCode);
                model.FSETTLEORGID = new() { FNumber = organization.FSETTLEORGID };
                model.FPAYORGID = new() { FNumber = organization.FPAYORGID };
                model.FPURCHASEORGID = new() { FNumber = organization.FPURCHASEORGID };
                //本位币
                bill.TargetCurrencyCode = organization.MainBookCurrencyCode;
            });
        var getExchangeRatesInputs = result
            .Where(x => x.TargetCurrencyCode != x.BaseCurrencyCode || x.TargetCurrencyCode != x.CurrencyCode)
            .Select(x => new { x.BaseCurrencyCode, x.TargetCurrencyCode })
            .Distinct()
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.BaseCurrencyCode,
                TargetCurrencyCode = x.TargetCurrencyCode,
            });
        var exchangeRates = await _currencyExchangeRateService.GetExchangeRates(getExchangeRatesInputs);
        result.ForEach(bill =>
        {
            var targetCurrency = currencies.FirstOrDefault(x => x.FCODE == bill.TargetCurrencyCode);
            if (targetCurrency is not null)
            {
                bill.Model.FMAINBOOKID = new(targetCurrency.FNumber);
                bill.Model.FSETTLEMAINBOOKID = new(targetCurrency.FNumber);
            }

            var exchangeRate = bill.CurrencyCode != bill.TargetCurrencyCode ?
                exchangeRates.FirstOrDefault(x => x.BaseCurrencyCode == bill.CurrencyCode
                    && x.TargetCurrencyCode == bill.TargetCurrencyCode)?.ExchangeRate : 1;
            bill.Model.FEXCHANGERATE = exchangeRate;
            bill.ExchangeRate = exchangeRate;

            var targetRate = bill.BaseCurrencyCode != bill.TargetCurrencyCode ?
                exchangeRates.FirstOrDefault(x => x.BaseCurrencyCode == bill.BaseCurrencyCode
                    && x.TargetCurrencyCode == bill.TargetCurrencyCode)?.ExchangeRate : 1;
            bill.Model.FSETTLERATE = targetRate;
            bill.TargetExchangeRate = targetRate;
        });

        return result;
    }

}
