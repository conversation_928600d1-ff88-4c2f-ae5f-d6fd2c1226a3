using AutoMapper;
using Common.ServicesHttpClient;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Tenant.DTOs.Supplier;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Order.Api.Extensions;
using Order.Api.Model;
using Order.Api.Services.Interfaces;
using System.Net.Http;

namespace Order.Api.Services;

public class InsurePolicyHolderService : IInsurePolicyHolderService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;

    public InsurePolicyHolderService(IMapper mapper, 
        CustomDbContext dbContext)
    {
        _mapper = mapper;
        _dbContext = dbContext;
    }

    public async Task<InsurePolicyHolderOutput> Detail()
    {
        var insurePolicyHolder = await _dbContext.InsurePolicyHolders.AsNoTracking().FirstOrDefaultAsync();
        return _mapper.Map<InsurePolicyHolderOutput>(insurePolicyHolder);
    }

    public async Task Save(SaveInsurePolicyHolderInput input)
    {
        var entity = await _dbContext.InsurePolicyHolders.FirstOrDefaultAsync();
        if (entity is null)
        {
            var insurePolicyHolder = new InsurePolicyHolder();
            _mapper.Map(input, insurePolicyHolder);
            await _dbContext.AddAsync(insurePolicyHolder);
        }
        else
        {
            _mapper.Map(input, entity);
            entity.UpdateTime = DateTime.Now;
        }
        await _dbContext.SaveChangesAsync();
    }
}
