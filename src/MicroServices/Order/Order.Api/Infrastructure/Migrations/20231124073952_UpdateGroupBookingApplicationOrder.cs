using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class UpdateGroupBookingApplicationOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "UnionOrderId",
                table: "HotelApiOrder",
                type: "bigint",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BaseOrderId",
                table: "GroupBookingApplicationOrder",
                type: "varchar(200)",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "PriceStrategyCount",
                table: "GroupBookingApplicationOrder",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UnionOrderId",
                table: "HotelApiOrder");

            migrationBuilder.DropColumn(
                name: "PriceStrategyCount",
                table: "GroupBookingApplicationOrder");

            migrationBuilder.AlterColumn<long>(
                name: "BaseOrderId",
                table: "GroupBookingApplicationOrder",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(200)",
                oldNullable: true)
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }
    }
}
