// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Order.Api.Infrastructure;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20240416103034_UpdateGroupBookingInquiryFormProvince")]
    partial class UpdateGroupBookingInquiryFormProvince
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Order.Api.Model.BaseOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ChannelOrderNo")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ContactsPhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("GroupNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Message")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentChannel")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentExternalNo")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentMode")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("PaymentType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ProductSkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ResourceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("SellingChannels")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<string>("SeriesNumber")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserNickName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("VipLevelId")
                        .HasColumnType("bigint");

                    b.Property<string>("VipLevelName")
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.HasIndex("TenantId", "SeriesNumber")
                        .IsUnique();

                    b.ToTable("BaseOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.BaseOrderDiscount", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("DiscountId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("DiscountType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("BaseOrderDiscount", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.BaseOrderRemark", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Imgs")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.ToTable("BaseOrderRemark", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.BaseOrderSeries", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Prefix")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SeriesNumber")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "OrderType")
                        .IsUnique();

                    b.HasIndex("TenantId", "Prefix")
                        .IsUnique();

                    b.ToTable("BaseOrderSeries", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarHailingOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CarHailingType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("ClaimantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClaimantName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Image")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("LanguageType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Passengers")
                        .HasColumnType("int");

                    b.Property<string>("ProductTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<int>("Seats")
                        .HasColumnType("int");

                    b.Property<string>("SkuName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarHailingOrderPoint", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<int>("DepartureCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DepartureCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DeparturePointName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DestinationCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DestinationCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DestinationPointName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FlightNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("Time")
                        .HasColumnType("time");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingOrderPoint", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationForm", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AdultNum")
                        .HasColumnType("int");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Applicant")
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("ApplicantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ChildNum")
                        .HasColumnType("int");

                    b.Property<int?>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int?>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("varchar(32)");

                    b.Property<int?>("DoubleBedNum")
                        .HasColumnType("int");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(100)");

                    b.Property<bool?>("IncludeBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Nationalities")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("OtherRequirements")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("QueenBedNum")
                        .HasColumnType("int");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationForm", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AdultNum")
                        .HasColumnType("int");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Applicant")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("ApplicantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<string>("BaseOrderId")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ChildNum")
                        .HasColumnType("int");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<int>("DoubleBedNum")
                        .HasColumnType("int");

                    b.Property<DateTime>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(200)");

                    b.Property<bool>("IncludeBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Nationalities")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("OtherRequirements")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("PriceStrategyCount")
                        .HasColumnType("int");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("QueenBedNum")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationSuggestItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationSuggestItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingInquiryForm", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("BudgetAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<int>("DoubleBedNum")
                        .HasColumnType("int");

                    b.Property<bool>("IncludeBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("QueenBedNum")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingInquiryForm", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingOperationLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyName")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("OperationType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OriginalStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingOperationLog", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("CancellationPolicy")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ChildPolicy")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("FinalPaymentTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("InitialPaymentRatio")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("QuotationId")
                        .HasColumnType("bigint");

                    b.Property<string>("SpecialRemarks")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ValidityPeriod")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrderAddition", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AdditionName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("PreOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrderAddition", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrderItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelRoomName")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("NightCount")
                        .HasColumnType("int");

                    b.Property<long>("PreOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PriceStrategyId")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PriceStrategyName")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("RoomCount")
                        .HasColumnType("int");

                    b.Property<sbyte>("SupplierApiType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrderItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrderSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ElectronicSeal")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("HeaderImage")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrderSetting", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingQuotation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("DoubleBedPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("HotelENName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("HotelZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IncludeBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("InquiryFormId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("QueenBedPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingQuotation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelApiOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int?>("ChildNum")
                        .HasColumnType("int");

                    b.Property<string>("ChildrenAges")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HotelId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("MinNumberOfRooms")
                        .HasColumnType("int");

                    b.Property<string>("PriceStrategyId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("RoomId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int>("RoomsCount")
                        .HasColumnType("int");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SupplierApiType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("UnionOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelApiOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ConfirmCode")
                        .HasColumnType("varchar(128)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<bool>("HotelIsAutoConfirmRoomStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelRoomImgPath")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("HotelRoomName")
                        .HasColumnType("varchar(100)");

                    b.Property<bool?>("IsDirect")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<bool>("PriceStrategyIsAutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("PriceStrategyMaximumOccupancy")
                        .HasColumnType("int");

                    b.Property<string>("PriceStrategyName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("PriceStrategyNightsCount")
                        .HasColumnType("int");

                    b.Property<int>("PriceStrategyNumberOfBreakfast")
                        .HasColumnType("int");

                    b.Property<int>("PriceStrategyRoomsCount")
                        .HasColumnType("int");

                    b.Property<long>("PriceStrategySupplierId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceStrategyType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SupplierApiType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrderCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("CostExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("ExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("OrgPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("VipPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("HotelOrderId");

                    b.ToTable("HotelOrderCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrderCancelRule", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("BeforeCheckInDays")
                        .HasColumnType("int");

                    b.Property<sbyte>("CancelChargeType")
                        .HasColumnType("tinyint");

                    b.Property<long>("CancelRuleId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CancelRulesType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ChargeValue")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("CheckInDateTime")
                        .HasColumnType("time");

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOrderCancelRule", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrderGuest", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("GuestName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("LastName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOrderGuest", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InvoiceConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("AuthCode")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("ContentPrefix")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ExtensionNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ITIN")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Invoicer")
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Payee")
                        .HasColumnType("varchar(8)");

                    b.Property<string>("ProofReader")
                        .HasColumnType("varchar(8)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WebServiceUrl")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("InvoiceConfig", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InvoiceRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ITIN")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("InvCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("InvNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InvoicePdfUrl")
                        .HasColumnType("longtext");

                    b.Property<string>("InvoiceSerialNo")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Invoicer")
                        .IsRequired()
                        .HasColumnType("varchar(8)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("SourceChannel")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int>("TitleType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InvoiceRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InvoiceTitle", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ITIN")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("TitleType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InvoiceTitle", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeAPPayable", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("KingdeePushId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ReceivableType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeAPPayable", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeARReceivable", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("KingdeePushId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ReceivableType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeARReceivable", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDCustomer", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeBDCustomer", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDSupplier", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeBDSupplier", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeePush", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("BeginDate")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("BillType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeePush", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.LogisticsCompany", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Code")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("LogisticsCompany", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 926382999087087616L,
                            Code = "yuantong",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "圆通速递"
                        },
                        new
                        {
                            Id = 926382999087087617L,
                            Code = "yunda",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "韵达快递"
                        },
                        new
                        {
                            Id = 926382999087087618L,
                            Code = "zhongtong",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "中通快递"
                        },
                        new
                        {
                            Id = 926382999087087619L,
                            Code = "shentong",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "申通快递"
                        },
                        new
                        {
                            Id = 926382999087087620L,
                            Code = "youzhengguonei",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "邮政快递包裹"
                        },
                        new
                        {
                            Id = 926382999087087621L,
                            Code = "huitongkuaidi",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "百世快递"
                        },
                        new
                        {
                            Id = 926382999087087622L,
                            Code = "shunfeng",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "顺丰速运"
                        },
                        new
                        {
                            Id = 926382999087087623L,
                            Code = "ems",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "EMS"
                        },
                        new
                        {
                            Id = 926382999087087624L,
                            Code = "jtexpress",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "极兔速递"
                        },
                        new
                        {
                            Id = 926382999087087625L,
                            Code = "jd",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "京东物流"
                        },
                        new
                        {
                            Id = 926382999087087626L,
                            Code = "youzhengbk",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "邮政标准快递"
                        },
                        new
                        {
                            Id = 926382999087087627L,
                            Code = "fengwang",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "丰网速运"
                        },
                        new
                        {
                            Id = 926382999087087628L,
                            Code = "debangkuaidi",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "德邦快递"
                        },
                        new
                        {
                            Id = 926382999087087629L,
                            Code = "debangwuliu",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "德邦"
                        },
                        new
                        {
                            Id = 926382999087087630L,
                            Code = "zhongtongguoji",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "中通国际"
                        },
                        new
                        {
                            Id = 926382999087087631L,
                            Code = "baishiwuliu",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "百世快运"
                        },
                        new
                        {
                            Id = 926382999087087632L,
                            Code = "emsbg",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "EMS包裹"
                        },
                        new
                        {
                            Id = 926382999087087633L,
                            Code = "zhongtongkuaiyun",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "中通快运"
                        },
                        new
                        {
                            Id = 926382999087087634L,
                            Code = "yundakuaiyun",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "韵达快运"
                        },
                        new
                        {
                            Id = 926382999087087635L,
                            Code = "annengwuliu",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "安能快运"
                        });
                });

            modelBuilder.Entity("Order.Api.Model.MailOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductImagePath")
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("ProductIsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductSkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ProductSupplierId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SignTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TrackingNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TrackingNumber");

                    b.ToTable("MailOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.MailOrderLogistics", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("LogisticsCompanyCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("LogisticsCompanyName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Status")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TrackData")
                        .HasColumnType("text");

                    b.Property<string>("TrackingNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "TrackingNumber")
                        .IsUnique();

                    b.ToTable("MailOrderLogistics", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.MailOrderPostage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("MailOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<string>("PostageTemplateName")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.ToTable("MailOrderPostage", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.MailOrderReceiverAddress", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ReceiverAddressId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("MailOrderReceiverAddress", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OffsetOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<sbyte>("OffsetType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductSkuName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UpdaterId")
                        .HasColumnType("bigint");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OffsetOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderLogs", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("OperationRole")
                        .HasColumnType("tinyint");

                    b.Property<int>("OperationType")
                        .HasColumnType("int");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("OrderLogType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderLogs", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("CostExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("CostPriceType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("ExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<int>("OrderSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("OrgCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OrgPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderPrice", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderShareInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("BuyerId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SharerId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TraceId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderShareInfo", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptSettlementOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("BillingCycleBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("BillingCycleEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("OrderCount")
                        .HasColumnType("int");

                    b.Property<long?>("PayeeId")
                        .HasColumnType("bigint");

                    b.Property<string>("PayeeName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("PayeeTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ProofImg")
                        .HasColumnType("varchar(200)");

                    b.Property<decimal?>("ReceivedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceivedAmountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<decimal>("RechargeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("SettlementTransferType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TotalAmountCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiptSettlementOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptSettlementOrderDate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OrderDateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("ReceiptSettlementOrderDate", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptSettlementOrderDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BusinessOrderId")
                        .IsUnique();

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("ReceiptSettlementOrderDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.RefundOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ExtRefundNo")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FailedReason")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("PostageAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProofImgs")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Reason")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("RejectedReason")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("ReviewTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrdeId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("SubOrdeId");

                    b.HasIndex("TenantId");

                    b.ToTable("RefundOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReservationOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("ClaimantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClaimantName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ConfirmNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsSendPDF")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Message")
                        .HasColumnType("varchar(200)");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentChannel")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentExternalNo")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentMode")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("PaymentType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResourceName")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("ResourceType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("TravelDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("TravelDateEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("Traveler")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketOrderId");

                    b.ToTable("ReservationOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReservationOrderPayment", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("ExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<decimal>("OrgPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ReservationOrderId");

                    b.ToTable("ReservationOrderPayment", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReservationOrderTicketCode", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("Code")
                        .HasColumnType("bigint");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TicketCodeId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ReservationOrderId");

                    b.ToTable("ReservationOrderTicketCode", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketKlookOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("KlookActiveId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("KlookOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("KlookPackageId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("KlookSkuId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("KlookSkuPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("KlookSkuPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<sbyte>("OrderStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketKlookOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketKlookOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ScenicTicketKlookOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketKlookOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketKlookVoucher", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ScenicTicketKlookOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketKlookVoucher", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("AutoRefundRate")
                        .HasColumnType("int");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("CredentialSourceType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ExchangeLocation")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ExchangeNote")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("ExchangeProof")
                        .HasColumnType("int");

                    b.Property<string>("FeeNote")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("NeedToExchange")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte?>("OpenSupplierType")
                        .HasColumnType("tinyint");

                    b.Property<string>("OpeningTime")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OtherNote")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("RefundDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<int>("RefundRate")
                        .HasColumnType("int");

                    b.Property<TimeOnly>("RefundTimeInAdvance")
                        .HasColumnType("time");

                    b.Property<string>("ScenicSpotAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<string>("ScenicSpotPhotoPath")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("ScenicTicketId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ScenicTicketsType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierActivityId")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SupplierPackageId")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SupplierSkuId")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TicketsCombinationOrderId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("TimeSlot")
                        .HasColumnType("time");

                    b.Property<long?>("TimeSlotId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketOrderTravelers", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CardValidDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ExtendInfo")
                        .HasColumnType("mediumtext");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Gender")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("Height")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("LastName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NationalityCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NationalityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ScenicTicketOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("ShoeSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "BaseOrderId");

                    b.ToTable("ScenicTicketOrderTravelers", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseDeliveryRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseDeliveryRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BatchName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseVoucher", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("OriginalName")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("PurchaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("UsedTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseVoucher", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseVoucherUsedDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("PurchaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketVoucherId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseVoucherUsedDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketSupplierOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActiveId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Buyer")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("OrderStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("PackageId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SkuId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("SkuPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("SupplierType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("TimeSlot")
                        .HasColumnType("time");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketSupplierOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketSupplierOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ScenicTicketSupplierOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketSupplierOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketSupplierOrderVoucher", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ScenicTicketSupplierOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketSupplierOrderVoucher", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AbroadAccountAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AbroadAccountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("BankAccountType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1)
                        .HasComment("银行账户类型 1:国内 2:国外");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("BillingCycleBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("BillingCycleEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("OrderCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PayTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("PayerId")
                        .HasColumnType("bigint");

                    b.Property<string>("PayerName")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentAmountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ProofImg")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SwiftCode")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TotalAmountCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SettlementOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrderDate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderDateType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SettlementBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("SettlementOrderDate", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrderDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<sbyte>("SettlementBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BusinessOrderId")
                        .IsUnique();

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("SettlementOrderDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrderTransferRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("BankFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("PayerId")
                        .HasColumnType("bigint");

                    b.Property<string>("PayerName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TransferType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.ToTable("SettlementOrderTransferRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrderGearGift", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("GiftType")
                        .HasColumnType("tinyint");

                    b.Property<long>("StoredValueCardOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardOrderGearGift", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrderGearGiftItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<long>("GiftItemId")
                        .HasColumnType("bigint");

                    b.Property<string>("ItemName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("StoredValueCardOrderGearGiftId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardOrderGearGiftItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketCode", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("Code")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("OrderType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("SubOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketCode", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketCodeUsed", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AmountCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte>("OrderType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("ProductBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ProductSupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResourceName")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("ResourceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("UsedSource")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("SubOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketCodeUsed", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketCodeUsedDetails", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("Code")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketCodeId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketCodeUsedId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("TicketCodeUsedDetails", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<bool>("ProductAutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("ProductAutoRefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<bool>("ProductIsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("ProductNeedConfirmReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("ProductNeedReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("ProductNeedWriteOff")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("ProductRefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<int>("ProductReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<long>("ProductSupplierId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductTicketBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ProductTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<string>("SkuCostDescription")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("SkuImagePath")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("SkuNumberOfNights")
                        .HasColumnType("int");

                    b.Property<DateTime>("SkuValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("SkuValidityEnd")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketSaleType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketOrderModifyRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(20)");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PayAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicektOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketOrderModifyRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketOrderTraveler", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "BaseOrderId");

                    b.ToTable("TicketOrderTraveler", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketsCombinationOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("OrderStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketsCombinationId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketsCombinationName")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketsCombinationOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AdultsStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChildrenStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("ClaimantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClaimantName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Days")
                        .HasColumnType("int");

                    b.Property<string>("FeeIncludes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ImagePath")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<int>("Nights")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRooms")
                        .HasColumnType("int");

                    b.Property<string>("RallyPointAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<Point>("RallyPointLocation")
                        .HasColumnType("point");

                    b.Property<TimeSpan?>("RallyPointTime")
                        .HasColumnType("time");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TourGuideCarNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("TourGuideName")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("TourGuidePhoneNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("TravelBeginDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("TravelEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOrderTravelers", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CardValidDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ExtendInfo")
                        .HasColumnType("mediumtext");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Gender")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("Height")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("LastName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("NationalityCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NationalityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(20)");

                    b.Property<decimal?>("ShoeSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TravelLineOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOrderTravelers", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOtaOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOtaOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOtaOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TravelLineOtaOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOtaOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.WorkOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyFullName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("HopWorkOrderNumber")
                        .HasColumnType("varchar(64)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WorkOrderType")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("HopWorkOrderNumber")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("WorkOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.WorkOrderReply", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(1024)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HopReplyNumber")
                        .HasColumnType("varchar(64)");

                    b.Property<sbyte>("ReplyUserType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("WorkOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("WorkOrderReplyType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("HopReplyNumber")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("WorkOrderReply", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeAPPayable", b =>
                {
                    b.OwnsOne("Order.Api.Model.APPayable", "APPayable", b1 =>
                        {
                            b1.Property<long>("KingdeeAPPayableId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FBillNo")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据编号");

                            b1.Property<string>("FBillTypeID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据类型");

                            b1.Property<string>("FCURRENCYID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("币别");

                            b1.Property<DateTime>("FDATE")
                                .HasColumnType("datetime")
                                .HasComment("业务日期");

                            b1.Property<string>("FEXCHANGETYPE")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("汇率类型");

                            b1.Property<decimal>("FEntryTaxRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("税率(%)");

                            b1.Property<decimal>("FExchangeRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("汇率");

                            b1.Property<int?>("FID")
                                .HasColumnType("int");

                            b1.Property<string>("FMAINBOOKSTDCURRID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("本位币");

                            b1.Property<string>("FMATERIALID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("物料编码");

                            b1.Property<string>("FPAYORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("付款组织");

                            b1.Property<string>("FPRICEUNITID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("计价单位");

                            b1.Property<string>("FPURCHASEDEPTID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("采购部门");

                            b1.Property<string>("FPURCHASERGROUPID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("采购组织");

                            b1.Property<int>("FPriceQty")
                                .HasColumnType("int")
                                .HasComment("计价数量");

                            b1.Property<string>("FSETTLEORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("结算组织");

                            b1.Property<string>("FSUPPLIERID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("供应商");

                            b1.Property<decimal>("FTaxPrice")
                                .HasColumnType("decimal(18,2)")
                                .HasComment("含税单价");

                            b1.Property<string>("F_qwe_Assistant")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("产品线");

                            b1.Property<string>("F_qwe_Assistant1")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("抵冲类型");

                            b1.HasKey("KingdeeAPPayableId");

                            b1.ToTable("KingdeeAPPayable");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeAPPayableId");
                        });

                    b.Navigation("APPayable")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeARReceivable", b =>
                {
                    b.OwnsOne("Order.Api.Model.ARReceivable", "ARReceivable", b1 =>
                        {
                            b1.Property<long>("KingdeeARReceivableId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FAR_Remark")
                                .HasColumnType("varchar(200)")
                                .HasComment("备注");

                            b1.Property<string>("FBillNo")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据编号");

                            b1.Property<string>("FBillTypeID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据类型");

                            b1.Property<string>("FCURRENCYID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("币种");

                            b1.Property<string>("FCUSTOMERID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("客户");

                            b1.Property<DateTime>("FDATE")
                                .HasColumnType("datetime")
                                .HasComment("业务日期");

                            b1.Property<string>("FEXCHANGETYPE")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("汇率类型");

                            b1.Property<decimal?>("FEntryTaxRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("税率%");

                            b1.Property<decimal?>("FExchangeRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("汇率");

                            b1.Property<string>("FID")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FMAINBOOKSTDCURRID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("本位币");

                            b1.Property<string>("FMATERIALID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("物料编码");

                            b1.Property<string>("FPAYORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("收款组织");

                            b1.Property<string>("FPRICEUNITID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("计价单位");

                            b1.Property<int?>("FPriceQty")
                                .HasColumnType("int")
                                .HasComment("计价数量");

                            b1.Property<string>("FSALEDEPTID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("销售部门");

                            b1.Property<string>("FSALEORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("销售组织");

                            b1.Property<string>("FSETTLEORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("结算组织");

                            b1.Property<decimal?>("FTaxPrice")
                                .HasColumnType("decimal(18,2)")
                                .HasComment("含税单价");

                            b1.Property<string>("F_qwe_Assistant")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("产品线");

                            b1.Property<string>("F_qwe_Assistant1")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("抵冲类型");

                            b1.HasKey("KingdeeARReceivableId");

                            b1.ToTable("KingdeeARReceivable");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeARReceivableId");
                        });

                    b.Navigation("ARReceivable")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDCustomer", b =>
                {
                    b.OwnsOne("Order.Api.Model.BDCustomer", "BDCustomer", b1 =>
                        {
                            b1.Property<long>("KingdeeBDCustomerId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FCUSTID")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FCreateOrgId")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FName")
                                .IsRequired()
                                .HasColumnType("varchar(256)");

                            b1.Property<string>("FNumber")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FTRADINGCURRID")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.HasKey("KingdeeBDCustomerId");

                            b1.ToTable("KingdeeBDCustomer");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeBDCustomerId");
                        });

                    b.Navigation("BDCustomer")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDSupplier", b =>
                {
                    b.OwnsOne("Order.Api.Model.BDSupplier", "BDSupplier", b1 =>
                        {
                            b1.Property<long>("KingdeeBDSupplierId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FCreateOrgId")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FGroup")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FName")
                                .IsRequired()
                                .HasColumnType("varchar(256)");

                            b1.Property<string>("FNumber")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FPayCurrencyId")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FShortName")
                                .IsRequired()
                                .HasColumnType("varchar(128)");

                            b1.Property<int?>("FSupplierId")
                                .HasColumnType("int");

                            b1.HasKey("KingdeeBDSupplierId");

                            b1.ToTable("KingdeeBDSupplier");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeBDSupplierId");
                        });

                    b.Navigation("BDSupplier")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrder", b =>
                {
                    b.OwnsOne("Order.Api.Model.StoredValueCardGearInfo", "StoredValueCardGearInfo", b1 =>
                        {
                            b1.Property<long>("StoredValueCardOrderId")
                                .HasColumnType("bigint");

                            b1.Property<decimal>("GiftValue")
                                .HasColumnType("decimal(8,2)");

                            b1.Property<long>("Id")
                                .HasColumnType("bigint");

                            b1.Property<decimal>("Price")
                                .HasColumnType("decimal(8,2)");

                            b1.HasKey("StoredValueCardOrderId");

                            b1.ToTable("StoredValueCardOrder");

                            b1.WithOwner()
                                .HasForeignKey("StoredValueCardOrderId");
                        });

                    b.OwnsOne("Order.Api.Model.StoredValueCardInfo", "StoredValueCardInfo", b1 =>
                        {
                            b1.Property<long>("StoredValueCardOrderId")
                                .HasColumnType("bigint");

                            b1.Property<string>("CardName")
                                .IsRequired()
                                .HasColumnType("varchar(200)");

                            b1.Property<sbyte>("CardType")
                                .HasColumnType("tinyint");

                            b1.Property<string>("Cover")
                                .IsRequired()
                                .HasColumnType("varchar(500)");

                            b1.Property<long>("Id")
                                .HasColumnType("bigint");

                            b1.Property<string>("Instruction")
                                .IsRequired()
                                .HasColumnType("varchar(1000)");

                            b1.Property<int>("ProductBusinessType")
                                .HasColumnType("int");

                            b1.HasKey("StoredValueCardOrderId");

                            b1.ToTable("StoredValueCardOrder");

                            b1.WithOwner()
                                .HasForeignKey("StoredValueCardOrderId");
                        });

                    b.Navigation("StoredValueCardGearInfo");

                    b.Navigation("StoredValueCardInfo");
                });
#pragma warning restore 612, 618
        }
    }
}
