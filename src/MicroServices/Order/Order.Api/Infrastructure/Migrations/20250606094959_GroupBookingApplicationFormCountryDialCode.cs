using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class GroupBookingApplicationFormCountryDialCode : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CountryDialCode",
                table: "GroupBookingApplicationForm",
                type: "varchar(10)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<bool>(
                name: "NeedRecommendHotel",
                table: "GroupBookingApplicationDemand",
                type: "tinyint(1)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryDialCode",
                table: "GroupBookingApplicationForm");

            migrationBuilder.DropColumn(
                name: "NeedRecommendHotel",
                table: "GroupBookingApplicationDemand");
        }
    }
}
