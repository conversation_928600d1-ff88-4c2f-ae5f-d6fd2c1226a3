using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddScenicTicketOrderDelivery : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<sbyte>(
                name: "CredentialSourceType",
                table: "ScenicTicketOrder",
                type: "tinyint",
                nullable: true);

            migrationBuilder.AddColumn<sbyte>(
                name: "Status",
                table: "ScenicTicketOrder",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0);

            migrationBuilder.CreateTable(
                name: "ScenicTicketPurchaseDeliveryRecord",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    BaseOrderId = table.Column<long>(type: "bigint", nullable: false),
                    SubOrderId = table.Column<long>(type: "bigint", nullable: false),
                    Status = table.Column<sbyte>(type: "tinyint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScenicTicketPurchaseDeliveryRecord", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "ScenicTicketPurchaseVoucherUsedDetail",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    BaseOrderId = table.Column<long>(type: "bigint", nullable: false),
                    SubOrderId = table.Column<long>(type: "bigint", nullable: false),
                    PurchaseOrderId = table.Column<long>(type: "bigint", nullable: false),
                    TicketVoucherId = table.Column<long>(type: "bigint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScenicTicketPurchaseVoucherUsedDetail", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_ScenicTicketPurchaseDeliveryRecord_TenantId",
                table: "ScenicTicketPurchaseDeliveryRecord",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_ScenicTicketPurchaseVoucherUsedDetail_TenantId",
                table: "ScenicTicketPurchaseVoucherUsedDetail",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ScenicTicketPurchaseDeliveryRecord");

            migrationBuilder.DropTable(
                name: "ScenicTicketPurchaseVoucherUsedDetail");

            migrationBuilder.DropColumn(
                name: "CredentialSourceType",
                table: "ScenicTicketOrder");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "ScenicTicketOrder");
        }
    }
}
