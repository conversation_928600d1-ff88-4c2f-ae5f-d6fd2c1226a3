using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AfterSaleFinancialHandleOrderaddProp : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AccountTime",
                table: "AfterSaleFinancialHandleOrder",
                type: "datetime",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Operator",
                table: "AfterSaleFinancialHandleOrder",
                type: "varchar(100)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<long>(
                name: "OperatorId",
                table: "AfterSaleFinancialHandleOrder",
                type: "bigint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccountTime",
                table: "AfterSaleFinancialHandleOrder");

            migrationBuilder.DropColumn(
                name: "Operator",
                table: "AfterSaleFinancialHandleOrder");

            migrationBuilder.DropColumn(
                name: "OperatorId",
                table: "AfterSaleFinancialHandleOrder");
        }
    }
}
