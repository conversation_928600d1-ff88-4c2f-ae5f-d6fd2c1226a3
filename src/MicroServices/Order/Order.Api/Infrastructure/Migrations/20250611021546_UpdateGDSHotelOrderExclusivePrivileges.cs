using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class UpdateGDSHotelOrderExclusivePrivileges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ExclusivePrivileges",
                table: "GDSHotelOrder",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "RoomDescribe",
                table: "GDSHotelOrder",
                type: "varchar(1000)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExclusivePrivileges",
                table: "GDSHotelOrder");

            migrationBuilder.DropColumn(
                name: "RoomDescribe",
                table: "GDSHotelOrder");
        }
    }
}
