using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddKlookOrderIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ScenicTicketKlookVoucher_ScenicTicketKlookOrderId",
                table: "ScenicTicketKlookVoucher",
                column: "ScenicTicketKlookOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_ScenicTicketKlookOrderRecord_ScenicTicketKlookOrderId",
                table: "ScenicTicketKlookOrderRecord",
                column: "ScenicTicketKlookOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_ScenicTicketKlookOrder_BaseOrderId",
                table: "ScenicTicketKlookOrder",
                column: "BaseOrderId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ScenicTicketKlookVoucher_ScenicTicketKlookOrderId",
                table: "ScenicTicketKlookVoucher");

            migrationBuilder.DropIndex(
                name: "IX_ScenicTicketKlookOrderRecord_ScenicTicketKlookOrderId",
                table: "ScenicTicketKlookOrderRecord");

            migrationBuilder.DropIndex(
                name: "IX_ScenicTicketKlookOrder_BaseOrderId",
                table: "ScenicTicketKlookOrder");
        }
    }
}
