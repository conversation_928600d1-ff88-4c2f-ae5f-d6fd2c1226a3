using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class UpdateGroupBookingOperationLog : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<sbyte>(
                name: "OriginalStatus",
                table: "GroupBookingOperationLog",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0);

            migrationBuilder.AddColumn<string>(
                name: "Remark",
                table: "GroupBookingOperationLog",
                type: "varchar(500)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<sbyte>(
                name: "Status",
                table: "GroupBookingOperationLog",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OriginalStatus",
                table: "GroupBookingOperationLog");

            migrationBuilder.DropColumn(
                name: "Remark",
                table: "GroupBookingOperationLog");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "GroupBookingOperationLog");
        }
    }
}
