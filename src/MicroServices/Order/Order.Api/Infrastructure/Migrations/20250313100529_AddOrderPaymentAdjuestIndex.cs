using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddOrderPaymentAdjuestIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_OrderPaymentAdjust_BaseOrderId",
                table: "OrderPaymentAdjust",
                column: "BaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderPaymentAdjust_OrderId",
                table: "OrderPaymentAdjust",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_GroupBookingOrderPaymentAdjust_GroupBookingOrderId",
                table: "GroupBookingOrderPaymentAdjust",
                column: "GroupBookingOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_GroupBookingOrderPaymentAdjust_GroupBookingOrderPaymentId",
                table: "GroupBookingOrderPaymentAdjust",
                column: "GroupBookingOrderPaymentId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_OrderPaymentAdjust_BaseOrderId",
                table: "OrderPaymentAdjust");

            migrationBuilder.DropIndex(
                name: "IX_OrderPaymentAdjust_OrderId",
                table: "OrderPaymentAdjust");

            migrationBuilder.DropIndex(
                name: "IX_GroupBookingOrderPaymentAdjust_GroupBookingOrderId",
                table: "GroupBookingOrderPaymentAdjust");

            migrationBuilder.DropIndex(
                name: "IX_GroupBookingOrderPaymentAdjust_GroupBookingOrderPaymentId",
                table: "GroupBookingOrderPaymentAdjust");
        }
    }
}
