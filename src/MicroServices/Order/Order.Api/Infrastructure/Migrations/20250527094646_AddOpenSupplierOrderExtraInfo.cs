using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddOpenSupplierOrderExtraInfo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OpenSupplierOrderExtraInfo",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    BaseOrderId = table.Column<long>(type: "bigint", nullable: false),
                    OrderType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    OpenSupplierType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    DataType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    OptionKey = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OptionValue = table.Column<string>(type: "varchar(500)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenSupplierOrderExtraInfo", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_OpenSupplierOrderExtraInfo_BaseOrderId_TenantId",
                table: "OpenSupplierOrderExtraInfo",
                columns: new[] { "BaseOrderId", "TenantId" });

            migrationBuilder.CreateIndex(
                name: "IX_OpenSupplierOrderExtraInfo_TenantId",
                table: "OpenSupplierOrderExtraInfo",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OpenSupplierOrderExtraInfo");
        }
    }
}
