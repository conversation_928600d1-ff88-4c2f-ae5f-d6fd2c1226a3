using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddOpenChannelSyncFailOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OpenChannelSyncFailOrder",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    ChannelOrderNo = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ProductId = table.Column<long>(type: "bigint", nullable: false),
                    ProductName = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SkuId = table.Column<long>(type: "bigint", nullable: false),
                    SkuName = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AgencyId = table.Column<long>(type: "bigint", nullable: true),
                    TravelDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    ContactsName = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ContactsPhoneNumber = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ContactsEmail = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PaymentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OrderType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    SellingPlatform = table.Column<sbyte>(type: "tinyint", nullable: false),
                    SellingChannels = table.Column<sbyte>(type: "tinyint", nullable: false),
                    SyncStatus = table.Column<sbyte>(type: "tinyint", nullable: false),
                    DataContentJson = table.Column<string>(type: "text", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenChannelSyncFailOrder", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "OpenChannelSyncFailOrderRecord",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    OpenChannelSyncFailOrderId = table.Column<long>(type: "bigint", nullable: false),
                    ChannelOrderNo = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Reason = table.Column<string>(type: "varchar(500)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RecordSource = table.Column<sbyte>(type: "tinyint", nullable: false),
                    OperatorId = table.Column<long>(type: "bigint", nullable: true),
                    Operator = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenChannelSyncFailOrderRecord", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_OpenChannelSyncFailOrder_ChannelOrderNo",
                table: "OpenChannelSyncFailOrder",
                column: "ChannelOrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_OpenChannelSyncFailOrder_TenantId",
                table: "OpenChannelSyncFailOrder",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_OpenChannelSyncFailOrderRecord_ChannelOrderNo",
                table: "OpenChannelSyncFailOrderRecord",
                column: "ChannelOrderNo");

            migrationBuilder.CreateIndex(
                name: "IX_OpenChannelSyncFailOrderRecord_TenantId",
                table: "OpenChannelSyncFailOrderRecord",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OpenChannelSyncFailOrder");

            migrationBuilder.DropTable(
                name: "OpenChannelSyncFailOrderRecord");
        }
    }
}
