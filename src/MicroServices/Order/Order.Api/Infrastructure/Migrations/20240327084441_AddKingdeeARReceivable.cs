using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddKingdeeARReceivable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "King<PERSON>eARReceivable",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    KingdeePushId = table.Column<long>(type: "bigint", nullable: false),
                    ReceivableType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime", nullable: false),
                    AgencyId = table.Column<long>(type: "bigint", nullable: false),
                    ARReceivable_FID = table.Column<string>(type: "varchar(32)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FBillTypeID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "单据类型")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FBillNo = table.Column<string>(type: "varchar(32)", nullable: false, comment: "单据编号")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FDATE = table.Column<DateTime>(type: "datetime", nullable: false, comment: "业务日期"),
                    ARReceivable_FMAINBOOKSTDCURRID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "本位币")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FEXCHANGETYPE = table.Column<string>(type: "varchar(32)", nullable: false, comment: "汇率类型")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FExchangeRate = table.Column<decimal>(type: "decimal(8,2)", nullable: true, comment: "汇率"),
                    ARReceivable_FMATERIALID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "物料编码")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FPRICEUNITID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "计价单位")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FPriceQty = table.Column<int>(type: "int", nullable: true, comment: "计价数量"),
                    ARReceivable_F_qwe_Assistant = table.Column<string>(type: "varchar(32)", nullable: false, comment: "产品线")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_F_qwe_Assistant1 = table.Column<string>(type: "varchar(32)", nullable: false, comment: "抵冲类型")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FTaxPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true, comment: "含税单价"),
                    ARReceivable_FEntryTaxRate = table.Column<decimal>(type: "decimal(8,2)", nullable: true, comment: "税率%"),
                    ARReceivable_FSETTLEORGID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "结算组织")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FPAYORGID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "收款组织")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FSALEORGID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "销售组织")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FSALEDEPTID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "销售部门")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FCUSTOMERID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "客户")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FCURRENCYID = table.Column<string>(type: "varchar(32)", nullable: false, comment: "币种")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ARReceivable_FAR_Remark = table.Column<string>(type: "varchar(200)", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KingdeeARReceivable", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_KingdeeARReceivable_TenantId",
                table: "KingdeeARReceivable",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "KingdeeARReceivable");
        }
    }
}
