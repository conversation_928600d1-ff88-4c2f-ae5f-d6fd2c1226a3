using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddTravelLineOrderSkuTypeItem : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TravelLineOrderSkuTypeItem",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    BaseOrderId = table.Column<long>(type: "bigint", nullable: false),
                    TravelLineOrderId = table.Column<long>(type: "bigint", nullable: false),
                    SkuTypeItemId = table.Column<long>(type: "bigint", nullable: false),
                    SkuTypeItemName = table.Column<string>(type: "varchar(500)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SkuPriceType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    ActivityId = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PackageId = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SkuId = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TravelLineOrderSkuTypeItem", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TravelLineOrderSkuTypeItem_BaseOrderId",
                table: "TravelLineOrderSkuTypeItem",
                column: "BaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_TravelLineOrderSkuTypeItem_TenantId",
                table: "TravelLineOrderSkuTypeItem",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TravelLineOrderSkuTypeItem");
        }
    }
}
