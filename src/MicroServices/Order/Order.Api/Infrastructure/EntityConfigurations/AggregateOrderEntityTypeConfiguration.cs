using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class AggregateOrderEntityTypeConfiguration : TenantBaseConfiguration<Model.AggregateOrder>, IEntityTypeConfiguration<Model.AggregateOrder>
    {
        public void Configure(EntityTypeBuilder<Model.AggregateOrder> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.OrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.OrderStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.ConfirmStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.TravelStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.RefundStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.Remark)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.OrderMark)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.OrderType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CarHailingType)
                .HasColumnType("tinyint");

            builder.Property(s => s.TicketsType)
                .HasColumnType("tinyint");

            builder.Property(s => s.TicketBusinessType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CarProductType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CarProductPurchaseSourceType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CountryCode)
                .HasColumnType("int");

            builder.Property(s => s.ProvinceCode)
                .HasColumnType("int");

            builder.Property(s => s.CityCode)
                .HasColumnType("int");

            builder.Property(s => s.ResourceId)
                .HasColumnType("bigint");

            builder.Property(s => s.ResourceName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.EnResourceName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.ProductId)
                .HasColumnType("bigint");

            builder.Property(s => s.ProductName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.EnProductName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.ProductSkuId)
                .HasColumnType("bigint");

            builder.Property(s => s.ProductSkuName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.EnProductSkuName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.StaffTag)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.Tags)
                .HasColumnType("tinyint");

            builder.Property(s => s.CredentialSourceType)
                .HasColumnType("tinyint");

            builder.Property(s => s.SkuValidityBegin)
                .HasColumnType("datetime");

            builder.Property(s => s.SkuValidityEnd)
                .HasColumnType("datetime");

            builder.Property(s => s.TicketCodes)
                .HasColumnType("varchar(2048)");

            builder.Property(s => s.DeparturePointName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.DestinationPointName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.Status)
                .HasColumnType("tinyint");

            builder.Property(s => s.LineProductPurchaseSourceType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.OrderExpired)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.AirportTransferType)
                .HasColumnType("tinyint");

            builder.Property(s => s.TenantDeaprtmentId)
                .HasColumnType("bigint");

            builder.Property(s => s.AgencyId)
                .HasColumnType("bigint");

            builder.Property(s => s.AgencyName)
                .HasColumnType("varchar(256)");

            builder.Property(s => s.SellingPlatform)
                .HasColumnType("tinyint");

            //tinyint 默认有符号 范围 -128~127,不满足该字段的要求，使用其他整数类型
            builder.Property(s => s.SellingChannels)
                .HasColumnType("smallint");

            builder.Property(s => s.ChannelOrderNo)
                .HasColumnType("varchar(512)");

            builder.Property(s => s.GroupNo)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.ContactsName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.ContactsPhoneNumber)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.ContactsEmail)
                .HasColumnType("varchar(50)");

            //tinyint 默认有符号 范围 -128~127,不满足该字段的要求，使用其他整数类型
            builder.Property(s => s.SupplierApiType)
                .HasColumnType("smallint");

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");

            builder.Property(s => s.TotalCost)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CostDiscountAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CostDiscountRate)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CostCurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.CostExchangeRate)
                .HasColumnType("decimal(18,6)");

            builder.Property(s => s.SupplierOrderId)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.ConfirmCode)
                .HasColumnType("varchar(128)");

            builder.Property(s => s.HotelOrderCancelRule)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.Quantity)
                .HasColumnType("int");

            builder.Property(s => s.TotalAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.DiscountAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.PaymentAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.ExchangeRate)
                .HasColumnType("decimal(18,6)");

            builder.Property(s => s.RefundTotalAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.PaymentCurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.PaymentType)
                .HasColumnType("int");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.ConfirmTime)
                .HasColumnType("datetime");

            builder.Property(s => s.TravelDate)
                .HasColumnType("datetime");

            builder.Property(s => s.FinishDate)
                .HasColumnType("datetime");

            builder.Property(s => s.TravelBeginDate)
                .HasColumnType("datetime");

            builder.Property(s => s.TravelEndDate)
                .HasColumnType("datetime");

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");

            builder.Property(s => s.UserNickName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.Travelers)
                .HasColumnType("text");

            builder.Property(s => s.TravelersCountJson)
               .HasColumnType("text");

            builder.Property(s => s.SalespersonId)
                .HasColumnType("bigint");

            builder.Property(s => s.SalespersonName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.DevelopUserId)
                .HasColumnType("bigint");

            builder.Property(s => s.OperatorUserId)
                .HasColumnType("bigint");

            builder.Property(s => s.TrackingUserId)
                .HasColumnType("bigint");

            builder.Property(s => s.IsGroupRoom)
              .HasColumnType("tinyint");

            builder.Property(s => s.UserPhone)
              .HasColumnType("varchar(50)");

            builder.Property(s => s.SeriesNumber)
             .HasColumnType("varchar(100)");

            builder.Property(s => s.DeliveryErrorMsg)
              .HasColumnType("varchar(1000)");

            builder.Property(s => s.DeliveryErrorCode)
                .HasColumnType("int");

            builder.Property(s => s.VccPaymentStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.AbnormalReason)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.AbnormalOrderSourceType)
                .HasColumnType("tinyint");

            builder.Property(s => s.DeliveryStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.Passengers)
                 .HasColumnType("int");

            builder.Property(s => s.PayableOffsetAmount)
                .HasColumnType("decimal(18,2)");
            builder.Property(s => s.ReceiptOffsetAmount)
                .HasColumnType("decimal(18,2)");
            builder.Property(s => s.CommissionFee)
                .HasColumnType("decimal(18,2)");
            builder.Property(s => s.InsurePurchaseAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.ClosedDate)
                .HasColumnType("datetime");

            builder.Property(s => s.ProcessingLevelTag)
                .HasColumnType("tinyint");

            builder.Property(s => s.CommissionRate)
              .HasColumnType("decimal(18,2)");

            builder.Property(s => s.OperatorAssistantUserId)
              .HasColumnType("bigint");

            builder.Property(s => s.DelayedPayStatus)
               .HasColumnType("tinyint(1)")
               .ValueGeneratedNever();

            builder.Property(s => s.DelayedPayDeadline)
                .HasColumnType("datetime");

            builder.Property(s => s.OrderCategory)
                .HasColumnType("tinyint")
                .HasDefaultValue(OrderCategory.RegularOrder);

            builder.Property(s => s.SupplierCommissionFee)
               .HasColumnType("decimal(18,2)");
            builder.Property(s => s.SupplierCommissionRate)
               .HasColumnType("decimal(18,2)");

            builder.Property(s => s.OrderUserPhones)
              .HasColumnType("text");
            builder.Property(s => s.OrderUserEmails)
             .HasColumnType("text");
            builder.Property(s => s.OrderUserNames)
             .HasColumnType("text");

            builder.HasIndex(x => x.BaseOrderId);
            builder.HasIndex(x => x.TrackingUserId);
            builder.HasIndex(x => x.SupplierOrderId);
            builder.HasIndex(x => x.CreateTime);
        }
    }
}
