using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class TicketsCombinationMergeOrderItemEntityTypeConfiguration :
        TenantBaseConfiguration<Model.TicketsCombinationMergeOrderItem>,
        IEntityTypeConfiguration<Model.TicketsCombinationMergeOrderItem>
    {
        public void Configure(EntityTypeBuilder<Model.TicketsCombinationMergeOrderItem> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.CombinationOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.MergeOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.SupplierOrderId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
            
            //index
            builder.HasIndex(s => s.BaseOrderId);
            builder.HasIndex(s => s.CombinationOrderId);
        }
    }
}