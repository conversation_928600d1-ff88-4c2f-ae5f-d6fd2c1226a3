using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class GroupBookingQuotationAdditionEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupBookingQuotationAddition>, IEntityTypeConfiguration<Model.GroupBookingQuotationAddition>
    {
        public void Configure(EntityTypeBuilder<Model.GroupBookingQuotationAddition> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.GroupBookingQuotationId)
                .HasColumnType("bigint");

            builder.Property(s => s.GroupBookingQuotationItemId)
                .HasColumnType("bigint");

            builder.Property(s => s.AdditionName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.Cost)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CostCurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.Quantity)
                .HasColumnType("int");

            builder.Property(s => s.Remark)
                .HasColumnType("varchar(500)");

            builder.HasIndex(s => s.GroupBookingQuotationId);
        }
    }
}
