using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class ScenicTicketKlookVoucherEntityTypeConfiguration : TenantBaseConfiguration<Model.ScenicTicketKlookVoucher>, IEntityTypeConfiguration<Model.ScenicTicketKlookVoucher>
    {
        public void Configure(EntityTypeBuilder<Model.ScenicTicketKlookVoucher> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ScenicTicketKlookOrderId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.FilePath)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.Thumbnail)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.SourcePath)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.PdfSourcePath)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.ImageSourcePath)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            //索引
            builder.HasIndex(s => s.ScenicTicketKlookOrderId);
        }
    }
}
