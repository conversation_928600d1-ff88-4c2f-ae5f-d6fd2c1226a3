using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class AggregateChannelOrderNoEntityTypeConfiguration : TenantBaseConfiguration<Model.AggregateChannelOrderNo>, IEntityTypeConfiguration<Model.AggregateChannelOrderNo>
    {
        public void Configure(EntityTypeBuilder<Model.AggregateChannelOrderNo> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.ChannelOrderNo)
               .HasColumnType("varchar(500)");

            builder.HasIndex(x => x.BaseOrderId);
            builder.HasIndex(x => new { x.TenantId, x.ChannelOrderNo });
        }
    }
}
