using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations;

public class WorkOrderServiceEvaluationEntityTypeConfiguration : KeyBaseConfiguration<WorkOrderServiceEvaluation>, IEntityTypeConfiguration<WorkOrderServiceEvaluation>
{
    public void Configure(EntityTypeBuilder<WorkOrderServiceEvaluation> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.WorkOrderId)
               .HasColumnType("bigint");

        builder.Property(s => s.Evaluation)
               .HasColumnType("tinyint");

        builder.Property(s => s.Content)
               .HasColumnType("varchar(1024)");

        builder.Property(s => s.CreateTime)
               .HasColumnType("datetime");
    }
}
