using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations;

public class OrderPriceChangeRecordEntityTypeConfiguration : TenantBaseConfiguration<Model.OrderPriceChangeRecord>, IEntityTypeConfiguration<Model.OrderPriceChangeRecord>
{
    public void Configure(EntityTypeBuilder<OrderPriceChangeRecord> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.BaseOrderId)
              .HasColumnType("bigint")
              .IsRequired();

        builder.Property(s => s.OldAmount)
           .HasColumnType("decimal(18,6)")
           .IsRequired();

        builder.Property(s => s.NewAmount)
           .HasColumnType("decimal(18,6)")
           .IsRequired();

        builder.Property(s => s.CurrencyCode)
           .HasColumnType("varchar(20)")
           .IsRequired();

        builder.Property(s => s.Rate)
           .HasColumnType("decimal(18,6)")
           .IsRequired();

        builder.Property(s => s.Amount)
          .HasColumnType("decimal(18,6)")
          .IsRequired();

        builder.Property(s => s.SupplierApiType)
         .HasColumnType("tinyint")
         .IsRequired();

        builder.Property(s => s.SupplierOrderId)
           .HasColumnType("varchar(100)");

        builder.Property(s => s.CreateTime)
          .HasColumnType("datetime");
    }
}
