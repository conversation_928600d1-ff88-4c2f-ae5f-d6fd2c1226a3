using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class GroupBookingOperatorUserEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupBookingOperatorUser>, IEntityTypeConfiguration<Model.GroupBookingOperatorUser>
    {
        public void Configure(EntityTypeBuilder<Model.GroupBookingOperatorUser> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Order)
                .HasColumnType("int");

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");

            builder.Property(s => s.UserName)
                .HasColumnType("varchar(256)");

            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");
        }
    }
}
