using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class GroupBookingPreOrderItemEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupBookingPreOrderItem>, IEntityTypeConfiguration<Model.GroupBookingPreOrderItem>
    {
        public void Configure(EntityTypeBuilder<Model.GroupBookingPreOrderItem> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.PreOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.SupplierApiType)
                .HasColumnType("tinyint");

            builder.Property(s => s.HotelId)
                .HasColumnType("bigint");

            builder.Property(s => s.HotelName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.HotelRoomId)
                .HasColumnType("bigint");

            builder.Property(s => s.HotelRoomName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.PriceStrategyId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.PriceStrategyName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.RoomCount)
                .HasColumnType("int");

            builder.Property(s => s.NightCount)
                .HasColumnType("int");

            builder.Property(s => s.NumberOfBreakfast)
                .HasColumnType("int");

            builder.Property(s => s.BedTypeJson)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.PreBookingCode)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.Price)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.NightlyPriceJson)
                .HasColumnType("text");

            builder.Property(s => s.CheckIn)
              .HasColumnType("datetime");

            builder.Property(s => s.CheckOut)
               .HasColumnType("datetime");

            builder.Property(s => s.ChildPolicy)
                .HasColumnType("varchar(500)");

        }
    }
}
