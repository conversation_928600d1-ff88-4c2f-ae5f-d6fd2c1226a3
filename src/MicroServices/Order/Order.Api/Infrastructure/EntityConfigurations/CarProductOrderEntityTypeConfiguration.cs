using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class CarProductOrderEntityTypeConfiguration : TenantBaseConfiguration<Model.CarProductOrder>, IEntityTypeConfiguration<Model.CarProductOrder>
    {
        public void Configure(EntityTypeBuilder<Model.CarProductOrder> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.CarProductId)
                .HasColumnType("bigint");

            builder.Property(s => s.Title)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.CarProductType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.PurchaseSourceType)
                .HasColumnType("tinyint")
                .HasDefaultValue(CarProductPurchaseSourceType.OfflinePurchase);

            builder.Property(s => s.Instructions)
                .HasColumnType("mediumtext");

            builder.Property(s => s.FeeInclude)
                .HasColumnType("mediumtext");

            builder.Property(s => s.FeeExclude)
                .HasColumnType("mediumtext");

            builder.Property(s => s.CancelType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CancelRule)
                .HasColumnType("varchar(1000)");

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");

            builder.Property(s => s.CarProductSkuId)
                .HasColumnType("bigint");

            builder.Property(s => s.AirportTransferType)
                .HasColumnType("tinyint");

            builder.Property(s => s.Name)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.CarTypeGradeId)
                .HasColumnType("bigint");

            builder.Property(s => s.GradeName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.SupplierOrderId)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.Quantity)
                .HasColumnType("int");

            builder.Property(s => s.Passengers)
                .HasColumnType("int");

            builder.Property(s => s.Baggages)
                .HasColumnType("int");

            builder.Property(s => s.DepartureCityCode)
                .HasColumnType("int");

            builder.Property(s => s.DepartureCityName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.DepartureAddress)
                .HasColumnType("varchar(256)");

            builder.Property(s => s.DestinationCityCode)
                .HasColumnType("int");

            builder.Property(s => s.DestinationCityName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.DestinationAddress)
                .HasColumnType("varchar(256)");

            builder.Property(s => s.TravelDate)
                .HasColumnType("datetime");

            builder.Property(s => s.AirportId)
                .HasColumnType("bigint");

            builder.Property(s => s.FlightNumber)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.FlightTime)
                .HasColumnType("datetime");

            builder.Property(s => s.AirportTerminal)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.IsLandingVisa)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.AlternatePhoneNumber)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.WeChatID)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.Status)
                .HasColumnType("tinyint")
                .IsConcurrencyToken();

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.FinishTime)
                .HasColumnType("datetime");

            builder.Property(s => s.ConfirmTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.AutoConfirm)
              .HasColumnType("tinyint(1)");

            builder.Property(s => s.OpenSupplierType)
                .HasColumnType("tinyint")
                .HasDefaultValue(OpenSupplierType.System);
            
            builder.Property(s => s.ConfirmCode)
                .HasColumnType("varchar(128)");

            builder.Property(s => s.MinProfit)
             .HasColumnType("decimal(18,2)");

            builder.Property(s => s.BookingDuration)
               .HasColumnType("int");
        }
    }
}
