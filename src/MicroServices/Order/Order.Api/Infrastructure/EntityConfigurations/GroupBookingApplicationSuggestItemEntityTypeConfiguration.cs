using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class GroupBookingApplicationSuggestItemEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupBookingApplicationSuggestItem>, IEntityTypeConfiguration<Model.GroupBookingApplicationSuggestItem>
    {
        public void Configure(EntityTypeBuilder<Model.GroupBookingApplicationSuggestItem> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ApplicationOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.HotelId)
                .HasColumnType("bigint");

            builder.Property(s => s.HotelName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.CityCode)
                .HasColumnType("int");

            builder.Property(s => s.CityName)
                .HasColumnType("varchar(50)");
        }
    }
}
