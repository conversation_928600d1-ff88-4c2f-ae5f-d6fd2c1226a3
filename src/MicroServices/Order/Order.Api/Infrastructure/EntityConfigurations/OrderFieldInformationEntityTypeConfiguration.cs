using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations;

public class OrderFieldInformationEntityTypeConfiguration : TenantBaseConfiguration<Model.OrderFieldInformation>, IEntityTypeConfiguration<Model.OrderFieldInformation>
{
    public void Configure(EntityTypeBuilder<Model.OrderFieldInformation> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.OrderFieldInformationTypeId)
          .HasColumnType("bigint");

        builder.Property(s => s.FieldName)
            .HasColumnType("varchar(100)");

        builder.Property(s => s.FieldCode)
           .HasColumnType("varchar(50)")
           .IsRequired();

        builder.Property(s => s.FieldType)
          .HasColumnType("tinyint")
          .IsRequired();

        builder.Property(s => s.FieldValue)
          .HasColumnType("text");

        builder.Property(s => s.Sort)
          .HasColumnType("tinyint");

        builder.Property(s => s.Group)
          .HasColumnType("varchar(64)");

        builder.Property(s => s.GroupName)
          .HasColumnType("varchar(20)");

        builder.Property(s => s.GroupSort)
          .HasColumnType("tinyint");

        builder.Property(s => s.IsRequired)
           .HasColumnType("tinyint(1)");

        builder.HasIndex(s => s.OrderFieldInformationTypeId);
    }
}
