using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations;

public class GroupBookingHotelInquiryRecordRenderEntityTypeConfiguration
    : TenantBaseConfiguration<GroupBookingHotelInquiryRecordRender>,
      IEntityTypeConfiguration<GroupBookingHotelInquiryRecordRender>
{
    public void Configure(EntityTypeBuilder<GroupBookingHotelInquiryRecordRender> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.GroupBookingHotelInquiryRecordId)
            .HasColumnType("bigint");

        builder.Property(s => s.GroupBookingHotelInquiryId)
            .HasColumnType("bigint");

        builder.Property(s => s.ApplicationFormId)
            .HasColumnType("bigint");

        builder.Property(s => s.Description)
            .HasColumnType("text");

        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime");
    }
}

