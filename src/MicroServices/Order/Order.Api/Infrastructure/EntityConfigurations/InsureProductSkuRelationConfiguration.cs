using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class InsureProductSkuRelationConfiguration : TenantBaseConfiguration<Model.InsureProductSkuRelation>, IEntityTypeConfiguration<Model.InsureProductSkuRelation>
    {
        public void Configure(EntityTypeBuilder<Model.InsureProductSkuRelation> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ProductId)
                .HasColumnType("bigint")
                .IsRequired();

            builder.Property(s => s.ProductSkuId)
                .HasColumnType("bigint")
                .IsRequired();

            builder.Property(s => s.InsureProductId)
                .HasColumnType("bigint");

            builder.Property(s => s.Type)
                .HasColumnType("tinyint")
                .IsRequired();

        }
    }
}
