using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class SettlementOrderTransferRecordEntityTypeConfiguration : KeyBaseConfiguration<Model.SettlementOrderTransferRecord>, IEntityTypeConfiguration<Model.SettlementOrderTransferRecord>
    {
        public void Configure(EntityTypeBuilder<Model.SettlementOrderTransferRecord> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.SettlementOrderId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.TransferType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.BankFee)
                .HasColumnType("decimal(18,2)");
            
            builder.Property(s => s.PayerId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.PayerName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.Status)
                .HasColumnType("tinyint")
                .IsConcurrencyToken();
            
            builder.Property(s => s.ErrorMsg)
                .HasColumnType("varchar(500)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
        }
    }
}
