using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 结算单提现记录
/// </summary>
public class SettlementOrderTransferRecord : KeyBase
{
    /// <summary>
    /// 结算单号
    /// </summary>
    public long SettlementOrderId { get; set; }

    /// <summary>
    /// 结算单转账类型
    /// </summary>
    public SettlementTransferType TransferType { get; set; }

    /// <summary>
    /// 银行手续费
    /// </summary>
    public decimal BankFee { get; set; }

    /// <summary>
    /// 付款人Id
    /// </summary>
    public long? PayerId { get; set; }

    /// <summary>
    /// 付款人
    /// </summary>
    public string? PayerName { get; set; }

    /// <summary>
    /// 转账状态
    /// </summary>
    public SettlementTransferStatus Status { get; set; }

    /// <summary>
    /// 失败信息
    /// </summary>
    public string? ErrorMsg { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}
