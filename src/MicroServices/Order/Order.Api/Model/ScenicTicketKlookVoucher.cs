using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 门票客路订单凭证
/// </summary>
public class ScenicTicketKlookVoucher : TenantBase
{
    /// <summary>
    /// 门票客路订单Id
    /// </summary>
    public long ScenicTicketKlookOrderId { get; set; }
    
    /// <summary>
    /// 凭证(PDF)文件路径
    /// </summary>
    public string FilePath { get; set; }

    /// <summary>
    /// 凭证(PDF)缩略图(第一页)
    /// </summary>
    public string Thumbnail { get; set; }
    
    /// <summary>
    /// 凭证来源路径
    /// </summary>
    public string? SourcePath { get; set; }

    /// <summary>
    /// 凭证来源PDF路径
    /// </summary>
    public string? PdfSourcePath { get; set; }
    
    /// <summary>
    /// 凭证来源图片路径
    /// </summary>
    public string? ImageSourcePath { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
}