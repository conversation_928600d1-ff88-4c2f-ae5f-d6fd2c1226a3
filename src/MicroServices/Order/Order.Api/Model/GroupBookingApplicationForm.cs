using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.User.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 团房申请单
/// </summary>
public class GroupBookingApplicationForm : TenantBase
{
    /// <summary>
    /// 分销商
    /// </summary>
    public long AgencyId { get; set; }

    public string AgencyName { get; set; } = string.Empty;

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public string? SalespersonName { get; set; }

    /// <summary>
    /// 团队性质
    /// </summary>
    public TeamNatureType? TeamNatureType { get; set; }

    /// <summary>
    /// 团队性质-其他 活动名称
    /// </summary>
    public string? TeamNatureTitle { get; set; }

    /// <summary>
    /// 团队性质 - 英文
    /// </summary>
    public string? EnTeamNatureTitle { get; set; }

    /// <summary>
    /// 账号类型
    /// </summary>
    public Contracts.Common.Order.Enums.UserType UserType { get; set; }

    /// <summary>
    /// 账号id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 账号名称
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 用户来源类型
    /// </summary>
    public UserPlatform UserPlatform { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string? ContactName { get; set; }

    /// <summary>
    /// 国家区号
    /// </summary>
    public string? CountryDialCode { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string ContactPhone { get; set; } = string.Empty;

    /// <summary>
    /// 联系邮箱
    /// </summary>
    public string? ContactEmail { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 其他需求
    /// </summary>
    public string? OtherRequirements { get; set; }

    /// <summary>
    /// 其他需求 英文
    /// </summary>
    public string? ENOtherRequirements { get; set; }

    /// <summary>
    /// 是否接受推荐酒店
    /// </summary>
    public bool AcceptRecommendedHotels { get; set; }

    /// <summary>
    /// 申请人id
    /// </summary>
    public long? ApplicantId { get; set; }

    /// <summary>
    /// 申请人
    /// </summary>
    public string? Applicant { get; set; }

    /// <summary>
    /// 申请人类型
    /// </summary>
    public Contracts.Common.Order.Enums.UserType ApplicantUserType { get; set; }

    /// <summary>
    /// 申请时间
    /// </summary>
    public DateTime ApplicationTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 完结时间 预订单已付尾款/申请单已取消 记录完结时间
    /// </summary>
    public DateTime? FinishTime { get; set; }

    public GroupBookingApplicationFormStatus Status { get; set; }

    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 指派人id 管理后台员工id
    /// </summary>
    public long? AssignorId { get; set; }

    /// <summary>
    /// 指派人
    /// </summary>
    public string? Assignor { get; set; }

    /// <summary>
    /// 跟进运营id 商户员工id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 合同内容
    /// </summary>
    public string? ContractContent { get; set; }

    /// <summary>
    /// 合同是否已签署
    /// </summary>
    public bool? ContractSigned { get; set; }

    /// <summary>
    /// 推广活码id
    /// </summary>
    public long? PromotionTraceId { get; set; }
}

