using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

public class TravelLineSupplierOrderRecord : TenantBase
{
    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }
    
    /// <summary>
    /// 供应商订单id
    /// </summary>
    public string? SupplierOrderId { get; set; }
    
    /// <summary>
    /// 记录类型
    /// </summary>
    public LineProductSupplierOrderRecordType RecordType { get; set; }

    /// <summary>
    /// 表示对应记录类型的操作结果是否成功
    /// <list type="bullet">
    /// <item>
    /// <description>RecordType = 1, IsSuccess = false 表示创建订单失败</description>
    /// </item>
    /// </list>
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 失败信息
    /// </summary>
    public string? ErrorMsg { get; set; }

    /// <summary>
    /// 错误码
    /// </summary>
    public int? ErrorCode { get; set; }
    
    public DateTime CreateTime { get; set; } = DateTime.Now;
}