using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 工单服务评价
/// </summary>
public class WorkOrderServiceEvaluation : TenantBase
{
    public long WorkOrderId { get; set; }
    /// <summary>
    /// 评价
    /// </summary>
    public EvaluationType Evaluation { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 评价用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 评价用户名
    /// </summary>
    public string UserName { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}

