using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

public class InvoiceRecord : TenantBase
{
    /// <summary>
    /// 发票流水号
    /// </summary>
    public string InvoiceSerialNo { get; set; }

    /// <summary>
    /// 开票来源
    /// </summary>
    public InvoiceSourceChannel SourceChannel { get; set; }

    /// <summary>
    /// 系统订单Id - BaseOrderId / 结算单Id
    /// </summary>
    public long OrderId { get; set; }

    /// <summary>
    /// 发票内容
    /// </summary>
    public string ContentType { get; set; }

    /// <summary>
    /// 开票金额
    /// </summary>
    public decimal InvoiceAmount { get; set; }

    /// <summary>
    /// 发票接收邮箱
    /// </summary>
    public string EmailAddress { get; set; }

    /// <summary>
    /// 开票状态
    /// </summary>
    public InvoiceStatus Status { get; set; }

    /// <summary>
    /// 开票中，子状态，标识 是否为查询中的状态
    /// </summary>
    public bool IsQueryingStatus { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 发票号码
    /// </summary>
    public string? InvNumber { get; set; }

    /// <summary>
    /// 发票代码
    /// </summary>
    public string? InvCode { get; set; }

    #region 抬头信息

    /// <summary>
    /// 抬头类型
    /// </summary>
    public InvoiceTitleType TitleType { get; set; }

    /// <summary>
    /// 抬头
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 纳税识别号
    /// </summary>
    public string? ITIN { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? ContactNumber { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    public string? BankName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public string? BankNumber { get; set; }

    #endregion

    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>
    public string CreatorName { get; set; }

    /// <summary>
    /// 开票人
    /// </summary>
    public string Invoicer { get; set; }

    /// <summary>
    /// 发票PDF下载链接
    /// </summary>
    public string? InvoicePdfUrl { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 发票接口配置Id
    /// </summary>
    public long? InvoiceConfigId { get; set; }

}
