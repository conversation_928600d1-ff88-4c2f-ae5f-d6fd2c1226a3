using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model
{
    /// <summary>
    /// 主订单 - 可能关联多个子订单
    /// </summary>
    public class BaseOrder : TenantBase
    {
        /// <summary>
        /// 连续单号
        /// </summary>
        public string? SeriesNumber { get; set; }

        #region 订单用户信息

        /// <summary>
        /// 下单用户Id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 下单用户昵称
        /// </summary>
        public string? UserNickName { get; set; }

        /// <summary>
        /// 会员等级id
        /// </summary>
        public long VipLevelId { get; set; }

        /// <summary>
        /// 会员等级
        /// </summary>
        public string? VipLevelName { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactsName { get; set; }

        /// <summary>
        /// 联系人手机号
        /// </summary>
        public string ContactsPhoneNumber { get; set; }

        /// <summary>
        /// 联系人邮箱
        /// </summary>
        public string? ContactsEmail { get; set; }

        /// <summary>
        /// 分销商id
        /// </summary>
        public long AgencyId { get; set; }

        /// <summary>
        /// 分销商
        /// </summary>
        public string? AgencyName { get; set; }

        /// <summary>
        /// 销售BD
        /// </summary>
        public long? SalespersonId { get; set; }

        /// <summary>
        /// 销售BD
        /// </summary>
        public string? SalespersonName { get; set; }

        #endregion

        /// <summary>
        /// 资源名称
        /// </summary>
        public string? ResourceName { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 规格名称
        /// </summary>
        public string? ProductSkuName { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public OrderType OrderType { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public BaseOrderStatus Status { get; set; }

        /// <summary>
        /// 售卖平台
        /// </summary>
        public SellingPlatform SellingPlatform { get; set; }

        /// <summary>
        /// 售卖渠道
        /// </summary>
        public SellingChannels SellingChannels { get; set; }

        /// <summary>
        /// 售卖渠道单号
        /// </summary>
        public string? ChannelOrderNo { get; set; }

        /// <summary>
        /// 团号 可选项
        /// </summary>
        public string? GroupNo { get; set; }

        /// <summary>
        /// 订单总额 = DiscountAmount + Payment.Amount
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠金额
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// 支付金额
        /// </summary>
        public decimal PaymentAmount { get; set; }
        
        /// <summary>
        /// 采购总折扣金额
        /// </summary>
        public decimal CostDiscountAmount { get; set; }

        #region Payment

        /// <summary>
        /// 订单支付币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; } = Currency.CNY.ToString();

        /// <summary>
        /// 支付类型
        /// </summary>
        public PayType PaymentType { get; set; }

        /// <summary>
        /// 支付渠道
        /// </summary>
        public string? PaymentChannel { get; set; }

        /// <summary>
        /// 支付方式  微信h5、微信小程序、微信扫码等
        /// </summary>
        public string? PaymentMode { get; set; }

        /// <summary>
        /// 外部支付单号
        /// </summary>
        public string? PaymentExternalNo { get; set; }

        /// <summary>
        /// 支付时间 2024-09-25新增后支付成功有值
        /// </summary>
        public DateTime? PayTime { get; set; }

        /// <summary>
        /// 延时支付状态 null-非延迟支付订单 0-延时订单未实付 1-延时订单已实付
        /// </summary>
        public bool? DelayedPayStatus { get; set; }

        /// <summary>
        /// 延时支付订单时有值 实付截至时间
        /// </summary>
        public DateTime? DelayedPayDeadline { get; set; }

        /// <summary>
        /// 分销佣金 门票 线路 用车 指定渠道 存在佣金 （出账）
        /// </summary>
        public decimal? CommissionFee { get; set; }

        /// <summary>
        /// 分销佣金 百分比
        /// </summary>
        public decimal? CommissionRate { get; set; }

        #endregion

        /// <summary>
        /// 留言
        /// </summary>
        public string? Message { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 产品人id
        /// </summary>
        public long? DevelopUserId { get; set; }

        /// <summary>
        /// 运营人id
        /// </summary>
        public long? OperatorUserId { get; set; }

        /// <summary>
        /// 跟单人id
        /// </summary>
        public long? TrackingUserId { get; set; }
        
        /// <summary>
        /// vcc支付状态
        /// <value>默认值：待发卡</value>
        /// </summary>
        public OrderVirtualCreditCardPaymentStatus VccPaymentStatus { get; set; }

        /// <summary>
        /// 运营助理
        /// </summary>
        public long? OperatorAssistantUserId { get; set; }

        /// <summary>
        /// 资源名称-英文
        /// </summary>
        public string? EnResourceName { get; set; }

        /// <summary>
        /// 产品名称-英文
        /// </summary>
        public string? EnProductName { get; set; }

        /// <summary>
        /// 规格名称-英文
        /// </summary>
        public string? EnProductSkuName { get; set; }

        /// <summary>
        /// 订单分类
        /// <value>支持位运算</value>
        /// </summary>
        public OrderCategory OrderCategory { get; set; }

    }
}
