using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

public class BaseOrderSeries : TenantBase
{
    public OrderType OrderType { get; set; }

    public DateOnly Date { get; set; }

    /// <summary>
    /// 序号，0开始，使用时+1
    /// </summary>
    public int SeriesNumber { get; set; }

    /// <summary>
    /// 前缀
    /// </summary>
    public string Prefix { get; set; }
}
