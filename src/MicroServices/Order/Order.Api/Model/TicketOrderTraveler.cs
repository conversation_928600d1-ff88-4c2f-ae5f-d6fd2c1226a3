using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 票券订单出行信息
/// </summary>
public class TicketOrderTraveler : TenantBase
{
    /// <summary>
    /// 主单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 票券订单id
    /// </summary>
    public long TicketOrderId { get; set; }

    /// <summary>
    /// 预约单Id
    /// </summary>
    public long ReservationOrderId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号
    /// </summary>
    public string? IDCard { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? PhoneNumber { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}