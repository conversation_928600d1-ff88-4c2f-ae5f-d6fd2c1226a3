using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 线路日游-渠道订单
/// 表名命名错误.后续其他品类都是XXChannelOrder
/// </summary>
public class TravelLineOtaOrder : TenantBase
{
    /// <summary>
    /// 主单Id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 子订单Id
    /// </summary>
    public long SubOrderId { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public string? ChannelOrderNo { get; set; }
    
    /// <summary>
    /// 售卖渠道主单号
    /// </summary>
    public string? ChannelMasterOrderNo { get; set; }

    /// <summary>
    /// ota订单状态
    /// </summary>
    public TravelLineOtaOrderStatus Status { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}