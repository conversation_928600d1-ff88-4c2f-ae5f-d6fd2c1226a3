using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 发票抬头
/// </summary>
public class InvoiceTitle : TenantBase
{
    public long UserId { get; set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    public InvoiceTitleUserType UserType { get; set; }

    /// <summary>
    /// 抬头类型
    /// </summary>
    public InvoiceTitleType TitleType { get; set; }

    /// <summary>
    /// 抬头
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 纳税识别号
    /// </summary>
    public string? ITIN { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? ContactNumber { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    public string? BankName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public string? BankNumber { get; set; }

    /// <summary>
    /// 发票接口配置Id
    /// </summary>
    public long? InvoiceConfigId { get; set; }

    /// <summary>
    /// 额度收款订单支持开票
    /// </summary>
    public bool CreditReceiptOrderInvoiceIsSupport { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; }
}
