using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 订单佣金
/// </summary>
public class OrderCommission : TenantBase
{
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 分销的佣金（入账）
    /// </summary>
    public decimal? AgencyCommissionFee { get; set; }

    /// <summary>
    /// 分销的佣金 百分比
    /// </summary>
    public decimal? AgencyCommissionRate { get; set; }

    /// <summary>
    /// 佣金状态
    /// </summary>
    public CommisionStatus? AgencyCommisionStatus { get; set; }

    /// <summary>
    /// 供应商给的佣金（入账）
    /// </summary>
    public decimal? SupplierCommissionFee { get; set; }

    /// <summary>
    /// 供应商给的佣金 百分比
    /// </summary>
    public decimal? SupplierCommissionRate { get; set; }

    /// <summary>
    /// 佣金状态
    /// </summary>
    public CommisionStatus? SupplierCommisionStatus { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
