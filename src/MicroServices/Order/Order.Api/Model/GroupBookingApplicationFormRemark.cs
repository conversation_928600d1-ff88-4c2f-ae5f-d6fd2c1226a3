using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 团房申请单跟单备注
/// </summary>
public class GroupBookingApplicationFormRemark : TenantBase
{
    public long ApplicationFormId { get; set; }

    public UserType UserType { get; set; }

    public long UserId { get; set; }

    public string Name { get; set; }

    /// <summary>
    /// 备注内容
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}
