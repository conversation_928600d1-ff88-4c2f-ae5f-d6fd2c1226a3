using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

public class TravelLineSupplierOrderVoucher : TenantBase
{
    /// <summary>
    /// saas主订单id
    /// </summary>
    public long BaseOrderId { get; set; }
    
    /// <summary>
    /// 凭证文件路径
    /// </summary>
    public string FilePath { get; set; }

    /// <summary>
    /// 凭证缩略图(PDF取第一页)
    /// </summary>
    public string Thumbnail { get; set; }

    /// <summary>
    /// 凭证来源路径
    /// </summary>
    public string? SourcePath { get; set; }
    
    /// <summary>
    /// 凭证来源PDF路径
    /// 1.凭证来源类型是API对接.存的是API返回的Pdf完整路径
    /// 2.凭证来源类型是手工上传,该字段目前没保存,手工上传的PDF会直接存到FilePath
    /// </summary>
    public string? PdfSourcePath { get; set; }
    
    /// <summary>
    /// 凭证来源图片路径
    /// 1.凭证来源类型是API对接.存的是API返回的图片完整路径
    /// 2.凭证来源类型是手工上传.存的是上传PDF的转图片的路径(saas需要oss配置acl)
    /// </summary>
    public string? ImageSourcePath { get; set; }

    /// <summary>
    /// 凭证来源类型
    /// </summary>
    public LineProductVoucherSourceType VoucherSourceType { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
}