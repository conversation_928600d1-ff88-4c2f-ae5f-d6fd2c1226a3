using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 订单字段信息
/// </summary>
public class OrderFieldInformation : TenantBase
{
    public long OrderFieldInformationTypeId { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public ProductTemplateType? ProductTemplateType { get; set; }

    public string FieldName { get; set; }

    public string FieldCode { get; set; }

    /// <summary>
    /// 字段类型
    /// </summary>
    public FieldsType FieldType { get; set; }

    /// <summary>
    /// 字段值
    /// </summary>
    public string? FieldValue { get; set; }

    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 字段排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 组合字段 - 如：区号+手机号码
    /// </summary>
    public string? Group { get; set; }

    /// <summary>
    /// 组合字段名称
    /// </summary>
    public string? GroupName { get; set; }

    /// <summary>
    /// 组合字段顺序
    /// </summary>
    public int? GroupSort { get; set; }

}
