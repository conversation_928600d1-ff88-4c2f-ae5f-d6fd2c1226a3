using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model
{
    public class ReservationOrder : TenantBase
    {
        /// <summary>
        /// 主单Id
        /// </summary>
        public long BaseOrderId { get; set; }

        /// <summary>
        /// 票券订单Id
        /// </summary>
        public long TicketOrderId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public ReservationStatus Status { get; set; }

        /// <summary>
        /// 预约数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 确认号
        /// </summary>
        public string? ConfirmNumber { get; set; }

        /// <summary>
        /// 资源类型
        /// </summary>
        public ProductResourceType ResourceType { get; set; }

        /// <summary>
        /// 资源Id
        /// </summary>
        public long ResourceId { get; set; }

        /// <summary>
        /// 资源名称
        /// </summary>
        public string? ResourceName { get; set; }

        /// <summary>
        /// 出行人
        /// </summary>
        public string? Traveler { get; set; }

        /// <summary>
        /// 出行日期 - 起
        /// </summary>
        public DateTime TravelDateBegin { get; set; }

        /// <summary>
        /// 出行日期 - 止
        /// </summary>
        public DateTime TravelDateEnd { get; set; }

        /// <summary>
        /// 认领人Id
        /// </summary>
        public long ClaimantId { get; set; }

        /// <summary>
        /// 认领人名称
        /// </summary>
        public string? ClaimantName { get; set; }

        /// <summary>
        /// 是否已发单
        /// </summary>
        public bool IsSendPDF { get; set; }

        /// <summary>
        /// 留言
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 支付金额
        /// </summary>
        public decimal PaymentAmount { get; set; }

        #region Payment

        /// <summary>
        /// 支付币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; } = Currency.CNY.ToString();

        /// <summary>
        /// 支付类型
        /// </summary>
        public PayType PaymentType { get; set; }

        /// <summary>
        /// 支付渠道
        /// </summary>
        public string? PaymentChannel { get; set; }

        /// <summary>
        /// 支付方式  微信h5、微信小程序、微信扫码等
        /// </summary>
        public string? PaymentMode { get; set; }

        /// <summary>
        /// 外部支付单号
        /// </summary>
        public string? PaymentExternalNo { get; set; }

        #endregion

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }
}
