using EfCoreExtensions.EntityBase;

namespace Order.Api.Model
{
    /// <summary>
    /// 储值卡订单赠送类型项
    /// </summary>
    public class StoredValueCardOrderGearGiftItem : TenantBase
    {
        /// <summary>
        /// 储值卡订单赠送项id
        /// </summary>
        public long StoredValueCardOrderGearGiftId { get; set; }

        /// <summary>
        /// 赠送类型项id 比如:等级id 优惠券id
        /// </summary>
        public long GiftItemId { get; set; }

        /// <summary>
        /// 项名称
        /// </summary>
        public string? ItemName { get; set; }

        /// <summary>
        /// 赠送数量 (等级：月份；优惠券：张数)
        /// </summary>
        public int Count { get; set; }
    }
}
