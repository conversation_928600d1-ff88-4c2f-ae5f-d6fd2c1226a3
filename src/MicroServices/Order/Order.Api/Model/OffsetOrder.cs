using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 抵冲单
/// </summary>
public class OffsetOrder : TenantBase
{
    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 业务单号，酒店订单Id / 邮寄订单Id / 预约单Id /
    /// </summary>
    public long BusinessOrderId { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 工单id
    /// </summary>
    public long? WorkOrderId { get; set; }

    /// <summary>
    /// 抵冲金额类型 1-加收 2-退款 3-赔付 类型为退款/赔付时 抵冲金额为负数
    /// </summary>
    public OffsetOrderMoneyType OffsetAmountType { get; set; }

    /// <summary>
    /// 抵冲金额(追加和退款 用正负区分)
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 抵冲金额币种
    /// </summary>
    public string CurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 产品SkuId
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// 产品Sku名称
    /// </summary>
    public string ProductSkuName { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public OffsetOrderBusinessType BusinessType { get; set; }

    /// <summary>
    /// 抵冲类型
    /// </summary>
    public OffsetOrderType OffsetType { get; set; }

    /// <summary>
    /// 抵冲单状态
    /// </summary>
    public OffsetOrderStatus Status { get; set; }

    /// <summary>
    /// 抵冲单结算方式 0-结算单结算 默认 1-退款结算 2-售后处理单结算
    /// </summary>
    public OffsetOrderSettlementType SettlementType { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatorName { get; set; }

    /// <summary>
    /// 更新人id
    /// </summary>
    public long UpdaterId { get; set; }

    /// <summary>
    /// 更新人名称
    /// </summary>
    public string? UpdaterName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 图片
    /// </summary>
    public List<string>? Images { get; set; }

    /// <summary>
    /// 批次号 批量添加抵冲单时有值
    /// </summary>
    public long? BatchNumber { get; set; }

    /// <summary>
    /// 处理状态
    /// 处理中（审核中，审核通过-》触发相关事件；不需要审核时，直接触发相关事件）
    /// 成功（触发相关事件完成），结算时只查询处理成功的    
    /// 失败（审核拒绝，审核撤销，触发事件失败）
    /// </summary>
    public OffsetOrderProcessingStatus? ProcessStatus { get; set; }

    /// <summary>
    /// 处理失败原因
    /// </summary>
    public string? ProcessErrorMsg { get; set; }
}

