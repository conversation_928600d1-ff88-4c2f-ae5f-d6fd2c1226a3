using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 线路日游-渠道订单记录
/// </summary>
public class TravelLineOtaOrderRecord : TenantBase
{
    /// <summary>
    /// 主单Id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 子订单Id
    /// </summary>
    public long SubOrderId { get; set; }

    /// <summary>
    /// ota订单id
    /// </summary>
    public long TravelLineOtaOrderId { get; set; }

    /// <summary>
    /// 记录类型
    /// </summary>
    public TravelLineOtaOrderRecordType RecordType { get; set; }
    
    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 失败信息
    /// </summary>
    public string? ErrorMsg { get; set; }

    /// <summary>
    /// 错误码
    /// </summary>
    public int? ErrorCode { get; set; }
    
    public DateTime CreateTime { get; set; } = DateTime.Now;
}