using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 团房预订单设置
/// </summary>
public class GroupBookingPreOrderSetting : TenantBase
{
    /// <summary>
    /// 页头图片
    /// </summary>
    public string HeaderImage { get; set; }

    /// <summary>
    /// 电子章
    /// </summary>
    public string ElectronicSeal { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; }
}
