using Contracts.Common.Order.DTOs.AggregateOrder;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers;

/// <summary>
/// 订单整合表
/// </summary>
[Route("[controller]/[action]")]
[ApiController]
public class AggregateOrderController : ControllerBase
{
    public readonly IAggregateOrderService _aggregateOrderService;
    public AggregateOrderController(IAggregateOrderService aggregateOrderService)
    {
        _aggregateOrderService = aggregateOrderService;
    }

    /// <summary>
    /// 搜索
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchAggregateOrderOutput, SearchAggregateStatusStat>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchAggregateOrderInput input)
    {

        var result = await _aggregateOrderService.Search(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<ExportAggregateOrderOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Export(ExportAggregateOrderInput input)
    {
        var result = await _aggregateOrderService.Export(input);
        return Ok(result);
    }

    /// <summary>
    /// 设置标记-颜色
    /// </summary>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetOrderRemark(SetAggregateOrderRemarkInput input)
    {

        await _aggregateOrderService.SetOrderRemark(input);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<SearchAggregateStatusOuput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchAggregateOrderStatus(SearchAggregateStatusInput input)
    {
        var result = await _aggregateOrderService.SearchAggregateStatus(input);
        return Ok(result);
    }

    /// <summary>
    /// 玩乐财务报表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<PagingModel<SearchAggregateOrderFinanceOutput, AggregateOrderFinanceSummary>> FinanceSearch(SearchAggregateOrderInput input)
    {
        var result = await _aggregateOrderService.FinanceSearch(input);
        PagingModel<SearchAggregateOrderFinanceOutput, AggregateOrderFinanceSummary> paging = new()
        {
            Data = result.Data,
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
            Total = result.Total,
            Supplement = await _aggregateOrderService.AggregateOrderFinanceSummary(input)
        };
        return paging;
    }
    
    /// <summary>
    /// 移除订单处理等级标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> RemoveProcessingLevelTag(RemoveProcessingLevelTagInput input)
    {
        await _aggregateOrderService.RemoveProcessingLevelTag(input);
        return Ok();
    }

}
