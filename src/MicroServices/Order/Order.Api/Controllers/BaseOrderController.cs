using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class BaseOrderController : ControllerBase
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IBaseOrderService _baseOrderService;

        public BaseOrderController(IHttpContextAccessor httpContextAccessor,
            IBaseOrderService baseOrderService)
        {
            _httpContextAccessor = httpContextAccessor;
            _baseOrderService = baseOrderService;
        }

        /// <summary>
        /// 获取订单支付信息
        /// </summary>
        /// <param name="orderId">订单Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(PaymentInfoOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> PaymentInfo(long orderId)
        {
            var result = await _baseOrderService.GetPaymentInfo(orderId);
            return Ok(result);
        }

        /// <summary>
        /// 用户订单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<SearchOrderByUserOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchOrderByUser([FromBody] SearchOrderByUserInput input)
        {
            var currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
            var result = await _baseOrderService.SearchOrderByUser(currentUser.userid, input);
            return Ok(result);
        }

        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<OrderLogsOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> OperatingRecords(long orderId)
        {
            var result = await _baseOrderService.OperatingRecords(orderId);
            return Ok(result);
        }

        /// <summary>
        /// 通用取消未支付订单
        /// </summary>
        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
        public async Task<IActionResult> Cancel(CancelOrderInput input)
        {
            await _baseOrderService.Cancel(input);
            return Ok();
        }

        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> CancelByTenant(long baseOrderId)
        {
            var user = HttpContext.GetCurrentUser();
            OperationUserDto operationUser = new()
            {
                UserType = UserType.Merchant,
                UserId = user.userid,
                Name = user.nickname
            };
            await _baseOrderService.Cancel(new CancelOrderInput { BaseOrderId = baseOrderId, OperationUser = operationUser });
            return Ok();
        }

        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> CancelByCustomer(long baseOrderId)
        {
            var user = HttpContext.GetCurrentUser();
            OperationUserDto operationUser = new()
            {
                UserType = UserType.Customer,
                UserId = user.userid,
                Name = user.nickname
            };
            await _baseOrderService.Cancel(new CancelOrderInput { BaseOrderId = baseOrderId, OperationUser = operationUser });
            return Ok();
        }

        [HttpPost, ActionName("supplier/cancel")]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> CancelBySupplier(long baseOrderId)
        {
            var user = HttpContext.GetCurrentUser();
            OperationUserDto operationUser = new()
            {
                UserType = UserType.Supplier,
                UserId = user.userid,
                Name = user.nickname
            };
            await _baseOrderService.Cancel(new CancelOrderInput { BaseOrderId = baseOrderId, OperationUser = operationUser });
            return Ok();
        }

        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> CancelByJob(long baseOrderId)
        {
            OperationUserDto operationUser = new()
            {
                UserType = UserType.None
            };
            await _baseOrderService.Cancel(new CancelOrderInput { BaseOrderId = baseOrderId, OperationUser = operationUser });
            return Ok();
        }

        /// <summary>
        /// 查询用户不同订单状态的订单数量 [个人中心展示]
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GetBaseOrderStatusCountOutPut), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetBaseOrderStatusCount(GetBaseOrderStatusCountInput input)
        {
            var result = await _baseOrderService.GetBaseOrderStatusCount(input);
            return Ok(result);
        }

        /// <summary>
        /// 根据订单类型订单号 获取订单价格信息（对账单订单总额、优惠金额）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IList<OrderAmountInfoOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> OrderAmountInfos(OrderAmountInfoInput input)
        {
            var result = await _baseOrderService.GetOrderAmountInfos(input);
            return Ok(result);
        }

        /// <summary>
        /// 查询用户消费统计分页数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<SearchUserConsumptionOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchConsumption(
            SearchUserConsumptionPageInput input)
        {
            var result = await _baseOrderService.SearchConsumptionStatistic(input);
            return Ok(result);
        }

        /// <summary>
        /// 通过用户id查询用户的消费累积数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IEnumerable<SearchUserConsumptionOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetConsumptionByIds(List<long> ids)
        {
            var result = await _baseOrderService.GetConsumptionStatisticByUserIds(ids);
            return Ok(result);
        }

        /// <summary>
        /// 检查分销商渠道单号是否存在
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CheckChannelOrderNoExist(CheckChannelOrderNoExistInput input)
        {
            var result = await _baseOrderService.CheckChannelOrder(new CheckChannelOrderInput
            {
                AgencyId = input.AgencyId,
                ChannelOrderNo = input.ChannelOrderNo
            });
            return Ok(result.IsExists);
        }


        /// <summary>
        /// 获取分销商最近购买时间
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<SearchAgencyOrderLastTimeOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchAgencyOrderRecencyTime(SearchAgencyOrderRecencyTimeInput input)
        {
            input.EndTime = input.EndTime.HasValue ? input.EndTime : input.BeginTime.AddDays(1);
            var result = await _baseOrderService.SearchAgencyOrderRecencyTime(input);
            return Ok(result);
        }

        /// <summary>
        /// 检查分销商渠道单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(CheckChannelOrderOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CheckChannelOrder(CheckChannelOrderInput input)
        {
            var result = await _baseOrderService.CheckChannelOrder(input);
            return Ok(result);
        }

        /// <summary>
        /// 检查渠道异常单渠道单号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(CheckChannelOrderOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CheckChannelOrderAbnormalOrder(CheckChannelOrderInput input)
        {
            var result = await _baseOrderService.CheckChannelOrderAbnormalOrder(input);
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType(typeof(CheckSupplierOrderOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CheckSupplierOrder(CheckSupplierOrderInput input)
        {
            var result = await _baseOrderService.CheckSupplierOrder(input);
            return Ok(result);
        }

        /// <summary>
        /// 设置跟单人
        /// </summary>
        /// <param name="baseOrderId"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.OPSetTrackingUserExcess)]
        public async Task<IActionResult> SetTrackingUserId(SetTrackingUserIdInput input)
        {
            var result = await _baseOrderService.SetTrackingUserId(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取能开票的列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<CanBeInvoiceDto>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetListByCanBeInvoice(GetListByCanBeInvoiceInput input)
        {
            var result = await _baseOrderService.GetListByCanBeInvoice(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取是否支持开票订单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<CanBeInvoiceDto>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> CanIssueInvoiceList(CanIssueInvoiceListInput input)
        {
            var result = await _baseOrderService.CanIssueInvoiceList(input);
            return Ok(result);
        }

        /// <summary>
        /// 获取能开票的订单数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(BeInvoiceCountDto), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetBeInvoiceCount(GetListByCanBeInvoiceInput input)
        {
            var result = await _baseOrderService.GetBeInvoiceCountAsync(input);
            return Ok(result);
        }

        /// <summary>
        /// 新增订单信用卡信息
        /// </summary>
        /// <param name="payInput"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> AddOrderPaymentCard(CreditCardGuaranteeInput payInput)
        {
            var result = await _baseOrderService.AddOrderPaymentCard(payInput);
            return Ok(result);
        }

        /// <summary>
        /// 设置订单运营、产品、运营助理
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
        public async Task<IActionResult> SetOrderOpUserId(SetOrderOpUserIdInput input)
        {
            var result = await _baseOrderService.SetOrderOpUserId(input);
            return Ok(result);
        }

        #region CapSubscribe

        [NonAction]
        [CapSubscribe(CapTopics.Order.StatusChangeByPaySuccess)]
        public async Task OrderStatusChangeByPaySuccess(OrderStatusChangeByPaySuccessMessage receive)
        {
            await _baseOrderService.OrderStatusChangeByPaySuccess(receive);
        }

        [NonAction]
        [CapSubscribe(CapTopics.Order.RefundResult)]
        public async Task RefundResult(RefundResultMessage receive)
        {
            await _baseOrderService.RefundResult(receive);
        }

        [NonAction]
        [CapSubscribe(CapTopics.Order.UpdateVccPaymentStatus)]
        public async Task UpdateVccPaymentStatus(UpdateVccPaymentStatusMessage receive)
        {
            await _baseOrderService.UpdateVccPaymentStatus(receive);
        }

        #endregion
    }
}