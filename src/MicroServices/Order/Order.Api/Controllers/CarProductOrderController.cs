using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.CarProductOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers;

/// <summary>
/// 用车 - 订单
/// </summary>
[Route("[controller]/[action]")]
[ApiController]
public class CarProductOrderController : ControllerBase
{
    private readonly ICarProductOrderService _carProductOrderService;

    public CarProductOrderController(ICarProductOrderService carProductOrderService)
    {
        _carProductOrderService = carProductOrderService;
    }

    /// <summary>
    /// 下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<CreateCarProductOrderOutput> Create(CreateCarProductOrderInput input)
    {
        var result = await _carProductOrderService.Create(input);
        return result;
    }

    /// <summary>
    /// 订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<GetDetailOutput> Detail(GetDetailInput input)
    {
        var result = await _carProductOrderService.Detail(input);
        return result;
    }

    /// <summary>
    /// 订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<PagingModel<SearchOrderOutput>> Search(SearchOrderInput input)
    {
        var result = await _carProductOrderService.Search(input);
        return result;
    }

    /// <summary>
    /// 确认
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Confirm(ConfirmOrderInput input)
    {
        await _carProductOrderService.Confirm(input);
        return Ok();
    }

    /// <summary>
    /// 完成
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Finish(FinishOrderInput input)
    {
        await _carProductOrderService.Finish(input);
        return Ok();
    }

    /// <summary>
    /// 退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Refund(RefundOrderInput input)
    {
        await _carProductOrderService.Refund(input);
        return Ok();
    }

    /// <summary>
    /// 编辑采购价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> EditCost(UpdateCostInput input)
    {
        await _carProductOrderService.EditCost(input);
        return Ok();
    }

    /// <summary>
    /// 编辑用车信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> EditCarUsingInfo(EditCarUsingInfoInput input)
    {
        await _carProductOrderService.EditCarUsingInfo(input);
        return Ok();
    }

    /// <summary>
    /// 查询订单数量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetOrderCountOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetOrderCount(GetOrderCountInput input)
    {
        var result = await _carProductOrderService.GetOrderCount(input);
        return Ok(result);
    }


    /// <summary>
    /// 修改确认信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> UpdateOrderConfirmation(UpdateOrderConfirmationInput input)
    {
        var res = await _carProductOrderService.UpdateOrderConfirmation(input);
        return Ok(res);
    }

    /// <summary>
    /// 修改出行信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> UpdateTravelInfo(UpdateTravelInfoInput input)
    {
        var res = await _carProductOrderService.UpdateTravelInfo(input);
        return Ok(res);
    }

    /// <summary>
    /// 修改联系人
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> UpdateContact(UpdateContactInput input)
    {
        var res = await _carProductOrderService.UpdateContact(input);
        return Ok(res);
    }

    /// <summary>
    /// 通过渠道订单号获取订单信息
    /// </summary>
    /// <param name="channelOrderNo"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetCarProductChannelOrderInfoOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetChannelOrderInfo(string channelOrderNo)
    {
        var result = await _carProductOrderService.GetChannelOrderInfo(channelOrderNo);
        return Ok(result);
    }

    /// <summary>
    /// 修改采购单号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> UpdateSupplierOrderId(UpdateSupplierOrderIdInput input)
    {
        var res = await _carProductOrderService.UpdateSupplierOrderId(input);
        return Ok(res);
    }

    #region CapSubscribe

    [NonAction]
    [CapSubscribe(CapTopics.Order.CarProductOtaSyncDelivery)]
    public async Task OtaSyncDeliveryCapSubscribe(CarProductOtaSyncDeliveryMessage input)
    {
        await _carProductOrderService.OtaSyncDeliveryCapSubscribe(input);
    }

    #endregion
}
