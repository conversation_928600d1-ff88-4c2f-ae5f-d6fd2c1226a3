using AutoMapper;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.RefundOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers
{
    /// <summary>
    /// 保险产品管理
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiController]
    public class InsureProductController : ControllerBase
    {
        private readonly IInsureProductService _insureProductService;
        private readonly ISupplierInsureProductService _supplierInsureProductService;
        private readonly IMapper _mapper;

        public InsureProductController(IInsureProductService insureProductService,
            IMapper mapper,
            ISupplierInsureProductService supplierInsureProductService)
        {
            _insureProductService = insureProductService;
            _mapper = mapper;
            _supplierInsureProductService = supplierInsureProductService;
        }

        /// <summary>
        /// 获取保险产品列表
        /// </summary>
        /// <param name="orderId">订单Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<InsureProductOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetList()
        {
            var result = await _insureProductService.GetList();
            return Ok(result);
        }

        /// <summary>
        /// 获取保险产品
        /// </summary>
        /// <param name="orderId">订单Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(InsureProductOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Get(long id)
        {
            var result = await _insureProductService.Get(id);
            return Ok(result);
        }

        /// <summary>
        /// 获取保险产品
        /// </summary>
        /// <param name="orderId">订单Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(InsureProductOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetByBaseOrderId(long baseOrderId)
        {
            var result = await _insureProductService.GetByBaseOrderId(baseOrderId);
            return Ok(result);
        }

        /// <summary>
        /// 保存保险产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(InsureProductOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.InsureProductVaildError)]
        public async Task<IActionResult> Save(SaveInsureProductInput input)
        {
            var result =  await _insureProductService.Save(input);
            return Ok(result);
        }

        /// <summary>
        /// 删除保险产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> Delete(long id)
        {
            await _insureProductService.Delete(id);
            return Ok();
        }

        /// <summary>
        /// 保险产品上移下移功能
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> Move(List<MoveInsureProductInput> input)
        {
            await _insureProductService.Move(input);
            return Ok();
        }

        /// <summary>
        /// 保险产品下拉框
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<InsureProductOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetSelection()
        {
            var result = await _insureProductService.GetSelection();
            return Ok(result);
        }

        /// <summary>
        /// 根据产品id获取关联的保险产品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(InsureProductRelationsOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetInsureProductRelation(long productId)
        {
            var result = await _insureProductService.GetInsureProductRelation(productId);
            return Ok(result);
        }

        /// <summary>
        /// 保存产品和保险产品配置
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> SaveInsureProductRelation(SaveInsureProductRelationsInput input)
        {
            if(input.InsureProductId.HasValue)
                await _insureProductService.SaveInsureProductRelation(input);
            else
                await _insureProductService.DeleteInsureProductRelation(input.ProductId);

            return Ok();
        }

        /// <summary>
        /// 获取供应商保险产品列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<SupplierInsureProductOutput>),(int)HttpStatusCode.OK)]
        public async Task<IActionResult> SearchSupplierInsureProducts(SearchSupplierInsureProductInput input)
        {
            var result = await _supplierInsureProductService.SearchSupplierInsureProducts(input);
            return Ok(result);
        }

    }
}
