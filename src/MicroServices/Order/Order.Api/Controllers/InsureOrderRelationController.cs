using AutoMapper;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.RefundOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers
{
    /// <summary>
    /// 订单的保险配置
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiController]
    public class InsureOrderRelationController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IInsureOrderRelationService _insureOrderRelationService;

        public InsureOrderRelationController(IInsureOrderRelationService insureOrderRelationService, 
            IMapper mapper)
        {
            _insureOrderRelationService = insureOrderRelationService;
            _mapper = mapper;
        }

        /// <summary>
        /// 设置订单的保险是否自动购买
        /// </summary>
        /// <param name="orderId">订单Id</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> SetAuto(UpdateInsureOrderRelationInput input)
        {
            await _insureOrderRelationService.Update(input);
            return Ok();
        }


        [NonAction]
        [CapSubscribe(CapTopics.Order.CreateInsureOrderRelation)]
        public async Task CreateInsureOrderRelation(CreateInsureOrderRelationMessage receive)
        {
            var input = _mapper.Map<AddInsureOrderRelationInput>(receive);
            await _insureOrderRelationService.Create(input);
        }
    }
}
