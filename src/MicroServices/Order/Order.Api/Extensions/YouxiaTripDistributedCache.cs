using Cit.OpenAPI.YouxiaTrip.Extensions.Cache;
using Cit.Storage.Redis;

namespace Order.Api.Extensions;

public class YouxiaTripDistributedCache : IDistributedCache
{
    private readonly IRedisClient _redisClient;

    public YouxiaTripDistributedCache(IRedisClient redisClient)
    {
        _redisClient = redisClient;
    }

    public Task<T?> GetAsync<T>(string key)
    {
        return _redisClient.StringGetAsync<T?>(key);
    }

    public Task<bool> SetAsync<T>(string key, T value, TimeSpan expiry)
    {
        return _redisClient.StringSetAsync<T?>(key, value, expiry);
    }

    public Task<bool> DeleteAsync(string key)
    {
        return _redisClient.KeyDeleteAsync(key);
    }
}
