using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.ReservationOrder;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.SkuCalendarPrice;
using Contracts.Common.Product.Enums;
using DotNetCore.CAP;
using Hangfire;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using Order.Api.Infrastructure;
using Order.Api.Model;
using Order.Api.Requests;
using Order.Api.Services;
using Order.Api.Services.Interfaces;
using Order.Api.Services.MappingProfiles;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.UnitTest
{
    public class ReservationOrderServiceTest : TestBase<CustomDbContext>
    {
        private readonly long _tenantId = 1;
        private ReservationOrderService CreateService(CustomDbContext dbContext,
            IHttpContextAccessor httpContextAccessor = null,
            IMapper mapper = null,
            ICapPublisher capPublisher = null,
            IHttpClientFactory httpClientFactory = null,
            IOptions<ServicesAddress> options = null,
            ILogger<ReservationOrderService> logger = null,
            IBackgroundJobClient backgroundJobClient = null,
            IMediator mediator = null,
            IMessageNotifyService messageNotifyService = null,
            ICurrencyExchangeRateService currencyExchangeRateService = null,
            IRedisClient redisClient = null)
        {
            if (options is null)
                options = Options.Create(new ServicesAddress());

            return new ReservationOrderService(dbContext,
                httpContextAccessor,
                mapper,
                capPublisher,
                httpClientFactory,
                options,
                logger,
                backgroundJobClient,
                mediator,
                messageNotifyService,
                currencyExchangeRateService,
                redisClient);
        }

        [Fact(DisplayName = "预约单支付成功")]
        public async Task ReservationOrderPaySuccess_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderNotifyProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var service = CreateService(dbContext: dbContext, capPublisher: capPublisher,
                mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher));

            var ticketOrder = new TicketOrder() { ProductNeedConfirmReservation = true };
            await dbContext.AddAsync(ticketOrder);

            //fake data
            var baseOrder = new BaseOrder
            {
                ContactsName = "",
                ContactsPhoneNumber = "",
                PaymentAmount = 1,
                Status = BaseOrderStatus.UnFinished
            };
            await dbContext.AddAsync(baseOrder);
            var reservationOrder = new ReservationOrder
            {
                BaseOrderId = baseOrder.Id,
                TicketOrderId = ticketOrder.Id,
                Status = ReservationStatus.WaitingForPay,
                ClaimantId = 222,
            };
            await dbContext.AddAsync(reservationOrder);
            //券
            var ticketCode = new TicketCode
            {
                SubOrderId = reservationOrder.TicketOrderId,
                Status = TicketCodeStatus.Reservation
            };
            await dbContext.AddAsync(ticketCode);
            await dbContext.AddAsync(new ReservationOrderTicketCode
            {
                ReservationOrderId = reservationOrder.Id,
                TicketCodeId = ticketCode.Id
            });

            await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            //act
            await service.ReservationOrderPaySuccess(new OrderStatusChangeByPaySuccessMessage
            {
                PaymentType = PayType.Offline,
                OrderId = reservationOrder.Id,
                PaymentExternalNo = "offline10022112154"
            });
            await dbContext.SaveChangesAsync();
            //1，预约单状态
            //2，券码状态WaitingForUse
            //3，支付成功日志
            var reservationStatus = ticketOrder.ProductNeedConfirmReservation
                ? ReservationStatus.WaitingForClaim
                : ReservationStatus.Confirmed;
            Assert.True(dbContext.ReservationOrders.IgnoreQueryFilters()
                .Any(s => s.Id == reservationOrder.Id && s.Status == reservationStatus));
            Assert.True(dbContext.TicketCodes.IgnoreQueryFilters()
                .Any(s => s.Status == TicketCodeStatus.WaitingForUse));
            Assert.True(dbContext.OrderLogs.IgnoreQueryFilters()
                .Any(s => s.OperationType == OrderOperationType.Paid));
        }


        #region Vebk

        [Fact(DisplayName = "创建预约单")]
        public async Task Add()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var capPublisher = GetCapPublisher();
            CurrentUser currentUser = new()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            };
            var httpContextAccessor = GetHttpContextAccessor(currentUser);
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<ReservationOrder, CreateDto>().ReverseMap();
            }).CreateMapper();
            var options = Options.Create(new ServicesAddress() { Product = "http://127.0.0.1/" });
            HttpResponseMessage[] responses = new HttpResponseMessage[]{
                new HttpResponseMessage()
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(new GetProductSkuCalendarPriceOutput()
                    {
                        SkuCalendarPrices = new List<GetProductSkuCalendarPrice_CalendarPrice>()
                        {
                            new GetProductSkuCalendarPrice_CalendarPrice()
                            {
                                Date = DateTime.Today,
                                AvailableQuantity = 10,
                                Price = 10,
                                CostPrice = 1,
                                Enabled = true
                            }
                        }
                    }), Encoding.UTF8, "application/json")
                }
            };

            var httpClientFactory = GetHttpClientFactoryMock(responses);
            var currencyExchangeRateService = new Mock<ICurrencyExchangeRateService>();
            currencyExchangeRateService.Setup(x => x.GetOrderPriceExchange(It.IsAny<OrderPriceExchangeRateInput>()))
                .ReturnsAsync(new OrderPriceExchangeRateOutput { CostExchangeRate = 1, ExchangeRate = 1 });
            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                httpContextAccessor: httpContextAccessor,
                httpClientFactory: httpClientFactory,
                options: options,
                mapper: mapper,
                currencyExchangeRateService: currencyExchangeRateService.Object);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            await AddTicketCode(dbContext, baseOrderId, ticketOrderId, 11112222, TicketCodeStatus.WaitingForReservation);

            var input = new CreateDto()
            {
                BaseOrderId = baseOrderId,
                TravelDateBegin = DateTime.Today,
                TravelDateEnd = DateTime.Today,
                TicketOrderTravelers = new List<TicketOrderTravelerDto>
                {
                    new TicketOrderTravelerDto
                    {
                        Name = "张三"
                    }
                },
                Message = "",
                Quantity = 1,
                ConfirmNumber = "",
                PaymentInfos = new List<ReservationOrderPaymentDto>()
                {
                    new ReservationOrderPaymentDto()
                    {
                        Date = DateTime.Today,
                        Amount = 10
                    }
                }
            };

            var reservationOrderId = await service.Create(input, new OperationUserDto
            {
                Name = currentUser.nickname,
                UserId = currentUser.userid,
                UserType = UserType.Merchant
            });

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.NotNull(result);
        }

        [Fact(DisplayName = "获取预约单信息")]
        public async Task Get()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<ReservationOrder, GetReservationOrder_Detail>().ReverseMap();
            }).CreateMapper();
            var httpResponseMessage = new HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = new StringContent("{\"ShortName\":\"测试供应商\"}")
            };
            var httpClientFactory = GetHttpClientFactoryMock(httpResponseMessage);
            var options = Options.Create(new ServicesAddress() { Tenant = "http://127.0.0.1/" });

            var service = CreateService(
                dbContext: dbContext,
                mapper: mapper,
                httpClientFactory: httpClientFactory,
                options: options);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.Confirmed);
            await AddReservationOrderTicketCode(
                dbContext,
                baseOrderId, ticketOrderId, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.WaitingForUse,
                    TicketCodeStatus.Finished
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.WaitingForUse,
                    ReservationTicketCodeStatus.Finished
                });

            var result = await service.Get(new ReservationOrderDetailInput { ReservationOrderId = reservationOrderId });

            Assert.NotNull(result);
        }

        [Fact(DisplayName = "标记发单")]
        public async Task SetSendTag()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var service = CreateService(dbContext: dbContext);

            var reservationOrderId = await AddReservationOrder(dbContext,
                48464257454647841, 8574894513248548, ReservationStatus.Confirmed);

            await service.SetSendTag(reservationOrderId);

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.True(result.IsSendPDF);
        }

        [Fact(DisplayName = "认领")]
        public async Task Claimant()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var service = CreateService(dbContext: dbContext,
                httpContextAccessor: httpContextAccessor);

            var reservationOrderId = await AddReservationOrder(dbContext,
                48464257454647841, 8574894513248548, ReservationStatus.WaitingForClaim);

            await service.Claimant(new ClaimantInput { ReservationOrderId = reservationOrderId }, new OperationUserDto { UserType = UserType.Merchant, UserId = 1 });

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.Equal(ReservationStatus.WaitingForConfirm, result.Status);
        }

        [Fact(DisplayName = "确认订单")]
        public async Task Confirm()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var httpClientFactory = GetHttpClientFactoryMock(new HttpResponseMessage
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = new StringContent(JsonConvert.SerializeObject(new
                {
                    AfterSalePhone = "123456"
                }))
            });
            var mediator = new Mock<IMediator>();
            mediator.Setup(x => x.Send(It.IsAny<ProductSupportStaffRequest>(), default))
                .ReturnsAsync(new ProductSupportStaffResponse
                {
                    AfterSalePhone = ""
                });
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddProfile<BaseOrderNotifyProfile>();
            });
            var mapper = mapperConfiguration.CreateMapper();
            var capPublisher = GetCapPublisher();
            var service = CreateService(dbContext: dbContext,
                httpContextAccessor: httpContextAccessor,
                capPublisher: capPublisher,
                httpClientFactory: httpClientFactory,
                mediator: mediator.Object,
                mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher));
            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForConfirm);

            await service.Confirm(new ConfirmReservationOrderInput()
            {
                ReservationOrderId = reservationOrderId,
                ConfirmNumber = "546845487328"
            }, new OperationUserDto { UserType = UserType.Merchant, UserId = 1 });

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.Equal(ReservationStatus.Confirmed, result.Status);
        }

        [Fact(DisplayName = "更新确认号")]
        public async Task UpdateConfirmNumber()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var service = CreateService(dbContext: dbContext);

            var reservationOrderId = await AddReservationOrder(dbContext,
                48464257454647841, 8574894513248548, ReservationStatus.Confirmed);

            var input = new UpdateReservationOrderConfirmNumberInput()
            {
                ReservationOrderId = reservationOrderId,
                ConfirmNumber = "546845487328"
            };
            await service.UpdateConfirmNumber(input);

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.Equal(input.ConfirmNumber, result.ConfirmNumber);
        }

        [Fact(DisplayName = "搜索")]
        public async Task Search()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var service = CreateService(dbContext: dbContext);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext, baseOrderId, ticketOrderId, ReservationStatus.WaitingForConfirm);

            var input = new SearchReservationOrderInput()
            {
                PageIndex = 1,
                PageSize = 10
            };
            var result = await service.Search(input);
            Assert.True(result.Supplement.WaitingForConfirmCount == 1);
        }

        #endregion

        #region Mall


        [Fact(DisplayName = "创建预约单")]
        public async Task MallAdd()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var capPublisher = GetCapPublisher();
            CurrentUser currentUser = new()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            };
            var httpContextAccessor = GetHttpContextAccessor(currentUser);
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<ReservationOrder, CreateDto>().ReverseMap();
            }).CreateMapper();
            var options = Options.Create(new ServicesAddress() { Product = "http://127.0.0.1/" });
            HttpResponseMessage[] responses = new HttpResponseMessage[]{
                new HttpResponseMessage()
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(new GetProductSkuCalendarPriceOutput()
                    {
                        SkuCalendarPrices = new List<GetProductSkuCalendarPrice_CalendarPrice>()
                        {
                            new GetProductSkuCalendarPrice_CalendarPrice()
                            {
                                Date = DateTime.Today,
                                AvailableQuantity = 10,
                                Price = 10,
                                CostPrice = 1,
                                Enabled = true
                            },
                        }
                    }), Encoding.UTF8, "application/json")
                }
            };

            var httpClientFactory = GetHttpClientFactoryMock(responses);
            var backgroundJobClient = new Mock<IBackgroundJobClient>();
            var currencyExchangeRateService = new Mock<ICurrencyExchangeRateService>();
            currencyExchangeRateService.Setup(x => x.GetOrderPriceExchange(It.IsAny<OrderPriceExchangeRateInput>()))
                .ReturnsAsync(new OrderPriceExchangeRateOutput { CostExchangeRate = 1, ExchangeRate = 1 });
            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                httpContextAccessor: httpContextAccessor,
                httpClientFactory: httpClientFactory,
                options: options,
                mapper: mapper,
                backgroundJobClient: backgroundJobClient.Object,
                currencyExchangeRateService: currencyExchangeRateService.Object);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            await AddTicketCode(dbContext, baseOrderId, ticketOrderId, 11112222, TicketCodeStatus.WaitingForReservation);

            var input = new CreateDto()
            {
                BaseOrderId = baseOrderId,
                TravelDateBegin = DateTime.Today,
                TravelDateEnd = DateTime.Today,
                TicketOrderTravelers = new List<TicketOrderTravelerDto>
                {
                    new TicketOrderTravelerDto
                    {
                        Name = "张三"
                    }
                },
                Message = "",
                Quantity = 1,
                PaymentInfos = new List<ReservationOrderPaymentDto>()
                {
                    new ReservationOrderPaymentDto()
                    {
                        Date = DateTime.Today,
                        Amount = 10
                    }
                }
            };

            var reservationOrderId = await service.Create(input, new OperationUserDto
            {
                UserType = UserType.Customer,
                Name = currentUser.nickname,
                UserId = currentUser.userid
            });

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.NotNull(result);
        }

        [Fact(DisplayName = "获取主订单下的预约单")]
        public async Task GetByBaseOrderId()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForClaim);
            await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.Confirmed);

            var result = await service.GetByBaseOrderId(baseOrderId);

            Assert.True(result.Count() == 2);
        }

        [Fact(DisplayName = "获取预约单信息")]
        public async Task MallGet()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<ReservationOrder, MallGetReservationOrderOutput>().ReverseMap();
            }).CreateMapper();
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });

            var service = CreateService(
                dbContext: dbContext,
                mapper: mapper,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForClaim);
            await AddReservationOrderTicketCode(
                dbContext,
                baseOrderId, ticketOrderId, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.WaitingForUse,
                    TicketCodeStatus.WaitingForUse
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.WaitingForUse,
                    ReservationTicketCodeStatus.WaitingForUse
                });

            var result = await service.MallGet(reservationOrderId);

            Assert.NotNull(result);
        }

        [Fact(DisplayName = "预约单取消支付_成功")]
        public async Task MallCancelPay_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var capPublisher = GetCapPublisher();
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });

            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForPay);
            await AddReservationOrderTicketCode(
                dbContext,
                baseOrderId, ticketOrderId, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.Reservation,
                    TicketCodeStatus.Reservation
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.Reservation,
                    ReservationTicketCodeStatus.Reservation
                });

            await service.MallCancelPay(reservationOrderId);

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.Equal(ReservationStatus.Canceled, result.Status);
        }

        [Fact(DisplayName = "预约单取消支付_失败")]
        public async Task MallCancelPay_Fail()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForClaim);

            try
            {
                await service.MallCancelPay(reservationOrderId);
            }
            catch (BusinessException ex)
            {
                Assert.Equal(ErrorTypes.Common.NotSupportedOperation.ToString(), ex.BusinessErrorType);
            }
        }

        #endregion

        #region 取消预约

        [Fact(DisplayName = "用户取消预约单_成功")]
        public async Task CancelByCustomer_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var capPublisher = GetCapPublisher();
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });

            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForClaim);
            ReservationOrderPayment reservationOrderPayment = new() { ReservationOrderId = reservationOrderId, Amount = 1, Date = DateTime.Today };
            await dbContext.AddAsync(reservationOrderPayment);
            await AddReservationOrderTicketCode(
                dbContext,
                baseOrderId, ticketOrderId, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.WaitingForUse,
                    TicketCodeStatus.WaitingForUse
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.WaitingForUse,
                    ReservationTicketCodeStatus.WaitingForUse
                });

            var currentUser = new OperationUserDto()
            {
                UserType = UserType.Customer,
                UserId = 89757,
                Name = "测试"
            };
            await service.Cancel(new CancelReservationOrderInput()
            {
                ReservationOrderId = reservationOrderId,
                Message = ""
            }, currentUser);

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.Equal(ReservationStatus.Refunding, result.Status);
        }

        [Fact(DisplayName = "用户取消预约单_失败_无操作权限")]
        public async Task CancelByCustomer_Fail_NoOperationPermission()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89756,
                nickname = "用户2号",
                tenant = _tenantId
            });

            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForClaim);

            try
            {
                var currentUser = new OperationUserDto()
                {
                    UserType = UserType.Customer,
                    UserId = 89756,
                    Name = "用户2号"
                };
                await service.Cancel(new CancelReservationOrderInput()
                {
                    ReservationOrderId = reservationOrderId
                }, currentUser);
            }
            catch (BusinessException ex)
            {
                Assert.Equal(ErrorTypes.Common.NotSupportedOperation.ToString(), ex.BusinessErrorType);
            }
        }

        [Fact(DisplayName = "用户取消预约单_失败_状态不支持当前操作")]
        public async Task CancelByCustomer_Fail_StatusNotSupportOperation()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "用户2号",
                tenant = _tenantId
            });

            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForPay);

            try
            {
                var currentUser = new OperationUserDto()
                {
                    UserType = UserType.Customer,
                    UserId = 89757,
                    Name = "测试"
                };
                await service.Cancel(new CancelReservationOrderInput()
                {
                    ReservationOrderId = reservationOrderId
                }, currentUser);
            }
            catch (BusinessException ex)
            {
                Assert.Equal(ErrorTypes.Common.NotSupportedOperation.ToString(), ex.BusinessErrorType);
            }
        }

        [Fact(DisplayName = "用户取消预约单_失败_可退券码不足")]
        public async Task CancelByCustomer_Fail_TicketCodeNotEnough()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var capPublisher = GetCapPublisher();
            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });

            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                httpContextAccessor: httpContextAccessor);

            var baseOrderId = await AddBaseOrder(dbContext);
            var ticketOrderId = await AddTicketOrder(dbContext, baseOrderId);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, ticketOrderId, ReservationStatus.WaitingForClaim);
            await AddReservationOrderTicketCode(
                dbContext,
                baseOrderId, ticketOrderId, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.WaitingForUse,
                    TicketCodeStatus.Finished
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.WaitingForUse,
                    ReservationTicketCodeStatus.Finished
                });

            try
            {
                var currentUser = new OperationUserDto()
                {
                    UserType = UserType.Customer,
                    UserId = 89757,
                    Name = "测试"
                };
                await service.Cancel(new CancelReservationOrderInput()
                {
                    ReservationOrderId = reservationOrderId
                }, currentUser);
            }
            catch (BusinessException ex)
            {
                Assert.Equal(ErrorTypes.Order.TicketCodeNotEnough.ToString(), ex.BusinessErrorType);
            }
        }

        [Fact(DisplayName = "商户取消预约单_成功")]
        public async Task CancelByTenant_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);

            var httpContextAccessor = GetHttpContextAccessor(new CurrentUser()
            {
                userid = 89757,
                nickname = "测试",
                tenant = _tenantId
            });
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(BaseOrderNotifyProfile).Assembly);
            });
            var service = CreateService(
                dbContext: dbContext,
                httpContextAccessor: httpContextAccessor,
                capPublisher: capPublisher,
                mapper: mapperConfiguration.CreateMapper(),
                messageNotifyService: new MessageNotifyService(capPublisher));
            var baseOrderId = await AddBaseOrder(dbContext);
            var reservationOrderId = await AddReservationOrder(dbContext,
                baseOrderId, 45568778654457889, ReservationStatus.Confirmed, PayType.None);
            await AddReservationOrderTicketCode(
                dbContext,
                baseOrderId, 45568778654457889, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.WaitingForUse,
                    TicketCodeStatus.WaitingForUse
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.WaitingForUse,
                    ReservationTicketCodeStatus.WaitingForUse
                });

            var input = new CancelReservationOrderInput()
            {
                ReservationOrderId = reservationOrderId,
                Message = ""
            };
            var currentUser = new OperationUserDto()
            {
                UserType = UserType.Merchant,
                UserId = 89757,
                Name = "测试"
            };
            await service.Cancel(input, currentUser);

            var result = await dbContext.ReservationOrders
                .FirstOrDefaultAsync(x => x.Id == reservationOrderId);

            Assert.Equal(ReservationStatus.Canceled, result.Status);
        }

        #endregion


        [Fact(DisplayName = "预约单过期自动完结(仅预约产品的预约订单)")]
        public async Task FinishExpirationReservationOrder()
        {
            var dbContext = GetNewDbContext(0);
            var httpResponseMessage = new HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                Content = new StringContent("[{\"Id\":1,\"OperationStatus\":2},{\"Id\":2,\"OperationStatus\":2}]")
            };
            var httpClientFactory = GetHttpClientFactoryMock(httpResponseMessage);
            var options = Options.Create(new ServicesAddress() { Tenant = "http://127.0.0.1" });
            var service = CreateService(
                dbContext: dbContext,
                httpClientFactory: httpClientFactory,
                capPublisher: GetCapPublisher(),
                options: options);

            #region fake data 订单1 未完成 需预约 需核销
            // 结果: 预约单1-已完结 预约单2-待确认 主订单状态-待完成
            var baseOrder1 = new BaseOrder() { Status = BaseOrderStatus.UnFinished, ContactsPhoneNumber = "", ContactsName = "订单1" };
            var ticketOrder1 = new TicketOrder()
            {
                BaseOrderId = baseOrder1.Id,
                SkuValidityEnd = DateTime.Today,
                TicketSaleType = TicketSaleType.GroupPurchase,
                ProductNeedReservation = true,
                ProductNeedWriteOff = true
            };
            var ticketCodes1 = new List<TicketCode>()
            {
                new TicketCode(){ BaseOrderId = baseOrder1.Id, SubOrderId = ticketOrder1.Id, Code = 1001, Status = TicketCodeStatus.Finished },
                new TicketCode(){ BaseOrderId = baseOrder1.Id, SubOrderId = ticketOrder1.Id, Code = 1002, Status = TicketCodeStatus.WaitingForUse },
                new TicketCode(){ BaseOrderId = baseOrder1.Id, SubOrderId = ticketOrder1.Id, Code = 1003, Status = TicketCodeStatus.WaitingForUse },
                new TicketCode(){ BaseOrderId = baseOrder1.Id, SubOrderId = ticketOrder1.Id, Code = 1004, Status = TicketCodeStatus.WaitingForReservation }
            };
            await dbContext.BaseOrders.AddAsync(baseOrder1);
            await dbContext.TicketOrders.AddAsync(ticketOrder1);
            await dbContext.TicketCodes.AddRangeAsync(ticketCodes1);

            var reservationOrders1 = new List<ReservationOrder>();
            var reservationOrderTicketCodes1 = new List<ReservationOrderTicketCode>();

            //过期预约单1 券码1已完结 券码2待使用
            var reservationOrder101 = new ReservationOrder()
            {
                BaseOrderId = baseOrder1.Id,
                TicketOrderId = ticketOrder1.Id,
                Status = ReservationStatus.Confirmed,
                TravelDateBegin = DateTime.Today.AddDays(-30),
                TravelDateEnd = DateTime.Today.AddDays(-1)
            };
            reservationOrders1.Add(reservationOrder101);
            var reservationOrder101_TicketCode1 = new ReservationOrderTicketCode() { Code = ticketCodes1[0].Code, TicketCodeId = ticketCodes1[0].Id, ReservationOrderId = reservationOrder101.Id, Status = ReservationTicketCodeStatus.Finished };
            reservationOrderTicketCodes1.Add(reservationOrder101_TicketCode1);
            var reservationOrder101_TicketCode2 = new ReservationOrderTicketCode() { Code = ticketCodes1[1].Code, TicketCodeId = ticketCodes1[1].Id, ReservationOrderId = reservationOrder101.Id, Status = ReservationTicketCodeStatus.WaitingForUse };
            reservationOrderTicketCodes1.Add(reservationOrder101_TicketCode2);

            //未过期预约单2 券码1待使用
            var reservationOrder102 = new ReservationOrder()
            {
                BaseOrderId = baseOrder1.Id,
                TicketOrderId = ticketOrder1.Id,
                Status = ReservationStatus.WaitingForConfirm,
                TravelDateBegin = DateTime.Today.AddDays(-30),
                TravelDateEnd = DateTime.Today
            };
            reservationOrders1.Add(reservationOrder102);
            var reservationOrder102_TicketCode1 = new ReservationOrderTicketCode() { Code = ticketCodes1[2].Code, TicketCodeId = ticketCodes1[2].Id, ReservationOrderId = reservationOrder102.Id, Status = ReservationTicketCodeStatus.WaitingForUse };
            reservationOrderTicketCodes1.Add(reservationOrder102_TicketCode1);

            await dbContext.ReservationOrders.AddRangeAsync(reservationOrders1);
            await dbContext.ReservationOrderTicketCodes.AddRangeAsync(reservationOrderTicketCodes1);
            await dbContext.SetTenantId(1).SaveChangesAsync();

            #endregion

            #region fake data 订单2 未完成 仅预约
            // 结果: 预约单1-已完结 预约单2-已关闭 主订单状态-已完成
            var baseOrder2 = new BaseOrder() { Status = BaseOrderStatus.UnFinished, ContactsPhoneNumber = "", ContactsName = "订单2" };
            var ticketOrder2 = new TicketOrder()
            {
                BaseOrderId = baseOrder2.Id,
                SkuValidityEnd = DateTime.Today,
                TicketSaleType = TicketSaleType.GroupPurchase,
                ProductNeedReservation = true,
                ProductNeedWriteOff = false
            };
            var ticketCodes2 = new List<TicketCode>()
            {
                new TicketCode(){ BaseOrderId = baseOrder2.Id, SubOrderId = ticketOrder2.Id, Code = 2001, Status = TicketCodeStatus.Finished },
                new TicketCode(){ BaseOrderId = baseOrder2.Id, SubOrderId = ticketOrder2.Id, Code = 2002, Status = TicketCodeStatus.WaitingForUse },
                new TicketCode(){ BaseOrderId = baseOrder2.Id, SubOrderId = ticketOrder2.Id, Code = 2003, Status = TicketCodeStatus.Refunded },
                new TicketCode(){ BaseOrderId = baseOrder2.Id, SubOrderId = ticketOrder2.Id, Code = 2004, Status = TicketCodeStatus.Refunded }
            };
            await dbContext.BaseOrders.AddAsync(baseOrder2);
            await dbContext.TicketOrders.AddAsync(ticketOrder2);
            await dbContext.TicketCodes.AddRangeAsync(ticketCodes2);

            var reservationOrders2 = new List<ReservationOrder>();
            var reservationOrderTicketCodes2 = new List<ReservationOrderTicketCode>();

            //过期预约单1 券码1已关闭 券码2待使用
            var reservationOrder201 = new ReservationOrder()
            {
                BaseOrderId = baseOrder2.Id,
                TicketOrderId = ticketOrder2.Id,
                Status = ReservationStatus.Confirmed,
                TravelDateBegin = DateTime.Today.AddDays(-30),
                TravelDateEnd = DateTime.Today.AddDays(-1)
            };
            reservationOrders2.Add(reservationOrder201);
            var reservationOrder201_TicketCode1 = new ReservationOrderTicketCode() { Code = ticketCodes2[0].Code, TicketCodeId = ticketCodes2[0].Id, ReservationOrderId = reservationOrder201.Id, Status = ReservationTicketCodeStatus.Finished };
            reservationOrderTicketCodes2.Add(reservationOrder201_TicketCode1);
            var reservationOrder201_TicketCode2 = new ReservationOrderTicketCode() { Code = ticketCodes2[1].Code, TicketCodeId = ticketCodes2[1].Id, ReservationOrderId = reservationOrder201.Id, Status = ReservationTicketCodeStatus.WaitingForUse };
            reservationOrderTicketCodes2.Add(reservationOrder201_TicketCode2);

            //过期预约单2 券码1已完结 券码2已退款
            var reservationOrder202 = new ReservationOrder()
            {
                BaseOrderId = baseOrder2.Id,
                TicketOrderId = ticketOrder2.Id,
                Status = ReservationStatus.Canceled,
                TravelDateBegin = DateTime.Today.AddDays(-30),
                TravelDateEnd = DateTime.Today.AddDays(-1)
            };
            reservationOrders2.Add(reservationOrder202);
            var reservationOrder202_TicketCode1 = new ReservationOrderTicketCode() { Code = ticketCodes2[2].Code, TicketCodeId = ticketCodes2[2].Id, ReservationOrderId = reservationOrder202.Id, Status = ReservationTicketCodeStatus.Refunded };
            reservationOrderTicketCodes2.Add(reservationOrder202_TicketCode1);
            var reservationOrder202_TicketCode2 = new ReservationOrderTicketCode() { Code = ticketCodes2[3].Code, TicketCodeId = ticketCodes2[3].Id, ReservationOrderId = reservationOrder202.Id, Status = ReservationTicketCodeStatus.Refunded };
            reservationOrderTicketCodes2.Add(reservationOrder202_TicketCode2);

            await dbContext.ReservationOrders.AddRangeAsync(reservationOrders2);
            await dbContext.ReservationOrderTicketCodes.AddRangeAsync(reservationOrderTicketCodes2);
            await dbContext.SetTenantId(2).SaveChangesAsync();

            #endregion


            var result = await service.FinishExpirationReservationOrder();
            await dbContext.SaveChangesAsync();
            var finishedBaseOrder = await dbContext.BaseOrders.IgnoreQueryFilters().FirstOrDefaultAsync(x => x.Id == baseOrder2.Id);
            Assert.Equal(BaseOrderStatus.Finished, finishedBaseOrder.Status);
            var finishedReservationOrderCount = await dbContext.ReservationOrders.IgnoreQueryFilters().CountAsync(x => x.Status == ReservationStatus.Finished);
            Assert.True(finishedReservationOrderCount == 1);
        }

        #region fake data

        private async Task<long> AddBaseOrder(CustomDbContext dbContext,
            BaseOrderStatus status = BaseOrderStatus.UnFinished)
        {
            var baseOrder = new BaseOrder()
            {
                UserId = 89757,
                UserNickName = "",
                ContactsName = "",
                ContactsPhoneNumber = "",
                ResourceName = "",
                ProductName = "",
                ProductSkuName = "",
                OrderType = OrderType.Ticket,
                Status = status,
                SellingPlatform = SellingPlatform.WeChatApplet,
                SellingChannels = SellingChannels.WechatMall,
                ChannelOrderNo = "",
                TotalAmount = 200,
                DiscountAmount = 0,
                PaymentAmount = 200,
                PaymentType = PayType.YeePay,
                PaymentChannel = "",
                PaymentMode = "",
                PaymentExternalNo = "",
                Message = ""
            };
            await dbContext.AddAsync(baseOrder);
            await dbContext.SaveChangesAsync();
            return baseOrder.Id;
        }

        private async Task<long> AddTicketOrder(CustomDbContext dbContext,
            long baseOrderId)
        {
            var ticketOrder = new TicketOrder()
            {
                BaseOrderId = baseOrderId,
                Quantity = 2,
                ProductId = 20415478923588981,
                ProductTicketBusinessType = TicketBusinessType.HotelPackages,
                ProductTitle = "",
                ProductNeedWriteOff = true,
                ProductNeedReservation = true,
                ProductNeedConfirmReservation = true,
                ProductReservationDaysInAdvance = 0,
                ProductIsSupportRefund = true,
                ProductRefundRate = 0.5m,
                ProductAutoRefundAfterExpiration = false,
                ProductAutoRefundRate = 0m,
                ProductSupplierId = 4878515348789548,
                SkuId = 897513148974151315,
                SkuName = "",
                SkuNumberOfNights = 1,
                SkuCostDescription = "",
                SkuImagePath = "",
                SkuValidityBegin = DateTime.Today,
                SkuValidityEnd = DateTime.Today
            };
            await dbContext.AddAsync(ticketOrder);
            OrderPrice orderPrice = new()
            {
                SubOrderId = ticketOrder.Id,
                OrderType = OrderType.Ticket,
                Quantity = 2,
                CostPrice = 80,
                Price = 100
            };
            await dbContext.AddAsync(orderPrice);
            await dbContext.SaveChangesAsync();
            return ticketOrder.Id;
        }

        private async Task<long> AddTicketCode(CustomDbContext dbContext,
            long baseOrderId, long ticketOrderId, long code, TicketCodeStatus status)
        {
            var ticketCode = new TicketCode()
            {
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                Code = code,
                Status = status
            };
            await dbContext.AddAsync(ticketCode);
            await dbContext.SaveChangesAsync();
            return ticketCode.Id;
        }

        private async Task<long> AddReservationOrder(CustomDbContext dbContext,
            long baseOrderId, long ticketOrderId, ReservationStatus status, PayType payType = PayType.Offline)
        {
            var reservationOrder = new ReservationOrder()
            {
                BaseOrderId = baseOrderId,
                TicketOrderId = ticketOrderId,
                TravelDateBegin = new DateTime(2022, 02, 15),
                TravelDateEnd = new DateTime(2022, 02, 16),
                Traveler = "张三,李四",
                Status = status,
                PaymentType = payType,
                PaymentAmount = 200,
                Quantity = 2
            };
            await dbContext.AddAsync(reservationOrder);
            await dbContext.SaveChangesAsync();
            return reservationOrder.Id;
        }

        private async Task AddReservationOrderTicketCode(CustomDbContext dbContext,
            long baseOrderId, long ticketOrderId, long reservationOrderId,
            List<TicketCodeStatus> tStatuses,
            List<ReservationTicketCodeStatus> rtStatuses)
        {
            var ticketCodes = new List<TicketCode>();
            var reservationOrderTicketCodes = new List<ReservationOrderTicketCode>();

            //已核销券码
            var ticketCode1 = new TicketCode()
            {
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                Code = 11112222,
                Status = tStatuses[0]
            };
            ticketCodes.Add(ticketCode1);
            var reservationOrderTicketCode1 = new ReservationOrderTicketCode()
            {
                ReservationOrderId = reservationOrderId,
                TicketCodeId = ticketCode1.Id,
                Code = ticketCode1.Code,
                Status = rtStatuses[0],
                UpdateTime = DateTime.Now
            };
            reservationOrderTicketCodes.Add(reservationOrderTicketCode1);

            //待使用券码
            var ticketCode2 = new TicketCode()
            {
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                Code = 11113333,
                Status = tStatuses[1]
            };
            ticketCodes.Add(ticketCode2);
            var reservationOrderTicketCode2 = new ReservationOrderTicketCode()
            {
                ReservationOrderId = reservationOrderId,
                TicketCodeId = ticketCode2.Id,
                Code = ticketCode2.Code,
                Status = rtStatuses[1],
                UpdateTime = DateTime.Now
            };
            reservationOrderTicketCodes.Add(reservationOrderTicketCode2);

            await dbContext.AddRangeAsync(ticketCodes);
            await dbContext.AddRangeAsync(reservationOrderTicketCodes);
            await dbContext.SaveChangesAsync();
        }

        #endregion
    }
}
