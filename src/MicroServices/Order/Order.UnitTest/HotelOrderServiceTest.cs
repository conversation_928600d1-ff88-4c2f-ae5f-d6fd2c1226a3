using AutoMapper;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.AgencyCredit;
using DotNetCore.CAP;
using FluentValidation.TestHelper;
using Hangfire;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using Order.Api.Infrastructure;
using Order.Api.Model;
using Order.Api.Services;
using Order.Api.Services.Interfaces;
using Order.Api.Services.MappingProfiles;
using Order.Api.Services.Validator.HotelOrder;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.UnitTest;

public class HotelOrderServiceTest : TestBase<CustomDbContext>
{
    private readonly static long _tenantId = 1;
    private HotelOrderService CreateService(
        IMapper mapper = null,
        IHttpClientFactory httpClientFactory = null,
        IOptions<ServicesAddress> options = null,
        ICapPublisher capPublisher = null,
        CustomDbContext customDbContext = null,
        IUserCouponOrderService userCouponOrderService = null,
        IMultPriceCalculateService multPriceCalculateService = null,
        IMessageNotifyService messageNotifyService = null,
        IBackgroundJobClient backgroundJobClient = null,
        IMediator mediator = null,
        IHttpContextAccessor httpContextAccessor = null,
        IAgencyCreditPayService agencyCreditPayService = null,
        IAgencyService agencyService = null,
        ICurrencyExchangeRateService currencyExchangeRateService = null,
        IHotelApiOrderService hotelApiOrderService = null,
        IRedisClient redisClient = null,
        ILogger<HotelOrderService> logger = null,
        IBaseOrderDiscountService baseOrderDiscountService = null,
        IGDSOrderService gDSOrderService = null,
        IHotelSupplierOrderDomainService hotelSupplierOrderDomainService = null,
        ISupplierService supplierService = null)

    {
        options ??= Options.Create(new ServicesAddress());

        return new HotelOrderService(mapper,
            httpClientFactory,
            options,
            capPublisher,
            customDbContext,
            userCouponOrderService,
            multPriceCalculateService,
            messageNotifyService,
            backgroundJobClient,
            mediator,
            httpContextAccessor,
            agencyCreditPayService,
            agencyService,
            currencyExchangeRateService,
            hotelApiOrderService,
            redisClient,
            logger,
            baseOrderDiscountService: baseOrderDiscountService,
            supplierService: supplierService,
            gDSOrderService: gDSOrderService,
            hotelSupplierOrderDomainService: hotelSupplierOrderDomainService);
    }

    /// <summary>
    /// 创建酒店日历手工单_成功_返回订单id
    /// </summary>
    [Fact(DisplayName = "创建酒店手工单_成功_返回订单id")]
    public async Task CreateByManual_Success_ReturnOrderIds()
    {
        #region arrange

        var dbContext = GetNewDbContext(_tenantId);
        var capPublic = GetCapPublisher();
        var backgroundJobClient = new Mock<IBackgroundJobClient>().Object;
        var options = Options.Create(new ServicesAddress
        {
            Hotel = "http://127.0.0.1"
        });
        var httpResponseMsg = new HttpResponseMessage[]
        {
            //检验酒店库存价格房态
            new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(
                    JsonConvert.SerializeObject(new CheckPriceStrategySaleOutput
                    {
                        Code = 0,
                        Data = new CheckPriceStrategySaleData
                        {
                            Room = new CheckPriceStrategySale_Room {Id = 1},
                            PriceStrategy = new CheckPriceStrategySale_PriceStrategy
                            {
                                Id = 1,
                                MaximumOccupancy = 5
                            }
                        }
                    }), Encoding.UTF8, "application/json")
            }
        };
        var httpClientFactory = GetHttpClientFactoryMock(httpResponseMsg);
        var httpContextAccessor = GetHttpContextAccessor(new CurrentUser
        {
            tenant = _tenantId
        });
        var mapperConfiguration = new MapperConfiguration(x =>
        {
            x.AddProfile<HotelOrderProfiles>();
        });
        var agencyService = new Mock<IAgencyService>();
        agencyService.Setup(x => x.GetAgencyDetail(It.IsAny<long>(), null, true))
            .ReturnsAsync(new Contracts.Common.Tenant.DTOs.Agency.GetAgenciesByIdsOutput
            {
                CurrencyCode = Currency.CNY.ToString()
            });
        var currencyExchangeRateService = new Mock<ICurrencyExchangeRateService>();
        currencyExchangeRateService.Setup(x => x.GetOrderPriceExchange(It.IsAny<OrderPriceExchangeRateInput>()))
            .ReturnsAsync(new OrderPriceExchangeRateOutput { CostExchangeRate = 1, ExchangeRate = 1 });
        var service = CreateService(mapper: mapperConfiguration.CreateMapper(),
            httpClientFactory: httpClientFactory,
            options: options,
            capPublisher: capPublic,
            customDbContext: dbContext,
            userCouponOrderService: null,
            multPriceCalculateService: null,
            backgroundJobClient: backgroundJobClient,
            mediator: null,
            httpContextAccessor: httpContextAccessor,
            agencyService: agencyService.Object,
            currencyExchangeRateService: currencyExchangeRateService.Object);

        #endregion

        #region fake data

        var day = DateTime.Today;
        var input = new CreateByManualInput
        {
            CalendarPrices = GetCalendarPrice(day, 2).Select(x => new CalendarPriceInput
            {
                CostPrice = x.CostPrice,
                Date = x.Date,
                SalePrice = x.SalePrice.Value
            }).ToList(),
            Num = 2,
            DateBegin = day,
            DateEnd = day.AddDays(2),
            ContactsName = "联系人",
            ContactsPhoneNumber = "15918787671"
        };
        var operationUser = new OperationUserDto
        {
            UserId = 2,
            Name = "test",
            UserType = UserType.Merchant
        };

        #endregion

        //act
        var result = await service.CreateByManual(input, operationUser);
        var hotelOrder = await dbContext.HotelOrders.FirstOrDefaultAsync();

        //assert
        Assert.True(await dbContext.BaseOrders.AnyAsync());
        Assert.True(hotelOrder.PriceStrategyMaximumOccupancy > 0);
    }

    /// <summary>
    /// 创建酒店日历手工单_失败_返回存在日期价格无效
    /// </summary>
    [Fact(DisplayName = "创建酒店手工单_失败_返回存在日期价格无效")]
    public async Task CreateByManual_Fail_ReturnCalendarUnavailable()
    {
        #region arrange

        var dbContext = GetNewDbContext(_tenantId);
        var capPublic = GetCapPublisher();
        var backgroundJobClient = new Mock<IBackgroundJobClient>().Object;
        var options = Options.Create(new ServicesAddress
        {
            Hotel = "http://127.0.0.1"
        });
        var httpResponseMsg = new HttpResponseMessage[]
        {
            //检验酒店库存价格房态
            new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(
                    JsonConvert.SerializeObject(new CheckPriceStrategySaleOutput
                    {
                        Code = Contracts.Common.Hotel.Enums.CheckPriceStrategySaleCode.CalendarNotEnable,
                        Data = new ()
                    }), Encoding.UTF8, "application/json")
            }
        };
        var httpClientFactory = GetHttpClientFactoryMock(httpResponseMsg);
        var httpContextAccessor = GetHttpContextAccessor(new CurrentUser
        {
            tenant = _tenantId
        });
        var mapperConfiguration = new MapperConfiguration(x =>
        {
            x.AddProfile<HotelOrderProfiles>();
        });
        var service = CreateService(mapper: mapperConfiguration.CreateMapper(),
            httpClientFactory: httpClientFactory,
            options: options,
            capPublisher: capPublic,
            customDbContext: dbContext,
            userCouponOrderService: null,
            multPriceCalculateService: null,
            backgroundJobClient: backgroundJobClient,
            mediator: null,
            httpContextAccessor: httpContextAccessor);

        #endregion

        #region fake data

        var day = DateTime.Today;
        var input = new CreateByManualInput
        {
            HotelId = 1,
            RoomId = 1,
            StrategyId = 1,
            DateBegin = day,
            DateEnd = day.AddDays(1),
            Num = 1,
            ContactsName = "",
            ContactsPhoneNumber = ""
        };

        #endregion

        //act
        //assert
        _ = await Assert.ThrowsAsync<BusinessException>(() => service.CreateByManual(input, new OperationUserDto { UserId = 1 }));
    }

    /// <summary>
    /// 创建酒店日历手工单_失败_入参验证错误
    /// </summary>
    [Theory(DisplayName = "创建酒店手工单_失败_入参验证错误")]
    [ClassData(typeof(CreateByManualInputData))]
    public async Task CreateByManual_Fail_ValidatorError(CreateByManualInput input)
    {
        //act 
        var validator = new CreateByManualInputValidator();
        var result = await validator.TestValidateAsync(input);

        //assert
        result.ShouldHaveValidationErrorFor(m => m.HotelId);
        result.ShouldHaveValidationErrorFor(m => m.AgencyId);
        result.ShouldHaveValidationErrorFor(m => m.AgencyName);
        result.ShouldHaveValidationErrorFor(m => m.RoomId);
        result.ShouldHaveValidationErrorFor(m => m.StrategyId);
        result.ShouldHaveValidationErrorFor(m => m.Num);
        result.ShouldHaveValidationErrorFor(m => m.ContactsName);
        result.ShouldHaveValidationErrorFor(m => m.ContactsPhoneNumber);
        if (input.DateBegin < DateTime.Today.AddDays(-1))
            result.ShouldHaveValidationErrorFor(m => m.DateBegin);
        if (input.DateEnd <= input.DateBegin)
            result.ShouldHaveValidationErrorFor(m => m.DateEnd);
        result.ShouldHaveValidationErrorFor(m => m.CalendarPrices);

    }

    /// <summary>
    /// 编辑酒店日历手工单_成功
    /// </summary>
    [Theory(DisplayName = "编辑酒店手工单_成功")]
    [ClassData(typeof(EditByManualInputData))]
    public async Task EditByManual_Success(EditByManualInput input)
    {
        #region arrange

        var dbContext = GetNewDbContext(_tenantId);
        var capPublic = GetCapPublisher();
        var backgroundJobClient = new Mock<IBackgroundJobClient>().Object;
        var options = Options.Create(new ServicesAddress
        {
            Hotel = "http://127.0.0.1"
        });
        var httpResponseMsg = new HttpResponseMessage[]
        {
            //检验酒店库存价格房态
            new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(
                    JsonConvert.SerializeObject(new CheckPriceStrategySaleOutput
                    {
                        Code = 0,
                        Data = new ()
                    }), Encoding.UTF8, "application/json")
            },
            new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(
                    JsonConvert.SerializeObject(new CheckPriceStrategySaleOutput
                    {
                        Code = 0,
                        Data = new ()
                    }), Encoding.UTF8, "application/json")
            },
            new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(
                    JsonConvert.SerializeObject(new CheckPriceStrategySaleOutput
                    {
                        Code = 0,
                        Data = new ()
                    }), Encoding.UTF8, "application/json")
            }
        };
        var httpClientFactory = GetHttpClientFactoryMock(httpResponseMsg);

        var agencyCreditEditPayServiceMock = new Mock<IAgencyCreditPayService>();
        agencyCreditEditPayServiceMock
            .Setup(s => s.OrderEditPay(It.IsAny<OrderEditPayRecordInput>()));
        var currencyExchangeRateService = new Mock<ICurrencyExchangeRateService>();
        currencyExchangeRateService.Setup(x => x.GetOrderPriceExchange(It.IsAny<OrderPriceExchangeRateInput>()))
            .ReturnsAsync(new OrderPriceExchangeRateOutput { CostExchangeRate = 1, ExchangeRate = 1 });
        var service = CreateService(mapper: null,
            httpClientFactory: httpClientFactory,
            options: options,
            capPublisher: capPublic,
            customDbContext: dbContext,
            userCouponOrderService: null,
            multPriceCalculateService: null,
            backgroundJobClient: backgroundJobClient,
            mediator: null,
            httpContextAccessor: null,
            agencyCreditPayService: agencyCreditEditPayServiceMock.Object,
            currencyExchangeRateService: currencyExchangeRateService.Object);

        #endregion

        #region fake data

        var baseOrder = new BaseOrder
        {
            Id = 1,
            TotalAmount = 400,
            PaymentAmount = 400,
            ContactsName = "沛",
            ContactsPhoneNumber = "18787564521",
            SellingPlatform = SellingPlatform.System,
            Status = BaseOrderStatus.WaitingForPay
        };
        await dbContext.AddAsync(baseOrder);

        var day = DateTime.Today;
        var hotelOrder = new HotelOrder
        {
            Id = 11,
            BaseOrderId = 1,
            CheckInDate = day.AddDays(2),
            CheckOutDate = day.AddDays(5),
            PriceStrategyRoomsCount = 1,
            PriceStrategyIsAutoConfirm = true,
            HotelIsAutoConfirmRoomStatus = true,
            Status = HotelOrderStatus.WaitingForCheckIn
        };
        await dbContext.AddAsync(hotelOrder);

        var calendarPrices = new List<HotelOrderCalendarPrice>
        {
            new HotelOrderCalendarPrice
            {
                Date = hotelOrder.CheckInDate, CostPrice = 50, SalePrice = 100, HotelOrderId = hotelOrder.Id
            },
            new HotelOrderCalendarPrice
            {
                Date = hotelOrder.CheckInDate.AddDays(1), CostPrice = 50, SalePrice = 100, HotelOrderId = hotelOrder.Id
            },
            new HotelOrderCalendarPrice
            {
                Date = hotelOrder.CheckInDate.AddDays(2), CostPrice = 50, SalePrice = 100, HotelOrderId = hotelOrder.Id
            },
            new HotelOrderCalendarPrice
            {
                Date = hotelOrder.CheckOutDate, CostPrice = 50, SalePrice = 100, HotelOrderId = hotelOrder.Id
            }
        };
        await dbContext.AddRangeAsync(calendarPrices);
        await dbContext.SaveChangesAsync();

        input.CalendarPrices = GetCalendarPrice(input.DateBegin, input.DateEnd.Subtract(input.DateBegin).Days);
        var operationUser = new OperationUserDto
        {
            UserId = 2,
            Name = "test",
            UserType = UserType.Merchant
        };

        #endregion

        //act
        await service.EditByManual(input, operationUser);

        //assert
        Assert.True(dbContext.BaseOrders.Any());
        Assert.True(dbContext.HotelOrderCalendarPrices.Any());
        Assert.True(dbContext.OrderLogs.Any());
        Assert.True(dbContext.BaseOrderRemarks.Any());
    }

    #region Fake Data

    /// <summary>
    /// 酒店日历价格
    /// </summary>
    /// <returns></returns>
    private static List<CalendarPrice> GetCalendarPrice(DateTime day, int dayNum)
    {
        var calendarPrices = new List<CalendarPrice>();
        for (var i = 0; i < dayNum; i++)
        {
            calendarPrices.Add(
                new CalendarPrice
                {
                    Date = day.AddDays(i),
                    CostPrice = 100,
                    SalePrice = 150
                });
        }
        return calendarPrices;
    }

    class CreateByManualInputData : IEnumerable<object[]>
    {
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return new object[]
            {
                new CreateByManualInput
                {
                    DateBegin = DateTime.Today,
                    DateEnd = DateTime.Today.AddDays(3),
                    CalendarPrices = GetCalendarPrice(DateTime.Today, 2).Select(x=>new CalendarPriceInput{
                         Date=x.Date,
                          SalePrice=x.SalePrice.Value,
                        CostPrice=x.CostPrice
                    }).ToList(),
                }
            };
            yield return new object[]
            {
                new CreateByManualInput()
            };
            yield return new object[]
            {
                new CreateByManualInput
                {
                    CalendarPrices = new List<CalendarPriceInput>
                    {
                        new CalendarPriceInput
                        {
                            CostPrice = 1,
                            Date = DateTime.Today
                        }
                    }
                }
            };
        }
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }

    class EditByManualInputData : IEnumerable<object[]>
    {
        private readonly DateTime _day = DateTime.Today;
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return new object[]
            {
                new EditByManualInput
                {
                    BaseOrderId = 1,
                    ChannelOrderNo = "",
                    ContactsName = "test",
                    ContactsPhoneNumber = "15958768591",
                    Num = 2,
                    Remark = "remark",
                    DateBegin = _day,
                    DateEnd = _day.AddDays(1)
                }
            };
            yield return new object[]
            {
                new EditByManualInput
                {
                    BaseOrderId = 1,
                    ChannelOrderNo = "",
                    ContactsName = "test",
                    ContactsPhoneNumber = "15958768591",
                    Num = 4,
                    Remark = "remark",
                    DateBegin = _day.AddDays(1),
                    DateEnd = _day.AddDays(4)
                }
            };
            yield return new object[]
            {
                new EditByManualInput
                {
                    BaseOrderId = 1,
                    ChannelOrderNo = "",
                    ContactsName = "test",
                    ContactsPhoneNumber = "15958768591",
                    Num = 2,
                    Remark = "remark",
                    DateBegin = _day.AddDays(2),
                    DateEnd = _day.AddDays(3)
                }
            };
            yield return new object[]
            {
                new EditByManualInput
                {
                    BaseOrderId = 1,
                    ChannelOrderNo = "",
                    ContactsName = "test",
                    ContactsPhoneNumber = "15958768591",
                    Num = 3,
                    Remark = "remark",
                    DateBegin = _day.AddDays(4),
                    DateEnd = _day.AddDays(6)
                }
            };
            yield return new object[]
            {
                new EditByManualInput
                {
                    BaseOrderId = 1,
                    ChannelOrderNo = "",
                    ContactsName = "test",
                    ContactsPhoneNumber = "15958768591",
                    Num = 2,
                    Remark = "remark",
                    DateBegin = _day.AddDays(6),
                    DateEnd = _day.AddDays(7)
                }
            };
        }
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }
    #endregion
}