using AutoMapper;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.TicketCodeUsed;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.DTOs.ProductResource;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.Infrastructure;
using Order.Api.Model;
using Order.Api.Services;
using Order.Api.Services.Interfaces;
using Order.Api.Services.MappingProfiles;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;
using UserType = Contracts.Common.Order.Enums.UserType;

namespace Order.UnitTest
{
    public class TicketCodeUsedServiceTest : TestBase<CustomDbContext>
    {
        private readonly long _tenantId = 1;
        public TicketCodeUsedService CreateService(
            CustomDbContext dbContext,
            ICapPublisher capPublisher = null,
            IHttpClientFactory httpClientFactory = null,
            IOptions<ServicesAddress> options = null,
            IMapper mapper = null,
            IMessageNotifyService messageNotifyService = null)
        {
            if (options is null)
                options = Options.Create(new ServicesAddress());

            return new TicketCodeUsedService(
                dbContext,
                capPublisher,
                httpClientFactory,
                options,
                mapper,
                messageNotifyService);
        }

        [Fact(DisplayName = "创建核销单_TicketOrder")]
        public async Task Add_TicketOrder()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var operationUser = new OperationUserDto()
            {
                UserId = 89757,
                Name = "测试",
                UserType = UserType.Merchant
            };
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x => {
                x.AddMaps(typeof(BaseOrderNotifyProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                mapper: mapper,
                messageNotifyService: new MessageNotifyService(capPublisher));

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.Ticket);
            var ticketOrderId = await fakeData.AddTicketOrder(baseOrderId, 2, true, true);
            var reservationOrderId = await fakeData.AddReservationOrder(baseOrderId, ticketOrderId, ReservationStatus.Confirmed);
            await fakeData.AddReservationOrderTicketCode(
                baseOrderId, ticketOrderId, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.Finished,
                    TicketCodeStatus.WaitingForUse
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.Finished,
                    ReservationTicketCodeStatus.WaitingForUse
                });

            var input = new AddTicketCodeUsedInput()
            {
                BaseOrderId = baseOrderId,
                ReservationOrderId = reservationOrderId,
                Message = "",
                Quantity = 1
            };
            var ticketCodeUsedId = await service.Add(input, operationUser);

            var result = await dbContext.BaseOrders
                .FirstOrDefaultAsync(x => x.Id == baseOrderId);

            Assert.Equal(BaseOrderStatus.Finished, result.Status);
        }

        [Fact(DisplayName = "创建核销单_ScenicTicketOrder")]
        public async Task Add_ScenicTicketOrder()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var operationUser = new OperationUserDto()
            {
                UserId = 89757,
                Name = "测试",
                UserType = UserType.Merchant
            };
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(x => {
                x.AddMaps(typeof(BaseOrderNotifyProfile).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();
            var service = CreateService(
                dbContext: dbContext,
                capPublisher: capPublisher,
                mapper: mapper,
                messageNotifyService:new MessageNotifyService(capPublisher));

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.ScenicTicket);
            var scenicTicketOrderId = await fakeData.AddScenicTicketOrder(baseOrderId, 2);
            await fakeData.AddTicketCode(OrderType.ScenicTicket, baseOrderId, scenicTicketOrderId, 11112222, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.ScenicTicket, baseOrderId, scenicTicketOrderId, 11118888, TicketCodeStatus.Finished);

            var input = new AddTicketCodeUsedInput()
            {
                BaseOrderId = baseOrderId,
                ReservationOrderId = 0,
                Message = "",
                Quantity = 1
            };
            var ticketCodeUsedId = await service.Add(input, operationUser);

            var result = await dbContext.BaseOrders
                .FirstOrDefaultAsync(x => x.Id == baseOrderId);

            Assert.Equal(BaseOrderStatus.Finished, result.Status);
        }

        [Fact(DisplayName = "核销检验_需预约")]
        public async Task Check_NeedReservation()
        {
            var dbContext = GetNewDbContext(_tenantId);

            var service = CreateService(dbContext: dbContext);

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.Ticket);
            var ticketOrderId = await fakeData.AddTicketOrder(baseOrderId, 3, true, true);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11111111, TicketCodeStatus.WaitingForReservation);
            var reservationOrderId = await fakeData.AddReservationOrder(baseOrderId, ticketOrderId, ReservationStatus.Confirmed);
            await fakeData.AddReservationOrderTicketCode(
                baseOrderId, ticketOrderId, reservationOrderId,
                new List<TicketCodeStatus>()
                {
                    TicketCodeStatus.WaitingForUse,
                    TicketCodeStatus.WaitingForUse
                },
                new List<ReservationTicketCodeStatus>()
                {
                    ReservationTicketCodeStatus.WaitingForUse,
                    ReservationTicketCodeStatus.WaitingForUse
                });


            var input = new CheckTicketCodeUsedInput()
            {
                KeyWord = "11112222"
            };
            var result = await service.Check(input);
            Assert.True(result.Count == 1);

            input = new CheckTicketCodeUsedInput()
            {
                KeyWord = "15766228888"
            };
            result = await service.Check(input);
            Assert.True(result.Count == 2);
        }

        [Fact(DisplayName = "核销检验_不需预约")]
        public async Task Check_NoNeedReservation()
        {
            var dbContext = GetNewDbContext(_tenantId);
            var service = CreateService(dbContext: dbContext);

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.Ticket);
            var ticketOrderId = await fakeData.AddTicketOrder(baseOrderId, 3, false, true);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11111111, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11112222, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11113333, TicketCodeStatus.Finished);

            var input = new CheckTicketCodeUsedInput()
            {
                KeyWord = "11112222"
            };
            var result = await service.Check(input);
            Assert.True(result.Count == 1);

            input = new CheckTicketCodeUsedInput()
            {
                KeyWord = "15766228888"
            };
            result = await service.Check(input);
            Assert.True(result.Count == 1);
        }


        [Fact(DisplayName = "搜索")]
        public async Task Search()
        {
            var dbContext = GetNewDbContext(_tenantId);

            var service = CreateService(dbContext: dbContext);

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.Ticket);
            var ticketOrderId = await fakeData.AddTicketOrder(baseOrderId, 3, false, true);
            await fakeData.AddTicketCodeUsed(baseOrderId, ticketOrderId);

            var input = new SearchTicketCodeUsedInput()
            {
                PageIndex = 1,
                PageSize = 10,
                SearchType = TicketCodeUsedSearchType.TicketCode,
                KeyWord = "11111111",
                ProductBusinessType = TicketCodeBusinessType.HotelPackages
            };
            var result = await service.Search(input);
            Assert.True(result.Total == 1);
        }

        [Fact(DisplayName = "供应商获取核销数据_TicketOrder_成功")]
        public async Task CheckBySupplier_TicketOrder_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);

            var response = new HttpResponseMessage()
            {
                Content = new StringContent(JsonConvert.SerializeObject(new List<GetResourceBySupplierOutput>()
                {
                    new GetResourceBySupplierOutput(){ Id = 1001, Name = "" },
                    new GetResourceBySupplierOutput(){ Id = 1002, Name = "" },
                    new GetResourceBySupplierOutput(){ Id = 2001, Name = "" },
                    new GetResourceBySupplierOutput(){ Id = 2002, Name = "" }
                }), Encoding.UTF8)
            };
            var httpClientFactory = GetHttpClientFactoryMock(response);
            var options = Options.Create(new ServicesAddress { Product = "http://127.0.0.1/" });

            #region fake data

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.Ticket);
            var ticketOrderId = await fakeData.AddTicketOrder(baseOrderId, 3, false, true);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11111111, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11112222, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11113333, TicketCodeStatus.Finished);

            #endregion

            var service = CreateService(
                dbContext: dbContext,
                httpClientFactory: httpClientFactory,
                options: options);

            var input = new CheckUseabledByTicketCodeInput()
            {
                TicketCodes = new List<long>() { 11111111, 11112222 },
                ResourceId = 1001
            };
            var result = await service.CheckUseabledByTicketCode(10001, input);

            Assert.NotNull(result);
        }

        [Fact(DisplayName = "供应商获取核销数据_ScenicTicketOrder_成功")]
        public async Task CheckBySupplier_ScenicTicketOrder_Success()
        {
            var dbContext = GetNewDbContext(_tenantId);

            #region fake data

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.ScenicTicket);
            var scenicTicketOrderId = await fakeData.AddScenicTicketOrder(baseOrderId, 3);
            await fakeData.AddTicketCode(OrderType.ScenicTicket, baseOrderId, scenicTicketOrderId, 11111111, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.ScenicTicket, baseOrderId, scenicTicketOrderId, 11112222, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.ScenicTicket, baseOrderId, scenicTicketOrderId, 11113333, TicketCodeStatus.Finished);

            #endregion

            var service = CreateService(
                dbContext: dbContext);

            var input = new CheckUseabledByTicketCodeInput()
            {
                TicketCodes = new List<long>() { 11111111 },
                ResourceId = 98373272319840
            };
            var result = await service.CheckUseabledByTicketCode(10001, input);

            Assert.NotNull(result);
        }

        [Fact(DisplayName = "供应商获取核销数据_TicketOrder_失败_券码无效")]
        public async Task CheckBySupplier_TicketOrder_Fail_TicketCodeInvalid()
        {
            var dbContext = GetNewDbContext(_tenantId);

            #region fake data

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.Ticket);
            var ticketOrderId = await fakeData.AddTicketOrder(baseOrderId, 3, false, true);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11111111, TicketCodeStatus.WaitingForUse);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11112222, TicketCodeStatus.WaitingForReservation);
            await fakeData.AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11113333, TicketCodeStatus.Finished);

            #endregion

            var service = CreateService(dbContext: dbContext);

            try
            {
                var input = new CheckUseabledByTicketCodeInput()
                {
                    TicketCodes = new List<long>() { 11111111, 11112222 },
                    ResourceId = 1001
                };
                var result = await service.CheckUseabledByTicketCode(4878515348789548, input);
            }
            catch (BusinessException ex)
            {
                Assert.True(ex.BusinessErrorType == nameof(ErrorTypes.Order.TicketCodeInvalid));
            }
        }

        [Fact(DisplayName = "供应商获取核销记录_TicketOrder")]
        public async Task GetUsedBySupplier_TicketOrder()
        {
            var dbContext = GetNewDbContext(_tenantId);

            #region fake data

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.Ticket);
            var ticketOrderId = await fakeData.AddTicketOrder(baseOrderId, 1, false, true);

            var used = new TicketCodeUsed()
            {
                OrderType = OrderType.Ticket,
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                ProductSupplierId = 10001,
                ResourceId = 1001
            };
            await dbContext.TicketCodeUseds.AddAsync(used);
            await dbContext.SaveChangesAsync();

            #endregion

            var service = CreateService(dbContext: dbContext);

            var input = new GetTicketCodeUsedInput()
            {
                PageIndex = 1,
                PageSize = 10,
                ResourceId = 1001
            };
            var result = await service.GetUsedInfo(10001, input);

            Assert.True(result.Total == 1);
        }


        [Fact(DisplayName = "供应商获取核销记录_ScenicTicketOrder")]
        public async Task GetUsedBySupplier_ScenicTicketOrder()
        {
            var dbContext = GetNewDbContext(_tenantId);

            #region fake data

            var fakeData = new FakeData(dbContext);
            var baseOrderId = await fakeData.AddBaseOrder(OrderType.ScenicTicket);
            var scenicTicketOrder = await fakeData.AddScenicTicketOrder(baseOrderId, 1);

            var used = new TicketCodeUsed()
            {
                OrderType = OrderType.ScenicTicket,
                BaseOrderId = baseOrderId,
                SubOrderId = scenicTicketOrder,
                ProductSupplierId = 10001,
                ResourceId = 1001
            };
            await dbContext.TicketCodeUseds.AddAsync(used);
            await dbContext.SaveChangesAsync();

            #endregion

            var service = CreateService(dbContext: dbContext);

            var input = new GetTicketCodeUsedInput()
            {
                PageIndex = 1,
                PageSize = 10,
                ResourceId = 1001
            };
            var result = await service.GetUsedInfo(10001, input);

            Assert.True(result.Total == 1);
        }
    }

    public class FakeData
    {
        private readonly CustomDbContext _dbContext;
        public FakeData(CustomDbContext dbContext) 
        {
            _dbContext = dbContext;
        }

        #region fake data

        public async Task<long> AddBaseOrder(OrderType orderType,
            BaseOrderStatus status = BaseOrderStatus.UnFinished)
        {
            var baseOrder = new BaseOrder()
            {
                UserId = 89757,
                UserNickName = "",
                ContactsName = "",
                ContactsPhoneNumber = "15766228888",
                ResourceName = "",
                ProductName = "",
                ProductSkuName = "",
                OrderType = orderType,
                Status = status,
                SellingPlatform = SellingPlatform.WeChatApplet,
                SellingChannels = SellingChannels.WechatMall,
                ChannelOrderNo = "",
                TotalAmount = 200,
                DiscountAmount = 0,
                PaymentAmount = 200,
                PaymentType = PayType.YeePay,
                PaymentChannel = "",
                PaymentMode = "",
                PaymentExternalNo = "",
                Message = ""
            };
            await _dbContext.AddAsync(baseOrder);
            await _dbContext.SaveChangesAsync();
            return baseOrder.Id;
        }

        public async Task<long> AddTicketOrder(long baseOrderId, int quantity, bool needReservation, bool needWriteOff)
        {
            var ticketOrder = new TicketOrder()
            {
                BaseOrderId = baseOrderId,
                Quantity = quantity,
                ProductId = 20415478923588981,
                ProductTicketBusinessType = TicketBusinessType.HotelPackages,
                ProductTitle = "",
                ProductNeedWriteOff = needWriteOff,
                ProductNeedReservation = needReservation,
                ProductNeedConfirmReservation = true,
                ProductReservationDaysInAdvance = 2,
                ProductIsSupportRefund = true,
                ProductRefundRate = 0.5m,
                ProductAutoRefundAfterExpiration = false,
                ProductAutoRefundRate = 0m,
                ProductSupplierId = 10001,
                SkuId = 897513148974151315,
                SkuName = "",
                SkuNumberOfNights = 1,
                SkuCostDescription = "",
                SkuImagePath = "",
                SkuValidityBegin = DateTime.Today,
                SkuValidityEnd = DateTime.Today.AddDays(1)
            };
            await _dbContext.AddAsync(ticketOrder);
            var orderPrice = new OrderPrice
            {
                SubOrderId = ticketOrder.Id,
                OrderType = OrderType.Ticket,
                Quantity = quantity,
                Price = 100,
                CostPrice = 80
            };
            await _dbContext.AddAsync(orderPrice);
            await _dbContext.SaveChangesAsync();
            return ticketOrder.Id;
        }

        public async Task<long> AddScenicTicketOrder(long baseOrderId, int quantity)
        {
            var scenicTicketOrder = new ScenicTicketOrder()
            {
                BaseOrderId = baseOrderId,
                Quantity = quantity,
                ScenicTicketsType = ScenicTicketsType.Reservation,
                ValidityBegin = DateTime.Today,
                ValidityEnd = DateTime.Today.AddDays(1),
                ScenicSpotId = 98373272319840,
                SupplierId = 10001
            };
            await _dbContext.AddAsync(scenicTicketOrder);
            var orderPrice = new OrderPrice()
            {
                SubOrderId = scenicTicketOrder.Id,
                OrderType = OrderType.ScenicTicket,
                Quantity = quantity
            };
            await _dbContext.AddAsync(orderPrice);
            await _dbContext.SaveChangesAsync();
            return scenicTicketOrder.Id;
        }

        public async Task<long> AddTicketCode(OrderType orderType, long baseOrderId, long subOrderId,
            long code, TicketCodeStatus status)
        {
            var ticketCode = new TicketCode()
            {
                OrderType = orderType,
                BaseOrderId = baseOrderId,
                SubOrderId = subOrderId,
                Code = code,
                Status = status
            };
            await _dbContext.AddAsync(ticketCode);
            await _dbContext.SaveChangesAsync();
            return ticketCode.Id;
        }

        public async Task<long> AddReservationOrder(long baseOrderId, long ticketOrderId, ReservationStatus status)
        {
            var reservationOrder = new ReservationOrder()
            {
                BaseOrderId = baseOrderId,
                TicketOrderId = ticketOrderId,
                TravelDateBegin = DateTime.Today,
                TravelDateEnd = DateTime.Today,
                Traveler = "张三,李四",
                Status = status,
                PaymentType = PayType.Offline,
                PaymentAmount = 200,
                Quantity = 2
            };
            await _dbContext.AddAsync(reservationOrder);
            await _dbContext.SaveChangesAsync();
            return reservationOrder.Id;
        }

        public async Task AddReservationOrderTicketCode(
            long baseOrderId, long ticketOrderId, long reservationOrderId,
            List<TicketCodeStatus> tStatuses,
            List<ReservationTicketCodeStatus> rtStatuses)
        {
            var ticketCodes = new List<TicketCode>();
            var reservationOrderTicketCodes = new List<ReservationOrderTicketCode>();

            //已核销券码
            var ticketCode1 = new TicketCode()
            {
                OrderType = OrderType.Ticket,
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                Code = 11112222,
                Status = tStatuses[0]
            };
            ticketCodes.Add(ticketCode1);
            var reservationOrderTicketCode1 = new ReservationOrderTicketCode()
            {
                ReservationOrderId = reservationOrderId,
                TicketCodeId = ticketCode1.Id,
                Code = ticketCode1.Code,
                Status = rtStatuses[0],
                UpdateTime = DateTime.Now
            };
            reservationOrderTicketCodes.Add(reservationOrderTicketCode1);

            //待使用券码
            var ticketCode2 = new TicketCode()
            {
                OrderType = OrderType.Ticket,
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                Code = 11113333,
                Status = tStatuses[1]
            };
            ticketCodes.Add(ticketCode2);
            var reservationOrderTicketCode2 = new ReservationOrderTicketCode()
            {
                ReservationOrderId = reservationOrderId,
                TicketCodeId = ticketCode2.Id,
                Code = ticketCode2.Code,
                Status = rtStatuses[1],
                UpdateTime = DateTime.Now
            };
            reservationOrderTicketCodes.Add(reservationOrderTicketCode2);

            await _dbContext.AddRangeAsync(ticketCodes);
            await _dbContext.AddRangeAsync(reservationOrderTicketCodes);
            await _dbContext.SaveChangesAsync();
        }

        public async Task AddTicketCodeUsed(long baseOrderId, long ticketOrderId)
        {
            var ticketCodeId1 = await AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11111111, TicketCodeStatus.Finished);
            var ticketCodeId2 = await AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11112222, TicketCodeStatus.Finished);
            var ticketCodeId3 = await AddTicketCode(OrderType.Ticket, baseOrderId, ticketOrderId, 11113333, TicketCodeStatus.Finished);

            var useds = new List<TicketCodeUsed>();
            var details = new List<TicketCodeUsedDetails>();

            var ticketCodeUsed1 = new TicketCodeUsed()
            {
                OrderType = OrderType.Ticket,
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                Count = 2,
                Amount = 200,
                CreatorId = 10000,
                CreatorName = "",
                UsedSource = TicketCodeUsedSource.VEBK,
                ProductBusinessType = TicketCodeBusinessType.HotelPackages
            };
            useds.Add(ticketCodeUsed1);
            details.Add(new TicketCodeUsedDetails()
            {
                TicketCodeUsedId = ticketCodeUsed1.Id,
                TicketCodeId = ticketCodeId1,
                Code = 11111111
            });
            details.Add(new TicketCodeUsedDetails()
            {
                TicketCodeUsedId = ticketCodeUsed1.Id,
                TicketCodeId = ticketCodeId2,
                Code = 11112222
            });

            var ticketCodeUsed2 = new TicketCodeUsed()
            {
                OrderType = OrderType.Ticket,
                BaseOrderId = baseOrderId,
                SubOrderId = ticketOrderId,
                Count = 1,
                Amount = 100,
                CreatorId = 10000,
                CreatorName = "",
                UsedSource = TicketCodeUsedSource.VEBK,
                ProductBusinessType = TicketCodeBusinessType.HotelPackages
            };
            useds.Add(ticketCodeUsed2);
            details.Add(new TicketCodeUsedDetails()
            {
                TicketCodeUsedId = ticketCodeUsed2.Id,
                TicketCodeId = ticketCodeId3,
                Code = 11113333
            });

            await _dbContext.AddRangeAsync(useds);
            await _dbContext.AddRangeAsync(details);
            await _dbContext.SaveChangesAsync();
        }

        #endregion

    }
}
