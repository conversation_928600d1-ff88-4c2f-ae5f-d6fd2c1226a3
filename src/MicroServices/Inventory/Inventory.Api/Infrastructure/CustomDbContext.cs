using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Inventory.Api.Infrastructure;

public class CustomDbContext : DbContextBase
{
    public CustomDbContext(DbContextOptions dbContextOptions, ITenantIdentify tenantIdentify) : base(dbContextOptions, tenantIdentify)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }

    public DbSet<Model.Inventory> Inventories { get; set; }
    public DbSet<InventoryRecord> InventoryRecords { get; set; }
    public DbSet<ProductCalendarInventory> ProductCalendarInventories { get; set; }
    public DbSet<ProductInventory> ProductInventories { get; set; }
    public DbSet<ProductInventoryConfig> ProductInventoryConfigs { get; set; }

    public DbSet<ProductTimeSlotCalendarInventory> ProductTimeSlotCalendarInventories { get; set; }
}
