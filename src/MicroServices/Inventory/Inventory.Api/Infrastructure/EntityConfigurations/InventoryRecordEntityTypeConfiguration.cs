using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Inventory.Api.Infrastructure.EntityConfigurations
{
    public class InventoryRecordEntityTypeConfiguration : TenantBaseConfiguration<Model.InventoryRecord>, IEntityTypeConfiguration<Model.InventoryRecord>
    {
        public void Configure(EntityTypeBuilder<Model.InventoryRecord> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.InventoryId)
                .HasColumnType("bigint");

            builder.Property(s => s.InventoryChangeType)
                .HasColumnType("tinyint");

            builder.Property(s => s.OrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.Quantity)
                .HasColumnType("int");

            builder.Property(s => s.AfterAvailableQuantity)
                .HasColumnType("int");

            builder.Property(s => s.AfterFrozenQuantity)
                .HasColumnType("int");

            builder.Property(s => s.AfterTotalQuantity)
                .HasColumnType("int");

            builder.Property(s => s.AfterUsedQuantity)
               .HasColumnType("int");

            builder.Property(s => s.Remark)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");


            //索引
            builder.HasIndex(s => s.InventoryId);
            builder.HasIndex(s => s.OrderId);
        }
    }
}
