using Contracts.Common.Inventory.DTOs;
using FluentValidation;
using System;

namespace Inventory.Api.Services.Validator
{
    public class GetCalendarInventoryInputValidator : AbstractValidator<GetCalendarInventoryInput>
    {
        public GetCalendarInventoryInputValidator()
        {
            //RuleFor(i => i.StartDate).GreaterThanOrEqualTo(DateTime.Today.AddDays(-1));
            When(i => i.LimitDateRange, () =>
            {
                RuleFor(i => i.EndDate)
                    .GreaterThanOrEqualTo(i => i.StartDate) //>=日期
                    .LessThanOrEqualTo(i => i.StartDate.Date.AddMonths(6)); //<=6个月
            });
            
            RuleFor(i => i.CalendarProducts).NotNull();
        }
    }
}
