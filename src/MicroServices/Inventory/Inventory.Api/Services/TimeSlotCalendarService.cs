using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Inventory.Enums;
using Contracts.Common.Inventory.Messages;
using EfCoreExtensions.Extensions;
using Inventory.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Inventory.Api.Services;

public class TimeSlotCalendarService : ITimeSlotCalendarService
{
    private readonly CustomDbContext _dbContext;
    public TimeSlotCalendarService(
        CustomDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<GetTimeSlotInventoryOutput>> Query(GetTimeSlotInventoryInput input)
    {
        var result = new List<GetTimeSlotInventoryOutput>();
        var startDate = input.StartDate.Date;
        var endDate = input.EndDate.Date;
        var query = from timeSlotInventory in _dbContext.ProductTimeSlotCalendarInventories.AsQueryable()
            join inventory in _dbContext.Inventories.AsQueryable() on timeSlotInventory.InventoryId equals inventory.Id
            where input.SkuIds.Contains(timeSlotInventory.SkuId)
                  && timeSlotInventory.Date.Date >= startDate
                  && timeSlotInventory.Date.Date <= endDate
            orderby timeSlotInventory.Date.Date, timeSlotInventory.Time
            select new
            {
                timeSlotInventory,inventory
            };

        if (input.TimeSlotIds.Any())
        {
            query = query.Where(x => input.TimeSlotIds.Contains(x.timeSlotInventory.TimeSlotId));
        }
        
        var timeSlotInventories = await query
            .WhereIF(input.TimeSlots.Any(), x => input.TimeSlots.Contains(x.timeSlotInventory.Time))
            .ToListAsync();
        if (timeSlotInventories.Any() is false)
            return result;

        result = timeSlotInventories
            .GroupBy(x => new
            {
                x.timeSlotInventory.ProductId, 
                x.timeSlotInventory.SkuId,
                x.timeSlotInventory.TimeSlotId
            })
            .Select(x =>
            {
                var item = new GetTimeSlotInventoryOutput
                {
                    ProductId = x.Key.ProductId,
                    SkuId = x.Key.SkuId,
                    TimeSlotId = x.Key.TimeSlotId,
                    Inventories = new List<TimeSlotInventoryItem>()
                };
                var inventories = x.Select(i => 
                        new TimeSlotInventoryItem
                        {
                            Date = i.timeSlotInventory.Date.Date,
                            Time = i.timeSlotInventory.Time,
                            AvailableQuantity = i.inventory.AvailableQuantity,
                            TotalQuantity = i.inventory.TotalQuantity,
                            Enabled = i.inventory.Enabled
                        })
                    .ToList();
                item.Inventories = inventories.OrderBy(o => o.Time).ToList();
                
                return item;
            })
            .ToList();

        return result;
    }

    public async Task BatchSync(BatchSyncThirdTimeSlotInventoryMessage receive)
    {
        if (receive.ByteItem == null)
            return;
        
        var receiveItems =
            MessagePack.MessagePackSerializer.Typeless.Deserialize(receive.ByteItem) as List<BatchSyncThirdTimeSlotInventoryItem>;//反序列化元数据
        if (receiveItems == null || receiveItems.Any() is false)
            return;
        
        //第三方库存同步 直接覆盖
        var productIds = receiveItems.Select(x => x.ProductId).ToList();
        var skuIds = receiveItems.Select(x => x.SkuId).ToList();
        var timeSlotIds = receiveItems.Select(x => x.TimeSlotId).ToList();

        var query = from timeSlotInventory in _dbContext.ProductTimeSlotCalendarInventories.IgnoreQueryFilters()
            join inventory in _dbContext.Inventories.IgnoreQueryFilters() on timeSlotInventory.InventoryId equals
                inventory.Id
            where timeSlotInventory.TenantId == receive.TenantId && inventory.TenantId == receive.TenantId
                                                                 && skuIds.Contains(timeSlotInventory.SkuId)
                                                                 && timeSlotIds.Contains(timeSlotInventory.TimeSlotId)
            select new
            {
                timeSlotInventory,inventory
            };

        var oldInventoryInfos = await query.ToListAsync();
        var oldTimeSlotInventories = oldInventoryInfos.Select(x => x.timeSlotInventory);
        var oldInventories = oldInventoryInfos.Select(x => x.inventory);

        var productTimeSlotCalendarInventories = new List<ProductTimeSlotCalendarInventory>();//时段库存
        var inventories = new List<Model.Inventory>();//库存
        
        #region 无效日期库存推空

        if (receive.OtherDatesPushEmpty)
        {
            var syncDates = receiveItems.Select(x => x.Date.Date.Add(x.Time));
            var invalidCalendarPrices = oldTimeSlotInventories
                .Where(x => x.Date.Date >= DateTime.Today)
                .Where(x => !syncDates.Contains(x.Date.Date.Add(x.Time)))
                .ToList();
        
            foreach (var invalidItem in invalidCalendarPrices)
            {
                var inventory = oldInventories.FirstOrDefault(x => x.Id == invalidItem?.InventoryId);
                inventory.TotalQuantity = 0;
                inventory.AvailableQuantity = 0;
                inventory.Enabled = false;
            }
        }
        
        #endregion
        
        foreach (var syncItem in receiveItems)
        {
            var dateTimeSlotInvItem = oldTimeSlotInventories
                .FirstOrDefault(x => x.SkuId == syncItem.SkuId && x.TimeSlotId == syncItem.TimeSlotId
                                                               && x.Date.Date == syncItem.Date.Date);
            var inventory = oldInventories.FirstOrDefault(x => x.Id == dateTimeSlotInvItem?.InventoryId);
            if (dateTimeSlotInvItem != null)
            {
                inventory.TotalQuantity = syncItem.TotalQuantity;
                inventory.AvailableQuantity = syncItem.AvailableQuantity;
                if (syncItem.Enabled.HasValue) //是否开售.为null时,不更新.
                    inventory.Enabled = syncItem.Enabled.Value;
            }
            else
            {
                //库存
                inventory = new Model.Inventory
                {
                    AvailableQuantity = syncItem.AvailableQuantity,
                    Enabled = true,
                    TotalQuantity = syncItem.TotalQuantity
                };
                inventory.SetTenantId(receive.TenantId);
                inventories.Add(inventory);

                //时段库存
                var timeSlotInventory = new ProductTimeSlotCalendarInventory
                {
                    ProductId = syncItem.ProductId,
                    SkuId = syncItem.SkuId,
                    TimeSlotId = syncItem.TimeSlotId,
                    InventoryId = inventory.Id,
                    Date = syncItem.Date.Date,
                    Time = syncItem.Time
                };
                timeSlotInventory.SetTenantId(receive.TenantId);
                productTimeSlotCalendarInventories.Add(timeSlotInventory);
            }
        }
        
        
        await _dbContext.ProductTimeSlotCalendarInventories.AddRangeAsync(productTimeSlotCalendarInventories);
        await _dbContext.Inventories.AddRangeAsync(inventories);
        await _dbContext.SaveChangesAsync();
    }

    public async Task BatchDelete(BatchDeleteTimeSlotInventoryMessage receive)
    {
        if(receive.Items.Any() is false) return;

        var productIds = receive.Items.Select(x => x.ProductId).ToList();
        var skuIds = receive.Items.Select(x => x.SkuId).ToList();
        var timeSlotIds = receive.Items.Where(x => x.TimeSlotId.HasValue).Select(x => x.TimeSlotId).ToList();
        
        var inventories = await _dbContext.ProductTimeSlotCalendarInventories.IgnoreQueryFilters()
            .Where(x => x.TenantId == receive.TenantId
                        && productIds.Contains(x.ProductId)
                        && skuIds.Contains(x.SkuId))
            .WhereIF(timeSlotIds.Any(), x => timeSlotIds.Contains(x.TimeSlotId))
            .ToListAsync();
        
        _dbContext.RemoveRange(inventories);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<CheckInventoryEarlyWarningOutput> CheckEarlyWarning(CheckInventoryEarlyWarningInput input)
    {
        var output = new CheckInventoryEarlyWarningOutput
        {
            ProductId = input.ProductId
        };
        //查询可售库存的天数
        var query = from timeSlotInventory in _dbContext.ProductTimeSlotCalendarInventories.AsNoTracking()
            join inventory in _dbContext.Inventories.AsNoTracking()
                on timeSlotInventory.InventoryId equals inventory.Id
            where timeSlotInventory.ProductId == input.ProductId
                  && (input.SkuIds.Any() && input.SkuIds.Contains(timeSlotInventory.SkuId))
                  && (input.TimeSlotIds.Any() && input.TimeSlotIds.Contains(timeSlotInventory.TimeSlotId))
                  && timeSlotInventory.Date.Date >= input.StartDate
                  && inventory.Enabled == true && inventory.TotalQuantity > 0 && inventory.AvailableQuantity > 0
            select new
            {
                timeSlotInventory.ProductId,
                timeSlotInventory.SkuId,
                timeSlotInventory.TimeSlotId,
                timeSlotInventory.Date,
            };
        
        var inventoryInfos = await query.ToListAsync();

        output.EarlyWarningItems = inventoryInfos
            .GroupBy(x => new {x.ProductId, x.SkuId, x.TimeSlotId})
            .Select(x =>
            {
                var item = new InventoryEarlyWarningItem
                {
                    SkuId = x.Key.SkuId,
                    TimeSlotId = x.Key.TimeSlotId,
                    AvailableDays = x.Count()
                };
                return item;
            })
            .ToList();
            
        return output;
        
    }
}