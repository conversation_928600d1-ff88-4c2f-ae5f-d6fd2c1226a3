using EfCoreExtensions.EntityBase;

namespace Inventory.Api.Model;

/// <summary>
/// 时段产品日历库存
/// </summary>
public class ProductTimeSlotCalendarInventory : TenantBase
{
    /// <summary>
    /// 产品维度Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// sku纬度Id
    /// </summary>
    public long SkuId { get; set; }
    
    /// <summary>
    /// 时段维度Id
    /// </summary>
    public long TimeSlotId { get; set; }

    /// <summary>
    /// 库存Id
    /// </summary>
    public long InventoryId { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 时段
    /// </summary>
    public TimeSpan Time { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
}