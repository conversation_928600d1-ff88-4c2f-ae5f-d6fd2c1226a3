using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Marketing.DTOs.Lottery;
using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.DTOs.B2BPopUp;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Linq.Expressions;
using Tenant.Api.Model;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Services;

public class B2BPopUpService : IB2BPopUpService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;
    public B2BPopUpService(
        IMapper mapper,
        CustomDbContext dbContext)
    {
        _mapper = mapper;
        _dbContext = dbContext;
    }

    public async Task<List<GetPopUpByAgencyUserOutput>> GetPopUpByAgencyUser(GetPopUpByAgencyUserInput input)
    {
        //查询出符合时间和租户的弹窗
        var query = _dbContext.B2BPopUps.AsNoTracking()
                .GroupJoin(_dbContext.B2BPopUpContents.AsNoTracking(), x => x.Id, a => a.B2BPopUpId, (p, c) => new { p, c })
                .SelectMany(x => x.c.DefaultIfEmpty(), (x, c) => new { x.p, c })
                .GroupJoin(_dbContext.B2BPopUpAgencies.AsNoTracking(), x => x.p.Id, pa => pa.B2BPopUpId, (x, pa) => new { x.p, x.c, pa })
                .SelectMany(x => x.pa.DefaultIfEmpty(), (x, pa) => new { x.p, x.c, pa })
                .Where(x => x.c.B2BPopUpContentType == input.Type && x.c.Enable)
                .Where(x => x.p.Enable
                    && x.p.BeginTime <= input.CurrentTime
                    && x.p.EndTime >= input.CurrentTime
                    && (x.p.B2BPopUpAgencyType == B2BPopUpAgencyType.All
                        || (x.p.B2BPopUpAgencyType == B2BPopUpAgencyType.Appointed
                            && x.pa.AgencyId == input.AgencyId)))
                .Select(x => x.p);

        var b2BPopUps = await query.Distinct().ToListAsync();
        if (b2BPopUps.Any() is false)
            return null;

        Expression<Func<B2BPopUpLog, bool>> predicate = x => false;
        var b2BPopUpLogQuery = _dbContext.B2BPopUpLogs.AsNoTracking()
            .Where(x => x.UserId == input.UserId
                && x.AgencyId == input.AgencyId);

        var b2BPopUpsByOnce = b2BPopUps
            .Where(x => x.B2BPopUpShowType == B2BPopUpShowType.Once)
            .ToList();
        if (b2BPopUpsByOnce.Any())
        {
            var b2BPopUpsByOnceIds = b2BPopUpsByOnce.Select(x => x.Id).ToList();
            predicate = predicate.Or(x => b2BPopUpsByOnceIds.Contains(x.B2BPopUpId)
                && x.RecordDate == null);
        }
        var b2BPopUpsByDayOnce = b2BPopUps
            .Where(x => x.B2BPopUpShowType == B2BPopUpShowType.DayOnce)
            .ToList();
        if (b2BPopUpsByDayOnce.Any())
        {
            var b2BPopUpsByDayOnceIds = b2BPopUpsByDayOnce.Select(x => x.Id).ToList();
            predicate = predicate.Or(x => b2BPopUpsByDayOnceIds.Contains(x.B2BPopUpId)
                && x.RecordDate == input.CurrentTime.Date);
        }
        var removeB2BPopUpIds = await b2BPopUpLogQuery.Where(predicate).Select(x => x.B2BPopUpId).ToListAsync();
        var b2BPopUpIds = b2BPopUps.Select(x => x.Id).Except(removeB2BPopUpIds);
        var b2BPopUpContents = _dbContext.B2BPopUpContents.AsNoTracking()
            .Where(x => b2BPopUpIds.Contains(x.B2BPopUpId) && x.B2BPopUpContentType == input.Type)
            .OrderBy(x => x.B2BPopUpId)
            .ToList();

        var result = _mapper.Map<List<GetPopUpByAgencyUserOutput>>(b2BPopUpContents);
        return result;
    }

    public async Task<PagingModel<B2BPopUpListOutput, B2BPopUpStatOutput>> Search(SearchB2BPopUpInput input)
    {
        var now = DateTime.Now;
        var query = _dbContext.B2BPopUps.AsNoTracking()
            .Where(x => x.Enable);

        var b2BPopUps = await query
            .WhereIF(input.Stauts.HasValue && input.Stauts == B2BPopUpStatus.Effective,x => x.BeginTime <= now && x.EndTime >= now)
            .WhereIF(input.Stauts.HasValue && input.Stauts == B2BPopUpStatus.NotEffective, x => x.BeginTime > now)
            .WhereIF(input.Stauts.HasValue && input.Stauts == B2BPopUpStatus.End, x => x.EndTime < now)
            .OrderByDescending(x => x.CreateTime)
            .PagingAsync(input.PageIndex, input.PageSize);

        var statOutput = new B2BPopUpStatOutput
        {
            EffectiveCount = query.Where(x=> x.BeginTime <= now && x.EndTime >= now).Count(),
            NotEffectiveCount = query.Where(x => x.BeginTime > now).Count(),
            EndCount = query.Where(x => x.EndTime < now).Count(),
        };

        var pagingModel = _mapper.Map<PagingModel<B2BPopUpListOutput>>(b2BPopUps);
        PagingModel<B2BPopUpListOutput, B2BPopUpStatOutput> result = new()
        {
            PageSize = pagingModel.PageSize,
            PageIndex = pagingModel.PageIndex,
            Total = pagingModel.Total,
            Data = pagingModel.Data,
            Supplement = statOutput
        };
        return result;
    }

    public async Task<B2BPopUpDetailOutput> Detail(long id)
    {
        var b2BPopUp = await _dbContext.B2BPopUps.AsNoTracking()
            .Where(x => x.Id == id && x.Enable)
            .FirstOrDefaultAsync();
        if (b2BPopUp is null)
            return null;

        var agencyIds = await _dbContext.B2BPopUpAgencies.AsNoTracking()
            .Where(x => x.B2BPopUpId == id)
            .Select(x => x.AgencyId)
            .ToListAsync();

        var b2BPopUpContents = await _dbContext.B2BPopUpContents.AsNoTracking()
            .Where(x => x.B2BPopUpId == id)
            .ToListAsync();

        var result = _mapper.Map<B2BPopUpDetailOutput>(b2BPopUp);
        result.AgencyIds = agencyIds;
        result.B2BPopUpContents = _mapper.Map<List<B2BPopUpContentInfo>>(b2BPopUpContents);

        return result;
    }

    public async Task<long> Add(AddB2BPopUpInput input)
    {
        var b2BPopUp = _mapper.Map<B2BPopUp>(input);
        b2BPopUp.Enable = true;

        var b2BPopUpContents = _mapper.Map<List<B2BPopUpContent>>(input.B2BPopUpContents);

        if (b2BPopUp.B2BPopUpAgencyType == Contracts.Common.Tenant.Enums.B2BPopUpAgencyType.Appointed)
        {
            var b2BPopUpAgencies = input.AgencyIds.Select(x => new B2BPopUpAgency
            {
                AgencyId = x,
                B2BPopUpId = b2BPopUp.Id,
            });
            await _dbContext.B2BPopUpAgencies.AddRangeAsync(b2BPopUpAgencies);
        }
        b2BPopUpContents.ForEach(x =>
        {
            if (!string.IsNullOrEmpty(x.Path))
                x.Enable = true;
            x.B2BPopUpId = b2BPopUp.Id;
        });

        await _dbContext.B2BPopUps.AddAsync(b2BPopUp);
        await _dbContext.B2BPopUpContents.AddRangeAsync(b2BPopUpContents);
        await _dbContext.SaveChangesAsync();

        return b2BPopUp.Id;
    }

    public async Task<bool> Update(UpdateB2BPopUpInput input)
    {
        var b2BPopUp = (await _dbContext.B2BPopUps.FindAsync(input.Id)) ?? throw new BusinessException("商家弹窗不存在");
        b2BPopUp = _mapper.Map(input, b2BPopUp);

        if (b2BPopUp.B2BPopUpAgencyType == Contracts.Common.Tenant.Enums.B2BPopUpAgencyType.Appointed)
        {
            var b2BPopUpAgencies = await _dbContext.B2BPopUpAgencies
                .Where(x => x.B2BPopUpId == input.Id)
                .ToListAsync();
            if (b2BPopUpAgencies.Any())
                _dbContext.B2BPopUpAgencies.RemoveRange(b2BPopUpAgencies);

            var newB2BPopUpAgencies = input.AgencyIds.Select(x => new B2BPopUpAgency
            {
                AgencyId = x,
                B2BPopUpId = b2BPopUp.Id,
            });
            await _dbContext.B2BPopUpAgencies.AddRangeAsync(newB2BPopUpAgencies);
        }
        if (input.B2BPopUpContents.Any())
        {
            var removeB2BPopUpContents = await _dbContext.B2BPopUpContents.Where(x => x.B2BPopUpId == input.Id).ToListAsync();
            _dbContext.B2BPopUpContents.RemoveRange(removeB2BPopUpContents);

            var addB2BPopUpContents = _mapper.Map<List<B2BPopUpContent>>(input.B2BPopUpContents);
            addB2BPopUpContents.ForEach(x => 
            {
                if (!string.IsNullOrEmpty(x.Path))
                    x.Enable = true;
                x.B2BPopUpId = input.Id; 
            });
            await _dbContext.B2BPopUpContents.AddRangeAsync(addB2BPopUpContents);
        }

        await _dbContext.SaveChangesAsync();

        return true;
    }

    public async Task<bool> Delete(DeleteB2BPopUpInput input)
    {
        var b2BPopUp = await _dbContext.B2BPopUps.FindAsync(input.Id);
        b2BPopUp.Enable = false;
        b2BPopUp.UpdateTime = DateTime.Now;
        b2BPopUp.OperatorUserId = input.OperatorUserId;
        b2BPopUp.OperatorUserName = input.OperatorUserName;
        var b2BPopUpAgencies = await _dbContext.B2BPopUpAgencies.Where(x => x.B2BPopUpId == input.Id).ToListAsync();
        _dbContext.B2BPopUpAgencies.RemoveRange(b2BPopUpAgencies);
        await _dbContext.SaveChangesAsync();

        return true;
    }

    #region B2BPopUpLog
    public async Task<bool> AddB2BPopUpLog(AddB2BPopUpLogInput input)
    {
        var today = DateTime.Now.Date;

        var b2BPopUp = await _dbContext.B2BPopUps.AsNoTracking().FirstOrDefaultAsync(x => x.Id == input.B2BPopUpId);
        if (b2BPopUp is null)
            return true;

        var exists = await _dbContext.B2BPopUpLogs.AsNoTracking()
            .WhereIF(b2BPopUp.B2BPopUpShowType == B2BPopUpShowType.DayOnce, x => x.RecordDate != null && x.RecordDate == today)
            .Where(x => x.B2BPopUpId == input.B2BPopUpId &&
                x.AgencyId == input.AgencyId &&
                x.UserId == input.UserId)
            .AnyAsync();
        if (exists)
            return true;

        var addB2BPopUpLogI = new B2BPopUpLog
        {
            AgencyId = input.AgencyId,
            B2BPopUpId = input.B2BPopUpId,
            UserId = input.UserId,
            RecordDate = b2BPopUp.B2BPopUpShowType == B2BPopUpShowType.DayOnce ? today : null,
        };

        await _dbContext.B2BPopUpLogs.AddAsync(addB2BPopUpLogI);
        await _dbContext.SaveChangesAsync();

        return true;
    }
    #endregion

}
