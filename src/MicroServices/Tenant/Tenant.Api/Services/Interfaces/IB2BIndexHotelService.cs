using Contracts.Common.Tenant.DTOs.B2BIndexHotel;
using Contracts.Common.Tenant.DTOs.B2BIndexPage;

namespace Tenant.Api.Services.Interfaces;

public interface IB2BIndexHotelService
{
    /// <summary>
    /// 获取B2B热门酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetB2BHotHotelOutput>> GetB2BHotHotel(GetB2BHotHotelInput input);

    /// <summary>
    /// 通过第三方更新热门酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> UpdateB2BHotHotel(UpdateB2BHotHotelInput input);

    /// <summary>
    /// 冗余B2B热门酒店到首页酒店数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> SyncB2BIndexHotel(SyncB2BIndexHotelInput input);
}
