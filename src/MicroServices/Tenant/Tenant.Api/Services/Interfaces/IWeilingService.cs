using Contracts.Common.Tenant.DTOs.Weiling;

namespace Tenant.Api.Services.Interfaces;

public interface IWeilingService
{
    Task<GetOutput> Get();
    Task Set(SetInput input);
    Task Switch(SwitchInput input);

    /// <summary>
    /// 创建卫瓴企业
    /// </summary>
    /// <param name="message"></param>
    /// <returns>企业id</returns>
    Task CreateCompany(CreateCompanyMessage message);

    /// <summary>
    /// 编辑卫瓴企业
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    Task UpdateCompany(UpdateCompanyMessage message);
}
