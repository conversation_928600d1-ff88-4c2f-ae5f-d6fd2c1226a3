using Contracts.Common.Tenant.DTOs;
using Contracts.Common.Tenant.DTOs.AgencyCredit;
using EfCoreExtensions.Abstract;

namespace Tenant.Api.Services.Interfaces;

public interface IAgencyCreditService
{
    /// <summary>
    /// 新增分销商额度
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Create(CreateAgencyCreditInput input, OperationUserDto operationUser);

    /// <summary>
    /// 查询分销商额度列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchAgencyCreditOutput>> Search(SearchAgencyCreditInput input);

    /// <summary>
    /// 更新分销商额度信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Update(UpdateAgencyCreditInput input);

    /// <summary>
    /// 查询分销商额度流水列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchAgencyCreditRecordOutput>> SearchRecords(SearchAgencyCreditRecordInput input);

    /// <summary>
    /// 导出分销商额度流水
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SearchAgencyCreditRecordOutput>> ExportRecords(QueryAgencyCreditRecordInput input);

    /// <summary>
    /// 获取分销商额度信息
    /// </summary>
    /// <param name="agencyIds"></param>
    /// <returns></returns>
    Task<List<AgencyCreditOutput>> Get(params long[] agencyIds);

    /// <summary>
    /// 设置额度预警信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetEarlyWarning(SetEarlyWarningInput input);

    /// <summary>
    /// 批量更新分销商额度状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetEnable(SetEnableInput input);
}
