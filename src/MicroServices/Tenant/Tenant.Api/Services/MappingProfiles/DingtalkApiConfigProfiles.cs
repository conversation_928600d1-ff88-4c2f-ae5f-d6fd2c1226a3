using AutoMapper;
using Contracts.Common.Tenant.DTOs.DingtalkApiConfig;

namespace Tenant.Api.Services.MappingProfiles;

public class DingtalkApiConfigProfiles : Profile
{
    public DingtalkApiConfigProfiles()
    {
        CreateMap<DingtalkApiConfigSaveInput, DingtalkApiConfig>();
        CreateMap<DingtalkTriggerEventDto, DingtalkTriggerEvent>().ReverseMap();
        CreateMap<DingtalkTriggerEventFieldMapDto, DingtalkTriggerEventFieldMap>().ReverseMap();
        CreateMap<DingtalkApiConfig, DingtalkApiConfigOutput>();
    }
}
