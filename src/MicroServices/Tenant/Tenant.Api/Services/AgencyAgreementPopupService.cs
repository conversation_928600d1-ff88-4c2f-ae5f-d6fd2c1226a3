using Common.GlobalException;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.AgencyAgreement;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.Enums;
using DotNetCore.CAP;
using EfCoreExtensions.UOW;
using Extensions;
using Hangfire;
using HangfireClient.Jobs.Tenant;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Services;

public class AgencyAgreementPopupService : IAgencyAgreementPopupService
{
    private readonly CustomDbContext _dbContext;
    private readonly ICapPublisher _capPublisher;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IBackgroundJobClient _backgroundJobClient;

    public AgencyAgreementPopupService(
        CustomDbContext dbContext,
        ICapPublisher capPublisher,
        IHttpContextAccessor httpContextAccessor,
        IBackgroundJobClient backgroundJobClient)
    {
        _dbContext = dbContext;
        _capPublisher = capPublisher;
        _httpContextAccessor = httpContextAccessor;
        _backgroundJobClient = backgroundJobClient;

    }

    /// <summary>
    /// 获取补充协议是否可弹窗与弹窗内容
    /// </summary>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [UnitOfWork]
    public async Task<AgreementPopupDto> GetPopupAsync(AgencyBasePopupInput input)
    {

        var result = new AgreementPopupDto();

        try
        {
            result = await CheckPopupAsync(input);
        }
        catch { }

        return result;

    }

    private async Task<AgreementPopupDto> CheckPopupAsync(AgencyBasePopupInput input)
    {
        var result = new AgreementPopupDto();

        //是否选择 没有OTA 转售协议
        var isCheckOta = await _dbContext.AgencyAgreementRecords.AnyAsync(x => x.AgencyId == input.AgencyId && x.CheckOta == false);

        if (isCheckOta)
        {
            return result;
        }

        var agreement = await _dbContext.AgencyAgreements.AsNoTracking().FirstOrDefaultAsync(
              x => x.AgreementType == AgencyAgreementType.SideAgreement &&
                   x.Enabled &&
                   x.Language == input.Language &&
                   x.TenantId == input.TenantId);

        if (agreement is null)
        {
            return result;
        }

        result.Popup = true;
        result.Content = agreement.Content;

        return result;
    }

}
