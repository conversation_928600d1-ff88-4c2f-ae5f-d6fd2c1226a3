using Tenant.Api.Services.OpenPlatform.Contracts;
using Tenant.Api.Services.OpenPlatform.Contracts.CitApiAccount;
using Tenant.Api.Services.OpenPlatform.Contracts.CitAppAccount;

namespace Tenant.Api.Services.OpenPlatform;

/// <summary>
/// 开放平台业务服务接口
/// </summary>
public interface IOpenPlatformService
{
    /// <summary>
    /// 创建app账户
    /// </summary>
    /// <param name="request"></param>
    /// <param name="appKey"></param>
    /// <param name="appSecret"></param>
    /// <returns></returns>
    Task<ApiBaseResponse<CitAppAccountCreateResponse>> CreateAppAccount(CitAppAccountCreateRequest request,
        string appKey, string appSecret);


    /// <summary>
    /// 查询app账户列表
    /// </summary>
    /// <param name="request"></param>
    /// <param name="appKey"></param>
    /// <param name="appSecret"></param>
    /// <returns></returns>
    Task<ApiBaseResponse<CitAppAccountQueryListResponse>> QueryAppAccount(CitAppAccountQueryListRequest request,
        string appKey, string appSecret);
    
    /// <summary>
    /// 创建Api账户
    /// </summary>
    /// <param name="request"></param>
    /// <param name="appKey"></param>
    /// <param name="appSecret"></param>
    /// <returns></returns>
    Task<ApiBaseResponse<CitApiAccountCreateResponse>> CreateApiAccount(CitApiAccountCreateRequest request,
        string appKey, string appSecret);

    /// <summary>
    /// 更新Api账户信息
    /// </summary>
    /// <param name="request"></param>
    /// <param name="appKey"></param>
    /// <param name="appSecret"></param>
    /// <returns></returns>
    Task<ApiBaseResponse<CitApiAccountUpdateResponse>> UpdateApiAccount(CitApiAccountUpdateRequest request,
        string appKey, string appSecret);
    
    /// <summary>
    /// 查询Api账户信息
    /// </summary>
    /// <param name="request"></param>
    /// <param name="appKey"></param>
    /// <param name="appSecret"></param>
    /// <returns></returns>
    Task<ApiBaseResponse<CitApiAccountGetInfoResponse>> GetApiAccountInfo(CitApiAccountGetInfoRequest request,
        string appKey, string appSecret);
}