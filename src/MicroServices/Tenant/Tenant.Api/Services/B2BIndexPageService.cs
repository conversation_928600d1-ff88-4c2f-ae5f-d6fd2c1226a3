using Common.ServicesHttpClient;
using Contracts.Common.Tenant.DTOs.B2BIndexPage;
using Contracts.Common.Tenant.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Tenant.Api.Services.Interfaces;
using Tenant.Api.Extensions;
using Contracts.Common.Hotel.DTOs.Hotel;
using EfCoreExtensions.Abstract;
using Contracts.Common.Hotel.DTOs.HotelTag;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using AutoMapper;
using Tenant.Api.Model;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EfCoreExtensions.Extensions;
using SkyApm.Common;
using NetTopologySuite.Geometries;
using System.Linq.Expressions;
using System.Drawing;
using Contracts.Common.User.DTOs;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.DTOs;
using EfCoreExtensions.UOW;
using Contracts.Common.Tenant.DTOs.B2BIndexHotel;
using DotNetCore.CAP;
using Contracts.Common.Tenant.Messages;

namespace Tenant.Api.Services;

public class B2BIndexPageService : IB2BIndexPageService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IMapper _mapper;
    private readonly IB2BIndexHotelService _b2BHotHotelService;
    private readonly ICapPublisher _capPublisher;

    public B2BIndexPageService(CustomDbContext dbContext,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddress,
        IMapper mapper,
        IB2BIndexHotelService b2BHotHotelService,
        ICapPublisher capPublisher)
    {
        _dbContext = dbContext;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddress;
        _mapper = mapper;
        _b2BHotHotelService = b2BHotHotelService;
        _capPublisher = capPublisher;
    }

    public async Task<List<B2BIndexPageComponentDto>> GetComponents(GetComponentInput input)
    {
        var b2bIndexPageComponentQuery = _dbContext.B2BIndexPageComponents.AsNoTracking();
        var b2bIndexPageComponentItemQuery = _dbContext.B2BIndexPageComponentItems.AsNoTracking();
        if (input.IsIngoreTenant)
        {
            b2bIndexPageComponentQuery = b2bIndexPageComponentQuery.IgnoreQueryFilters();
            b2bIndexPageComponentItemQuery = b2bIndexPageComponentItemQuery.IgnoreQueryFilters();
        }
        //TODO: 语言多了会有问题，需要彻底改造
        var components = await b2bIndexPageComponentQuery
            .WhereIF(input.B2BIndexPageType.HasValue, x => x.B2BIndexPageType == input.B2BIndexPageType)
            .OrderBy(x => x.Index)
            .ToListAsync();
        if (components.Any() is false)
            return new List<B2BIndexPageComponentDto>();
        var componentIds = components.Select(x => x.Id);
        var componentItems = await b2bIndexPageComponentItemQuery
            .Where(x => componentIds.Contains(x.B2BIndexPageComponentId))
            //.WhereIF(!string.IsNullOrWhiteSpace(language), x => x.Language == language)
            .ToListAsync();
        var result = components
            .GroupJoin(componentItems, c => c.Id, ci => ci.B2BIndexPageComponentId, (c, ci) => new B2BIndexPageComponentDto
            {
                Id = c.Id,
                Index = c.Index,
                B2BComponentType = c.B2BComponentType,
                B2BIndexPageType = c.B2BIndexPageType,
                TenantId = c.TenantId,
                ComponentItems = ci.Any(x => x.Language == input.Language)
                    ? ci.Where(x => x.Language == input.Language)
                        .Select(x => new B2BIndexPageComponentItemDto
                        {
                            Id = x.Id,
                            Language = x.Language,
                            Content = JsonConvert.DeserializeObject<B2BIndexPageComponentItemContent>(x.Content)
                        })
                    : ci
                        .Select(x => new B2BIndexPageComponentItemDto
                        {
                            Id = x.Id,
                            Language = x.Language,
                            Content = JsonConvert.DeserializeObject<B2BIndexPageComponentItemContent>(x.Content)
                        })
            })
            .ToList();

        #region 过滤B2BLinkChooseType
        if (!input.B2BLinkChooseTypes.Any())
            return result;
        var removeB2BIndexPageComponentDtoIds = new List<long>();
        foreach (var b2bIndexPageComponentItemDto in result)
        {
            if (!b2bIndexPageComponentItemDto.B2BComponentType.Equals(B2BComponentType.ProductList))
                continue;
            var removeB2BIndexPageComponentItemDtoIds = new List<long>();
            var items = b2bIndexPageComponentItemDto.ComponentItems.ToList();
            foreach (var item in items)
            {
                if (item.Content.ProductList is null
                    || input.B2BLinkChooseTypes.Contains(item.Content.ProductList.LinkChooseType))
                    continue;
                removeB2BIndexPageComponentItemDtoIds.Add(item.Id!.Value);
            }
            items.RemoveAll(x => removeB2BIndexPageComponentItemDtoIds.Contains(x.Id!.Value));
            b2bIndexPageComponentItemDto.ComponentItems = items;
            if (!items.Any())
                removeB2BIndexPageComponentDtoIds.Add(b2bIndexPageComponentItemDto.Id);
        }
        result.RemoveAll(x => removeB2BIndexPageComponentDtoIds.Contains(x.Id));
        #endregion

        return result;
    }

    public async Task SetComponentIndex(SetComponentIndexInput input)
    {
        var components = await _dbContext.B2BIndexPageComponents
            .ToListAsync();
        foreach (var indexItem in input.Items)
        {
            var component = components.FirstOrDefault(x => x.Id == indexItem.ComponentId);
            if (component is null) continue;
            component.Index = indexItem.Index;
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<UpdateComponentOutput> UpdateComponent(UpdateComponentInput input)
    {
        var result = new UpdateComponentOutput() { Id = input.Id };
        var component = await _dbContext.B2BIndexPageComponents
            .FirstOrDefaultAsync(x => x.Id == input.Id);
        if (component is null)
            return null;
        var componentItems = await _dbContext.B2BIndexPageComponentItems
            .Where(x => x.B2BIndexPageComponentId == component.Id)
            .ToListAsync();
        foreach (var item in input.ComponentItems)
        {
            var componentItem = componentItems.FirstOrDefault(x => x.Language == item.Language);
            if (componentItem is not null)
            {
                componentItem.Content = JsonConvert.SerializeObject(item.Content);
            }
            else
            {
                componentItem = new B2BIndexPageComponentItem
                {
                    B2BIndexPageComponentId = component.Id,
                    Language = item.Language,
                    Content = JsonConvert.SerializeObject(item.Content)
                };
                _dbContext.Add(componentItem);
            }
            result.Details.Add(new AddOrUpdateComponentBaseOutput
            {
                Language = componentItem.Language,
                B2BIndexPageComponentItemId = componentItem.Id,
            });
        }
        await _dbContext.SaveChangesAsync();

        return result;
    }

    public async Task DeleteComponent(DeleteComponentInput input)
    {
        var component = await _dbContext.B2BIndexPageComponents
                .Where(x => x.Id == input.Id)
                .FirstAsync();
        var items = await _dbContext.B2BIndexPageComponentItems
            .Where(x => x.B2BIndexPageComponentId == component.Id)
            .ToListAsync();
        var itemIds = items.Select(x=> x.Id).ToList();
        var b2bIndexPageHotels = await _dbContext.B2BIndexPageHotels
            .Where(x => itemIds.Contains(x.B2BIndexPageComponentItemId))
            .ToListAsync();
        var b2bIndexPageTags = await _dbContext.B2BIndexPageTags
            .Where(x => itemIds.Contains(x.B2BIndexPageComponentItemId))
            .ToListAsync();

        _dbContext.Remove(component);
        _dbContext.RemoveRange(items);
        if(b2bIndexPageHotels != null && b2bIndexPageHotels.Any() is true)
            _dbContext.RemoveRange(b2bIndexPageHotels);
        if (b2bIndexPageTags != null && b2bIndexPageTags.Any() is true)
            _dbContext.RemoveRange(b2bIndexPageTags);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<AddComponentOutput> AddComponent(AddComponentInput input)
    {
        B2BIndexPageComponent component = new()
        {
            B2BComponentType = input.B2BComponentType,
            B2BIndexPageType = input.B2BIndexPageType,
        };
        var items = new List<B2BIndexPageComponentItem>();
        foreach (var componentItem in input.ComponentItems)
        {
            var b2bTopicPageComponentItem = new B2BIndexPageComponentItem
            {
                Id = EfCoreExtensions.Snowflake.IdWorker.IdGenerator.CreateId(),
                B2BIndexPageComponentId = component.Id,
                Language = componentItem.Language,
                Content = JsonConvert.SerializeObject(componentItem.Content)
            };
            items.Add(b2bTopicPageComponentItem);
        }

        await _dbContext.AddAsync(component);
        await _dbContext.AddRangeAsync(items);
        await _dbContext.SaveChangesAsync();
        var result = new AddComponentOutput
        {
            Details = items.Select(x => new AddOrUpdateComponentBaseOutput
            {
                B2BIndexPageComponentItemId = x.Id,
                Language = x.Language,
            }).ToList(),
            Id = component.Id,
            B2BIndexPageType = component.B2BIndexPageType,
        };
        return result;
    }

    [UnitOfWork]
    public async Task SyncComponentItemData(long tenantId,List<SyncComponentItemDataInput> input)
    {
        var b2bIndexPageComponentItemIds = input.Select(x => x.B2BIndexPageComponentItemId).ToList();
        var hotelsInput = input.Where(x => x.Hotels.Any()).SelectMany(x => x.Hotels).ToList();
        var hotels = _mapper.Map<List<B2BIndexPageHotel>>(hotelsInput);
        var hotelTagsInput = input.Where(x => x.HotelTags.Any()).SelectMany(x => x.HotelTags).ToList();
        var hotelTags = _mapper.Map<List<B2BIndexPageTag>>(hotelTagsInput);

        var delHotels = _dbContext.B2BIndexPageHotels.Where(x => b2bIndexPageComponentItemIds.Contains(x.B2BIndexPageComponentItemId));
        var delHotelTags = _dbContext.B2BIndexPageTags.Where(x => b2bIndexPageComponentItemIds.Contains(x.B2BIndexPageComponentItemId));
        _dbContext.RemoveRange(delHotels);
        _dbContext.RemoveRange(delHotelTags);
        if(hotels.Any())
            _dbContext.AddRange(hotels);
        if(hotelTags.Any())
            _dbContext.AddRange(hotelTags);

        await _dbContext.SaveChangesAsync();
    }

    public async Task<PagingModel<SearchComponentItemDataOutput>> SearchComponentItemData(SearchComponentItemDataInput input)
    {
        var query = QueryComponentItemData(input);

        var result = await query
            .OrderBy(x => x.Sort)
            .PagingAsync(input.PageIndex, input.PageSize);
        return _mapper.Map<PagingModel<SearchComponentItemDataOutput>>(result);
    }

    public async Task<PagingModel<SearchComponentItemDataOutput>> SearchRecommendComponentItemData(SearchComponentItemDataInput input)
    {
        var query = QueryComponentItemData(input)
            .Select(x=> x.HotelId);

        var result = await _dbContext.B2BIndexPageHotels.AsNoTracking()
            .Where(x => x.B2BIndexPageComponentItemId == input.B2BIndexPageComponentItemId)
            .WhereIF(!string.IsNullOrEmpty(input.Region), x => input.Region == x.Region)
            .WhereIF(!string.IsNullOrEmpty(input.SubRegion), x => input.SubRegion == x.SubRegion)
            .Where(x=> !query.Contains(x.HotelId))
            .OrderByDescending(x => x.OnTop)
            .ThenByDescending(x => x.WeightValue)
            .ThenBy(x => x.Sort)
            .PagingAsync(input.PageIndex, input.PageSize);

        return _mapper.Map<PagingModel<SearchComponentItemDataOutput>>(result);
    }

    public IQueryable<B2BIndexPageHotel> QueryComponentItemData(SearchComponentItemDataInput input)
    {
        var query = _dbContext.B2BIndexPageHotels.AsNoTracking()
            .Where(x => x.B2BIndexPageComponentItemId == input.B2BIndexPageComponentItemId)
            .WhereIF(input.B2BPageHotelType.HasValue, x => x.B2BPageHotelType == input.B2BPageHotelType)
            .WhereIF(input.CityCode.HasValue, x => x.CityCode == input.CityCode)
            .WhereIF(input.StarLevels.Any(), x => input.StarLevels.Contains(x.StarLevel))
            .WhereIF(!string.IsNullOrEmpty(input.Region), x => input.Region == x.Region)
            .WhereIF(!string.IsNullOrEmpty(input.SubRegion), x => input.SubRegion == x.SubRegion);

        if (input.TagIds.Any())
        {
            query = query.Join(_dbContext.B2BIndexPageTags.AsNoTracking(),
                h => new { h.HotelId, h.B2BIndexPageComponentItemId }, t => new { t.HotelId, t.B2BIndexPageComponentItemId }, (h, t) => new { h, t })
                .Where(x => input.TagIds.Contains(x.t.TagId))
                .Select(x => x.h)
                .Distinct();
        }

        if (input.Points != null)
        {
            var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);
            var inputPoint = geometryFactory.CreatePoint(new Coordinate(input.Points.Longitude, input.Points.Latitude));
            query = query.Where(x => x.Location != null && x.Location.Distance(inputPoint) <= input.Points.Distance);
        }

        return query;
    }

    public async Task<List<SearchB2BTopicPageDataOutput>> SearchB2BTopicPageComponentItemData(SearchB2BTopicPageDataInput input)
    {
        var result = new List<SearchB2BTopicPageDataOutput>();
        var b2bIndexPageComponentItem = await _dbContext.B2BIndexPageComponents.AsNoTracking()
            .Join(_dbContext.B2BIndexPageComponentItems.AsNoTracking(), x => x.Id, y => y.B2BIndexPageComponentId, (x, y) => new { x, y })
            .Where(x => x.x.B2BComponentType == B2BComponentType.B2BTopicPageList && x.y.Id == input.B2BIndexPageComponentItemId)
            .Select(x => new
            {
                Content = JsonConvert.DeserializeObject<B2BIndexPageComponentItemContent>(x.y.Content),
                Language = x.y.Language,
            })
            .FirstOrDefaultAsync();
        if(b2bIndexPageComponentItem is null || 
            b2bIndexPageComponentItem.Content.B2BTopicPageList is null ||
            b2bIndexPageComponentItem.Content.B2BTopicPageList.Items is null || 
            b2bIndexPageComponentItem.Content.B2BTopicPageList.Items.Any() is false)
            return result;

        var b2bTopicPageIds = b2bIndexPageComponentItem.Content.B2BTopicPageList.Items
            .Select(x => x.B2BTopicPageId)
            .ToList();
        var b2bTopicPages = await _dbContext.B2BTopicPages.AsNoTracking()
            .Where(x=> b2bTopicPageIds.Contains(x.Id))
            .ToListAsync();
        var b2bTopPageExtends = await _dbContext.B2BTopicPageExtends.AsNoTracking()
            .Where(x=> b2bTopicPageIds.Contains(x.B2BTopicPageId) && x.Language == b2bIndexPageComponentItem.Language)
            .ToListAsync();
        var b2bTopicPageTagItems = _dbContext.B2BTopicPageTagItems.AsNoTracking()
            .Join(_dbContext.B2BTopicPageTags.AsNoTracking(), x => x.B2BTopicPageTagId, y => y.Id, (x, y) => new { x, y })
            .Where(x=> b2bTopicPageIds.Contains(x.x.B2BTopicPageId) && x.x.Language == b2bIndexPageComponentItem.Language)
            .Select(x => new {
                B2BTopicPageTagId = x.x.B2BTopicPageTagId,
                B2BTopicPageId = x.x.B2BTopicPageId,
                Name = x.y.Name,
                Language = x.y.Language,
            });

        foreach (var item in b2bIndexPageComponentItem.Content.B2BTopicPageList.Items)
        {
            var b2bTopicPage = b2bTopicPages.FirstOrDefault(o => o.Id == item.B2BTopicPageId);
            if (b2bTopicPage is null)
                continue;
            var tags = b2bTopicPageTagItems
                .Where(o => o.B2BTopicPageId == item.B2BTopicPageId && o.Language == b2bIndexPageComponentItem.Language);

            var topPageExtend = b2bTopPageExtends
                .Where(o => o.B2BTopicPageId == item.B2BTopicPageId && o.Language == b2bIndexPageComponentItem.Language)
                .FirstOrDefault();

            var data = _mapper.Map<SearchB2BTopicPageDataOutput>(b2bTopicPage);
            if (topPageExtend != null)
                _mapper.Map(topPageExtend, data);
            else
                _mapper.Map(new B2BTopicPageExtend(), data);
            data.Tags = tags?.Select(x => x.Name).ToList();
            result.Add(data);
        }
        return result;
    }

    public async Task<bool> SyncB2BIndexPageHotel()
    {
        var hotels = await _dbContext.B2BIndexPageHotels.IgnoreQueryFilters()
            .Where(x=> x.B2BHotHotelType == B2BIndexPageHotHotelType.ApiHotel)
            .ToListAsync();
        var hotelIds = hotels.Select(x => x.HotelId).Distinct();

        using StringContent content = new(JsonConvert.SerializeObject(hotelIds), Encoding.UTF8, "application/json");
        var apiHotels = await _httpClientFactory.InternalPostAsync<List<GetApiHotelDetailOutput>>(_servicesAddress.Value.Hotel_GetApiHotelDetail(),
            httpContent: content);
        if (apiHotels.Any() is false)
            return true;

        var resourceHotelIds = apiHotels.Select(x => x.ResourceHotelId).Distinct();
        using StringContent photos = new(JsonConvert.SerializeObject(resourceHotelIds), Encoding.UTF8, "application/json");
        var apiHotelPhotos = await _httpClientFactory.InternalPostAsync<List<GetHotelFirstPhotoOutput>>(_servicesAddress.Value.Resource_GetHotelFirstPhoto(),
            httpContent: photos);

        hotels.ForEach(x =>
        {
            var apiHotel = apiHotels.FirstOrDefault(o => o.Id == x.HotelId);
            if (apiHotel != null)
            {
                x.ResourceHotelId = apiHotel.ResourceHotelId;
                x.ZHName = apiHotel.ZHName;
                x.ENName = apiHotel.ENName;
                x.CityCode = apiHotel.CityCode;
                x.CountryCode = apiHotel.CountryCode;
                x.CoordinateType = apiHotel.CoordinateType;
                if (apiHotel.Longitude.HasValue && apiHotel.Latitude.HasValue)
                    x.SetLocation(apiHotel.Longitude.Value, apiHotel.Latitude.Value);
                x.StarLevel = apiHotel.StarLevel;
                x.WeightValue = apiHotel.WeightValue;
                x.OnTop = apiHotel.OnTop;
                var apiHotelPhoto = apiHotelPhotos?.FirstOrDefault(r => r.HotelId == x.ResourceHotelId);
                if (apiHotelPhoto is not null)
                    x.Path = apiHotelPhoto.Path;
            }
        });
        await _dbContext.SaveChangesAsync();

        return true;
    }

    public async Task<List<long>> SearchRegionAllTags(SearchHotelTagsInput input)
    {
        var query = _dbContext.B2BIndexPageTags.AsNoTracking()
            .Where(x => x.B2BIndexPageComponentItemId == input.B2BIndexPageComponentItemId);

        if (!string.IsNullOrEmpty(input.Region) || !string.IsNullOrEmpty(input.SubRegion))
        {
            query = query.Join(_dbContext.B2BIndexPageHotels.AsNoTracking(),
                h =>  h.HotelId , t =>  t.HotelId , (h, t) => new { h, t })
                .WhereIF(!string.IsNullOrEmpty(input.Region), x => input.Region == x.t.Region)
                .WhereIF(!string.IsNullOrEmpty(input.SubRegion), x => input.SubRegion == x.t.SubRegion)
                .Select(x=> x.h);
        }
        return await query.Select(x=> x.TagId).Distinct().ToListAsync();
    }

    public async Task<List<CheckSpecializedHotelOutput>> CheckSpecializedHotel(CheckSpecializedHotelInput input)
    {
        var result = input.SpecializedHotelIds.Select(x => new CheckSpecializedHotelOutput { 
            Enable = false,
            SpecializedHotelId = x,
        }).ToList();
        var b2bIndexPageComponentItems = await _dbContext.B2BIndexPageComponentItems.AsNoTracking()
            .Where(x=> x.Content.Contains("popularIds"))
            .ToListAsync();

        foreach (var item in b2bIndexPageComponentItems)
        {
            var content = JsonConvert.DeserializeObject<B2BIndexPageComponentItemContent>(item.Content);
            if (content.ProductList != null &&
                content.ProductList.PopularIds != null &&
                content.ProductList.PopularIds.Any())
            {
                foreach (var popularId in content.ProductList.PopularIds)
                {
                    var match = result.FirstOrDefault(x => x.SpecializedHotelId == popularId);
                    if(match != null)
                        match.Enable = true;
                }
            }
        }

        var b2bTopicPageComponentItems = await _dbContext.B2BTopicPageComponentItems.AsNoTracking()
            .Where(x => x.Content.Contains("popularIds"))
            .ToListAsync();

        foreach (var item in b2bIndexPageComponentItems)
        {
            var content = JsonConvert.DeserializeObject<Contracts.Common.Tenant.DTOs.B2BTopicPage.B2BTopicPageComponentItemContent>(item.Content);
            if (content.ProductList != null &&
                content.ProductList.PopularIds != null &&
                content.ProductList.PopularIds.Any())
            {
                foreach (var popularId in content.ProductList.PopularIds)
                {
                    var match = result.FirstOrDefault(x => x.SpecializedHotelId == popularId);
                    if (match != null)
                        match.Enable = true;
                }
            }
        }

        return result;
    }
}
