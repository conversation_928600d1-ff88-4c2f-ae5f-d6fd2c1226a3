using AutoMapper;
using Contracts.Common.Tenant.DTOs.AgencyBankCard;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Services;

public class AgencyBankCardService : IAgencyBankCardService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;
    public AgencyBankCardService(
        IMapper mapper,
        CustomDbContext dbContext)
    {
        _mapper = mapper;
        _dbContext = dbContext;
    }
    
    /// <summary>
    /// 添加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task Add(BankCardInfo input)
    {
        var entity = _mapper.Map<AgencyBankCard>(input);
        await _dbContext.AddAsync(entity);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task Edit(EditInput input)
    {
        var bankCard = await _dbContext.AgencyBankCards.FirstOrDefaultAsync(x => x.Id == input.Id);
        if (bankCard is null) return;
        _mapper.Map(input, bankCard);
        bankCard.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagingModel<AgencyBankCard>> Search(SearchInput input)
    {
        var query = _dbContext.AgencyBankCards.AsNoTracking()
            .Where(x => x.AgencyId == input.AgencyId)
            .WhereIF(!string.IsNullOrEmpty(input.AccountName), x => x.AccountName.Contains(input.AccountName))
            .WhereIF(!string.IsNullOrEmpty(input.CustomName), x => x.CustomName.Contains(input.CustomName));
        
        var result = await query
            .OrderByDescending(x => x.UpdateTime)
            .PagingAsync(input.PageIndex, input.PageSize);
        return result;
    }

    /// <summary>
    /// 查询分销商银行卡账户列表
    /// </summary>
    /// <param name="agencyId"></param>
    /// <returns></returns>
    public async Task<IEnumerable<AgencyBankCard>> QueryByAgencyId(long agencyId)
    {
        var query = _dbContext.AgencyBankCards.AsNoTracking()
            .Where(x => x.AgencyId == agencyId);

        var result = await query.ToListAsync();
        return result;
    }
}