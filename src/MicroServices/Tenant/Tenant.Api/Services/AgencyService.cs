using AutoMapper;
using Common.GlobalException;
using Common.Jwt;
using Common.ServicesHttpClient;
using Contracts.Common.Marketing.DTOs.PromotionTrace;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Contracts.Common.Permission.DTOs.Role;
using Contracts.Common.Permission.Enums;
using Contracts.Common.Reflection;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.Agency.AgencyRegister;
using Contracts.Common.Tenant.DTOs.AgencyLevelDetail;
using Contracts.Common.Tenant.DTOs.AgencyRecencyOrder;
using Contracts.Common.Tenant.DTOs.AgencyTag;
using Contracts.Common.Tenant.DTOs.CitOpenTenantConfig;
using Contracts.Common.Tenant.DTOs.Focussend;
using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.Tenant.DTOs.Weiling;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.Tenant.Enums.OpenPlatform;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.DTOs.TenantUser;
using Contracts.Common.User.Enums;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Linq.Expressions;
using Tenant.Api.Extensions;
using Tenant.Api.Services.Interfaces;
using Tenant.Api.Services.OpenPlatform;

namespace Tenant.Api.Services;

public class AgencyService : IAgencyService
{
    #region  Fields

    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly CustomDbContext _dbContext;
    private readonly ITenantIdentify _tenantIdentify;
    private readonly IHttpContextAccessor _httpContextAccessor;

    private readonly ICitOpenTenantConfigService _citOpenTenantConfigService;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;



    //开放平台分销渠道api类型
    private readonly AgencyApiType[] _openChannelApiTypes = {
        AgencyApiType.FeiZhuTicket,
        AgencyApiType.CtripPlayLine,
        AgencyApiType.MeituanTravelAbroad,
        AgencyApiType.TikTokGroupPurchaseReservation
    };

    public AgencyService(CustomDbContext dbContext,
        IMapper mapper,
        ICapPublisher capPublisher,
        IOptions<ServicesAddress> servicesAddress,
        ITenantIdentify tenantIdentify,
        IHttpClientFactory httpClientFactory,
        IHttpContextAccessor httpContextAccessor,
        ICitOpenTenantConfigService citOpenTenantConfigService,
        IOpenPlatformBaseService openPlatformBaseService)
    {
        _mapper = mapper;
        _capPublisher = capPublisher;
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
        _dbContext = dbContext;
        _tenantIdentify = tenantIdentify;
        _httpContextAccessor = httpContextAccessor;
        _citOpenTenantConfigService = citOpenTenantConfigService;
        _openPlatformBaseService = openPlatformBaseService;
    }

    #endregion

    /// <summary>
    /// 添加分销商
    /// </summary>
    /// <param name="input"></param>
    [UnitOfWork]
    public async Task<bool> Add(AddAgencyInput input, long tenantId)
    {
        var agency = _mapper.Map<Agency>(input);
        agency.CurrencyCode = agency.CurrencyCode?.Trim();
        //默认单结
        agency.SettlementPeriod = SupplierSettlementPeriod.Single;
        agency.ChannelType = ChannelType.SelfChannel;
        agency.ContractStatus = TenantContractStatus.Signing;
        agency.RegisterSource = AgencyRegisterSourceType.VebkCreated;
        agency.CertificationStatus = AgencyCertificationStatus.Pengding;
        agency.ShortName = AgencyExtendField.AgencyShortName;
        //分销商校验
        await AgencyCheck(id: null, licenceNo: input.LicenceNo, fullName: input.FullName);
        // 跟随等级时给默认价格分组
        if (agency.IsCustom == false)
        {
            var config = await _dbContext.AgencyLevelConfigs.FirstOrDefaultAsync(x => x.TenantId == tenantId);
            if (config != null && config.Enable)
            {
                var setting = await _dbContext.AgencyLevelConfigSettings.FirstOrDefaultAsync(x => x.AgencyLevelConfigId == config.Id && x.Level == 1);
                if (setting != null)
                    agency.PriceGroupId = setting.PriceGroupId;
            }
        }

        //Api分销商配置
        if (agency.AgencyType is AgencyType.Api)
        {
            if (await _dbContext.AgencyApiSettings
                .AnyAsync(x => x.AgencyApiType.Equals(input.AgencyApiType)))
                throw new BusinessException(ErrorTypes.Tenant.AgencyApiTypeExist);

            //开放平台渠道-具体配置信息都是配置到CIT OPEN侧
            #region 配置到CIT OPEN侧

            if (_openChannelApiTypes.Contains(input.AgencyApiType.Value))
            {
                await _citOpenTenantConfigService.CreateApiAccount(new CreateCitOpenApiAccountInput
                {
                    TenantId = tenantId,
                    ApiSideType = CitOpenApiSideType.Channel,
                    ApiNameType =
                        _openPlatformBaseService.MapChannelApiTypeToCitOpenApiNameType(input.AgencyApiType.Value),
                    AgencyApiSetting = new AgencyApiSettingDto
                    {
                        FeiZhuShopName = input.FeiZhuShopName,
                        Code = input.Code,
                        Account = input.Account,
                        Password = input.Password,
                        AccountId = input.AccountId,
                        AesKey = input.AesKey,
                        AesIv = input.AesLv
                    }
                });
            }

            #endregion


            var agencyApiSetting = _mapper.Map<AgencyApiSetting>(input);
            agencyApiSetting.AgencyId = agency.Id;
            await _dbContext.AgencyApiSettings.AddAsync(agencyApiSetting);
        }
        agency.SetTenantId(tenantId);
        await _dbContext.AddAsync(agency);

        //新增默认分销商额度
        await AddAgencyDefaultCredit(agency.Id, agency.CurrencyCode, agency.FinancialStaffNumber, input.NoticeEmail);

        #region 更新分销商标签
        await UpdateAgencyTagItem(agency.Id, input.AgencyTagIds);
        #endregion

        if (input.AgencyType == AgencyType.Offline)
        {
            //分销商签约发放赠送注册优惠券
            await _capPublisher.PublishAsync(CapTopics.Marketing.CouponActivityByRegistration,
                new CouponActivityByRegistrationMessage
                {
                    TenantId = agency.TenantId,
                    AgencyId = agency.Id
                });
        }
        var log = CreateLog(input);
        log.OperationType = OperationType.Add;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        log.Content = "新增分销商：" + PropertyInfoHelper.PropertyInfoMsg(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input
        });

        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        //是否创建分销商账户
        if (input.IsCreateAgencyUser)
        {
            await _capPublisher.PublishAsync(CapTopics.User.AddAgencyUser, new AddAgencyUserMessage
            {
                AgencyId = agency.Id,
                RealName = input.Contact,
                UserName = input.Email,
                Email = input.Email,
                PhoneNumber = input.ContactNumber,
                Password = "Abcd1234",
                CountryDialCode = input.ContactCountryDialCode ?? "86",
                AgencyAcls = input.AgencyAcls
            });
        }

        await _capPublisher.PublishAsync(CapTopics.Tenant.SyncAddAgencyInitAgencyLevelDetail, new SyncAgencyDetail
        {
            AgencyId = agency.Id,
        });

        await PushWeilingCreateCompany(agency, tenantId);

        //推送Focussend创建或更新联系人
        await PushFocussendAsync(agency, input.IndustryInvolved);

        return true;
    }

    [UnitOfWork]
    public async Task<long> Register(AgencyRegisterInput input)
    {
        //分销商校验
        await AgencyCheck(null, null, input.FullName);

        var tenantId = _tenantIdentify.GetTenantId();

        var tenantInfo = await _dbContext.Tenants.Where(x => x.Id == tenantId).FirstAsync();
        var defaultPriceGroup = await _dbContext.AgencyPriceGroups.FirstOrDefaultAsync(x => x.Default);

        var agency = new Agency
        {
            FullName = input.FullName,
            Address = input.Address,
            ShortName = AgencyExtendField.AgencyShortName,
            Contact = input.Contact,
            ContactNumber = input.ContactNumber,
            ChannelType = ChannelType.SelfChannel,
            SettlementPeriod = SupplierSettlementPeriod.Single,
            PriceGroupId = defaultPriceGroup.Id,
            CurrencyCode = tenantInfo.CurrencyCode,
            ContractStatus = TenantContractStatus.WaitAduit,
            AgencyType = AgencyType.Offline,
            RegisterSource = input.RegisterSource,
            CertificationStatus = AgencyCertificationStatus.Pengding,
            CountryCode = input.CountryCode,
            CountryName = input.CountryName,
            ProvinceCode = input.ProvinceCode,
            ProvinceName = input.ProvinceName,
            CityCode = input.CityCode,
            CityName = input.CityName,
            Email = input.Email,
            PromotionTraceId = input.PromotionTraceId,
            IndustryInvolved = input.IndustryInvolved?.ToString(),
            ContactCountryDialCode = input.ContactCountryDialCode,
            SalespersonId = input.SalespersonId,
            RegisterParentSource = input.RegisterParentSource
        };

        //如果注册行业为搬单，默认非会员
        if (input.IndustryInvolved.HasValue && input.IndustryInvolved.Value == AgencyIndustryType.MovingOrder)
            agency.IsVip = false;

        await _dbContext.Agencies.AddAsync(agency);

        await _dbContext.SaveChangesAsync();

        //新增默认分销商额度
        await AddAgencyDefaultCredit(agency.Id, agency.CurrencyCode, agency.FinancialStaffNumber);

        await _capPublisher.PublishAsync(CapTopics.Tenant.SyncAddAgencyInitAgencyLevelDetail, new SyncAgencyDetail
        {
            AgencyId = agency.Id,
        });

        return agency.Id;
    }

    [UnitOfWork]
    public async Task Sign(AgencySignInput input)
    {
        var agency = await _dbContext.Agencies.FindAsync(input.Id);
        if (agency is null) return;

        if (agency.ContractStatus == TenantContractStatus.WaitAduit)
            throw new BusinessException(ErrorTypes.Tenant.AgencyNotAudited);

        var dep = await _dbContext.TenantDepartments.FirstOrDefaultAsync(x => x.Id == input.TenantDepartmentId);

        if (dep?.Enable != true)
            throw new BusinessException(ErrorTypes.Tenant.TenantDepartmentIsDisable);

        var oldData = new AgencySignInput();
        _mapper.Map(agency, oldData);

        _mapper.Map(input, agency);

        input.PriceGroupName = (await _dbContext.AgencyPriceGroups.FirstOrDefaultAsync(x => x.Id == input.PriceGroupId))?.Name;
        input.SignSubjectName = (await _dbContext.SignSubjects.FirstOrDefaultAsync(x => x.Id == input.SignSubjectId))?.Name;
        AgencyIndustryType? agencyIndustry = null;
        var msg = "签约分销商";
        if (Enum.TryParse(agency.IndustryInvolved, out AgencyIndustryType outRes))
        {
            agencyIndustry = outRes;
        }
        if (agency.ContractStatus == TenantContractStatus.Signing)
        {
            msg = "签约分销商";
            agency.SettlementPeriod = SupplierSettlementPeriod.Single;
            agency.ContractStatus = TenantContractStatus.Signed;
            agency.ChannelType = ChannelType.SelfChannel;
            agency.CertificationStatus = AgencyCertificationStatus.Pengding;

            var agencyCredit = await _dbContext.AgencyCredits.FirstOrDefaultAsync(x => x.AgencyId == agency.Id);
            agencyCredit.CurrencyCode = agency.CurrencyCode;

            //根据会员和所属行业增加成长值
            await IncreaseAgencyGrowUpValueAsync(agency, agencyIndustry);
        }
        else // 修改
        {
            msg = "修改签约分销商";
            agency.CurrencyCode = oldData.CurrencyCode;//已签约的，修改签约内容时不能再改币种
        }
        agency.UpdateTime = DateTime.Now;
        //更新分销商标签
        await UpdateAgencyTagItem(input.Id, input.AgencyTagIds);

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        log.Content = $"{msg}：" + PropertyInfoHelper.PropertyInfoMsg(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData,
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        await PushWeilingUpdateCompany(agency);

        //推送Focussend创建或更新联系人
        await PushFocussendAsync(agency, agencyIndustry);

    }

    private async Task IncreaseAgencyGrowUpValueAsync(Agency agency, AgencyIndustryType? agencyIndustryType)
    {
        if (agency.IsVip && agencyIndustryType.HasValue)
        {
            if (agencyIndustryType is not AgencyIndustryType.MovingOrder)
            {
                //如果是会员并且行业 不是搬单，默认增加100成长值
                await _capPublisher.PublishAsync(CapTopics.Tenant.IncreaseAgencyGrowUpValue, new IncreaseAgencyGrowUpValueInput()
                {
                    AgencyId = agency.Id,
                    Value = 100
                });
            }

        }
    }

    /// <summary>
    /// 通过分销商id列表获取分销商详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    public async Task<List<GetAgenciesByIdsOutput>> GetbyIds(List<long> ids)
    {
        var result = new List<GetAgenciesByIdsOutput>();

        var agencies = await _dbContext.Agencies.IgnoreQueryFilters()
            .AsNoTracking()
            .GroupJoin(_dbContext.AgencyApiSettings.AsNoTracking(), agency => agency.Id, setting => setting.AgencyId,
                (agency, settings) => new { agency, settings }).SelectMany(x => x.settings.DefaultIfEmpty(),
                (s, setting) => new { s.agency, setting }).Select(x => new { x.agency, x.setting })
            .GroupJoin(_dbContext.AgencyCredits.AsNoTracking(), x => x.agency.Id, agencyCredit => agencyCredit.AgencyId,
                (x, agencyCredit) => new { x.agency, x.setting, agencyCredit }).SelectMany(x => x.agencyCredit.DefaultIfEmpty(),
                (x, agencyCredit) => new { x.agency, x.setting, agencyCredit })
            .GroupJoin(_dbContext.SignSubjects.AsNoTracking(), x => x.agency.SignSubjectId, signSubject => signSubject.Id,
                (x, signSubject) => new { x.agency, x.setting, x.agencyCredit, signSubject }).SelectMany(x => x.signSubject.DefaultIfEmpty(), (x, signSubject) => new { x.agency, x.setting, x.agencyCredit, signSubject })
            .Where(x => ids.Contains(x.agency.Id))
            .ToListAsync();

        if (!agencies.Any())
            return result;

        var salespersonIds = agencies.Where(x => x.agency.SalespersonId.HasValue)
                .Select(x => x.agency.SalespersonId!.Value)
                .Distinct()
                .ToList();
        var customerOperationIds = agencies.Where(x => x.agency.CustomerOperationId.HasValue)
                .Select(x => x.agency.CustomerOperationId!.Value)
                .Distinct()
                .ToArray();
        salespersonIds.AddRange(customerOperationIds);
        Contracts.Common.User.DTOs.TenantUser.SearchTenantUsersInput userSearchInput = new()
        {
            Ids = salespersonIds.ToArray()
        };
        using StringContent content = new(JsonConvert.SerializeObject(userSearchInput), Encoding.UTF8, "application/json");
        var tenantUsers = await _httpClientFactory.InternalPostAsync<IEnumerable<UserSearchOuput>>(_servicesAddress.Value.User_SearchTenantUsers(),
            httpContent: content);

        var levelDetails = await _dbContext.AgencyLevelDetails.IgnoreQueryFilters().AsNoTracking()
             .Join(_dbContext.AgencyLevelConfigs.AsNoTracking(), detail => detail.TenantId, config => config.TenantId,
             (detail, config) => new { detail.AgencyId, agencyLevel = detail.Level, detail.CycleGrowUpValue, detail.TenantId, config })
             .Join(_dbContext.AgencyLevelConfigSettings.AsNoTracking(), x => x.config.Id, setting => setting.AgencyLevelConfigId, (x, setting) => new
             {
                 x.config.Enable,
                 x.AgencyId,
                 x.agencyLevel,
                 x.CycleGrowUpValue,
                 setting.Level,
                 setting.Name
             })
             .Where(x => x.Enable && ids.Contains(x.AgencyId) && x.agencyLevel == x.Level)
             .ToListAsync();

        var departmentIds = agencies.Where(x => x.agency.TenantDepartmentId > 0).Select(x => x.agency.TenantDepartmentId).Distinct().ToList();
        var departments = await _dbContext.TenantDepartments.Where(x => departmentIds.Contains(x.Id)).ToListAsync();
        var searchAgencyTagDtos = await _dbContext.AgencyTagItems
           .Join(_dbContext.AgencyTags, ati => ati.AgencyTagId, a => a.Id, (ati, a) => new { ati, a })
           .Where(x => ids.Contains(x.ati.AgencyId))
           .Select(x => new
           {
               x.ati.AgencyId,
               x.a.Id,
               x.a.Name
           })
           .ToListAsync();

        agencies.ForEach(x =>
        {
            var getAgenciesByIdsOutput = _mapper.Map<GetAgenciesByIdsOutput>(x.agency);
            getAgenciesByIdsOutput.SalespersonName = tenantUsers.FirstOrDefault(s => s.Id == x.agency.SalespersonId)?.Name;
            getAgenciesByIdsOutput.CustomerOperationName = tenantUsers.FirstOrDefault(s => s.Id == x.agency.CustomerOperationId)?.Name;
            if (x.agencyCredit is not null)
            {
                getAgenciesByIdsOutput.AgencyCreditStatus = x.agencyCredit.Status;
                getAgenciesByIdsOutput.DimensionType = x.agencyCredit.DimensionType;
                getAgenciesByIdsOutput.NoticeEmail = x.agencyCredit.NoticeEmail;
            }
            getAgenciesByIdsOutput.SignSubjectName = x.signSubject?.Name;
            _mapper.Map(x.setting, getAgenciesByIdsOutput);

            var detail = levelDetails.FirstOrDefault(d => d.AgencyId == x.agency.Id);
            getAgenciesByIdsOutput.Level = detail?.Level;
            getAgenciesByIdsOutput.LevelName = detail?.Name;
            getAgenciesByIdsOutput.CycleGrowUpValue = detail?.CycleGrowUpValue ?? 0;

            var department = departments.FirstOrDefault(d => d.Id == x.agency.TenantDepartmentId);
            getAgenciesByIdsOutput.TenantDepartmentName = department?.Name;

            //getAgenciesByIdsOutput.IndustryInvolved = string.IsNullOrEmpty(x.agency.IndustryInvolved)
            //    ? null 
            //    : Enum.Parse<AgencyIndustryType>(x.agency.IndustryInvolved);
            var addAgencyTagDtos = searchAgencyTagDtos
                .Where(m => m.AgencyId.Equals(x.agency.Id))
                .Select(x => new SearchAgencyTagDto
                {
                    Id = x.Id,
                    Name = x.Name,
                }).ToList();
            getAgenciesByIdsOutput.AgencyTagDtos = addAgencyTagDtos;

            result.Add(getAgenciesByIdsOutput);
        });



        return result;
    }

    /// <summary>
    /// 修改分销商
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [UnitOfWork]
    public async Task<bool> Edit(EditAgencyInput input)
    {
        //分销商校验
        await AgencyCheck(id: input.Id, licenceNo: null, fullName: input.FullName);

        var agency = await _dbContext.Agencies.FirstOrDefaultAsync(x => x.Id == input.Id)
            ?? throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);

        if (input.UpdateOperationType == UpdateAgencyOperationType.Aduit)
        {
            if (agency.ContractStatus != TenantContractStatus.WaitAduit)
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        var oldData = new EditAgencyInput();
        _mapper.Map(agency, oldData);

        if (input.ContactNumber == null)
            input.ContactNumber = agency.ContactNumber;
        if (input.ContactCountryDialCode == null)
            input.ContactCountryDialCode = agency.ContactCountryDialCode;
        if (input.Email == null)
            input.Email = agency.Email;
        _mapper.Map(input, agency);

        if (agency.AgencyType is AgencyType.Api)
        {
            //渠道Api分销商配置到CitOpen侧,配置成功才会修改分销商
            #region 配置到CitOpen侧

            if (_openChannelApiTypes.Contains(input.AgencyApiType))
            {
                await _citOpenTenantConfigService.UpdateApiAccount(new UpdateCitOpenApiAccountInput()
                {
                    TenantId = agency.TenantId,
                    ApiSideType = CitOpenApiSideType.Channel,
                    ApiNameType = _openPlatformBaseService.MapChannelApiTypeToCitOpenApiNameType(input.AgencyApiType),
                    AgencyApiSetting = new AgencyApiSettingDto
                    {
                        FeiZhuShopName = input.FeiZhuShopName,
                        Code = input.Code,
                        Account = input.Account,
                        Password = input.Password,
                        AccountId = input.AccountId,
                        AesKey = input.AesKey,
                        AesIv = input.AesLv
                    }
                });
            }

            #endregion

            var agencyApiSetting = await _dbContext.AgencyApiSettings.FirstOrDefaultAsync(x => x.AgencyId == input.Id);
            //更新飞猪SessionKey到期时间
            if (agencyApiSetting.AgencyApiType == AgencyApiType.FeiZhuTicket)
            {
                if (input.Code != agencyApiSetting.Code)
                    agencyApiSetting.CodeTime = DateTime.Now;
            }
            _mapper.Map(input, agencyApiSetting);
            agencyApiSetting.UpdateTime = DateTime.Now;
        }

        agency.UpdateTime = DateTime.Now;

        if (input.UpdateOperationType == UpdateAgencyOperationType.Aduit)
        {
            agency.CurrencyCode = input.CurrencyCode;
            agency.ContractStatus = TenantContractStatus.Signing;

            var agencyCredit = await _dbContext.AgencyCredits.FirstOrDefaultAsync(x => x.AgencyId == agency.Id);
            agencyCredit.CurrencyCode = agency.CurrencyCode;

            if (input.AgencyType == AgencyType.Offline)
            {
                //分销商签约发放赠送注册优惠券
                await _capPublisher.PublishAsync(CapTopics.Marketing.CouponActivityByRegistration,
                    new CouponActivityByRegistrationMessage
                    {
                        TenantId = agency.TenantId,
                        AgencyId = agency.Id
                    });
            }
        }
        if (string.IsNullOrEmpty(agency.WeilingCompanyId))
        {
            await PushWeilingCreateCompany(agency, agency.TenantId);
        }
        else
        {
            await PushWeilingUpdateCompany(agency);
        }

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        int? optype = null;
        var operationTypeMsg = "编辑分销商";
        switch (input.UpdateOperationType)
        {
            case UpdateAgencyOperationType.Aduit:
                optype = (int)UpdateAgencyOperationType.Aduit;
                operationTypeMsg = "审核分销商";
                break;
            case UpdateAgencyOperationType.Update:
                optype = (int)UpdateAgencyOperationType.Update;
                break;
        }
        log.Content = $"{operationTypeMsg}：" + PropertyInfoHelper.PropertyInfoMsg(input, optype);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData,
        });

        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);

        var rows = await _dbContext.SaveChangesAsync();

        await PushFocussendAsync(agency, input.IndustryInvolved);

        if (input.UpdateOperationType == UpdateAgencyOperationType.Aduit && input.IsCreateAgencyUser)
        {
            await _capPublisher.PublishAsync(CapTopics.User.AddAgencyUser, new AddAgencyUserMessage
            {
                AgencyId = agency.Id,
                RealName = input.FullName,
                UserName = input.Email,
                Email = input.Email,
                PhoneNumber = input.ContactNumber,
                Password = "Abcd1234",
                CountryDialCode = input.ContactCountryDialCode ?? "86",
                AgencyAcls = input.AgencyAcls ?? new()
            });

        }
        return rows > 0;
    }

    /// <summary>
    /// 卫翎修改分销商-不需要入参校验直接覆盖修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> WeilingEdit(WeilingUpdateAgencyInput input)
    {
        //分销商校验
        await AgencyCheck(id: input.Id, licenceNo: input.LicenceNo, fullName: input.FullName);

        var agency = await _dbContext.Agencies.FirstOrDefaultAsync(x => x.Id == input.Id)
            ?? throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);

        var oldData = new WeilingUpdateAgencyInput();
        _mapper.Map(agency, oldData);

        _mapper.Map(input, agency);

        await UpdateAgencyTagItem(input.Id, input.AgencyTagIds);
        if (!string.IsNullOrWhiteSpace(input.NoticeEmail))
            oldData.NoticeEmail = await UpdateAgencyCreditNoticeEmail(input.Id, input.NoticeEmail);

        agency.UpdateTime = DateTime.Now;

        if (input.PushWeiling)
            await PushWeilingUpdateCompany(agency);

        await AddOperationLogAsync(agency, input, oldData, input.UpdateOperationType);

        var rows = await _dbContext.SaveChangesAsync();

        await PushFocussendAsync(agency, input.IndustryInvolved);

        return rows > 0;
    }

    private async Task AddOperationLogAsync(Agency agency, UpdateAgencyInput input,
        UpdateAgencyInput oldData,
        UpdateAgencyOperationType? operationType)
    {
        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        int? optype = null;
        var operationTypeMsg = "编辑分销商";
        switch (operationType)
        {
            case UpdateAgencyOperationType.Aduit:
                optype = (int)UpdateAgencyOperationType.Aduit;
                operationTypeMsg = "审核分销商";
                break;
            case UpdateAgencyOperationType.CRMUpdate:
                operationTypeMsg = "CRM更新分销商";
                break;
            case UpdateAgencyOperationType.Update:
                optype = (int)UpdateAgencyOperationType.Update;
                break;
        }
        log.Content = $"{operationTypeMsg}：" + PropertyInfoHelper.PropertyInfoMsg(input, optype);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData,
        });
        if (input.PushWeiling is false)
        {
            log.OperationUserId = default(long);
            log.OperationUserName = "卫瓴crm";
        }

        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
    }

    /// <summary>
    /// 推送至Focussend
    /// </summary>
    /// <param name="agency"></param>
    /// <param name="agencyIndustry"></param>
    /// <returns></returns>
    private async Task PushFocussendAsync(Agency agency, AgencyIndustryType? agencyIndustry)
    {
        //推送Focussend创建或更新联系人
        var createUpdateContactMessage = new CreateUpdateContactByAgencyMessage
        {
            TenantId = agency.TenantId,
            AgencyId = agency.Id,
            AgencyFullName = agency.FullName,
            Contact = agency.Contact,
            ContactNumber = agency.ContactNumber,
            CountryName = agency.CountryName,
            CertificationStatus = agency.CertificationStatus,
            AgencyRecencyStatus = agency.AgencyRecencyStatus,
            ProvinceName = agency.ProvinceName,
            CityName = agency.CityName,
            IndustryInvolved = agencyIndustry,
            Email = agency.Email,
            CurrencyCode = agency.CurrencyCode,
        };
        await _capPublisher.PublishAsync(CapTopics.Tenant.FocussendCreateUpdateContactByAgency, createUpdateContactMessage);
    }

    /// <summary>
    /// 切换分销商状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    public async Task<bool> SwitchEnable(SwitchAgencyEnableInput input)
    {
        var agency = await _dbContext.Agencies.FirstOrDefaultAsync(x => x.Id == input.Id)
            ?? throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);
        agency.Enable = input.Enable;
        agency.UpdateTime = DateTime.Now;
        var oldData = new SwitchAgencyEnableInput()
        {
            Id = input.Id,
            Enable = !input.Enable,
        };
        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        log.Content = input.Enable ? "恢复已停用分销商" : "停用分销商";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData,
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        var rows = await _dbContext.SaveChangesAsync();
        return rows > 0;
    }

    /// <summary>
    /// 查询分销商分页数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<PagingModel<SearchAgenciesOutput, SearchAgencyTabStatusStat>> Search(SearchAgenciesInput input)
    {
        var query = _dbContext.Agencies.IgnoreQueryFilters()
            .WhereIF(input.AgencyIds.Any(), x => input.AgencyIds.Contains(x.Id))
            .WhereIF(!string.IsNullOrEmpty(input.KeyWord), x => x.FullName.Contains(input.KeyWord!))
            .WhereIF(input.PriceGroupId is > 0, x => x.PriceGroupId == input.PriceGroupId!.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.CurrencyCode), x => x.CurrencyCode.Equals(input.CurrencyCode))
            .WhereIF(input.Enable.HasValue, x => x.Enable == input.Enable!.Value)
            .WhereIF(input.AgencyType.HasValue, x => x.AgencyType == input.AgencyType!.Value)
            .WhereIF(input.SalespersonId is not null, x => x.SalespersonId.Equals(input.SalespersonId))
            .WhereIF(input.CertificationStatus is not null, x => x.CertificationStatus.Equals(input.CertificationStatus))
            .WhereIF(input.IsNoSalePerson, x => x.SalespersonId == null)
            .WhereIF(input.AgencyRecencyStatus.HasValue, x => x.AgencyRecencyStatus == input.AgencyRecencyStatus)
            .WhereIF(input.BusinessVolume is not null, x => x.BusinessVolume.Equals(input.BusinessVolume))
            .WhereIF(input.CustomerOperationId is not null, x => input.CustomerOperationId.Equals(x.CustomerOperationId))
            .WhereIF(input.RegisterSource.HasValue, x => x.RegisterSource == input.RegisterSource)
            .WhereIF(input.IsAduit is false, x => x.ContractStatus != TenantContractStatus.WaitAduit)
            .WhereIF(input.IsAduit is true, x => x.ContractStatus == TenantContractStatus.WaitAduit)
            .WhereIF(input.RegisterSubSources.HasValue, x => input.RegisterSubSources!.Value == x.RegisterSubSource);
        //.WhereIF(input.PromotionTraceId.HasValue, x => input.PromotionTraceId!.Value == x.PromotionTraceId);

        Expression<Func<Agency, bool>> expressionMember = p => false;
        if (input.IndustryInvolved.Any())
        {
            foreach (var item in input.IndustryInvolved)
            {
                var industryInvolvedItem = Convert.ToInt32(item).ToString();
                //合并Expression表达式
                expressionMember = expressionMember.Or(x => x.IndustryInvolved.Contains(industryInvolvedItem));
            }
            query = query.Where(expressionMember);
        }
        if (input.IsHasBusinessLicense is not null)
        {
            if (input.IsHasBusinessLicense!.Value)
                query = query.Where(x => x.BusinessLicensePath != null && !x.BusinessLicensePath.Equals(string.Empty));
            else
                query = query.Where(x => x.BusinessLicensePath == null || x.BusinessLicensePath.Equals(string.Empty));
        }

        if (input.IncludeHuiDeng)
            query = query.Where(x => x.TenantId == input.TenantId || x.Id == 1).OrderBy(x => x.Id);
        else
            query = query.Where(x => x.TenantId == input.TenantId).OrderByDescending(x => x.UpdateTime);

        //分组搜索
        if (input.AgencyTagIds.Any())
        {
            input.AgencyTagIds.ForEach(agencyTagId =>
            {
                query = query.Join(
                _dbContext.AgencyTagItems.IgnoreQueryFilters()
                .Where(ati => agencyTagId.Equals(ati.AgencyTagId))
                .Select(ati => new { ati.AgencyId })
                .Distinct(),
                a => a.Id, ati => ati.AgencyId, (a, ati) => a);
            });
        }

        if (input.AgencyCreditStatus is not null)
        {
            query = query.Join(_dbContext.AgencyCredits.AsNoTracking()
                .Where(ac => ac.Status.Equals(input.AgencyCreditStatus))
                .Select(ac => new { ac.AgencyId }),
                a => a.Id, ac => ac.AgencyId, (a, ac) => a);
        }

        //区域搜索
        if (input.RegionConfigId.HasValue)
        {
            var isOther = await _dbContext.RegionConfigs.Where(x => x.Id == input.RegionConfigId && x.Type == RegionConfigType.Other).AnyAsync();
            if (!isOther)
            {
                query = from o in query
                        join r in _dbContext.RegionConfigScopes.AsNoTracking() on o.CountryCode equals r.CountryCode
                        where r.RegionConfigId == input.RegionConfigId && (o.ProvinceCode == r.ProvinceCode || r.ProvinceCode == null)
                        select o;
            }
            else //搜索其他区域
            {
                var regionConfigIds = _dbContext.RegionConfigs.Where(x => x.Type != RegionConfigType.Other).Select(x => x.Id).ToList();
                var excludeAgency = from o in _dbContext.Agencies.AsNoTracking()
                                    join r in _dbContext.RegionConfigScopes.AsNoTracking() on o.CountryCode equals r.CountryCode
                                    where regionConfigIds.Contains(r.RegionConfigId) && (o.ProvinceCode == r.ProvinceCode || r.ProvinceCode == null)
                                    select o.Id;
                query = query.Where(x => !excludeAgency.Contains(x.Id));
            }

        }

        //最近购买时间搜索
        if (input.IsRecencyTime && (input.RecencyBeginTime.HasValue || input.RecencyEndTime.HasValue))
        {
            query = query.Join(_dbContext.AgencyRecencyOrder.AsNoTracking()
                .WhereIF(input.RecencyBeginTime.HasValue, x => x.RecencyTime >= input.RecencyBeginTime!.Value)
                .WhereIF(input.RecencyEndTime.HasValue, x => x.RecencyTime <= input.RecencyEndTime!.Value)
                .Select(ac => new { ac.AgencyId }),
                a => a.Id, ac => ac.AgencyId, (a, ac) => a);
        }

        //分销商bd归属区域过滤
        if (input.TenantUserId.HasValue)
        {
            query = await GetAgenciesForBDRegionAsync(query, input.TenantUserId.Value!);
        }

        // 分销商延迟支付状态过滤
        if (input.DelayedStatus.HasValue)
        {
            query = query.GroupJoin(_dbContext.AgencyDelayedCredits.AsNoTracking(), ac => ac.Id, ad => ad.AgencyId, (ac, ad) => new { ac, ad })
                     .SelectMany(x => x.ad.DefaultIfEmpty(), (x, ad) => new { x.ac, ad })
                     .WhereIF(input.DelayedStatus == true, x => x.ad != null && x.ad.Status == input.DelayedStatus.Value)
                     .WhereIF(input.DelayedStatus == false, x => x.ad == null || x.ad.Status == input.DelayedStatus.Value)
                     .Select(x => x.ac);
        }
        var levelQuery = _dbContext.AgencyLevelDetails;


        var linqQuery = from ac in query
                        join l in levelQuery on ac.Id equals l.AgencyId into lv
                        from level in lv.DefaultIfEmpty()
                        join agencyCredit in _dbContext.AgencyCredits.AsNoTracking() on ac.Id equals agencyCredit.AgencyId into agencyCreditList
                        from agencyCredit in agencyCreditList.DefaultIfEmpty()
                        select new
                        {
                            ac,
                            level,
                            agencyCredit.NoticeEmail
                        };
        linqQuery = linqQuery.WhereIF(input.Level.HasValue, x => x.level.Level == input.Level);


        var countData = await linqQuery.GroupBy(x => 1).Select(x => new
        {
            WaitAuditCount = x.Where(s => s.ac.ContractStatus == TenantContractStatus.WaitAduit).Count(),
            WaitSignCount = x.Where(s => s.ac.ContractStatus == TenantContractStatus.Signing).Count(),
            SignedCount = x.Where(s => s.ac.ContractStatus == TenantContractStatus.Signed).Count(),

        }).FirstOrDefaultAsync();

        linqQuery = linqQuery.WhereIF(input.ContractStatus.HasValue, x => x.ac.ContractStatus.Equals(input.ContractStatus));
        var count = await linqQuery.CountAsync();

        var agenies = await linqQuery
            .OrderByDescending(x => x.ac.CreateTime)
            .Skip((input.PageIndex - 1) * input.PageSize)
            .Take(input.PageSize)
            .ToListAsync();

        var data = new List<SearchAgenciesOutput>();
        var salespersonIds = agenies.Where(x => x.ac.SalespersonId.HasValue)
                .Select(x => x.ac.SalespersonId!.Value)
                .Distinct()
                .ToList();
        var customerOperationIds = agenies.Where(x => x.ac.CustomerOperationId.HasValue)
                .Select(x => x.ac.CustomerOperationId!.Value)
                .Distinct()
                .ToArray();
        salespersonIds.AddRange(customerOperationIds);
        Contracts.Common.User.DTOs.TenantUser.SearchTenantUsersInput userSearchInput = new()
        {
            Ids = salespersonIds.ToArray()
        };
        using StringContent content = new(JsonConvert.SerializeObject(userSearchInput), Encoding.UTF8, "application/json");
        var tenantUsers = await _httpClientFactory.InternalPostAsync<IEnumerable<UserSearchOuput>>(_servicesAddress.Value.User_SearchTenantUsers(),
            httpContent: content);
        foreach (var item in agenies)
        {
            var searchAgenciesOutput = _mapper.Map<SearchAgenciesOutput>(item.ac);
            searchAgenciesOutput.NoticeEmail = item.NoticeEmail;
            searchAgenciesOutput.Level = item.level?.Level;
            searchAgenciesOutput.CycleGrowUpValue = item.level?.CycleGrowUpValue;
            searchAgenciesOutput.SalespersonName = tenantUsers.FirstOrDefault(s => s.Id == item.ac.SalespersonId)?.Name;
            searchAgenciesOutput.CustomerOperationName = tenantUsers.FirstOrDefault(s => s.Id == item.ac.CustomerOperationId)?.Name;
            data.Add(searchAgenciesOutput);
        }

        var result = new PagingModel<SearchAgenciesOutput, SearchAgencyTabStatusStat>
        {
            Total = count,
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Data = data,
            Supplement = new SearchAgencyTabStatusStat()
            {
                SignedCount = countData?.SignedCount ?? 0,
                WaitAuditCount = countData?.WaitAuditCount ?? 0,
                WaitSignCount = countData?.WaitSignCount ?? 0,
            }
        };

        //var result = _mapper.Map<PagingModel<SearchAgenciesOutput>>(agencies);

        #region 填充分销商标签
        var agencyIds = result.Data.Select(x => x.Id);
        var searchAgencyTagDtos = await _dbContext.AgencyTagItems
            .Join(_dbContext.AgencyTags, ati => ati.AgencyTagId, a => a.Id, (ati, a) => new { ati, a })
            .Where(x => agencyIds.Contains(x.ati.AgencyId))
            .Select(x => new
            {
                x.ati.AgencyId,
                x.a.Id,
                x.a.Name
            })
            .ToListAsync();

        #endregion

        #region 授信额度状态
        var agencyCreditsQuery = _dbContext.AgencyCredits.IgnoreQueryFilters().AsNoTracking()
            .Where(x => agencyIds.Contains(x.AgencyId));
        if (input.IncludeHuiDeng)
            agencyCreditsQuery = agencyCreditsQuery.Where(x => x.TenantId == input.TenantId || x.AgencyId == 1);
        else
            agencyCreditsQuery = agencyCreditsQuery.Where(x => x.TenantId == input.TenantId);

        var agencyCreditStatuss = await agencyCreditsQuery
            .Select(x => new
            {
                x.Status,
                x.AgencyId
            })
            .ToListAsync();
        #endregion

        #region 最近购买时间
        var agencyRecencyOrder = input.IsRecencyTime == false ? null : await _dbContext.AgencyRecencyOrder.AsNoTracking()
            .Where(x => agencyIds.Contains(x.AgencyId))
            .Select(x => new
            {
                x.AgencyId,
                x.RecencyTime
            })
            .ToListAsync();
        #endregion

        var tenatiId = _tenantIdentify.GetTenantId();
        var levelConfigSettings = await _dbContext.AgencyLevelConfigSettings.Where(x => x.TenantId == tenatiId).ToListAsync();
        var levelConfig = await _dbContext.AgencyLevelConfigs.Where(x => x.TenantId == tenatiId).FirstOrDefaultAsync();

        var agencyAgreementRecordList = await _dbContext.AgencyAgreementRecords
              .AsNoTracking()
              .Where(x => agencyIds.Contains(x.AgencyId))
              .Select(x => new { x.AgencyId, x.CheckOta, x.ConfirmContent, x.ConfirmTime })
              .ToListAsync();


        var agencyCertificationAuditList = await _dbContext.AgencyCertificationAudit
            .AsNoTracking()
            .Where(x => agencyIds.Contains(x.AgencyId))
            .Select(x => new { x.AgencyId, x.FinishTime })
            .ToListAsync();

        var promotionTraceList = await GetPromotionTraceListDto();

        string defaultPromotionTraceName = "常规";

        foreach (var item in result.Data)
        {
            var addAgencyTagDtos = searchAgencyTagDtos
                .Where(x => x.AgencyId.Equals(item.Id))
                .Select(x => new SearchAgencyTagDto
                {
                    Id = x.Id,
                    Name = x.Name,
                }).ToList();
            var agencyCreditStatus = agencyCreditStatuss.Where(x => x.AgencyId.Equals(item.Id))
                .Select(x => x.Status)
                .FirstOrDefault();
            item.AgencyTagDtos = addAgencyTagDtos;
            item.AgencyCreditStatus = agencyCreditStatus;
            item.RecencyTime = agencyRecencyOrder?.FirstOrDefault(x => x.AgencyId == item.Id)?.RecencyTime;

            var setting = levelConfigSettings.Where(x => x.Level == item.Level).FirstOrDefault();
            item.LevelName = setting?.Name;
            item.LevelEnbale = levelConfig?.Enable ?? false;

            item.CheckOtaContent = agencyAgreementRecordList?
                .OrderByDescending(x => x.ConfirmTime)?
                .FirstOrDefault(x => x.AgencyId == item.Id)?.ConfirmContent ?? string.Empty;

            item.FinishTime = agencyCertificationAuditList?
                .FirstOrDefault(x => x.AgencyId == item.Id)?.FinishTime;

            item.PromotionTraceName = item.PromotionTraceId.HasValue ? promotionTraceList?
                .FirstOrDefault(x => x.Id == item.PromotionTraceId)?.Title ?? string.Empty : defaultPromotionTraceName;

        }
        return result;
    }

    /// <summary>
    /// 根据租户用户 角色 数据权限与分销商BD 对分销商区域过滤
    /// </summary>
    /// <param name="query"></param>
    /// <param name="tenantUserId"></param>
    /// <returns></returns>
    private async Task<IQueryable<Agency>> GetAgenciesForBDRegionAsync(IQueryable<Agency> query, long tenantUserId)
    {
        var userId = tenantUserId;

        var tenantUser = await GetTenantUserRoleInfoDto(userId);
        if (tenantUser is null)
            return query;

        var tenantUserRole = await GetTenantRole(tenantUser.TenantRoleId);
        if (tenantUserRole is null)
            return query;

        var allAgencyDataAccess = tenantUserRole.DataPermissions?.Any(x => x == RoleDataPermissions.AllAgency) ?? false;

        //包含全部分销商数据权限，直接跳过 查询全部
        if (allAgencyDataAccess)
            return query;



        //其他
        var otherQueryQuery = _dbContext.RegionConfigUsers
           .Join(_dbContext.RegionConfigs,
              rcu => rcu.RegionConfigId,
              rc => rc.Id, (rcu, rc) => new
              {
                  rcu.UserId,
                  rc.Type,
                  rcu.RegionConfigId
              });

        var otherQuery = otherQueryQuery.Where(x => x.Type == RegionConfigType.Other && x.UserId == userId);

        //查询分销商区域范围
        var regionQuery = _dbContext.RegionConfigUsers
            .Join(_dbContext.RegionConfigs,
               rcu => rcu.RegionConfigId,
               rc => rc.Id, (rcu, rc) => new
               {
                   rcu,
                   rc
               })
            .Join(_dbContext.RegionConfigScopes,
               rr => rr.rc.Id,
               rcs => rcs.RegionConfigId, (rr, rcs) => new
               {
                   rcs.ProvinceCode,
                   rcs.RegionConfigId,
                   rr.rcu.UserId,
                   rr.rc.Type

               });


        //自定义
        var customQuery = regionQuery.Where(x => x.Type == RegionConfigType.Custom && x.UserId == userId);


        //其他区域过滤
        if (await otherQuery.AnyAsync())
        {
            var provinceCode = _dbContext.RegionConfigScopes.Select(x => x.ProvinceCode);

            query = query.Where(a => a.SalespersonId == userId || (!provinceCode.Contains(a.ProvinceCode) && (a.SalespersonId == null || a.SalespersonId == userId)) || customQuery.Where(x =>
                             (x.ProvinceCode == a.ProvinceCode && a.SalespersonId == userId) ||
                             (x.ProvinceCode == a.ProvinceCode && a.SalespersonId == null))
                          .Any());
        }
        else
        {
            //自定义区域包含用户
            if (await customQuery.AnyAsync())
            {
                query = query.Where(a => a.SalespersonId == userId ||
                          customQuery.Where(x =>
                            (x.ProvinceCode == a.ProvinceCode && a.SalespersonId == userId) ||
                            (x.ProvinceCode == a.ProvinceCode && a.SalespersonId == null))
                         .Any());
            }
            else // 查询bd为用户自己的
            {
                query = query.Where(a => a.SalespersonId == userId);
            }

        }

        return query;
    }


    /// <summary>
    /// 通过租户id查询可用分销商数据
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetAgenciesSelectOutput>> Selection(GetAgenciesSelectInput input)
    {
        var agencies = new List<GetAgenciesSelectOutput>();
        if (input.IncludeHuiDeng)
        {
            var huiDengAgency = await _dbContext.Agencies
                .IgnoreQueryFilters()
                .Select(x => new GetAgenciesSelectOutput
                {
                    Id = x.Id,
                    FullName = x.FullName,
                    CurrencyCode = x.CurrencyCode,
                    Enable = x.Enable
                })
                .FirstOrDefaultAsync(x => x.Id == 1);
            if (huiDengAgency != null) agencies.Add(huiDengAgency);
        }
        var tenantAgencies = await _dbContext.Agencies.AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => x.TenantId == input.TenantId)
            .WhereIF(input.Enable.HasValue, x => x.Enable == input.Enable)
            .WhereIF(input.AgencyIds?.Length is > 0, x => input.AgencyIds!.Contains(x.Id))
            .Select(x => new GetAgenciesSelectOutput
            {
                Id = x.Id,
                FullName = x.FullName,
                CurrencyCode = x.CurrencyCode,
                Enable = x.Enable
            })
            .ToListAsync();
        agencies.AddRange(tenantAgencies);

        return agencies;
    }

    public async Task<GetAgencyApiSettingOutput> GetAgencyApiSetting(GetAgencyApiSettingInput input)
    {
        var result = await _dbContext.AgencyApiSettings
            .Where(x => x.AgencyApiType == input.AgencyApiType)
            .Select(x => new GetAgencyApiSettingOutput
            {
                AgencyApiType = x.AgencyApiType,
                Account = x.Account,
                AgencyId = x.AgencyId,
                Code = x.Code,
                Password = x.Password,
                TenantId = x.TenantId
            })
            .FirstOrDefaultAsync();
        return result;
    }

    public Task<AgencyFirstOrDefaultDto> GetAgencyFirstOrDefault(long agencyId)
    {
        var result = _dbContext.Agencies
            .Where(x => x.Id == agencyId)
            .Join(_dbContext.AgencyLevelDetails, a => a.Id, ad => ad.AgencyId, (a, ad) => new AgencyFirstOrDefaultDto
            {
                Id = a.Id,
                FullName = a.FullName,
                Enable = a.Enable,
                SignSubjectId = a.SignSubjectId,
                TenantId = a.TenantId,
                IsVip = a.IsVip,
                Level = ad.Level
            })
            .FirstOrDefaultAsync();
        return result;
    }

    public async Task UpdateSalesperson(UpdateAgencySalespersonInput input)
    {
        var agency = await _dbContext.Agencies
            .Where(x => x.Id == input.AgencyId)
            .FirstOrDefaultAsync();
        if (agency is null)
            return;
        agency.SalespersonId = input.SalespersonId;
        agency.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    [UnitOfWork]
    public async Task<bool> SaveCertified(UpdateCertifiedInput input)
    {
        var agency = await _dbContext.Agencies
            .Where(x => x.Id == input.Id)
            .FirstOrDefaultAsync();
        if (agency is null)
            throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);

        if (agency.ContractStatus == TenantContractStatus.WaitAduit)
            throw new BusinessException(ErrorTypes.Tenant.AgencyNotAudited);

        if (agency.CertificationStatus == AgencyCertificationStatus.Certified)
            throw new BusinessException(ErrorTypes.Tenant.AgencyCertificationPassed);

        //存在审核中的
        if (agency.CertificationStatus == AgencyCertificationStatus.CertifyProcessing)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        var oldData = new UpdateCertifiedInput();
        _mapper.Map(agency, oldData);
        agency.BusinessLicensePath = input.BusinessLicensePath;
        agency.LicenceNo = input.LicenceNo;
        agency.CountryCode = input.CountryCode;
        agency.CountryName = input.CountryName;
        agency.ProvinceCode = input.ProvinceCode;
        agency.ProvinceName = input.ProvinceName;
        agency.CityCode = input.CityCode;
        agency.CityName = input.CityName;
        agency.UpdateTime = DateTime.Now;
        agency.CertificationStatus = AgencyCertificationStatus.CertifyProcessing;
        agency.FinancialCountryDialCode = input.FinancialCountryDialCode;
        agency.FinancialStaff = input.FinancialStaff;
        if (input.FinancialStaffNumber != null)
        {
            agency.FinancialStaffNumber = input.FinancialStaffNumber;
        }

        if (!string.IsNullOrWhiteSpace(input.NoticeEmail))
            oldData.NoticeEmail = await UpdateAgencyCreditNoticeEmail(input.Id, input.NoticeEmail);

        var audit = new AgencyCertificationAudit()
        {
            AgencyId = agency.Id,
            CreateTime = DateTime.Now,
            CreateUserId = input.CreateUserId,
            CreateName = input.CreateName,
            Status = AgencyCertificationAuditStatus.Pending,
            AgencyName = agency.FullName,
            Message = input.Message,
        };
        audit.SetTenantId(input.TenantId);

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        log.Content = $"认证申请";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData,
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        await _dbContext.AgencyCertificationAudit.AddAsync(audit);
        await _dbContext.SaveChangesAsync();
        //推送卫瓴
        await PushWeilingUpdateCompany(agency);
        return true;
    }

    public async Task<GetCertifiedDetailOutput> GetCertifiedDetail(long id)
    {
        var agency = await _dbContext.Agencies
           .Where(x => x.Id == id)
           .FirstOrDefaultAsync();

        if (agency is null)
            throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);
        var detail = _mapper.Map<GetCertifiedDetailOutput>(agency);

        var agencyAudit = await _dbContext.AgencyCertificationAudit
           .Where(x => x.AgencyId == id)
           .OrderByDescending(x => x.CreateTime)
           .FirstOrDefaultAsync();

        var agencyCredit = _dbContext.AgencyCredits.Where(x => x.AgencyId == agency.Id)
            .FirstOrDefault();
        detail.NoticeEmail = agencyCredit?.NoticeEmail;
        detail.AuditStatus = agencyAudit?.Status;
        detail.Remark = agencyAudit?.Remark;
        detail.Message = agencyAudit?.Message;
        detail.AuditId = agencyAudit?.Id;
        return detail;
    }

    public async Task<bool> SavePriceGroup(UpdateAgencyPriceGroupInput input)
    {
        await AgencyCheck(input.Id, null, input.FullName);

        var agency = await _dbContext.Agencies
         .Where(x => x.Id == input.Id)
         .FirstOrDefaultAsync();
        if (agency is null)
            throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);
        var oldData = new UpdateAgencyPriceGroupInput();
        _mapper.Map(agency, oldData);

        agency.PriceGroupId = input.PriceGroupId;
        agency.SalespersonId = input.SalespersonId;
        agency.FullName = input.FullName.Trim();
        agency.UpdateTime = DateTime.Now;
        agency.IsCustom = input.IsCustom;
        agency.IsKA = input.IsKA;
        agency.IsVip = input.IsVip;

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        log.Content = "BD/价格调整：" + PropertyInfoHelper.PropertyInfoMsg<UpdateAgencyPriceGroupInput>(input);
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData,
        });
        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        await _dbContext.SaveChangesAsync();
        return true;
    }

    #region private

    private Task<TenantUserRoleInfoDto> GetTenantUserRoleInfoDto(long tenantUserId)
    {
        return _httpClientFactory.InternalGetAsync<TenantUserRoleInfoDto>(_servicesAddress.Value.User_FindTenantUserRoles(tenantUserId));
    }

    private Task<GetTenantRoleOutput> GetTenantRole(long tenantRoleId)
    {
        return _httpClientFactory.InternalGetAsync<GetTenantRoleOutput>(_servicesAddress.Value.Permission_GetTenantRole(tenantRoleId));
    }

    /// <summary>
    /// 更新分销商标签
    /// </summary>
    /// <param name="agencyId"></param>
    /// <param name="AgencyTagIds"></param>
    /// <returns></returns>
    private async Task UpdateAgencyTagItem(long agencyId, IEnumerable<long> AgencyTagIds)
    {
        var deleteAgencyTagItems = await _dbContext.AgencyTagItems
            .Where(x => x.AgencyId.Equals(agencyId))
            .ToListAsync();

        if (deleteAgencyTagItems.Any())
            _dbContext.RemoveRange(deleteAgencyTagItems);
        var addAgencyTagItems = new List<AgencyTagItem>();
        foreach (var agencyTagId in AgencyTagIds)
        {
            var agencyTagItem = new AgencyTagItem
            {
                AgencyId = agencyId,
                AgencyTagId = agencyTagId
            };
            addAgencyTagItems.Add(agencyTagItem);
        }
        await _dbContext.AddRangeAsync(addAgencyTagItems);
    }

    /// <summary>
    /// 更新财务联系邮箱
    /// </summary>
    /// <param name="agencyId"></param>
    /// <param name="noticeEmail"></param>
    /// <returns></returns>
    private async Task<string> UpdateAgencyCreditNoticeEmail(long agencyId, string noticeEmail)
    {
        var agencyCredit = await _dbContext.AgencyCredits
            .Where(x => x.AgencyId.Equals(agencyId))
            .FirstOrDefaultAsync();
        var oldEmail = agencyCredit?.NoticeEmail;
        if (agencyCredit is not null && string.IsNullOrEmpty(noticeEmail) is false)
            agencyCredit.NoticeEmail = noticeEmail;
        return oldEmail;
    }

    /// <summary>
    /// 分销商校验
    /// </summary>
    /// <returns></returns>
    private async Task AgencyCheck(long? id, string? licenceNo, string fullName, string phone = null, string email = null)
    {
        #region 名称，信用码校验
        if (!string.IsNullOrWhiteSpace(licenceNo) && await _dbContext.Agencies
            .WhereIF(id is not null, x => !x.Id.Equals(id))
            .AnyAsync(x => x.LicenceNo.Equals(licenceNo)))
            throw new BusinessException(ErrorTypes.Tenant.AgencyLicenceExist);

        if (await _dbContext.Agencies
            .WhereIF(id is not null, x => !x.Id.Equals(id))
            .AnyAsync(x => x.FullName.Equals(fullName)))
            throw new BusinessException(ErrorTypes.Tenant.AgencyFullNameExist);

        if (string.IsNullOrEmpty(phone) is false)
        {
            if (await _dbContext.Agencies
                      .WhereIF(id is not null, x => !x.Id.Equals(id))
                      .AnyAsync(x => x.ContactNumber.Equals(phone)))
                throw new BusinessException(ErrorTypes.Tenant.AgencyContactNumberExist);
        }

        if (string.IsNullOrEmpty(email) is false)
        {
            if (await _dbContext.Agencies
                      .WhereIF(id is not null, x => !x.Id.Equals(id))
                      .AnyAsync(x => x.Email.Equals(email)))
                throw new BusinessException(ErrorTypes.Tenant.AgencyEmailExist);
        }


        #endregion
    }

    /// <summary>
    /// 创建分销商新增默认分销商额度
    /// 授信额度为0。（通知电话=分销商财务联系电话），预警金额0
    /// </summary>
    /// <param name="agencyId"></param>
    /// <param name="agencyCurrencyCode"></param>
    /// <param name="noticePhone"></param>
    private async Task AddAgencyDefaultCredit(long agencyId,
        string agencyCurrencyCode,
        string? noticePhone,
        string? noticeEmail = null)
    {
        AgencyCredit agencyCredit = new()
        {
            AgencyId = agencyId,
            CurrencyCode = agencyCurrencyCode,
            CreditLine = 0,
            EarlyWarningRate = (decimal)0.01,
            Balance = 0,
            NoticePhone = noticePhone,
            NoticeEmail = noticeEmail,
            CreateTime = DateTime.Now
        };
        AgencyCreditRecord record = new()
        {
            AgencyCreditId = agencyCredit.Id,
            CreditBusinessType = CreditBusinessType.SetCreditLine,
            AfterCreditLine = agencyCredit.CreditLine,
            ChangeAmount = 0,
            CurrencyCode = agencyCredit.CurrencyCode,
            AfterBalance = agencyCredit.Balance,
            CreateTime = DateTime.Now,
            CreatorId = 0,
            CreatorType = UserType.None
        };
        await _dbContext.AddAsync(agencyCredit);
        await _dbContext.AddAsync(record);
    }


    /// <summary>
    /// 推送卫瓴修改企业
    /// </summary>
    /// <param name="agency">分销商</param>
    public async Task PushWeilingUpdateCompany(Agency agency)
    {
        var message = _mapper.Map<UpdateCompanyMessage>(agency);

        await _capPublisher.PublishAsync(CapTopics.Tenant.WeilingUpdateCompany, message);
    }

    /// <summary>
    /// //推送卫瓴创建企业
    /// </summary>
    /// <param name="agency">分销商</param>
    /// <returns></returns>
    public async Task PushWeilingCreateCompany(Agency agency, long tenantId)
    {
        var message = _mapper.Map<CreateCompanyMessage>(agency);
        message.TenantId = tenantId;

        await _capPublisher.PublishAsync(CapTopics.Tenant.WeilingCreateCompany, message);
    }

    /// <summary>
    /// //推送卫瓴修改企业
    /// </summary>
    /// <param name="agency">分销商</param>
    public void PushWeilingUpdateCompanySync(Agency agency)
    {
        var message = _mapper.Map<UpdateCompanyMessage>(agency);

        _capPublisher.Publish(CapTopics.Tenant.WeilingUpdateCompany, message);
    }

    /// <summary>
    /// 获取推广活码
    /// </summary>
    /// <param name="agency"></param>
    /// <returns></returns>
    private async Task<List<PromotionTraceListDto>> GetPromotionTraceListDto()
    {
        var request = new ListPromotionTraceInput
        {
            PromotionTraceTypes = new List<PromotionTraceType>()
            {
                 PromotionTraceType.NoSignGroupBooking,
                 PromotionTraceType.Register
            },
            ClienteleType = PromotionTraceClienteleType.B2b
        };

        using StringContent content = new(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

        var url = _servicesAddress.Value.Marketing_GetList();

        return await _httpClientFactory.InternalPostAsync<List<PromotionTraceListDto>>(url, httpContent: content);
    }

    #endregion

    #region AgencyRecencyOrderIn

    [UnitOfWork]
    public async Task<bool> SyncAgencyRecencyOrder(List<SyncAgencyRecencyOrderInput> input)
    {
        var today = DateTime.Now.Date;
        var agencyIds = input.Select(x => x.AgencyId).ToList();

        var deleteAgencyRecencyOrders = await _dbContext.AgencyRecencyOrder.IgnoreQueryFilters()
            .Where(x => agencyIds.Contains(x.AgencyId))
            .ToListAsync();
        if (deleteAgencyRecencyOrders.Any())
            _dbContext.AgencyRecencyOrder.RemoveRange(deleteAgencyRecencyOrders);

        var addAgencyRecencyOrders = _mapper.Map<List<AgencyRecencyOrder>>(input);
        if (addAgencyRecencyOrders.Any())
            _dbContext.AgencyRecencyOrder.AddRange(addAgencyRecencyOrders);
        await _dbContext.SaveChangesAsync();

        var agencyRecencyOrders = await _dbContext.AgencyRecencyOrder.IgnoreQueryFilters()
            .Select(x => new
            {
                RecencyTime = x.RecencyTime,
                AgencyId = x.AgencyId,
            })
            .ToListAsync();
        //正常：距上次下单，30天内有下单或者无下单
        var haveOrderAgecnyIdsByThirty = agencyRecencyOrders
            .Where(x => x.RecencyTime >= today.AddDays(-30) && x.RecencyTime < today)
            .Select(x => x.AgencyId)
            .ToList();
        var haveOrderAgecniesByThirty = await _dbContext.Agencies.IgnoreQueryFilters()
            .Where(x => haveOrderAgecnyIdsByThirty.Contains(x.Id) &&
                x.AgencyRecencyStatus != AgencyRecencyStatus.Normal)
            .ToListAsync();

        haveOrderAgecniesByThirty.ForEach(x =>
        {
            x.AgencyRecencyStatus = AgencyRecencyStatus.Normal;
            x.UpdateTime = DateTime.Now;
            //同步卫瓴
            PushWeilingUpdateCompanySync(x);
        });
        //待复购：距上次下单，30~90天无订单
        var haveOrderAgecnyIds = agencyRecencyOrders
            .Where(x => x.RecencyTime >= today.AddDays(-90) && x.RecencyTime < today.AddDays(-30))
            .Select(x => x.AgencyId)
            .ToList();
        var haveOrderAgecnies = await _dbContext.Agencies.IgnoreQueryFilters()
            .Where(x => haveOrderAgecnyIds.Contains(x.Id) &&
                x.AgencyRecencyStatus != AgencyRecencyStatus.WaitRepurchase)
            .ToListAsync();
        haveOrderAgecnies.ForEach(x =>
        {
            x.AgencyRecencyStatus = AgencyRecencyStatus.WaitRepurchase;
            x.UpdateTime = DateTime.Now;
            //同步卫瓴
            PushWeilingUpdateCompanySync(x);
        });
        //沉默：距上次下单，90天以上无订单
        var haveOrderAgecnyIdsBySilent = agencyRecencyOrders
            .Where(x => x.RecencyTime < today.AddDays(-90))
            .Select(x => x.AgencyId)
            .ToList();
        var haveOrderAgecniesBySilent = await _dbContext.Agencies.IgnoreQueryFilters()
         .Where(x => haveOrderAgecnyIdsBySilent.Contains(x.Id) &&
             x.AgencyRecencyStatus != AgencyRecencyStatus.Silent)
         .ToListAsync();
        haveOrderAgecniesBySilent.ForEach(x =>
        {
            x.AgencyRecencyStatus = AgencyRecencyStatus.Silent;
            x.UpdateTime = DateTime.Now;
            //同步卫瓴
            PushWeilingUpdateCompanySync(x);
        });

        return await _dbContext.SaveChangesAsync() > 0;
    }
    #endregion

    public OperationLogDto CreateLog(object input)
    {
        CurrentUser currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
        var request = _httpContextAccessor.HttpContext.Request;
        var ip = _httpContextAccessor.HttpContext.GetRemotingIp();
        var log = new OperationLogDto()
        {
            OperationType = OperationType.Add,
            System = SystemType.Vebk,
            Host = request.Host.ToString(),
            Url = request.Path.ToString(),
            Agent = request.Headers.UserAgent,
            Ip = ip,
            Query = JsonConvert.SerializeObject(request.Query),
            Body = JsonConvert.SerializeObject(input),
            OperationUserId = currentUser?.userid,
            OperationUserName = currentUser?.nickname,
            TenantId = currentUser?.tenant
        };
        return log;
    }

    public async Task<List<GetAgencySimpleInfoOutput>> GetSimpleInfos(GetSimpleInfoInput input)
    {
        var agency = await _dbContext.Agencies.AsNoTracking()
            .IgnoreQueryFilters()
            .Where(x => input.AgencyIds.Contains(x.Id))
            .Select(x => new GetAgencySimpleInfoOutput
            {
                AgencyId = x.Id,
                ContactNumber = x.ContactNumber,
                Email = x.Email,
                FullName = x.FullName,
            })
            .ToListAsync();
        return agency;
    }

    public async Task<List<GetAgencySimpleInfoOutput>> GetInfos(GetSimpleInfoInput input)
    {
        var agency = await _dbContext.Agencies.AsNoTracking()
            .Where(x => x.FullName == input.FullName)
            .Select(x => new GetAgencySimpleInfoOutput
            {
                AgencyId = x.Id,
                ContactNumber = x.ContactNumber,
                Email = x.Email,
                FullName = x.FullName,
            })
            .ToListAsync();
        return agency;
    }

    public async Task<bool> ClearAgencyBD(ClearAgencyBDInput input)
    {
        var agencies = await _dbContext.Agencies.IgnoreQueryFilters()
            .Where(x => x.SalespersonId == input.UserId || x.CustomerOperationId == input.UserId)
            .ToListAsync();

        List<OperationLogDto> logs = new List<OperationLogDto>();
        foreach (var agency in agencies)
        {
            var msg = new List<string>();
            if (agency.SalespersonId.HasValue && agency.SalespersonId == input.UserId)
            {
                agency.SalespersonId = null;
                msg.Add("销售BD");
            }
            if (agency.CustomerOperationId.HasValue && agency.CustomerOperationId == input.UserId)
            {
                agency.CustomerOperationId = null;
                msg.Add("客户运营");
            }
            agency.UpdateTime = DateTime.Now;

            var log = new OperationLogDto()
            {
                OperationType = OperationType.Edit,
                System = SystemType.Vebk,
                Body = JsonConvert.SerializeObject(input),
                TenantId = agency.TenantId,
                TabLogType = TabLogType.Agency,
                KeyId = agency.Id,
                OperationUserName = "系统",
                Content = "员工停用-清空分销商 " + string.Join(",", msg),
            };
            logs.Add(log);
        }

        var result = await _dbContext.SaveChangesAsync() > 0;

        foreach (var log in logs)
        {
            await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        }

        return result;
    }

    public async Task SetPaymentPasswordAsync(SetAgencyPaymentPasswordInput input)
    {
        var agency = await _dbContext.Agencies.FindAsync(input.AgencyId);
        if (agency != null)
        {
            agency.PaymentPassword = input.PaymentPassword;

            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task<List<GetAgenciesByReceiptOrderOutput>> GetAgenciesByReceiptOrder(GetAgenciesByReceiptOrderInput input)
    {
        var result = await _dbContext.Agencies.IgnoreQueryFilters().AsNoTracking()
            .Join(_dbContext.AgencyCredits.IgnoreQueryFilters().AsNoTracking(), agency => agency.Id, agencyCredit => agencyCredit.AgencyId,
                (agency, agencyCredit) => new { agency, agencyCredit })
            .Where(x => x.agency.Enable && x.agencyCredit.CreditLine > 0)
            .WhereIF(input.AgencyRecencyStatus.Any(), x => input.AgencyRecencyStatus.Contains(x.agency.AgencyRecencyStatus))
            .Select(x => new GetAgenciesByReceiptOrderOutput
            {
                Id = x.agency.Id,
                FullName = x.agency.FullName,
                ShortName = x.agency.ShortName,
                AgencyRecencyStatus = x.agency.AgencyRecencyStatus,
                AgencyType = x.agency.AgencyType,
                CurrencyCode = x.agency.CurrencyCode,
                DimensionType = x.agencyCredit.DimensionType,
                SettlementPeriod = x.agency.SettlementPeriod,
                TenantId = x.agency.TenantId,
                TenantDepartmentId = x.agency.TenantDepartmentId,
            })
            .ToListAsync();

        return result;
    }

    public async Task UpdateAppendicesOfContractPath(UpdateAppendicesOfContractPathInput input)
    {
        var agency = await _dbContext.Agencies
         .Where(x => x.Id == input.Id)
         .FirstOrDefaultAsync();
        if (agency is null)
            throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);
        var oldData = new UpdateAppendicesOfContractPathInput();
        oldData.AppendicesOfContractPaths = !string.IsNullOrEmpty(agency.AppendicesOfContractPath) ?
            agency.AppendicesOfContractPath.Split(',').ToList()
            : new List<string>();

        agency.AppendicesOfContractPath = input.AppendicesOfContractPaths?.Any() is true ? string.Join(',', input.AppendicesOfContractPaths) : "";
        agency.UpdateTime = DateTime.Now;

        var log = CreateLog(input);
        log.OperationType = OperationType.Edit;
        log.TabLogType = TabLogType.Agency;
        log.KeyId = agency.Id;
        log.TenantId = agency.TenantId;
        log.Content = "合同信息";
        log.DataContentJson = JsonConvert.SerializeObject(new OperationLogDataContent()
        {
            NewData = input,
            OldData = oldData,
        });

        await _capPublisher.PublishAsync(CapTopics.User.AddOperationLog, log);
        await _dbContext.SaveChangesAsync();
    }
}