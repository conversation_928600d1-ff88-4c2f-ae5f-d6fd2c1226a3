using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Tenant.DTOs.AgencyLevelAgreement;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfig;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfigEquityCoupon;
using Contracts.Common.Tenant.DTOs.Page;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.Tenant.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.UOW;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Tenant.Api.Model;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Services;

public class AgencyLevelConfigService : IAgencyLevelConfigService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ICapPublisher _capPublisher;
    public AgencyLevelConfigService(CustomDbContext dbContext,
        IMapper mapper,
        ICapPublisher capPublisher
        )
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _capPublisher = capPublisher;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [UnitOfWork]
    public async Task<bool> Save(AgencyLevelConfigInput input)
    {
        var inputSettings = input.Settings.OrderBy(x => x.Level).ToList();
        for (int i = 0, len = inputSettings.Count; i < len; i++)
        {
            var item = inputSettings[i];
            if (i == 0 && item.Level != 1)
                throw new BusinessException(ErrorTypes.Tenant.LevelSettingError);
            if (item.UpValue < item.KipValue)
                throw new BusinessException(ErrorTypes.Tenant.LevelSettingGrowthValueKipLessthanUp);
            if (i < len - 1 && item.UpValue > inputSettings[i + 1].UpValue)
                throw new BusinessException(ErrorTypes.Tenant.LevelSettingGrowthValueLessthanUp);
        }
        if (input.LevelAgreementEnable)
        {
            var levels = inputSettings.Select(x => x.Level).OrderBy(x => x).ToList();
            var agreementLevels = input.LevelAgreements.SelectMany(x => x.Levels).OrderBy(x => x).ToList();
            if (levels.SequenceEqual(agreementLevels) is false)
            {
                throw new BusinessException(ErrorTypes.Tenant.LevelAgreementEnableLevelRequired);
            }
        }
        var tenantInfo = await _dbContext.Tenants.FirstOrDefaultAsync();
        var info = await _dbContext.AgencyLevelConfigs.FirstOrDefaultAsync();
        bool isFirst = false;
        bool oldEnable = info?.Enable ?? false;
        var businessOpens = new List<AgencyLevelBusinessType>();
        if (info != null)
        {
            if (info.Enable == false)
            {
                _mapper.Map(input, info);
                info.EnableRunTime = DateTime.Now;
                info.StartCycleRunTime = DateTime.Now;
                var nexttime = info.EnableRunTime.Value.AddMonths(info.Cycle);
                // 若当天日期大于28号，则按28号作为结束日期
                var day = nexttime.Day;
                if (day > 28)
                {
                    day = 28;
                }
                info.Day = day;
                var nextExpireDay = new DateTime(nexttime.Year, nexttime.Month, day);//下个到期日
                info.NextCycleRunTime = nextExpireDay.AddDays(1); // 到期后执行时间
                if (info.Enable)
                {
                    isFirst = true;
                }
            }
            else
            {
                info.LevelAgreementEnable = input.LevelAgreementEnable;
                info.Description = input.Description;
            }
            info.CurrencyCode = tenantInfo.CurrencyCode;
            info.UpdateTime = DateTime.Now;
            var oldExpenseTransforms = await _dbContext.AgencyLevelConfigExpenseTransforms.Where(x => x.AgencyLevelConfigId == info.Id).ToListAsync();
            var addExpenseTransforms = new List<AgencyLevelConfigExpenseTransform>();
            input.ExpenseTransforms.ForEach(et =>
            {
                var oldexpenseTransform = oldExpenseTransforms.FirstOrDefault(o => o.BusinessType == et.BusinessType);
                if (oldexpenseTransform != null)
                {
                    // 首次启用这个业务，重复开启的不用处理
                    if (info.Enable && oldexpenseTransform.CloseFrist == false && et.Enable)
                    {
                        businessOpens.Add(oldexpenseTransform.BusinessType);
                        oldexpenseTransform.CloseFrist = true;
                    }
                    oldexpenseTransform.UpdateTime = DateTime.Now;
                    oldexpenseTransform.TransformToValue = et.TransformToValue;
                    oldexpenseTransform.Enable = et.Enable;
                    // 开启会员等级后 成长值销商认证开启后无法关闭
                    if (info.Enable && oldexpenseTransform.BusinessType == AgencyLevelBusinessType.Certified && oldexpenseTransform.Enable)
                    {
                        oldexpenseTransform.Enable = true;
                    }
                }
                else
                {
                    var newExpenseTransform = new AgencyLevelConfigExpenseTransform();
                    _mapper.Map(et, newExpenseTransform);
                    newExpenseTransform.AgencyLevelConfigId = info.Id;
                    newExpenseTransform.TransformFromValue = 100;
                    newExpenseTransform.SetTenantId(info.TenantId);
                    newExpenseTransform.CreateTime = DateTime.Now;
                    // 首次启用这个业务
                    if (info.Enable && newExpenseTransform.Enable)
                    {
                        businessOpens.Add(newExpenseTransform.BusinessType);
                        newExpenseTransform.CloseFrist = true;
                    }
                    addExpenseTransforms.Add(newExpenseTransform);
                }
            });
            if (addExpenseTransforms.Any())
                await _dbContext.AgencyLevelConfigExpenseTransforms.AddRangeAsync(addExpenseTransforms);
        }
        else
        {
            isFirst = true;
            info = new AgencyLevelConfig();
            _mapper.Map(input, info);
            info.CreateTime = DateTime.Now;
            info.EnableRunTime = DateTime.Now;
            info.StartCycleRunTime = DateTime.Now;
            var nexttime = info.EnableRunTime.Value.AddMonths(info.Cycle);
            var day = nexttime.Day;
            if (day > 28)
            {
                day = 28;
            }
            info.Day = day;
            var nextExpireDay = new DateTime(nexttime.Year, nexttime.Month, day);//下个到期日
            info.NextCycleRunTime = nextExpireDay.AddDays(1); // 到期后执行时间
            info.CurrencyCode = tenantInfo.CurrencyCode;
            await _dbContext.AgencyLevelConfigs.AddAsync(info);
            var expenseTransforms = new List<AgencyLevelConfigExpenseTransform>();
            //// 成长值途径
            _mapper.Map(input.ExpenseTransforms, expenseTransforms);
            expenseTransforms.ForEach(x =>
            {
                x.AgencyLevelConfigId = info.Id;
                x.TransformFromValue = 100;
                x.SetTenantId(info.TenantId);
                x.CreateTime = DateTime.Now;
                // 首次启用这个业务
                if (info.Enable && x.Enable)
                {
                    businessOpens.Add(x.BusinessType);
                    x.CloseFrist = true;
                }
            });
            await _dbContext.AgencyLevelConfigExpenseTransforms.AddRangeAsync(expenseTransforms);

        }

        #region 等级设置、权益描述、可退改酒店
        // 等级设置
        var oldSettings = await _dbContext.AgencyLevelConfigSettings.Where(x => x.AgencyLevelConfigId == info.Id).ToListAsync();

        if (oldEnable == true && input.Settings.Count < oldSettings.Count)
        {
            throw new BusinessException("等级不能删除");
        }
        _dbContext.AgencyLevelConfigSettings.RemoveRange(oldSettings);

        var mapSettings = new List<AgencyLevelConfigSetting>();
        // 等级设置
        _mapper.Map(input.Settings, mapSettings);
        var settings = mapSettings.OrderBy(x => x.Level).ToList();
        bool levelChange = false;
        bool priceGroupIdChange = false;
        var remark = "";
        settings.ForEach(x =>
        {
            x.AgencyLevelConfigId = info.Id;
            x.SetTenantId(info.TenantId);
            x.CreateTime = DateTime.Now;
            var oldSetting = oldSettings.FirstOrDefault(o => o.Level == x.Level);
            if (oldSetting == null || oldSetting.UpValue != x.UpValue)
            {
                levelChange = true;
                remark = oldSetting == null ? $"新增等级[{x.Level}]" : $"Level[{x.Level}]升级条件改变[{oldSetting.UpValue}=>{x.UpValue}]";
            }
            if (oldSetting == null || oldSetting.PriceGroupId != x.PriceGroupId)
            {
                priceGroupIdChange = true;
            }
        });
        await _dbContext.AgencyLevelConfigSettings.AddRangeAsync(settings);

        var oldSettingIds = oldSettings.Select(x => x.Id).ToList();
        //权益描述
        await AddEquityDes(info, input, settings);
        //可退改酒店
        await AddRefundHotel(info, input, settings);
        //超时赔付酒店
        await AddOvertimeHotel(oldSettingIds, input, settings);

        #endregion
        //预订协议
        await AddLevelAgreement(input);

        if (info.Enable)
        {
            #region 生成执行任务，默认次日凌晨运行
            var now = DateTime.Now;
            var nextDay = now.AddDays(1);
            var runDay = new DateTime(nextDay.Year, nextDay.Month, nextDay.Day);
            // 要求数据手动补
            // 目前先做记录，方便后续同步旧数据查询--20240521
            if (businessOpens.Any())
            {
                var task = new AgencyLevelCalculateTask()
                {
                    TaskType = AgencyLevelCalculateTaskType.BusineOpen,
                    Status = AgencyLevelCalculateTaskStatus.Pending,
                    CreateTime = now,
                    AgencyLevelConfigId = info.Id,
                    RunTime = runDay,
                };
                task.BusinessTypes = string.Join(",", businessOpens.Select(x => (int)x));
                await _dbContext.AgencyLevelCalculateTasks.AddAsync(task);
            }

            if (levelChange || isFirst)
            {
                var task = new AgencyLevelCalculateTask()
                {
                    TaskType = AgencyLevelCalculateTaskType.Edit,
                    Status = AgencyLevelCalculateTaskStatus.Pending,
                    CreateTime = now,
                    AgencyLevelConfigId = info.Id,
                    RunTime = runDay,
                    Remark = remark
                };

                // 新开启的时候也要次日执行
                // 要求修改后，在次日凌晨更新等级，当天修改有可能会有多次，所以保留最后一次
                var oldTasks = await _dbContext.AgencyLevelCalculateTasks
                    .Where(x => x.TaskType == AgencyLevelCalculateTaskType.Edit)
                    .Where(x => x.AgencyLevelConfigId == info.Id && x.Status == AgencyLevelCalculateTaskStatus.Pending)
                    .ToListAsync();
                _dbContext.AgencyLevelCalculateTasks.RemoveRange(oldTasks);
                await _dbContext.AgencyLevelCalculateTasks.AddAsync(task);
            }
            #endregion

            // 首次开启，初始化分销商等级明细
            if (isFirst)
            {
                await _capPublisher.PublishAsync(CapTopics.Tenant.InitAgencyLevel, new OpenAgencyLevelInput() { AgencyLevelConfigId = info.Id, TenantId = info.TenantId });
            }
            if (priceGroupIdChange)
            {
                await _capPublisher.PublishAsync(CapTopics.Tenant.AgencyLevelPriceGroupIdChange,
                    new AgencyLevelConfigMessage()
                    {
                        AgencyLevelConfigId = info.Id,
                        TenantId = info.TenantId
                    });
            }
        }
        return true;
    }

    #region private
    /// <summary>
    /// 权益描述
    /// </summary>
    /// <param name="oldSettingIds"></param>
    /// <param name="input"></param>
    /// <param name="settings"></param>
    /// <returns></returns>
    private async Task AddEquityDes(AgencyLevelConfig config, AgencyLevelConfigInput input, List<AgencyLevelConfigSetting> settings)
    {
        //删除旧的权益描述
        var oldEquityDesList = await _dbContext.AgencyLevelConfigEquityDes.Where(x => x.AgencyLevelConfigId == config.Id).ToListAsync();
        _dbContext.AgencyLevelConfigEquityDes.RemoveRange(oldEquityDesList);

        // 删除旧优惠券
        var oldEquityCoupons = await _dbContext.AgencyLevelConfigEquityCoupons.Where(x => x.AgencyLevelConfigId == config.Id).ToListAsync();
        _dbContext.AgencyLevelConfigEquityCoupons.RemoveRange(oldEquityCoupons);

        //权益描述
        var equityDesAllList = new List<AgencyLevelConfigEquityDes>();
        var equityCouponAllList = new List<AgencyLevelConfigEquityCoupon>();
        input.Settings.ForEach(inputSeting =>
        {
            //权益描述
            var setting = settings.FirstOrDefault(k => k.Level == inputSeting.Level);
            inputSeting.EquityDescriptions.ForEach(inputDes =>
            {
                var des = _mapper.Map<AgencyLevelConfigEquityDes>(inputDes);
                des.AgencyLevelConfigId = setting.AgencyLevelConfigId;
                des.SetTenantId(setting.TenantId);
                des.CreateTime = DateTime.Now;
                des.AgencyLevelSettingId = setting.Id;
                if (des.EquityType == AgencyLevelConfigEquityDesType.Coupon)
                {
                    var equityCouponList = new List<AgencyLevelConfigEquityCoupon>();
                    _mapper.Map(inputDes.EquityCoupons, equityCouponList);
                    equityCouponList.ForEach(coupon =>
                    {
                        coupon.AgencyLevelConfigId = setting.AgencyLevelConfigId;
                        coupon.AgencyLevelConfigEquityDesId = des.Id;
                        coupon.SetTenantId(setting.TenantId);
                    });
                    equityCouponAllList.AddRange(equityCouponList);
                }
                equityDesAllList.Add(des);
            });
        });
        //权益描述
        await _dbContext.AgencyLevelConfigEquityDes.AddRangeAsync(equityDesAllList);
        //权益描述-优惠券
        await _dbContext.AgencyLevelConfigEquityCoupons.AddRangeAsync(equityCouponAllList);
    }

    /// <summary>
    /// 可退改酒店
    /// </summary>
    /// <param name="oldSettingIds"></param>
    /// <param name="input"></param>
    /// <param name="settings"></param>
    /// <returns></returns>
    private async Task AddRefundHotel(AgencyLevelConfig config, AgencyLevelConfigInput input, List<AgencyLevelConfigSetting> settings)
    {
        //删除旧的可退改酒店
        var oldHotels = await _dbContext.AgencyLevelConfigHotels.Where(x => x.AgencyLevelConfigId == config.Id).ToListAsync();
        _dbContext.AgencyLevelConfigHotels.RemoveRange(oldHotels);

        var oldHotelDetails = await _dbContext.AgencyLevelConfigHotelDetails.Where(x => x.AgencyLevelConfigId == config.Id).ToListAsync();
        _dbContext.AgencyLevelConfigHotelDetails.RemoveRange(oldHotelDetails);

        //
        var hotelAllList = new List<AgencyLevelConfigHotel>();
        var hotelDetailAllList = new List<AgencyLevelConfigHotelDetail>();
        input.Settings.ForEach(inputSeting =>
        {
            var setting = settings.FirstOrDefault(k => k.Level == inputSeting.Level);
            var hotelDetailAllExistIds = new List<long>();
            //可退改酒店
            inputSeting.Hotels.ForEach(inputHotel =>
            {
                var hotel = new AgencyLevelConfigHotel();
                _mapper.Map(inputHotel, hotel);
                hotel.SetTenantId(setting.TenantId);
                hotel.CreateTime = DateTime.Now;
                hotel.AgencyLevelSettingId = setting.Id;
                hotel.AgencyLevelConfigId = setting.AgencyLevelConfigId;

                var existDetail = inputHotel.Details.Where(x => hotelDetailAllExistIds.Contains(x.HotelId)).FirstOrDefault();
                if (existDetail != null)
                    throw new BusinessException("酒店重复设置");
                var details = new List<AgencyLevelConfigHotelDetail>();
                _mapper.Map(inputHotel.Details, details);
                details.ForEach(detail =>
                {
                    detail.AgencyLevelHotelId = hotel.Id;
                    detail.AgencyLevelConfigId = setting.AgencyLevelConfigId;
                    detail.SetTenantId(setting.TenantId);
                });
                hotelDetailAllExistIds.AddRange(details.Select(x => x.HotelId));
                hotelDetailAllList.AddRange(details);
                hotelAllList.Add(hotel);
            });
        });
        //
        await _dbContext.AgencyLevelConfigHotels.AddRangeAsync(hotelAllList);
        //
        await _dbContext.AgencyLevelConfigHotelDetails.AddRangeAsync(hotelDetailAllList);
    }

    /// <summary>
    /// 超时赔付酒店
    /// </summary>
    /// <param name="oldSettingIds"></param>
    /// <param name="input"></param>
    /// <param name="settings"></param>
    /// <returns></returns>
    private async Task AddOvertimeHotel(List<long> oldSettingIds, AgencyLevelConfigInput input, List<AgencyLevelConfigSetting> settings)
    {
        //删除旧的超时赔付酒店
        var oldOvertimeHotels = await _dbContext.AgencyLevelConfigOvertimeHotels.Where(x => oldSettingIds.Contains(x.AgencyLevelSettingId)).ToListAsync();
        _dbContext.AgencyLevelConfigOvertimeHotels.RemoveRange(oldOvertimeHotels);
        var oldOvertimeHotelIds = oldOvertimeHotels.Select(x => x.Id).ToList();
        var oldOvertimeHotelDetails = await _dbContext.AgencyLevelConfigOvertimeHotelDetails.Where(x => oldOvertimeHotelIds.Contains(x.AgencyLevelOvertimeHotelId)).ToListAsync();
        _dbContext.AgencyLevelConfigOvertimeHotelDetails.RemoveRange(oldOvertimeHotelDetails);

        //超时赔付酒店 
        var overtimeHotelAllList = new List<AgencyLevelConfigOvertimeHotel>();
        var overtimeHotelDetailAllList = new List<AgencyLevelConfigOvertimeHotelDetail>();
        input.Settings.ForEach(inputSeting =>
        {
            var setting = settings.FirstOrDefault(k => k.Level == inputSeting.Level);
            #region 超时赔付酒店
            var overtimeHotelDetailAllExistIds = new List<long>();
            //超时赔付酒店
            inputSeting.OvertimeHotels.ForEach(inputHotel =>
            {
                var hotel = new AgencyLevelConfigOvertimeHotel();
                _mapper.Map(inputHotel, hotel);
                hotel.SetTenantId(setting.TenantId);
                hotel.CreateTime = DateTime.Now;
                hotel.AgencyLevelSettingId = setting.Id;
                hotel.AgencyLevelConfigId = setting.AgencyLevelConfigId;

                var existDetail = inputHotel.Details.Where(x => overtimeHotelDetailAllExistIds.Contains(x.HotelId)).FirstOrDefault();
                if (existDetail != null)
                    throw new BusinessException("酒店重复设置");
                var details = new List<AgencyLevelConfigOvertimeHotelDetail>();
                _mapper.Map(inputHotel.Details, details);
                details.ForEach(detail =>
                {
                    detail.AgencyLevelOvertimeHotelId = hotel.Id;
                    detail.AgencyLevelConfigId = setting.AgencyLevelConfigId;
                    detail.SetTenantId(setting.TenantId);
                });
                overtimeHotelDetailAllExistIds.AddRange(details.Select(x => x.HotelId));
                overtimeHotelAllList.Add(hotel);
                overtimeHotelDetailAllList.AddRange(details);
            });
            #endregion
        });
        //
        await _dbContext.AgencyLevelConfigOvertimeHotels.AddRangeAsync(overtimeHotelAllList);
        //
        await _dbContext.AgencyLevelConfigOvertimeHotelDetails.AddRangeAsync(overtimeHotelDetailAllList);
    }

    /// <summary>
    /// 等级预订协议
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task AddLevelAgreement(AgencyLevelConfigInput input)
    {
        if (input.Enable && input.LevelAgreementEnable)
        {
            List<int> levels = new();
            input.LevelAgreements.ForEach(x =>
            {
                levels.AddRange(x.Levels);
            });
            // 当前租户的等级协议
            var oldLevelAgreements = await _dbContext.AgencyLevelAgreement
                .ToListAsync();

            _dbContext.AgencyLevelAgreement.RemoveRange(oldLevelAgreements);

            var addlevelAgreements = new List<AgencyLevelAgreement>();
            foreach (var item in input.LevelAgreements)
            {
                var levelAgreement = _mapper.Map<AgencyLevelAgreement>(item);
                levelAgreement.Level = string.Join(',', item.Levels);
                addlevelAgreements.Add(levelAgreement);
            }
            await _dbContext.AgencyLevelAgreement.AddRangeAsync(addlevelAgreements);
        }
    }
    #endregion

    /// <summary>
    /// 获取等级设置详情
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task<AgencyLevelConfigDto> GetByTenantId()
    {
        var info = await _dbContext.AgencyLevelConfigs.FirstOrDefaultAsync();
        if (info == null)
            return new AgencyLevelConfigDto();
        var res = _mapper.Map<AgencyLevelConfigDto>(info);
        var expenseTransforms = await _dbContext.AgencyLevelConfigExpenseTransforms.Where(x => x.AgencyLevelConfigId == info.Id).ToListAsync();
        res.ExpenseTransforms = _mapper.Map<List<AgencyLevelConfigExpenseTransformDto>>(expenseTransforms);
        var settings = await _dbContext.AgencyLevelConfigSettings.Where(x => x.AgencyLevelConfigId == info.Id).ToListAsync();
        res.Settings = _mapper.Map<List<AgencyLevelConfigSettingDto>>(settings);

        var settingIds = settings.Select(x => x.Id).ToList();
        var equityDesList = await _dbContext.AgencyLevelConfigEquityDes.Where(x => settingIds.Contains(x.AgencyLevelSettingId)).ToListAsync();
        var equityDesIds = equityDesList.Select(x => x.Id).ToList();
        var equityDesCouponList = await _dbContext.AgencyLevelConfigEquityCoupons.Where(x => equityDesIds.Contains(x.AgencyLevelConfigEquityDesId)).ToListAsync();

        //可退改酒店
        var allHotels = await _dbContext.AgencyLevelConfigHotels.Where(x => settingIds.Contains(x.AgencyLevelSettingId)).ToListAsync();
        var hotelIds = allHotels.Select(x => x.Id).ToList();
        var allHotelDetails = await _dbContext.AgencyLevelConfigHotelDetails.Where(x => hotelIds.Contains(x.AgencyLevelHotelId)).ToListAsync();
        //超时赔付酒店
        var allOvertimeHotels = await _dbContext.AgencyLevelConfigOvertimeHotels.Where(x => settingIds.Contains(x.AgencyLevelSettingId)).ToListAsync();
        var overtimehotelIds = allOvertimeHotels.Select(x => x.Id).ToList();
        var allOvertimeHotelDetails = await _dbContext.AgencyLevelConfigOvertimeHotelDetails.Where(x => overtimehotelIds.Contains(x.AgencyLevelOvertimeHotelId)).ToListAsync();
        //预订协议
        var levels = settings.Select(x => x.Level).ToList();
        var agreements = await _dbContext.AgencyLevelAgreement
                              .Where(x => x.TenantId == info.TenantId)
                              .ToListAsync();

        res.Settings.ForEach(setting =>
        {
            var equityDes = equityDesList.Where(e => e.AgencyLevelSettingId == setting.Id).ToList();
            setting.EquityDescriptions = _mapper.Map<List<AgencyLevelConfigEquityDesDto>>(equityDes);
            setting.EquityDescriptions.ForEach(d =>
            {
                var hotelDetails = equityDesCouponList.Where(e => e.AgencyLevelConfigEquityDesId == d.Id).ToList();
                d.EquityCoupons = _mapper.Map<List<AgencyLevelConfigEquityCouponDto>>(hotelDetails);
            });

            //可退改酒店
            var hotels = allHotels.Where(e => e.AgencyLevelSettingId == setting.Id).ToList();
            setting.Hotels = _mapper.Map<List<AgencyLevelConfigHotelDto>>(hotels);
            setting.Hotels.ForEach(h =>
            {
                var hotelDetails = allHotelDetails.Where(e => e.AgencyLevelHotelId == h.Id).ToList();
                h.Details = _mapper.Map<List<AgencyLevelConfigHotelDetailDto>>(hotelDetails);
            });

            //超时赔付酒店
            var overtimeHotels = allOvertimeHotels.Where(e => e.AgencyLevelSettingId == setting.Id).ToList();
            setting.OvertimeHotels = _mapper.Map<List<AgencyLevelConfigOvertimeHotelDto>>(overtimeHotels);
            setting.OvertimeHotels.ForEach(h =>
            {
                var hotelDetails = allOvertimeHotelDetails.Where(e => e.AgencyLevelOvertimeHotelId == h.Id).ToList();
                h.Details = _mapper.Map<List<AgencyLevelConfigOvertimeHotelDetailDto>>(hotelDetails);
            });
        });
        //预订协议
        foreach (var item in agreements)
        {
            var levelAgreement = _mapper.Map<AgencyLevelAgreementDto>(item);
            levelAgreement.Levels = item.Level.Split(',').Select(x => int.Parse(x)).ToList();
            res.LevelAgreements.Add(levelAgreement);
        }
        return res;
    }

    /// <summary>
    /// 等级变化重新核算分销商等级-生成订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> RunAgencyLevelCalculateTaskMq(ReAgencyLevelCalculateInput input)
    {
        int page = 1, pageSize = 500;
        bool isDo = true;
        do
        {
            var tasks = await _dbContext.AgencyLevelCalculateTasks
                   .Where(x => x.TaskType == AgencyLevelCalculateTaskType.Edit)
                   .Where(x => x.Status == AgencyLevelCalculateTaskStatus.Pending)
                   .Where(x => x.RunTime <= input.Date)
                   .Skip((page - 1) * pageSize).Take(pageSize)
                   .ToListAsync();
            if (tasks.Any())
            {
                foreach (var task in tasks)
                {
                    task.Status = AgencyLevelCalculateTaskStatus.Processing;
                    task.UpdateTime = DateTime.Now;
                    await _capPublisher.PublishAsync(CapTopics.Tenant.ReAgencyLevelCalculate, new ReAgencyLevelCalculateTaskInput() { CalculateTaskId = task.Id });
                }
            }
            else
            {
                isDo = false;
            }
            page++;
        } while (isDo);
        return true;
    }

    /// <summary>
    /// 周期核算分销商等级-生成订阅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> RunCycleAgencyLevelCalculateTaskMq(ReAgencyLevelCalculateInput input)
    {
        int page = 1, pageSize = 500;
        bool isDo = true;
        do
        {
            var levelConfigs = await _dbContext.AgencyLevelConfigs
                   .Where(x => x.Enable == true)
                   .Where(x => x.NextCycleRunTime == input.Date)
                   .Skip((page - 1) * pageSize).Take(pageSize)
                   .ToListAsync();
            if (levelConfigs.Any())
            {
                var now = DateTime.Now;
                var runDay = new DateTime(now.Year, now.Month, now.Day);
                var addTasks = new List<AgencyLevelCalculateTask>();
                foreach (var config in levelConfigs)
                {
                    var task = new AgencyLevelCalculateTask()
                    {
                        TaskType = AgencyLevelCalculateTaskType.Cycle,
                        Status = AgencyLevelCalculateTaskStatus.Processing,
                        CreateTime = now,
                        AgencyLevelConfigId = config.Id,
                        RunTime = runDay,
                    };
                    task.SetTenantId(config.TenantId);
                    addTasks.Add(task);
                    config.StartCycleRunTime = config.NextCycleRunTime;
                    config.NextCycleRunTime = config.NextCycleRunTime!.Value.AddMonths(config.Cycle);
                }
                await _dbContext.AgencyLevelCalculateTasks.AddRangeAsync(addTasks);

                foreach (var task in addTasks)
                {
                    await _capPublisher.PublishAsync(CapTopics.Tenant.CycleAgencyLevelCalculate, new ReAgencyLevelCalculateTaskInput() { CalculateTaskId = task.Id });
                }
            }
            else
            {
                isDo = false;
            }
            page++;
        } while (isDo);
        return true;
    }
}
