using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using FluentValidation;

namespace Tenant.Api.Services.Validator.Agency;

public class SearchAgenciesInputValidator : AbstractValidator<SearchAgenciesInput>
{
    public SearchAgenciesInputValidator()
    {
        RuleFor(s => s.ContractStatus).IsInEnum().When(s => s.ContractStatus is not null);
        RuleFor(s => s.CurrencyCode)
            .SetValidator(new CurrencyEnumValidator<Currency>())
            .WithMessage("currency code error")
            .When(s => !string.IsNullOrWhiteSpace(s.CurrencyCode));
        RuleFor(s => s.RegionConfigId).GreaterThan(0);
        RuleFor(s => s.BusinessVolume)
            .LessThanOrEqualTo(5)
            .GreaterThanOrEqualTo(1);
        RuleFor(s => s.IndustryInvolved).ForEach(x => x.IsInEnum());

        RuleFor(s => s.RegisterSubSources).IsInEnum().When(s => s.RegisterSubSources.HasValue);
    }
}
