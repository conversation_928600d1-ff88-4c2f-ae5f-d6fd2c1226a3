using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.AgencyCredit;
using FluentValidation;

namespace Tenant.Api.Services.Validator.AgencyCredit;

public class CreateAgencyCreditInputValidator : AbstractValidator<CreateAgencyCreditInput>
{
    public CreateAgencyCreditInputValidator()
    {
        RuleFor(x => x.AgencyId).GreaterThan(0);
        RuleFor(x => x.CreditLine).GreaterThanOrEqualTo(0);
        RuleFor(x => x.EarlyWarningRate).GreaterThanOrEqualTo(0).LessThan(1);
        RuleFor(x => x.NoticeEmail).NotNull().NotEmpty();

    }
}
