using AspectCore.DynamicProxy;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.Enums;
using FluentValidation;
using FluentValidation.Validators;

namespace Tenant.Api.Services.Validator.Supplier
{
    public class UpdateSupplierInputValidator : AbstractValidator<UpdateSupplierInput>
    {
        public UpdateSupplierInputValidator()
        {
            RuleFor(x => x.Id).NotNull().GreaterThan(0);
            RuleFor(x => x.FullName).NotNull().Length(1, 256);
            RuleFor(x => x.ShortName).NotNull().Length(1, 8);
            RuleFor(x => x.ContactName).Length(0, 50);

            When(s => !string.IsNullOrWhiteSpace(s.ContactPhone), () =>
            {
                RuleFor(s => s.ContactPhone).Length(0, 50).SetValidator(new SensitiveDataValidator()).WithMessage("ContactPhone error");
            });

            When(s => !string.IsNullOrWhiteSpace(s.ContactEmail), () =>
            {
                RuleFor(s => s.ContactEmail).SetValidator(new SensitiveDataValidator()).WithMessage("ContactEmail error");
            });

            RuleFor(x => x.BankCode).NotNull().Length(1, 32).When(x => x.BankAccountType is TenantBankAccountType.Domestic);
            RuleFor(x => x.BankName).NotNull().Length(1, 100);
            RuleFor(x => x.BankAccount).NotNull().Length(1, 100).When(x => x.BankAccountType is TenantBankAccountType.Domestic);
            RuleFor(x => x.AccountName).NotNull().Length(1, 50);
            RuleFor(x => x.SupplierType)
                .NotEmpty()
                .IsInEnum()
                .NotEqual(SupplierType.None);
            RuleFor(x => x.SupplierApiSetting.SupplierApiType)
                .NotEmpty()
                .IsInEnum()
                .When(x => x.SupplierType.Equals(SupplierType.Api));
           
            When(x => x.SupplierType == SupplierType.Api, () =>
            {
                RuleFor(x => x.PriceBasisType).NotNull()
                .When(x => x.SupplierApiSetting.SupplierApiParentTypes.Contains(SupplierApiParentType.CarProduct));
                RuleFor(x => x.PriceAdjustmentType).NotNull()
                 .When(x => x.SupplierApiSetting.SupplierApiParentTypes.Contains(SupplierApiParentType.CarProduct));
                RuleFor(x => x.PriceAdjustmentValue).NotNull()
                 .When(x => x.SupplierApiSetting.SupplierApiParentTypes.Contains(SupplierApiParentType.CarProduct));

                RuleForEach(x=>x.SupplierApiSetting.SupplierApiParentTypes).ChildRules(c =>
                {
                    c.RuleFor(f => f).NotEmpty().IsInEnum();
                });

                RuleFor(x => x.SupplierApiSetting)
                    .NotNull()
                    .DependentRules(() =>
                    {
                        RuleFor(x => x.SupplierApiSetting.SupplierApiType)
                            .NotEmpty()
                            .IsInEnum();
                        RuleFor(x => x.SupplierApiSetting.SupplierApiParentTypes)
                            .NotEmpty();

                        #region 酒店

                        When(x => x.SupplierApiSetting.SupplierApiType == SupplierApiType.Hop, () =>
                        {
                            //汇智酒店api供应商.只能选CNY币种
                            RuleFor(r => r.CurrencyCode).Must(m => m.Equals(Currency.CNY.ToString()));
                        });

                        #endregion
                    });
            });


        }
    }
}

public class SensitiveDataValidator : PropertyValidator
{
    public SensitiveDataValidator()
        : base()
    {
    }

    protected override bool IsValid(PropertyValidatorContext context)
    {
        if (context.PropertyValue == null)
            return true;
        return !context.PropertyValue.ToString().Contains("*");
    }
}
