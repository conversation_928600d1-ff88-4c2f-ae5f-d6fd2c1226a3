using Contracts.Common.Tenant.DTOs.Tenant;
using FluentValidation;

namespace Tenant.Api.Services.Validator.Tenant
{
    public class SignTenantInputValidator : AbstractValidator<SignTenantInput>
    {
        public SignTenantInputValidator() 
        {
            RuleFor(x => x.TenantId).NotNull().GreaterThan(0);
            RuleFor(x => x.FullName).NotNull().Length(1, 50);
            RuleFor(x => x.ShortName).NotNull().Length(1, 50);
            RuleFor(x => x.IndustryType).NotNull().IsInEnum();
            RuleFor(x => x.CountryCode).NotNull().GreaterThan(0);
            RuleFor(x => x.CountryName).NotNull().Length(1, 50);
            RuleFor(x => x.ProvinceCode).NotNull().GreaterThan(0);
            RuleFor(x => x.ProvinceName).NotNull().Length(1, 50);
            RuleFor(x => x.CityCode).NotNull().GreaterThan(0);
            RuleFor(x => x.CityName).NotNull().Length(1, 50);
            RuleFor(x => x.Address).NotNull().Length(1, 100);
            RuleFor(x => x.UserId).NotNull().GreaterThan(0);
            RuleFor(x => x.UserName).NotNull().Length(1, 50);
            RuleFor(x => x.ContactPerson).NotNull().Length(1, 50);
            RuleFor(x => x.ContactNumber).Matches("^1[0-9]{10}$");
            RuleFor(x => x.ContactEmail).NotEmpty().EmailAddress();
            RuleFor(x => x.FinancialStaff).NotNull().Length(1, 50);
            RuleFor(x => x.FinancialStaffNumber).NotNull().Length(1, 50);
            RuleFor(x => x.PaymentFeeRate).NotNull().GreaterThanOrEqualTo(0.0026f).LessThan(1);
            RuleFor(x => x.PlatformCommissionRate).NotNull().GreaterThanOrEqualTo(0).LessThan(1);
            RuleFor(x => x.PaymentFeeRate + x.PlatformCommissionRate).LessThan(1).WithMessage("支付费率和平台佣金之和不大于1");
            RuleFor(x => x.AgencyOperation).NotNull().IsInEnum();
            RuleFor(x => x.AgencyOperationSettlement).NotNull().IsInEnum();
            RuleFor(x => x.AgencyOperationRate).NotNull().GreaterThanOrEqualTo(0).LessThan(1);
            RuleFor(x => x.AgentInvoice).NotNull().IsInEnum();
            RuleFor(x => x.AgentInvoiceRate).NotNull().GreaterThanOrEqualTo(0).LessThan(1);
            RuleFor(x => x.ContractFile).NotEmpty();
            RuleFor(x => x.Remark).Length(0, 200);
        }
    }
}
