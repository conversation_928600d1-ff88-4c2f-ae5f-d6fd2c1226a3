using Contracts.Common.Tenant.DTOs.AgencyLevelConfig;
using Contracts.Common.Tenant.DTOs.AgencyLevelDetail;
using FluentValidation;

namespace Tenant.Api.Services.Validator.AgencyLevelDetail;

public class IncreaseAgencyGrowUpValueValidator : AbstractValidator<IncreaseAgencyGrowUpValueInput>
{
    public IncreaseAgencyGrowUpValueValidator()
    {
        RuleFor(x => x.AgencyId).NotEmpty();
        RuleFor(x => x.Value)
            .GreaterThan(0)
            .LessThanOrEqualTo(99999)
            .NotEmpty();
    }
}
