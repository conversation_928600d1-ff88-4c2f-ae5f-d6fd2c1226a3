using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.RegionConfig;
using Contracts.Common.Tenant.DTOs.SignSubject;
using FluentValidation;

namespace Tenant.Api.Services.Validator.RegionConfig;

public class SaveRegionConfigScopeInputValidator : AbstractValidator<SaveRegionConfigScopeInput>
{
    public SaveRegionConfigScopeInputValidator()
    {
        RuleFor(x => x.CountryCode).GreaterThan(0);
    }
}
