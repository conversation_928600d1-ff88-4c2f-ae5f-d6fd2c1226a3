using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class AddSupplierApiFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Supplier<PERSON><PERSON>",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EnName = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SupplierApiType = table.Column<int>(type: "int", nullable: false),
                    SupplierApiParentType = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupplierApi", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "SupplierApiFields",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    SupplierApiType = table.Column<int>(type: "int", nullable: false),
                    SupplierApiParentType = table.Column<int>(type: "int", nullable: false),
                    FieldCode = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FieldType = table.Column<sbyte>(type: "tinyint", nullable: false, defaultValue: (sbyte)0),
                    ZhFieldName = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EnFieldName = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DataSecrecyType = table.Column<sbyte>(type: "tinyint", nullable: false, defaultValue: (sbyte)0),
                    RulerFilter = table.Column<string>(type: "text", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ValueRange = table.Column<string>(type: "text", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    IsRequired = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    IsMultiple = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    MinLength = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Maxlength = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Precision = table.Column<sbyte>(type: "tinyint", nullable: true),
                    DefaultValue = table.Column<string>(type: "varchar(1500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Unit = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Placeholder = table.Column<string>(type: "varchar(250)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EnPlaceholder = table.Column<string>(type: "varchar(250)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "varchar(250)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Sort = table.Column<sbyte>(type: "tinyint", nullable: false),
                    FieldComponentType = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Number = table.Column<bool>(type: "tinyint(1)", nullable: true, defaultValue: false),
                    Textarea = table.Column<bool>(type: "tinyint(1)", nullable: true, defaultValue: false),
                    Controls = table.Column<bool>(type: "tinyint(1)", nullable: true, defaultValue: false),
                    Append = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Radio = table.Column<bool>(type: "tinyint(1)", nullable: true, defaultValue: false),
                    UIExtend = table.Column<string>(type: "text", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupplierApiFields", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "SupplierApiSettingField",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    SupplierApiSettingId = table.Column<long>(type: "bigint", nullable: false),
                    ZhFieldName = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FieldCode = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FieldType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    FieldValue = table.Column<string>(type: "varchar(1500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupplierApiSettingField", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.InsertData(
                table: "SupplierApi",
                columns: new[] { "Id", "EnName", "Name", "SupplierApiParentType", "SupplierApiType" },
                values: new object[,]
                {
                    { 1347483297741664358L, "Hop", "汇智酒店", 1, 1 },
                    { 1347483297741664359L, "GDS", "高定GDS", 16, 4 },
                    { 1347483297741664360L, "GuestRoutePlay", "客路玩乐", 34, 201 },
                    { 1347483297741664361L, "Treep", "Treep", 2, 203 },
                    { 1347483297741664362L, "Exoz", "Exoz", 2, 204 },
                    { 1347483297741664363L, "CA", "CA", 2, 205 },
                    { 1347483297741664364L, "Mozio", "Mozio", 4, 206 },
                    { 1347483297741664365L, "CaoYueTianKong", "超越天空", 2, 207 },
                    { 1347483297741664366L, "Sctt", "新尚维Sctt", 2, 208 },
                    { 1347483297741664367L, "EY", "EY", 2, 209 },
                    { 1347483297741664368L, "PingAnHopeInsurance", "平安希望保", 8, 301 }
                });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[,]
                {
                    { 1344583947413168110L, null, false, "", "Key", "Please enter", "Hop_Key", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 1, 1, false, null, null, null, "Key" },
                    { 1344583947413168111L, null, false, "", "Secret", "Please enter", "Hop_Secret", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 1, 1, false, null, null, null, "Secret" },
                    { 1344583947413168200L, null, false, "", "User ID", "Please enter", "GDS_ApiID", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 16, 4, false, null, null, null, "用户ID" },
                    { 1344583947413168201L, null, false, "", "PCC", "Please enter", "GDS_PCC", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 16, 4, false, null, null, null, "PCC" },
                    { 1344583947413168202L, null, false, "", "Password", "Please enter", "GDS_Password", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 16, 4, false, null, null, null, "Password" },
                    { 1344583947413168203L, null, false, "", "Domain", "Please enter", "GDS_Domain", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 16, 4, false, null, null, null, "域" },
                    { 1344583947413168204L, null, false, "", "Base URL", "Please enter", "GDS_Host", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 16, 4, false, null, null, null, "Base URL" },
                    { 1344583947413168205L, null, false, "", "Agency Address", "Please enter", "GDS_AddressLine", "FieldInput", null, true, null, null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"机构地址\",\"msg\":\"只能输入字母、数字、空格\",\"pattern\":\"^[a-zA-Z0-9 ]+$\",\"enName\":\"Agency Address\",\"enMsg\":\"Only letters, numbers, and spaces can be entered\",\"zhName\":\"机构地址\",\"zhMsg\":\"只能输入字母、数字、空格\"}]", (sbyte)0, 16, 4, false, null, null, null, "机构地址" },
                    { 1344583947413168206L, null, false, "", "Agency City Name", "Please enter", "GDS_CityName", "FieldInput", null, true, null, null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"机构城市名称\",\"msg\":\"只能输入字母、数字、空格\",\"pattern\":\"^[a-zA-Z0-9 ]+$\",\"enName\":\"Agency City Name\",\"enMsg\":\"Only letters, numbers, and spaces can be entered\",\"zhName\":\"机构城市名称\",\"zhMsg\":\"只能输入字母、数字、空格\"}]", (sbyte)0, 16, 4, false, null, null, null, "机构城市名称" },
                    { 1344583947413168207L, null, false, "", "Agency Country Code", "Please enter", "GDS_CountryCode", "FieldInput", null, true, null, null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"机构国家代码\",\"msg\":\"只能输入字母、数字、空格\",\"pattern\":\"^[a-zA-Z0-9 ]+$\",\"enName\":\"Agency Country Code\",\"enMsg\":\"Only letters, numbers, and spaces can be entered\",\"zhName\":\"机构国家代码\",\"zhMsg\":\"只能输入字母、数字、空格\"}]", (sbyte)0, 16, 4, false, null, null, null, "机构国家代码" },
                    { 1344583947413168208L, null, false, "", "Agency Postal Code", "Please enter", "GDS_PostalCode", "FieldInput", null, true, null, null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"机构邮政编码\",\"msg\":\"只能输入字母、数字、空格\",\"pattern\":\"^[a-zA-Z0-9 ]+$\",\"enName\":\"Agency Postal Code\",\"enMsg\":\"Only letters, numbers, and spaces can be entered\",\"zhName\":\"机构邮政编码\",\"zhMsg\":\"只能输入字母、数字、空格\"}]", (sbyte)0, 16, 4, false, null, null, null, "机构邮政编码" },
                    { 1344583947413168209L, null, false, "", "Agency State Code", "Please enter", "GDS_StateCode", "FieldInput", null, true, null, null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"机构省/州代码\",\"msg\":\"只能输入字母、数字、空格\",\"pattern\":\"^[a-zA-Z0-9 ]+$\",\"enName\":\"Agency State Code\",\"enMsg\":\"Only letters, numbers, and spaces can be entered\",\"zhName\":\"机构省/州代码\",\"zhMsg\":\"只能输入字母、数字、空格\"}]", (sbyte)0, 16, 4, false, null, null, null, "机构省/州代码" },
                    { 1344583947413168210L, null, false, "", "Agency Street Number", "Please enter", "GDS_StreetNmbr", "FieldInput", null, true, null, null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"机构街道号码\",\"msg\":\"只能输入字母、数字、空格\",\"pattern\":\"^[a-zA-Z0-9 ]+$\",\"enName\":\"Agency Street Number\",\"enMsg\":\"Only letters, numbers, and spaces can be entered\",\"zhName\":\"机构街道号码\",\"zhMsg\":\"只能输入字母、数字、空格\"}]", (sbyte)0, 16, 4, false, null, null, null, "机构街道号码" }
                });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168211L, null, false, (sbyte)2, null, "Email", "Please enter", "GDS_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 16, 4, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[,]
                {
                    { 1344583947413168300L, null, false, "", "Key", "Please enter", "GuestRoutePlay_Key", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 34, 201, false, null, null, null, "Key" },
                    { 1344583947413168301L, null, false, "", "Secret", "Please enter", "GuestRoutePlay_Secret", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 34, 201, false, null, null, null, "Secret" }
                });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168302L, null, false, (sbyte)2, null, "Email", "Please enter", "GuestRoutePlay_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 34, 201, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[,]
                {
                    { 1344583947413168400L, null, false, "", "UserName", "Please enter", "Globaltix_UserName", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 202, false, null, null, null, "UserName" },
                    { 1344583947413168401L, null, false, "", "Password", "Please enter", "Globaltix_Password", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 202, false, null, null, null, "Password" },
                    { 1344583947413168402L, null, false, "", "Notification Secret", "Please enter", "Globaltix_NotificationSecret", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 202, false, null, null, null, "Notification Secret" }
                });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168403L, null, false, (sbyte)2, null, "Email", "Please enter", "Globaltix_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 2, 202, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[,]
                {
                    { 1344583947413168500L, null, false, "", "Key", "Please enter", "TreepFlyingEarth_Key", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 203, false, null, null, null, "Key" },
                    { 1344583947413168501L, null, false, "", "Secret", "Please enter", "TreepFlyingEarth_Secret", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 203, false, null, null, null, "Secret" }
                });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168502L, null, false, (sbyte)2, null, "Email", "Please enter", "TreepFlyingEarth_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 2, 203, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[,]
                {
                    { 1344583947413168600L, null, false, "", "UserName", "Please enter", "Exoz_UserName", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 204, false, null, null, null, "UserName" },
                    { 1344583947413168601L, null, false, "", "Password", "Please enter", "Exoz_Password", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 204, false, null, null, null, "Password" },
                    { 1344583947413168602L, null, false, "", "PartnerCode", "Please enter", "Exoz_PartnerCode", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 204, false, null, null, null, "PartnerCode" },
                    { 1344583947413168603L, null, false, "", "CatalogueApiHost", "Please enter", "Exoz_CatalogueApiHost", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 204, false, null, null, null, "CatalogueApiHost" }
                });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168604L, null, false, (sbyte)2, null, "Email", "Please enter", "Exoz_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 2, 204, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[,]
                {
                    { 1344583947413168700L, null, false, "", "Key", "Please enter", "CA_Key", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 205, false, null, null, null, "Key" },
                    { 1344583947413168701L, null, false, "", "Secret", "Please enter", "CA_Secret", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 205, false, null, null, null, "Secret" }
                });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168702L, null, false, (sbyte)2, null, "Email", "Please enter", "CA_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 2, 205, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168800L, null, false, "", "ApiKey", "Please enter", "Mozio_ApiKey", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 4, 206, false, null, null, null, "ApiKey" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168801L, null, false, (sbyte)2, null, "Email", "Please enter", "Mozio_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 4, 206, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168900L, null, false, "", "MerchantId", "Please enter", "Sctt_MerchantId", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 208, false, null, null, null, "MerchantId" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413168901L, null, false, (sbyte)2, null, "Email", "Please enter", "Sctt_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 2, 208, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413169000L, null, false, "", "ApiKey", "Please enter", "Eyounz_ApiKey", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 2, 209, false, null, null, null, "ApiKey" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DataSecrecyType", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[] { 1344583947413169001L, null, false, (sbyte)2, null, "Email", "Please enter", "Eyounz_Email", "FieldInput", null, false, "50", null, false, "请输入", null, false, null, "[{\"code\":null,\"name\":\"电子邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"电子邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]", (sbyte)0, 2, 209, false, null, null, null, "电子邮箱" });

            migrationBuilder.InsertData(
                table: "SupplierApiFields",
                columns: new[] { "Id", "Append", "Controls", "DefaultValue", "EnFieldName", "EnPlaceholder", "FieldCode", "FieldComponentType", "IsMultiple", "IsRequired", "Maxlength", "MinLength", "Number", "Placeholder", "Precision", "Radio", "Remark", "RulerFilter", "Sort", "SupplierApiParentType", "SupplierApiType", "Textarea", "UIExtend", "Unit", "ValueRange", "ZhFieldName" },
                values: new object[,]
                {
                    { 1344583947413169100L, null, false, "", "UserName", "Please enter", "PAHInsurance_UserName", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 8, 301, false, null, null, null, "UserName" },
                    { 1344583947413169101L, null, false, "", "Password", "Please enter", "PAHInsurance_Password", "FieldInput", null, true, null, null, false, "请输入", null, false, null, null, (sbyte)0, 8, 301, false, null, null, null, "Password" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_SupplierApiFields_FieldCode",
                table: "SupplierApiFields",
                column: "FieldCode");

            migrationBuilder.CreateIndex(
                name: "IX_SupplierApiSettingField_SupplierApiSettingId",
                table: "SupplierApiSettingField",
                column: "SupplierApiSettingId");

            migrationBuilder.CreateIndex(
                name: "IX_SupplierApiSettingField_TenantId",
                table: "SupplierApiSettingField",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SupplierApi");

            migrationBuilder.DropTable(
                name: "SupplierApiFields");

            migrationBuilder.DropTable(
                name: "SupplierApiSettingField");
        }
    }
}
