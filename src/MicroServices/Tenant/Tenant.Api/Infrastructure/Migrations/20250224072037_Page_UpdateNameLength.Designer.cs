// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Tenant.Api.Infrastructure;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250224072037_Page_UpdateNameLength")]
    partial class Page_UpdateNameLength
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Tenant.Api.Model.Agency", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("AgencyRecencyStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("AgencyType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<string>("AppendicesOfContractPath")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("BusinessLicensePath")
                        .HasColumnType("varchar(255)");

                    b.Property<int?>("BusinessVolume")
                        .HasColumnType("int");

                    b.Property<sbyte>("CertificationStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ChannelType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("CityCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Contact")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(32)");

                    b.Property<sbyte>("ContractStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)2);

                    b.Property<long?>("CountryCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long?>("CustomerOperationId")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerPotentialSize")
                        .HasColumnType("bigint");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(40)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FinancialStaff")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("FinancialStaffNumber")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("IndustryInvolved")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("IsCustom")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("IsKA")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("IsVip")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<string>("LicenceNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PaymentPassword")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("PriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<long?>("PromotionTraceId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ProvinceCode")
                        .HasColumnType("bigint");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(255)");

                    b.Property<sbyte>("RegisterSource")
                        .HasColumnType("tinyint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("SettlementPeriod")
                        .HasColumnType("tinyint");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasColumnType("varchar(16)");

                    b.Property<long?>("SignSubjectId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TenantDepartmentId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WeilingCompanyId")
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Agency", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            AgencyRecencyStatus = (sbyte)0,
                            AgencyType = (sbyte)0,
                            CertificationStatus = (sbyte)0,
                            ChannelType = (sbyte)2,
                            Contact = "林燕",
                            ContactNumber = "19121821857",
                            ContractStatus = (sbyte)0,
                            CreateTime = new DateTime(2022, 5, 25, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            CurrencyCode = "CNY",
                            CustomerPotentialSize = 0L,
                            Enable = true,
                            FinancialStaff = "石珍",
                            FinancialStaffNumber = "13825770238",
                            FullName = "广州汇登信息科技有限公司",
                            IsCustom = (sbyte)0,
                            IsKA = (sbyte)0,
                            IsVip = (sbyte)1,
                            LicenceNo = "1",
                            PriceGroupId = 0L,
                            RegisterSource = (sbyte)0,
                            Remark = "",
                            SettlementPeriod = (sbyte)2,
                            ShortName = "汇登科技",
                            TenantId = 0L,
                            UpdateTime = new DateTime(2022, 5, 25, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyAgreement", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AgreementType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("CheckOtaPriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("mediumtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Language")
                        .HasColumnType("varchar(32)");

                    b.Property<int>("Level")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("LoginImagePath")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<long?>("NotCheckOtaPriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgreementType", "TenantId", "Language")
                        .IsUnique();

                    b.ToTable("AgencyAgreement", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyAgreementPopup", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ChangeStatusTime")
                        .HasColumnType("datetime");

                    b.Property<int>("PopupCount")
                        .HasColumnType("int");

                    b.Property<bool>("PopupStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("PopupTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("timestamp(6)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyAgreementPopup", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyAgreementRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgreementName")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("CheckOta")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ConfirmContent")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime>("ConfirmTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("mediumtext");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyAgreementRecord", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyApiSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Account")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AesKey")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AesLv")
                        .HasColumnType("varchar(64)");

                    b.Property<int>("AgencyApiType")
                        .HasColumnType("int")
                        .HasComment("API分销商类型");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CodeTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FeiZhuShopName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgencyApiType", "TenantId")
                        .IsUnique();

                    b.ToTable("AgencyApiSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyBankCard", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CustomName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyBankCard", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCertificationAudit", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("CreateName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyCertificationAudit", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCredit", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Balance")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("CreditLine")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<sbyte>("DimensionType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("EarlyWarningRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NoticeEmail")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NoticePhone")
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("Status")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgencyId", "TenantId")
                        .IsUnique();

                    b.ToTable("AgencyCredit", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCreditCharge", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("ChargeStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ChargeType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ExtNo")
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("PayChannel")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("ReceiptSettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyCreditCharge", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCreditRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AfterBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("AfterCreditLine")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("AgencyCreditId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("ChangeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CreatorType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("CreditBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<sbyte?>("IsSettled")
                        .HasColumnType("tinyint");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("UniqueOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("AgencyCreditRecord", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelCalculateTask", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("BusinessTypes")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("RunTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("TaskType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelConfigId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelCalculateTask", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("varchar(64)");

                    b.Property<int>("Cycle")
                        .HasColumnType("int");

                    b.Property<int>("Day")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("mediumtext");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("EnableRunTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsRunNow")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("NextCycleRunTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("StartCycleRunTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigEquityCoupon", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigEquityDesId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<long>("CouponId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigEquityCoupon", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigEquityDes", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelSettingId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(1024)");

                    b.Property<sbyte>("EquityType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<string>("ImgUrl")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelSettingId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigEquityDes", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigExpenseTransform", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("CloseFrist")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TransformFromValue")
                        .HasColumnType("int");

                    b.Property<int>("TransformToValue")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelConfigId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigExpenseTransform", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelSettingId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(1024)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelSettingId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigHotel", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigHotelDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigHotelDetail", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigOvertimeHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelSettingId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(1024)");

                    b.Property<sbyte>("IsIgnore")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelSettingId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigOvertimeHotel", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigOvertimeHotelDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelOvertimeHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelOvertimeHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigOvertimeHotelDetail", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ImgUrl")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<int>("KipValue")
                        .HasColumnType("int");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("PriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("UpValue")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelConfigId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("CycleGrowUpValue")
                        .HasColumnType("int");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelDetail", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelDetailCalculateLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigSettingId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("CycleGrowUpValue")
                        .HasColumnType("int");

                    b.Property<sbyte?>("IsCustom")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("IsVip")
                        .HasColumnType("tinyint");

                    b.Property<int>("PreLevel")
                        .HasColumnType("int");

                    b.Property<long>("PrePriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<int>("SettingKipValue")
                        .HasColumnType("int");

                    b.Property<int>("SettingLevel")
                        .HasColumnType("int");

                    b.Property<long>("SettingPriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<int>("SettingUpValue")
                        .HasColumnType("int");

                    b.Property<sbyte>("TaskType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("AgencyLevelConfigSettingId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelDetailCalculateLog", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelDetailLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ConfigTransformFromValue")
                        .HasColumnType("int");

                    b.Property<int>("ConfigTransformToValue")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("OrderAmout")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrderNo")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(256)");

                    b.Property<int>("TransformToValue")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelDetailLog", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelDetailStatement", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ChangeValue")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("DetailLogId")
                        .HasColumnType("bigint");

                    b.Property<int>("NextValue")
                        .HasColumnType("int");

                    b.Property<int>("PreValue")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelDetailStatement", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyMonthlyReport", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AfterSaleFinancialHandleOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CheckInTotalSales")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("CheckOutTotalSales")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("CreditChangeAmountTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("EndCreditBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("EndCreditLine")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("EndReceiptPrepaymentBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("EndUnfinishAfterSaleFinancialHandleOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OffsetOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OnlineOffsetOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OnlineOrderPaymentTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OnlineRefundOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OrderDateTotalSales")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Period")
                        .HasColumnType("int");

                    b.Property<decimal>("ReceiptPrepaymentReChargeTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ReceiptPrepaymentRefundTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ReceiptSettlementOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("RefundOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("StartCreditBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("StartCreditLine")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("StartReceiptPrepaymentBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("StartUnfinishAfterSaleFinancialHandleOrderTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyMonthlyReport", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyPasswordRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyPasswordRecord", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyPriceGroup", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Default")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyPriceGroup", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyRecencyOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("RecencyTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyRecencyOrder", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencySecret", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Scope")
                        .HasColumnType("int");

                    b.Property<long>("SecretId")
                        .HasColumnType("bigint");

                    b.Property<string>("SecretValue")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgencyId", "Scope")
                        .IsUnique();

                    b.ToTable("AgencySecret", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencySecretHotelSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencySecretId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("OrderChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("PriceChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("RoomChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("StrategyChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "AgencySecretId");

                    b.ToTable("AgencySecretHotelSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencySupportStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Complaints")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ComplaintsWorkingHours")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CustomerLink")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("EnableWechatService")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("EnterpriseId")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PhoneNumberWorkingHours")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WechatQrcode")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("AgencySupportStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyTag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name")
                        .IsUnique();

                    b.ToTable("AgencyTag", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyTagItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyTagId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyTagItem", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BIndexPageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("B2BComponentType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("B2BIndexPageType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int>("Index")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BIndexPageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BIndexPageComponentItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BIndexPageComponentId")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BIndexPageComponentItem", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BIndexPageHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BIndexPageComponentItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("B2BPageHotelType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int?>("CityCode")
                        .HasColumnType("int");

                    b.Property<int?>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int?>("CountryCode")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ENName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<Point>("Location")
                        .HasColumnType("point");

                    b.Property<bool>("OnTop")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("Path")
                        .HasColumnType("longtext");

                    b.Property<int?>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("Region")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<decimal>("StarLevel")
                        .HasColumnType("decimal(2,1)");

                    b.Property<string>("SubRegion")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("WeightValue")
                        .HasColumnType("bigint");

                    b.Property<string>("ZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BIndexPageHotel", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BIndexPageTag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BIndexPageComponentItemId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TagId")
                        .HasColumnType("bigint");

                    b.Property<string>("TagName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BIndexPageTag", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BPageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ComponentType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BPageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BPopUp", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("B2BPopUpAgencyType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("B2BPopUpShowType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("BeginTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OperatorUserName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BPopUp", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BPopUpAgency", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BPopUpId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("B2BPopUpId");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BPopUpAgency", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BPopUpContent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("B2BPopUpContentSubType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("B2BPopUpContentType")
                        .HasColumnType("tinyint");

                    b.Property<long>("B2BPopUpId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Link")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("B2BPopUpId");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BPopUpContent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BPopUpLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BPopUpId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("RecordDate")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("B2BPopUpId");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BPopUpLog", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AppletDarenDescription")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AppletDarenImage")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("B2BTopicPageType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPage", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("B2BComponentType")
                        .HasColumnType("tinyint");

                    b.Property<long>("B2BTopicPageId")
                        .HasColumnType("bigint");

                    b.Property<int>("Index")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPageComponentItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BTopicPageComponentId")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPageComponentItem", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPageTag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(8)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPageTag", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPageTagItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BTopicPageId")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BTopicPageTagId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("B2BTopicPageId");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPageTagItem", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BWebConfiguration", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("B2BLanguageConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("B2BLogoConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("B2BMenuConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("B2BTitleConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BWebConfiguration", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.CitOpenTenantConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AppKey")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AppName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AppSecret")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ContactNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPerson")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CorpName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("OpenChannelCallbackUrl")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OpenSupplierCallbackUrl")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("CitOpenTenantConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.Contact", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("Channel")
                        .HasColumnType("varchar(64)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Destination")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("IndustryType")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Job")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "AgencyId", "Type", "PhoneNumber")
                        .IsUnique();

                    b.ToTable("Contact", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.DraftPage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("BackHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BgColor")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BgImage")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FontColor")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("PageId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PageType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Picture")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DraftPage", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.FinancialSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsSupportTransfer")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ReceivablesAccounts")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TransferAccounts")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("FinancialSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.Focussend", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<ulong>("AgencySyncIsEnabled")
                        .HasColumnType("bit");

                    b.Property<ulong>("ApiHotelSyncIsEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("AppKey")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("AppPassWord")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<ulong>("ContactsSyncIsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("MasterSecret")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Focussend", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.NavigationBarComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SelectedColor")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UnSelectedColor")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("NavigationBarComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.PageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("PageId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("PageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.ProductSupportStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("ProductType")
                        .HasColumnType("int")
                        .HasComment("产品类型 1:票券产品 2:邮寄产品 3:日历房酒店");

                    b.Property<long>("SupportSatffId")
                        .HasColumnType("bigint");

                    b.Property<int>("SupportStaffType")
                        .HasColumnType("int")
                        .HasComment("客服类型 1:售前 2:售后");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductSupportStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.PublishedPage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("BackHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BgColor")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BgImage")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FontColor")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("PageType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Picture")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("PublishedPage", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.RegionConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("RegionConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.RegionConfigScope", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("CountryCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(255)");

                    b.Property<long?>("ProvinceCode")
                        .HasColumnType("bigint");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(255)");

                    b.Property<long>("RegionConfigId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("RegionConfigScope", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.RegionConfigUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("RegionConfigId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("RegionConfigUser", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SearchPageConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsShow")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Type")
                        .IsUnique();

                    b.ToTable("SearchPageConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SignSubject", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("SignSubject", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.Supplier", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AbroadAccountAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AbroadAccountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("AppendicesOfContractPath")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("BankAccountType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1)
                        .HasComment("银行账户类型 1:国内 2:国外 3:第三方银行账户");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("ChannelType")
                        .HasColumnType("int")
                        .HasComment("渠道类型 1:自有渠道 2:平台渠道");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<int>("Enabled")
                        .HasColumnType("int");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<int>("HasOperationRight")
                        .HasColumnType("int");

                    b.Property<sbyte?>("PriceAdjustmentType")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte?>("PriceBasisType")
                        .HasColumnType("tinyint");

                    b.Property<int>("SettlementMode")
                        .HasColumnType("int")
                        .HasComment("结算方式 1:底价 2:实收金额");

                    b.Property<int>("SettlementPeriod")
                        .HasColumnType("int")
                        .HasComment("结算周期 1:单结 2:周结 3:半月结 4:月结");

                    b.Property<int>("SettlementType")
                        .HasColumnType("int")
                        .HasComment("结算类型 1:线上 2:线下");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasColumnType("varchar(10)");

                    b.Property<long?>("SignSubjectId")
                        .HasColumnType("bigint");

                    b.Property<int>("SupplierType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1)
                        .HasComment("供应商类型 1:线下 2:API");

                    b.Property<string>("SwiftCode")
                        .HasColumnType("varchar(200)");

                    b.Property<long?>("TenantBankAccountId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TenantDepartmentId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Supplier", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SupplierApiSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AppKey")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AppSecret")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AuthToken")
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NotificationSecret")
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Password")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("SettingJson")
                        .HasColumnType("text");

                    b.Property<int>("SupplierApiParentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("SupplierApiType")
                        .HasColumnType("int")
                        .HasComment("API供应商类型 1:HOP 2:携程 3:美团 201:客路玩乐");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("UserName")
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "SupplierApiType")
                        .IsUnique();

                    b.ToTable("SupplierApiSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SupplierCreditCharge", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AttachmentPath")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("ChargeStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ChargeType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ContactName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(50)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("text");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SupplierCreditCharge", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SupportStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsAutoAfterSale")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAutoPreSale")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsWeChatAppletSale")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("QrcodeUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SupportStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.Tenant", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPerson")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContractFile")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("ContractStatus")
                        .HasColumnType("int")
                        .HasComment("签约状态 1:未签约 2:已签约");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("FinancialStaff")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FinancialStaffNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("IndustryType")
                        .HasColumnType("int");

                    b.Property<string>("Logo")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("OperationStatus")
                        .HasColumnType("int")
                        .HasComment("运营状态 1:待跟进 2:运营中 3:已停用");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CityCode");

                    b.HasIndex("ProvinceCode");

                    b.ToTable("Tenant", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.TenantDepartment", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantDepartment", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.TenantServiceStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ServiceStaffType")
                        .HasColumnType("int")
                        .HasComment("服务人员类型 1:销售 2:运营");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("TenantServiceStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.TenantSysConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AgencyOperation")
                        .HasColumnType("int")
                        .HasComment("代运营模式 1:不开启 2:系统服务 3:系统服务加运营服务");

                    b.Property<decimal>("AgencyOperationRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("代运营返佣比例");

                    b.Property<int>("AgencyOperationSettlement")
                        .HasColumnType("int")
                        .HasComment("代运营结算方式 1:底价 2:返佣");

                    b.Property<int>("AgentInvoice")
                        .HasColumnType("int")
                        .HasComment("是否代开发票 1:不代开 2:代开");

                    b.Property<decimal>("AgentInvoiceRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("代开发票比例");

                    b.Property<int?>("MerchantNoType")
                        .HasColumnType("int")
                        .HasComment("支付号类型 1:商户注册账户, 2:汇智代理账户");

                    b.Property<decimal>("PaymentFeeRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("支付手续费率");

                    b.Property<decimal>("PlatformCommissionRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("平台佣金费率");

                    b.Property<string>("SettlementAccountName")
                        .HasColumnType("varchar(50)")
                        .HasComment("结算账户--开户名称");

                    b.Property<string>("SettlementAccountNo")
                        .HasColumnType("varchar(100)")
                        .HasComment("结算账户--银行账号");

                    b.Property<string>("SettlementBankCode")
                        .HasColumnType("varchar(32)")
                        .HasComment("结算账户--开户银行编号");

                    b.Property<string>("SettlementBranchName")
                        .HasColumnType("varchar(50)")
                        .HasComment("结算账户--分行名称");

                    b.Property<string>("Subdomain")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("域名");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Subdomain");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantSysConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.Weiling", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AppId")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("AppSecret")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<ulong>("ContactsSyncIsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("EncodingAesKey")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("VestingId")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("VestingName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Weiling", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.XbongbongApi", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ApiToken")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Corpid")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FromMapping")
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("UserId")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WebHookToken")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("XbongbongApi", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCreditCharge", b =>
                {
                    b.OwnsOne("Tenant.Api.Model.TenantAccountInfo", "AccountInfo", b1 =>
                        {
                            b1.Property<long>("AgencyCreditChargeId")
                                .HasColumnType("bigint");

                            b1.Property<string>("AccountName")
                                .HasColumnType("varchar(100)");

                            b1.Property<string>("AccountNo")
                                .HasColumnType("varchar(64)");

                            b1.Property<string>("BankCode")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("BankName")
                                .HasColumnType("varchar(64)");

                            b1.Property<string>("BranchName")
                                .HasColumnType("longtext");

                            b1.Property<string>("Proof")
                                .HasColumnType("varchar(200)");

                            b1.Property<long>("TenantBankAccountId")
                                .HasColumnType("bigint");

                            b1.HasKey("AgencyCreditChargeId");

                            b1.ToTable("AgencyCreditCharge");

                            b1.WithOwner()
                                .HasForeignKey("AgencyCreditChargeId");
                        });

                    b.Navigation("AccountInfo");
                });
#pragma warning restore 612, 618
        }
    }
}
