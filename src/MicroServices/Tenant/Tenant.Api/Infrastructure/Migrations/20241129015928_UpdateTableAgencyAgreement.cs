using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class UpdateTableAgencyAgreement : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "CheckO<PERSON>",
                table: "AgencyAgreementRecord",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "BatchVers<PERSON>",
                table: "AgencyAgreementPopup",
                type: "char(36)",
                nullable: true,
                collation: "ascii_general_ci");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CheckO<PERSON>",
                table: "AgencyAgreementRecord");

            migrationBuilder.DropColumn(
                name: "BatchVers<PERSON>",
                table: "AgencyAgreementPopup");
        }
    }
}
