using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class AddAgencyMonthlyReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AgencyMonthlyReport",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    AgencyId = table.Column<long>(type: "bigint", nullable: false),
                    Period = table.Column<int>(type: "int", nullable: false),
                    CurrencyCode = table.Column<string>(type: "varchar(32)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    StartCreditLine = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    StartCreditBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    StartReceiptPrepaymentBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OrderDateTotalSales = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CheckInTotalSales = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CheckOutTotalSales = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OffsetOrderTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    RefundOrderTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ReceiptSettlementOrderTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    AfterSaleFinancialHandleOrderTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OnlineOrderPaymentTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OnlineRefundOrderTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OnlineOffsetOrderTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ReceiptPrepaymentReChargeTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ReceiptPrepaymentRefundTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EndCreditLine = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EndCreditBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EndReceiptPrepaymentBalance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyMonthlyReport", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyMonthlyReport_TenantId",
                table: "AgencyMonthlyReport",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgencyMonthlyReport");
        }
    }
}
