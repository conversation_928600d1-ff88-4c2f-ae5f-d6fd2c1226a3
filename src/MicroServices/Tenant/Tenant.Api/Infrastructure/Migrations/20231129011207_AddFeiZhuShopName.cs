using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class AddFeiZhuShopName : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FeiZhuShopName",
                table: "AgencyApiSetting",
                type: "varchar(64)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FeiZhuShopName",
                table: "AgencyApiSetting");
        }
    }
}
