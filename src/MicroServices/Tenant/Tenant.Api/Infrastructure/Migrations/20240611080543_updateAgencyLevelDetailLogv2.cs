using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class updateAgencyLevelDetailLogv2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "BalanceOrderAmout",
                table: "AgencyLevelDetailLog",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<sbyte>(
                name: "Status",
                table: "AgencyLevelDetailLog",
                type: "tinyint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BalanceOrderAmout",
                table: "AgencyLevelDetailLog");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AgencyLevelDetailLog");
        }
    }
}
