using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class AddTableAgreementRecordPopup : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AgencyAgreementPopup",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    AgencyId = table.Column<long>(type: "bigint", nullable: false),
                    AgencyUserId = table.Column<long>(type: "bigint", nullable: false),
                    ChangeStatusTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastPopupTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    IsPopup = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    PopupCount = table.Column<int>(type: "int", nullable: false),
                    PopupStatus = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    RowVersion = table.Column<DateTime>(type: "timestamp(6)", rowVersion: true, nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.ComputedColumn),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyAgreementPopup", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "AgencyAgreementRecord",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    AgencyId = table.Column<sbyte>(type: "tinyint", nullable: false),
                    AgencyUserId = table.Column<sbyte>(type: "tinyint", nullable: false),
                    AgreementName = table.Column<string>(type: "varchar(255)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ConfirmTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    ConfirmContent = table.Column<string>(type: "varchar(255)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Content = table.Column<string>(type: "mediumtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyAgreementRecord", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyAgreementPopup_AgencyId",
                table: "AgencyAgreementPopup",
                column: "AgencyId");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyAgreementPopup_TenantId",
                table: "AgencyAgreementPopup",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyAgreementRecord_AgencyId",
                table: "AgencyAgreementRecord",
                column: "AgencyId");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyAgreementRecord_TenantId",
                table: "AgencyAgreementRecord",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgencyAgreementPopup");

            migrationBuilder.DropTable(
                name: "AgencyAgreementRecord");
        }
    }
}
