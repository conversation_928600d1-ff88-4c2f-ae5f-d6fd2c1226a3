using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class AgencyCertificationAudit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "BankAccountType",
                table: "Supplier",
                type: "int",
                nullable: false,
                defaultValue: 1,
                comment: "银行账户类型 1:国内 2:国外 3:第三方银行账户",
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 1,
                oldComment: "银行账户类型 1:国内 2:国外");

            migrationBuilder.AlterColumn<sbyte>(
                name: "CertificationStatus",
                table: "Agency",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0,
                oldClrType: typeof(bool),
                oldType: "tinyint(1)",
                oldNullable: true);

            migrationBuilder.AddColumn<long>(
                name: "CityCode",
                table: "Agency",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CityName",
                table: "Agency",
                type: "varchar(255)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<long>(
                name: "CountryCode",
                table: "Agency",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryName",
                table: "Agency",
                type: "varchar(255)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<long>(
                name: "ProvinceCode",
                table: "Agency",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProvinceName",
                table: "Agency",
                type: "varchar(255)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "AgencyCertificationAudit",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    AgencyId = table.Column<long>(type: "bigint", nullable: false),
                    AgencyName = table.Column<string>(type: "varchar(256)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    Status = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Operator = table.Column<string>(type: "varchar(256)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OperatorId = table.Column<long>(type: "bigint", nullable: true),
                    FinishTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    Remark = table.Column<string>(type: "varchar(256)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyCertificationAudit", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyCertificationAudit_AgencyId",
                table: "AgencyCertificationAudit",
                column: "AgencyId");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyCertificationAudit_TenantId",
                table: "AgencyCertificationAudit",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgencyCertificationAudit");

            migrationBuilder.DropColumn(
                name: "CityCode",
                table: "Agency");

            migrationBuilder.DropColumn(
                name: "CityName",
                table: "Agency");

            migrationBuilder.DropColumn(
                name: "CountryCode",
                table: "Agency");

            migrationBuilder.DropColumn(
                name: "CountryName",
                table: "Agency");

            migrationBuilder.DropColumn(
                name: "ProvinceCode",
                table: "Agency");

            migrationBuilder.DropColumn(
                name: "ProvinceName",
                table: "Agency");

            migrationBuilder.AlterColumn<int>(
                name: "BankAccountType",
                table: "Supplier",
                type: "int",
                nullable: false,
                defaultValue: 1,
                comment: "银行账户类型 1:国内 2:国外",
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 1,
                oldComment: "银行账户类型 1:国内 2:国外 3:第三方银行账户");

            migrationBuilder.AlterColumn<bool>(
                name: "CertificationStatus",
                table: "Agency",
                type: "tinyint(1)",
                nullable: true,
                oldClrType: typeof(sbyte),
                oldType: "tinyint");
        }
    }
}
