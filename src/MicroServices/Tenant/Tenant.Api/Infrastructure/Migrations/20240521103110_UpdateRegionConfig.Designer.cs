// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Tenant.Api.Infrastructure;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20240521103110_UpdateRegionConfig")]
    partial class UpdateRegionConfig
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Tenant.Api.Model.Agency", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AgencyType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<string>("AppendicesOfContractPath")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("BusinessLicensePath")
                        .HasColumnType("varchar(255)");

                    b.Property<sbyte>("CertificationStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ChannelType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("CityCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Contact")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(32)");

                    b.Property<sbyte>("ContractStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)2);

                    b.Property<long?>("CountryCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FinancialStaff")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("FinancialStaffNumber")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("LicenceNo")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("PriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ProvinceCode")
                        .HasColumnType("bigint");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(255)");

                    b.Property<sbyte>("RegisterSource")
                        .HasColumnType("tinyint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("SettlementPeriod")
                        .HasColumnType("tinyint");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasColumnType("varchar(16)");

                    b.Property<long?>("SignSubjectId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Agency", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1L,
                            AgencyType = (sbyte)0,
                            CertificationStatus = (sbyte)0,
                            ChannelType = (sbyte)2,
                            Contact = "林燕",
                            ContactNumber = "19121821857",
                            ContractStatus = (sbyte)0,
                            CreateTime = new DateTime(2022, 5, 25, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            CurrencyCode = "CNY",
                            Enable = true,
                            FinancialStaff = "石珍",
                            FinancialStaffNumber = "13825770238",
                            FullName = "广州汇登信息科技有限公司",
                            LicenceNo = "1",
                            PriceGroupId = 0L,
                            RegisterSource = (sbyte)0,
                            Remark = "",
                            SettlementPeriod = (sbyte)2,
                            ShortName = "汇登科技",
                            TenantId = 0L,
                            UpdateTime = new DateTime(2022, 5, 25, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyAgreement", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AgreementType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("mediumtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Language")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("LoginImagePath")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgreementType", "TenantId", "Language")
                        .IsUnique();

                    b.ToTable("AgencyAgreement", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyApiSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Account")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AesKey")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AesLv")
                        .HasColumnType("varchar(64)");

                    b.Property<int>("AgencyApiType")
                        .HasColumnType("int")
                        .HasComment("API分销商类型");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CodeTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FeiZhuShopName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgencyApiType", "TenantId")
                        .IsUnique();

                    b.ToTable("AgencyApiSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyBankCard", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CustomName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyBankCard", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCertificationAudit", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("CreateName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreateUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyCertificationAudit", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCredit", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Balance")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("CreditLine")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("EarlyWarningRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NoticeEmail")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("NoticePhone")
                        .HasColumnType("varchar(64)");

                    b.Property<bool>("Status")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgencyId", "TenantId")
                        .IsUnique();

                    b.ToTable("AgencyCredit", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCreditCharge", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("ChargeStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ExtNo")
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("PayChannel")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("ReceiptSettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyCreditCharge", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCreditRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AfterBalance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("AfterCreditLine")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("AgencyCreditId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("ChangeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CreatorType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("CreditBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long>("UniqueOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("AgencyCreditRecord", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Cycle")
                        .HasColumnType("int");

                    b.Property<int>("Day")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(1024)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRunNow")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigEquityDes", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelSettingId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("ImgUrl")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelSettingId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigEquityDes", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigExpenseTransform", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TransformFromValue")
                        .HasColumnType("int");

                    b.Property<int>("TransformToValue")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelConfigId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigExpenseTransform", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelSettingId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(1024)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelSettingId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigHotel", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigHotelDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelHotelId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigHotelDetail", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelConfigSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyLevelConfigId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ImgUrl")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<int>("KipValue")
                        .HasColumnType("int");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("PriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("UpValue")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AgencyLevelConfigId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelConfigSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("CycleGrowUpValue")
                        .HasColumnType("int");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelDetail", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelDetailLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("OrderAmout")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrderNo")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelDetailLog", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyLevelDetailStatement", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ChangeValue")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("DetailLogId")
                        .HasColumnType("bigint");

                    b.Property<int>("NextValue")
                        .HasColumnType("int");

                    b.Property<int>("PreValue")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("AgencyId");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyLevelDetailStatement", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyPriceGroup", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Default")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyPriceGroup", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyRecencyOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("RecencyTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyRecencyOrder", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencySecret", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Scope")
                        .HasColumnType("int");

                    b.Property<long>("SecretId")
                        .HasColumnType("bigint");

                    b.Property<string>("SecretValue")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("AgencyId", "Scope")
                        .IsUnique();

                    b.ToTable("AgencySecret", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencySecretHotelSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencySecretId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("OrderChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("PriceChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("RoomChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("StrategyChangeNotify")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "AgencySecretId");

                    b.ToTable("AgencySecretHotelSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencySupportStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WechatQrcode")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("AgencySupportStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyTag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Name")
                        .IsUnique();

                    b.ToTable("AgencyTag", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyTagItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyTagId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyTagItem", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BIndexPageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("B2BComponentType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Index")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BIndexPageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BIndexPageComponentItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BIndexPageComponentId")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BIndexPageComponentItem", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BPageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ComponentType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BPageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPage", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("B2BComponentType")
                        .HasColumnType("tinyint");

                    b.Property<long>("B2BTopicPageId")
                        .HasColumnType("bigint");

                    b.Property<int>("Index")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BTopicPageComponentItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("B2BTopicPageComponentId")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BTopicPageComponentItem", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.B2BWebConfiguration", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("B2BLanguageConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("B2BLogoConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("B2BMenuConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("B2BTitleConfigs")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("B2BWebConfiguration", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.DraftPage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("BackHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BgColor")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BgImage")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FontColor")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("PageId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PageType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Picture")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DraftPage", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.FinancialSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsSupportTransfer")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ReceivablesAccounts")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TransferAccounts")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("FinancialSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.NavigationBarComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SelectedColor")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UnSelectedColor")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("NavigationBarComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.PageComponent", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .HasColumnType("varchar(4000)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("PageId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("PageComponent", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.ProductSupportStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("ProductType")
                        .HasColumnType("int")
                        .HasComment("产品类型 1:票券产品 2:邮寄产品 3:日历房酒店");

                    b.Property<long>("SupportSatffId")
                        .HasColumnType("bigint");

                    b.Property<int>("SupportStaffType")
                        .HasColumnType("int")
                        .HasComment("客服类型 1:售前 2:售后");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductSupportStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.PublishedPage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("BackHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BgColor")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BgImage")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enable")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FontColor")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsHomePage")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte>("PageType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Picture")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("PublishedPage", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.RegionConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("RegionConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.RegionConfigScope", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("CountryCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(255)");

                    b.Property<long?>("ProvinceCode")
                        .HasColumnType("bigint");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(255)");

                    b.Property<long>("RegionConfigId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("RegionConfigScope", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.RegionConfigUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("RegionConfigId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("RegionConfigUser", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SearchPageConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsShow")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "Type")
                        .IsUnique();

                    b.ToTable("SearchPageConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SignSubject", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("SignSubject", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.Supplier", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AbroadAccountAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AbroadAccountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("AppendicesOfContractPath")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("BankAccount")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("BankAccountType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1)
                        .HasComment("银行账户类型 1:国内 2:国外 3:第三方银行账户");

                    b.Property<string>("BankCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("ChannelType")
                        .HasColumnType("int")
                        .HasComment("渠道类型 1:自有渠道 2:平台渠道");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<int>("Enabled")
                        .HasColumnType("int");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("HasOperationRight")
                        .HasColumnType("int");

                    b.Property<int>("SettlementMode")
                        .HasColumnType("int")
                        .HasComment("结算方式 1:底价 2:实收金额");

                    b.Property<int>("SettlementPeriod")
                        .HasColumnType("int")
                        .HasComment("结算周期 1:单结 2:周结 3:半月结 4:月结");

                    b.Property<int>("SettlementType")
                        .HasColumnType("int")
                        .HasComment("结算类型 1:线上 2:线下");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasColumnType("varchar(10)");

                    b.Property<long?>("SignSubjectId")
                        .HasColumnType("bigint");

                    b.Property<int>("SupplierType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1)
                        .HasComment("供应商类型 1:线下 2:API");

                    b.Property<string>("SwiftCode")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Supplier", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SupplierApiSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AppKey")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AppSecret")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("AuthToken")
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NotificationSecret")
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Password")
                        .HasColumnType("varchar(64)");

                    b.Property<int>("SupplierApiParentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("SupplierApiType")
                        .HasColumnType("int")
                        .HasComment("API供应商类型 1:HOP 2:携程 3:美团 201:客路玩乐");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("UserName")
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "SupplierApiType")
                        .IsUnique();

                    b.ToTable("SupplierApiSetting", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.SupportStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsAutoAfterSale")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAutoPreSale")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsWeChatAppletSale")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("QrcodeUrl")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SupportStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.Tenant", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPerson")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContractFile")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("ContractStatus")
                        .HasColumnType("int")
                        .HasComment("签约状态 1:未签约 2:已签约");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("FinancialStaff")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FinancialStaffNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("IndustryType")
                        .HasColumnType("int");

                    b.Property<string>("Logo")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("OperationStatus")
                        .HasColumnType("int")
                        .HasComment("运营状态 1:待跟进 2:运营中 3:已停用");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CityCode");

                    b.HasIndex("ProvinceCode");

                    b.ToTable("Tenant", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.TenantServiceStaff", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ServiceStaffType")
                        .HasColumnType("int")
                        .HasComment("服务人员类型 1:销售 2:运营");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("TenantServiceStaff", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.TenantSysConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AgencyOperation")
                        .HasColumnType("int")
                        .HasComment("代运营模式 1:不开启 2:系统服务 3:系统服务加运营服务");

                    b.Property<decimal>("AgencyOperationRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("代运营返佣比例");

                    b.Property<int>("AgencyOperationSettlement")
                        .HasColumnType("int")
                        .HasComment("代运营结算方式 1:底价 2:返佣");

                    b.Property<int>("AgentInvoice")
                        .HasColumnType("int")
                        .HasComment("是否代开发票 1:不代开 2:代开");

                    b.Property<decimal>("AgentInvoiceRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("代开发票比例");

                    b.Property<int?>("MerchantNoType")
                        .HasColumnType("int")
                        .HasComment("支付号类型 1:商户注册账户, 2:汇智代理账户");

                    b.Property<decimal>("PaymentFeeRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("支付手续费率");

                    b.Property<decimal>("PlatformCommissionRate")
                        .HasColumnType("decimal(10,4)")
                        .HasComment("平台佣金费率");

                    b.Property<string>("SettlementAccountName")
                        .HasColumnType("varchar(50)")
                        .HasComment("结算账户--开户名称");

                    b.Property<string>("SettlementAccountNo")
                        .HasColumnType("varchar(100)")
                        .HasComment("结算账户--银行账号");

                    b.Property<string>("SettlementBankCode")
                        .HasColumnType("varchar(32)")
                        .HasComment("结算账户--开户银行编号");

                    b.Property<string>("SettlementBranchName")
                        .HasColumnType("varchar(50)")
                        .HasComment("结算账户--分行名称");

                    b.Property<string>("Subdomain")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("域名");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Subdomain");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantSysConfig", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.XbongbongApi", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ApiToken")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Corpid")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FromMapping")
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("UserId")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WebHookToken")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("XbongbongApi", (string)null);
                });

            modelBuilder.Entity("Tenant.Api.Model.AgencyCreditCharge", b =>
                {
                    b.OwnsOne("Tenant.Api.Model.TenantAccountInfo", "AccountInfo", b1 =>
                        {
                            b1.Property<long>("AgencyCreditChargeId")
                                .HasColumnType("bigint");

                            b1.Property<string>("AccountName")
                                .HasColumnType("varchar(100)");

                            b1.Property<string>("AccountNo")
                                .HasColumnType("varchar(64)");

                            b1.Property<string>("BankCode")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("BankName")
                                .HasColumnType("varchar(64)");

                            b1.Property<string>("BranchName")
                                .HasColumnType("longtext");

                            b1.Property<string>("Proof")
                                .HasColumnType("varchar(200)");

                            b1.Property<long>("TenantBankAccountId")
                                .HasColumnType("bigint");

                            b1.HasKey("AgencyCreditChargeId");

                            b1.ToTable("AgencyCreditCharge");

                            b1.WithOwner()
                                .HasForeignKey("AgencyCreditChargeId");
                        });

                    b.Navigation("AccountInfo");
                });
#pragma warning restore 612, 618
        }
    }
}
