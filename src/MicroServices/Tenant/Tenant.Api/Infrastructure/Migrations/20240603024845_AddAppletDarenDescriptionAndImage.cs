using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class AddAppletDarenDescriptionAndImage : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AppletDarenDescription",
                table: "B2BTopicPageComponent",
                type: "varchar(64)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "AppletDarenImage",
                table: "B2BTopicPageComponent",
                type: "varchar(200)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AppletDarenDescription",
                table: "B2BTopicPageComponent");

            migrationBuilder.DropColumn(
                name: "AppletDarenImage",
                table: "B2BTopicPageComponent");
        }
    }
}
