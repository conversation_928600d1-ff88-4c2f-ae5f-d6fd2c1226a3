using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class Remove_ContactIsVisitor : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Contact_TenantId_PhoneNumber",
                table: "Contact");

            migrationBuilder.DropColumn(
                name: "IsVisitorUser",
                table: "Contact");

            migrationBuilder.CreateIndex(
                name: "IX_Contact_TenantId_AgencyId_Type_PhoneNumber",
                table: "Contact",
                columns: new[] { "TenantId", "AgencyId", "Type", "PhoneNumber" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Contact_TenantId_AgencyId_Type_PhoneNumber",
                table: "Contact");

            migrationBuilder.AddColumn<ulong>(
                name: "IsVisitorUser",
                table: "Contact",
                type: "bit",
                nullable: false,
                defaultValue: 0ul);

            migrationBuilder.CreateIndex(
                name: "IX_Contact_TenantId_PhoneNumber",
                table: "Contact",
                columns: new[] { "TenantId", "PhoneNumber" },
                unique: true);
        }
    }
}
