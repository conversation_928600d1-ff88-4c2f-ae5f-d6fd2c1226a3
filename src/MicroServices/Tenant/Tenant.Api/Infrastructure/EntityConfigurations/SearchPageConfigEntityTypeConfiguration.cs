using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Tenant.Api.Infrastructure.EntityConfigurations;

public class SearchPageConfigEntityTypeConfiguration : TenantBaseConfiguration<SearchPageConfig>,
        IEntityTypeConfiguration<SearchPageConfig>
{
    public void Configure(EntityTypeBuilder<SearchPageConfig> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.Type)
               .HasColumnType("tinyint");

        builder.Property(s => s.IsShow)
               .HasColumnType("tinyint(1)");

        builder.Property(s => s.CreateTime)
                .HasColumnType("datetime")
                .IsRequired();

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        builder.HasIndex(s => new { s.TenantId, s.Type }).IsUnique();
    }
}
