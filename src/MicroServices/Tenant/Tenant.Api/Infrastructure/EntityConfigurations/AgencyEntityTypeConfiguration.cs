using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Tenant.Api.Infrastructure.EntityConfigurations
{
    public class AgencyEntityTypeConfiguration : TenantBaseConfiguration<Model.Agency>, IEntityTypeConfiguration<Model.Agency>
    {
        public void Configure(EntityTypeBuilder<Model.Agency> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.AgencyType)
                .HasColumnType("tinyint")
                .HasDefaultValue(AgencyType.Offline);

            builder.Property(s => s.FullName)
                .HasColumnType("varchar(256)")
                .IsRequired();

            builder.Property(s => s.ShortName)
                .HasColumnType("varchar(16)")
                .IsRequired();

            builder.Property(s => s.Contact)
                .HasColumnType("varchar(40)");

            builder.Property(s => s.ContactNumber)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.FinancialStaff)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.FinancialStaffNumber)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.ChannelType)
                .HasColumnType("tinyint");

            builder.Property(s => s.SettlementPeriod)
                .HasColumnType("tinyint");

            builder.Property(s => s.Enable)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.Remark)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.CurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString());

            builder.Property(s => s.AppendicesOfContractPath)
                .HasColumnType("text");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.SalespersonId)
                .HasColumnType("bigint");

            builder.Property(s => s.ContractStatus)
                .HasColumnType("tinyint")
                .HasDefaultValue(TenantContractStatus.Signed);

            builder.Property(s => s.BusinessLicensePath)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.LicenceNo)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.RegisterSource)
                .HasColumnType("tinyint");

            builder.Property(s => s.CertificationStatus)
                .HasColumnType("tinyint")
                .IsConcurrencyToken();

            builder.Property(s => s.SignSubjectId)
                .HasColumnType("bigint");

            builder.Property(s => s.CountryCode)
                .HasColumnType("bigint");

            builder.Property(s => s.CountryName)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.ProvinceCode)
                .HasColumnType("bigint");

            builder.Property(s => s.ProvinceName)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.CityCode)
                .HasColumnType("bigint");

            builder.Property(s => s.CityName)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.TenantDepartmentId)
               .HasColumnType("bigint");

            builder.Property(s => s.IsCustom)
                .HasColumnType("tinyint")
                .HasDefaultValue(false);

            builder.Property(s => s.AgencyRecencyStatus)
                .HasColumnType("tinyint")
                .HasDefaultValue(AgencyRecencyStatus.NotActivated);

            builder.Property(s => s.CustomerPotentialSize)
                .HasColumnType("bigint");

            builder.Property(s => s.IsKA)
                .HasColumnType("tinyint")
                .HasDefaultValue(false);

            builder.Property(s => s.Email)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.Address)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.IndustryInvolved)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.BusinessVolume)
                .HasColumnType("int");

            builder.Property(s => s.CustomerOperationId)
               .HasColumnType("bigint");

            builder.Property(s => s.WeilingCompanyId)
               .HasColumnType("varchar(100)");

            builder.Property(s => s.PaymentPassword)
              .HasColumnType("varchar(64)");

            builder.Property(s => s.IsVip)
               .HasColumnType("tinyint")
               .HasDefaultValue(true)
               .ValueGeneratedNever();

            builder.Property(s => s.PromotionTraceId)
                .HasColumnType("bigint");

            builder.Property(s => s.FinancialCountryDialCode)
               .HasColumnType("varchar(10)");

            builder.Property(s => s.ContactCountryDialCode)
               .HasColumnType("varchar(10)");

            builder.Property(s => s.RegisterParentSource)
              .HasColumnType("varchar(128)");

            builder.Property(s => s.RegisterSubSource)
                .HasColumnType("tinyint");

            builder.HasData(new Agency
            {
                Id = 1,
                FullName = "广州汇登信息科技有限公司",
                ShortName = "汇登科技",
                Contact = "林燕",
                ContactNumber = "19121821857",
                FinancialStaff = "石珍",
                FinancialStaffNumber = "13825770238",
                ChannelType = ChannelType.PlatformChannel,//平台渠道
                SettlementPeriod = SupplierSettlementPeriod.Weekly,
                Enable = true,
                Remark = string.Empty,
                CurrencyCode = Currency.CNY.ToString(),//货币类型
                CreateTime = new DateTime(2022, 5, 25),
                UpdateTime = new DateTime(2022, 5, 25),
                LicenceNo = "1"
            });
        }
    }
}
