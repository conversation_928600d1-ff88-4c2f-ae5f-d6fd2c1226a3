using Common.ServicesHttpClient;

namespace Tenant.Api.Extensions
{
    public static class ServicesAddressExtensions
    {
        #region Notify

        public static string Notify_QuerySign(this ServicesAddress address, long tenantId)
               => $"{address.Notify}/ShortMessage/QuerySign?tenantId={tenantId}";
        public static string Notify_CheckSignValid(this ServicesAddress address)
               => $"{address.Notify}/ShortMessage/CheckSignValid";

        #endregion

        #region User

        public static string User_SearchTenantUsers(this ServicesAddress address)
            => $"{address.User}/TenantUser/SearchUsers";

        public static string User_FindTenantUserRoles(this ServicesAddress address, long tenantUserId)
           => $"{address.User}/TenantUser/FindTenantUserRoles?tenantUserId={tenantUserId}";

        #endregion

        #region Permission

        public static string Permission_GetTenantPackage(this ServicesAddress address, long tenantId)
              => $"{address.Permission}/Package/GetTenantPackage?tenantId={tenantId}";

        public static string Permission_GetTenantRole(this ServicesAddress address, long tenantRoleId)
          => $"{address.Permission}/Role/GetTenantRole?roleId={tenantRoleId}";

        #endregion

        #region Payment

        public static string Payment_ReceiptPrepaymentRefund(this ServicesAddress address)
           => $"{address.Payment}/ReceiptPrepayment/Refund";

        public static string Payment_ReceiptPrepaymentOfflineRefundConfirm(this ServicesAddress address)
           => $"{address.Payment}/ReceiptPrepayment/OfflineRefundConfirm";

        public static string Payment_FinanceAgencyReceiptPrepaymentReport(this ServicesAddress address)
            => $"{address.Payment}/Finance/AgencyReceiptPrepaymentReport";

        public static string Payment_GetTenantBankAccount(this ServicesAddress address, long id)
       => $"{address.Payment}/TenantBankAccount/Get?id=" + id;

        #endregion

        #region Order

        public static string Order_ReceiptSettlementOrderGetByOrderId(this ServicesAddress address)
           => $"{address.Order}/ReceiptSettlementOrder/GetByOrderId";

        public static string Order_FinanceSearchAgencyOrderTotals(this ServicesAddress address)
            => $"{address.Order}/Finance/SearchAgencyOrderTotals";

        #endregion

        #region Hotel
        public static string Hotel_GetApiHotelDetail(this ServicesAddress address)
            => $"{address.Hotel}/ApiHotel/Detail";
        public static string Hotel_GetApiHotelDetails(this ServicesAddress address)
            => $"{address.Hotel}/ApiHotel/Details";
        public static string Hotel_GetHotelFirstPhoto(this ServicesAddress address)
            => $"{address.Hotel}/Hotel/GetHotelFirstPhoto";
        public static string Hotel_GetHotelTagByApiHotelIds(this ServicesAddress address)
            => $"{address.Hotel}/HotelTag/GetByApiHotelIdsV2";

        public static string Hotel_GetBySpecializedHotelIds(this ServicesAddress address)
            => $"{address.Hotel}/SpecializedHotelDetail/GetBySpecializedHotelIds";
        #endregion

        #region resource
        public static string Resource_GetHotelFirstPhoto(this ServicesAddress address)
            => $"{address.Resource}/Hotel/GetHotelFirstPhoto";

        public static string Resource_GetThirdHotHotel(this ServicesAddress address)
            => $"{address.Resource}/ThirdHotel/GetHotHotel";

        public static string Resource_GetHotelByHopHotelIds(this ServicesAddress address)
            => $"{address.Resource}/Hotel/GetByHopHotelIds";

        public static string Resource_SearchCountry(this ServicesAddress address)
            => $"{address.Resource}/Country/Search";

        public static string Resource_QueryCity(this ServicesAddress address)
            => $"{address.Resource}/City/Query";
        #endregion

        #region Marketing

        public static string Marketing_GetList(this ServicesAddress address)
          => $"{address.Marketing}/PromotionTrace/List";

        #endregion
    }
}
