using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;
using Contracts.Common.Tenant.DTOs.B2BTopicPageTag;
using Common.Swagger;

namespace Tenant.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class B2BTopicPageTagController : ControllerBase
{
    private readonly IB2BTopicPageTagService _b2BTopicPageTagService;

    public B2BTopicPageTagController(IB2BTopicPageTagService b2BTopicPageTagService)
    {
        _b2BTopicPageTagService = b2BTopicPageTagService;
    }

    /// <summary>
    /// 查询专题页标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchB2BTopicPageTagOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        var result = await _b2BTopicPageTagService.Search(input);
        return Ok(result);
    }

    /// <summary>
    /// 添加专题页标签
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.B2BTopicPageTagNameExist)]
    public async Task<IActionResult> Add(AddB2BTopicPageTagInput input)
    {
        var result = await _b2BTopicPageTagService.Add(input);
        return Ok(result);
    }

    /// <summary>
    /// 删除专题页标签
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Delete(long id)
    {
        await _b2BTopicPageTagService.Delete(id);
        return Ok();
    }
}
