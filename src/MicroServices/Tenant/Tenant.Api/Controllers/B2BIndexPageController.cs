using Common.Jwt;
using Contracts.Common.Tenant.DTOs.B2BIndexPage;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class B2BIndexPageController : ControllerBase 
{
    private readonly IB2BIndexPageService _b2BIndexPageService;

    public B2BIndexPageController(IB2BIndexPageService b2BIndexPageService)
    {
        _b2BIndexPageService = b2BIndexPageService;
    }

    /// <summary>
    /// 获取组件列表
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<B2BIndexPageComponentDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetComponents(GetComponentInput input)
    {
        var result = await _b2BIndexPageService.GetComponents(input);
        return Ok(result);
    }

    /// <summary>
    /// 设置序号排序
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> SetComponentIndex(SetComponentIndexInput input)
    {
        await _b2BIndexPageService.SetComponentIndex(input);
        return Ok();
    }

    /// <summary>
    /// 更新保存组件信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(UpdateComponentOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> UpdateComponent(UpdateComponentInput input)
    {
        var result = await _b2BIndexPageService.UpdateComponent(input);
        return Ok(result);
    }

    /// <summary>
    /// 删除专题页组件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> DeleteComponent(DeleteComponentInput input)
    {
        await _b2BIndexPageService.DeleteComponent(input);
        return Ok();
    }

    /// <summary>
    /// 添加专题页组件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(AddComponentOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AddComponent(AddComponentInput input)
    {
        return Ok(await _b2BIndexPageService.AddComponent(input));
    }

    /// <summary>
    /// 冗余首页数据信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SyncComponentItemData(List<SyncComponentItemDataInput> input)
    {
        await _b2BIndexPageService.SyncComponentItemData(HttpContext.GetTenantId(),input);
        return Ok();
    }

    /// <summary>
    /// 查询首页数据信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchComponentItemDataOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchComponentItemData(SearchComponentItemDataInput input)
    {
        var result = await _b2BIndexPageService.SearchComponentItemData(input);
        return Ok(result);
    }

    /// <summary>
    /// 符合部分条件的酒店数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchComponentItemDataOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchRecommendComponentItemData(SearchComponentItemDataInput input)
    {
        var result = await _b2BIndexPageService.SearchRecommendComponentItemData(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询b2b专题页列表数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<SearchB2BTopicPageDataOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchB2BTopicPageComponentItemData(SearchB2BTopicPageDataInput input)
    {
        var result = await _b2BIndexPageService.SearchB2BTopicPageComponentItemData(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询产品列表-汇智酒店下所有标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<long>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchRegionAllTags(SearchHotelTagsInput input)
    {
        var result = await _b2BIndexPageService.SearchRegionAllTags(input);
        return Ok(result);
    }

    /// <summary>
    /// 同步B2B首页酒店权重
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SyncB2BIndexPageHotel()
    {
        var result = await _b2BIndexPageService.SyncB2BIndexPageHotel();
        return Ok(result);
    }

    /// <summary>
    /// 检查是否含有酒店专题
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<CheckSpecializedHotelOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CheckSpecializedHotel(CheckSpecializedHotelInput input)
    {
        var result = await _b2BIndexPageService.CheckSpecializedHotel(input);
        return Ok(result);
    }
}
