using Common.Swagger;
using Contracts.Common.Tenant.DTOs;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class AgencySupportStaffController : ControllerBase
{
    private readonly IAgencySupportStaffService _agencySupportStaffService;

    public AgencySupportStaffController(IAgencySupportStaffService agencySupportStaffService)
    {
        _agencySupportStaffService = agencySupportStaffService;
    }

    [HttpGet]
    [ProducesResponseType(typeof(AgencySupportStaffDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get()
    {
        var result = await _agencySupportStaffService.Get();
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Set(AgencySupportStaffDto dto)
    {
        await _agencySupportStaffService.AddOrUpdate(dto);
        return Ok();
    }
}
