using Common.Swagger;
using Contracts.Common.Tenant.DTOs.Contact;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class ContactController : ControllerBase
{
    private readonly IContactService _contactService;

    public ContactController(IContactService contactService)
    {
        _contactService = contactService;
    }

    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(GetOutput))]
    public async Task<IActionResult> Get(long? id, string? pn, ContactSourceType? sourceType)
    {
        if(id == null && pn == null && sourceType == null) 
            return Ok();
        return Ok(await _contactService.Get(id, pn, sourceType));
    }

    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(PagingModel<ContactDto>))]
    public async Task<IActionResult> GetList(GetListInput input)
    {
        var result = await _contactService.SearchAsync(input);
        return Ok(result);
    }

    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(List<string>))]
    public async Task<IActionResult> SearchPhoneNumber(List<string> phones)
    {
        var result = await _contactService.SearchPhoneNumberAsync(phones);
        return Ok(result);
    }

    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(PagingModel<ContactDto>))]
    public async Task<IActionResult> Create(List<CreateInput> input)
    {
        await _contactService.Create(input);
        return Ok();
    }

    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(ContactDto))]
    public async Task<IActionResult> CreateOrUpdateVisitor(CreateOrUpdateVisitorInput input)
    {
        var result = await _contactService.CreateOrUpdateVisitor(input);
        return Ok(result);
    }

}
