using Common.Swagger;
using Contracts.Common.Tenant.DTOs.DingtalkApiConfig;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class DingtalkApiConfigController : ControllerBase
{
    private readonly IDingtalkApiConfigService _dingtalkApiConfigService;
    private readonly ITenantIdentify _tenantIdentify;
    public DingtalkApiConfigController(IDingtalkApiConfigService dingtalkApiConfigService,
        ITenantIdentify tenantIdentify)
    {
        _dingtalkApiConfigService = dingtalkApiConfigService;
        _tenantIdentify = tenantIdentify;
    }

    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(DingtalkApiConfigOutput))]
    public async Task<IActionResult> Get()
    {
        var tenantId = _tenantIdentify.GetTenantId();
        return Ok(await _dingtalkApiConfigService.GetByTenatId(tenantId));
    }

    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Save(DingtalkApiConfigSaveInput input)
    {
        var tenantId = _tenantIdentify.GetTenantId();
        var result = await _dingtalkApiConfigService.Save(input, tenantId);
        return Ok(result);
    }


}
