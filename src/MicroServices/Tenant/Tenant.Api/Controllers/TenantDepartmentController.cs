using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.Tenant.DTOs.TenantDepartment;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class TenantDepartmentController : ControllerBase
{

    private readonly ITenantDepartmentService _tenantDepartmentService;

    public TenantDepartmentController(ITenantDepartmentService tenantDepartmentService)
    {
        _tenantDepartmentService = tenantDepartmentService;
    }

    /// <summary>
    /// 新增
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Add(AddTenantDepartmentInput input)
    {
        await _tenantDepartmentService.Add(input);
        return Ok();
    }

    /// <summary>
    /// 查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchTenantDepartmentOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchTenantDepartmentInput input)
    {
        var result = await _tenantDepartmentService.Search(input);
        return Ok(result);
    }

    [HttpPost]
    public async Task<List<SearchTenantDepartmentOutput>> SearchByIds(long[] ids)
    {
        var result = await _tenantDepartmentService.SearchByIds(ids);
        return result;
    }
}
