using Contracts.Common.Tenant.DTOs.AgencyLevelConfigHotel;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;

/// <summary>
/// 
/// </summary>
[ApiController]
[Route("[controller]/[action]")]
public class AgencyLevelConfigHotelController : ControllerBase
{
    private readonly IAgencyLevelConfigHotelService _agencyLevelConfigHotelService;
    public AgencyLevelConfigHotelController(IAgencyLevelConfigHotelService agencyLevelConfigHotelService)
    {
        _agencyLevelConfigHotelService = agencyLevelConfigHotelService;
    }

    /// <summary>
    /// 通过酒店Id获取退改信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<AgencyLevelConfigHotelOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetByHotelIds(AgencyLevelConfigHotelInput input)
    {
        var result = await _agencyLevelConfigHotelService.GetByHotelIds(input);
        return Ok(result);
    }
}
