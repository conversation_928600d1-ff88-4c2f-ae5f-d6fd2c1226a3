using Common.Swagger;
using Contracts.Common.Tenant.DTOs.B2BTopicPage;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class B2BTopicPageController : ControllerBase
{
    private readonly IB2BTopicPageService _b2BTopicPageService;

    public B2BTopicPageController(IB2BTopicPageService b2BTopicPageService)
    {
        _b2BTopicPageService = b2BTopicPageService;
    }

    #region 专题页

    /// <summary>
    /// 添加专题页
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AddTopicPage(B2BTopicPageInput input)
    {
        return Ok(await _b2BTopicPageService.AddTopicPage(input));
    }

    /// <summary>
    /// 移除专题页
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> RemoveTopicPage(RemoveTopicPageInput input)
    {
        await _b2BTopicPageService.RemoveTopicPage(input);
        return Ok();
    }

    /// <summary>
    /// 获取全部专题页
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<B2BTopicPageOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetTopicPages(B2BTopicPageType? type = null)
    {
        var result = await _b2BTopicPageService.GetTopicPages(new GetTopicPagesInput
        {
            Type = type,
        });
        return Ok(result);
    }

    /// <summary>
    /// 获取专题页
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<B2BTopicPageOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get(GetB2BTopicPageInput input)
    {
        var result = await _b2BTopicPageService.GetTopicPages(new GetTopicPagesInput
        {
            Ids = new List<long> { input.Id },
            Language = input.Language,
        });
        return Ok(result.FirstOrDefault());
    }

    /// <summary>
    /// 设置专题页序号排序
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> SetComponentIndex(SetComponentIndexInput input)
    {
        await _b2BTopicPageService.SetComponentIndex(input);
        return Ok();
    }

    /// <summary>
    /// 获取专题页组件列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<B2BTopicPageComponentDto>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.ResourceInvalid)]
    public async Task<IActionResult> GetComponents([FromBody] GetComponentInput input)
    {
        var result = await _b2BTopicPageService.GetComponents(input);
        return Ok(result);
    }

    /// <summary>
    /// 更新保存组件信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> UpdateComponent(UpdateComponentInput input)
    {
        var result = await _b2BTopicPageService.UpdateComponent(input);
        return Ok(result);
    }

    /// <summary>
    /// 删除专题页组件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> DeleteComponent(DeleteComponentInput input)
    {
        await _b2BTopicPageService.DeleteComponent(input);
        return Ok();
    }

    /// <summary>
    /// 添加专题页组件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AddComponent(AddComponentInput input)
    {
        return Ok(await _b2BTopicPageService.AddComponent(input));
    }

    /// <summary>
    /// 获取专题页标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GetB2BTopicPageTagsOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetTags(GetB2BTopicPageTagsInput input)
    {
        return Ok(await _b2BTopicPageService.GetTags(input));
    }

    /// <summary>
    /// 分页获取专题页
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<B2BTopicPageInfoOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(GetTopicPagesInput input)
    {
        var result = await _b2BTopicPageService.SearchAsync(input);
        return Ok(result);
    }

    #endregion

    #region 小程序分享配置

    /// <summary>
    /// 设置小程序分享配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> AddOrUpdateAppletDaren(AddOrUpdateAppletDarenInput input)
    {
        await _b2BTopicPageService.AddOrUpdateAppletDaren(input);
        return Ok();
    }
    #endregion

    #region JOB
    /// <summary>
    /// 获取专题页组件列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<B2BTopicPageComponentDto>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.ResourceInvalid)]
    public async Task<IActionResult> SearchByJob([FromBody] SearchComponentsItemInput input)
    {
        var result = await _b2BTopicPageService.SearchComponentsItems(input);
        return Ok(result);
    }
    #endregion
}
