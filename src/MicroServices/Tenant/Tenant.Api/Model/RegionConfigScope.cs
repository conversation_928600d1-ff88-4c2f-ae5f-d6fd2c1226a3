using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

/// <summary>
/// 区域配置-范围
/// </summary>
public class RegionConfigScope : TenantBase
{
    public long RegionConfigId { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public long? CountryCode { get; set; }

    /// <summary>
    /// 国家名字
    /// </summary>
    public string? CountryName { get; set; }

    /// <summary>
    /// 省编码
    /// </summary>
    public long? ProvinceCode { get; set; }

    /// <summary>
    /// 省名字
    /// </summary>
    public string? ProvinceName { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int Sort { get; set; }
}
