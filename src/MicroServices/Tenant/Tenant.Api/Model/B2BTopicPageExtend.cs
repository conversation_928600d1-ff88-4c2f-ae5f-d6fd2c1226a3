using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

public class B2BTopicPageExtend : TenantBase
{
    /// <summary>
    /// 专题页id
    /// </summary>
    public long B2BTopicPageId { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 展示标题
    /// </summary>
    public string? AppletDarenDescription { get; set; }

    /// <summary>
    /// 展示图片
    /// </summary>
    public string? AppletDarenImage { get; set; }

    /// <summary>
    /// 专题描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 语言
    /// </summary>
    public string Language { get; set; }
}
