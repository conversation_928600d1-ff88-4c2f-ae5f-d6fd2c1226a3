using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

/// <summary>
/// 分销商
/// </summary>
public class Agency : TenantBase
{
    /// <summary>
    /// 分销商类型
    /// </summary>
    public AgencyType AgencyType { get; set; }

    /// <summary>
    /// 公司全称 
    /// </summary>
    public string FullName { get; set; }

    /// <summary>
    /// 公司简称
    /// </summary>
    public string ShortName { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string? Contact { get; set; }

    /// <summary>
    /// 联系人电话
    /// </summary>
    public string? ContactNumber { get; set; }

    /// <summary>
    /// 联系人国家区号
    /// </summary>
    public string? ContactCountryDialCode { get; set; }

    /// <summary>
    /// 财务姓名
    /// </summary>
    public string? FinancialStaff { get; set; }

    /// <summary>
    /// 财务联系电话
    /// </summary>
    public string? FinancialStaffNumber { get; set; }

    /// <summary>
    /// 财务联系方式国家区号
    /// </summary>
    public string? FinancialCountryDialCode { get; set; }

    /// <summary>
    /// 渠道类型
    /// </summary>
    public ChannelType ChannelType { get; set; }

    /// <summary>
    /// 结算周期
    /// </summary>
    public SupplierSettlementPeriod SettlementPeriod { get; set; }

    /// <summary>
    /// 分销商价格分组id
    /// </summary>
    public long PriceGroupId { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enable { get; set; } = true;

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 货币代码
    /// </summary>
    public string CurrencyCode { get; set; }

    /// <summary>
    /// 合同附件地址
    /// </summary>
    public string? AppendicesOfContractPath { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public TenantContractStatus ContractStatus { get; set; }

    /// <summary>
    /// 营业执照地址
    /// </summary>
    public string? BusinessLicensePath { get; set; }

    /// <summary>
    /// 商户证件编号	String	是	64	
    /// 统一社会信用代码证编号、事业单位法人证书编号、社会团体证书编号等，与商户签约类型匹配。
    /// </summary>
    public string? LicenceNo { get; set; }

    /// <summary>
    /// 注册来源
    /// </summary>
    public AgencyRegisterSourceType RegisterSource { get; set; }

    /// <summary>
    /// 认证状态 0-待认证，1-认证成功 ，2-认证中，3-认证失败
    /// </summary>
    public AgencyCertificationStatus CertificationStatus { get; set; }

    /// <summary>
    /// 签约主体
    /// </summary>
    public long? SignSubjectId { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public long? CountryCode { get; set; }

    /// <summary>
    /// 国家名字
    /// </summary>
    public string? CountryName { get; set; }

    /// <summary>
    /// 省编码
    /// </summary>
    public long? ProvinceCode { get; set; }

    /// <summary>
    /// 省名字
    /// </summary>
    public string? ProvinceName { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public long? CityCode { get; set; }

    /// <summary>
    /// 城市名字
    /// </summary>
    public string? CityName { get; set; }

    /// <summary>
    /// 部门Id
    /// </summary>
    public long? TenantDepartmentId { get; set; }

    /// <summary>
    /// 是否自定义价格分组
    /// </summary>
    public bool IsCustom { get; set; } = false;

    /// <summary>
    /// 状态
    /// </summary>
    public AgencyRecencyStatus AgencyRecencyStatus { get; set; }

    /// <summary>
    /// 客户潜在规模
    /// </summary>
    public long CustomerPotentialSize { get; set; }

    /// <summary>
    /// KA业务定义（暂定） = 3个月内有机会或已单月10万GMV
    /// </summary>
    public bool IsKA { get; set; }

    /// <summary>
    /// 联系邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 公司地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 所属行业
    /// </summary>
    public string? IndustryInvolved { get; set; }

    /// <summary>
    /// 业务体量
    /// </summary>
    public int? BusinessVolume { get; set; }

    /// <summary>
    /// 客户运营
    /// </summary>
    public long? CustomerOperationId { get; set; }

    /// <summary>
    /// 卫瓴企业id
    /// </summary>
    public string? WeilingCompanyId { get; set; }

    /// <summary>
    /// 支付密码
    /// </summary>
    public string? PaymentPassword { get; set; }

    /// <summary>
    /// 是否会员  true 会员， false 非会员
    /// </summary>
    public bool IsVip { get; set; } = true;

    /// <summary>
    /// 推广活码id
    /// </summary>
    public long? PromotionTraceId { get; set; }

    /// <summary>
    /// 注册父级来源
    /// </summary>
    public string? RegisterParentSource { get; set; }

    /// <summary>
    /// 注册来源-子来源
    /// </summary>
    public AgencyRegisterSubSourceType? RegisterSubSource { get; set; }
}
