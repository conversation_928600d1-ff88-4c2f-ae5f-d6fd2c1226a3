using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

/// <summary>
/// B2b客服信息
/// </summary>
public class AgencySupportStaff : TenantBase
{
    /// <summary>
    /// 客服电话
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 微信二维码
    /// </summary>
    public string? WechatQrcode { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EmailAddress { get; set; }

    /// <summary>
    /// 投诉电话
    /// </summary>
    public string? Complaints { get; set; }

    /// <summary>
    /// 投诉电话工作时间
    /// </summary>
    public string? ComplaintsWorkingHours { get; set; }

    /// <summary>
    /// 客服电话工作时间
    /// </summary>
    public string? PhoneNumberWorkingHours { get; set; }

    /// <summary>
    /// 企业ID
    /// </summary>
    public string? EnterpriseId { get; set; }

    /// <summary>
    /// 客服链接
    /// </summary>
    public string? CustomerLink { get; set; }

    /// <summary>
    /// 是否开启微信小程序客服
    /// </summary>
    public bool EnableWechatService { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; } = DateTime.Now;
}
