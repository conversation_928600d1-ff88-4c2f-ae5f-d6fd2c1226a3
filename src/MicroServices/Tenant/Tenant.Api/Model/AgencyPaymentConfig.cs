using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

public class AgencyPaymentConfig : TenantBase
{
    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 是否开启海外信用卡支付
    /// </summary>
    public bool OverseasCreditCardPaymentEnabled { get; set; } = false;

    /// <summary>
    /// 是否开启海外银行卡收款
    /// </summary>
    public bool OverseasBankCardPaymentEnabled { get; set; } = false;

    public DateTime UpdateTime { get; set; } = DateTime.Now;
}
