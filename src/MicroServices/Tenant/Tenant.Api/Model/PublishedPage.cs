using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

public class PublishedPage : TenantBase
{
    public string? Name { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 分享描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 分享图片
    /// </summary>
    public string? Picture { get; set; }

    /// <summary>
    /// 背景颜色
    /// </summary>
    public string? BgColor { get; set; }

    /// <summary>
    /// 背景图片
    /// </summary>
    public string? BgImage { get; set; }

    /// <summary>
    /// 字体颜色
    /// </summary>
    public string? FontColor { get; set; }

    /// <summary>
    /// 是否展示返回首页按钮
    /// </summary>
    public bool BackHomePage { get; set; }

    /// <summary>
    /// 是否为首页
    /// </summary>
    public bool IsHomePage { get; set; }

    /// <summary>
    /// 页面类型
    /// </summary>
    public PageType PageType { get; set; }

    public bool Enable { get; set; } = true;

    public DateTime CreateTime { get; set; } = DateTime.Now;
    public DateTime UpdateTime { get; set; } = DateTime.Now;
}
