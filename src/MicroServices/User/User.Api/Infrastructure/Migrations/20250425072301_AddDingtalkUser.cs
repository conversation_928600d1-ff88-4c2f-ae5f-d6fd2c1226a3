using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace User.Api.Infrastructure.Migrations
{
    public partial class AddDingtalkUser : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DingtalkDeptId",
                table: "TenantUser");

            migrationBuilder.DropColumn(
                name: "DingtalkIsDeptLeader",
                table: "TenantUser");

            migrationBuilder.DropColumn(
                name: "DingtalkLeaderDeptId",
                table: "TenantUser");

            migrationBuilder.DropColumn(
                name: "DingtalkTitle",
                table: "TenantUser");

            migrationBuilder.DropColumn(
                name: "DingtalkUnionid",
                table: "TenantUser");

            migrationBuilder.DropColumn(
                name: "DingtalkUserId",
                table: "TenantUser");

            migrationBuilder.CreateTable(
                name: "DingtalkUser",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    JobId = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DingtalkUserId = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DingtalkDeptId = table.Column<string>(type: "varchar(256)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DingtalkUnionid = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DingtalkTitle = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DingtalkIsDeptLeader = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    DingtalkLeaderDeptId = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DingtalkUser", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_DingtalkUser_TenantId",
                table: "DingtalkUser",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DingtalkUser");

            migrationBuilder.AddColumn<string>(
                name: "DingtalkDeptId",
                table: "TenantUser",
                type: "varchar(256)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<bool>(
                name: "DingtalkIsDeptLeader",
                table: "TenantUser",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "DingtalkLeaderDeptId",
                table: "TenantUser",
                type: "varchar(50)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "DingtalkTitle",
                table: "TenantUser",
                type: "varchar(128)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "DingtalkUnionid",
                table: "TenantUser",
                type: "varchar(50)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "DingtalkUserId",
                table: "TenantUser",
                type: "varchar(50)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }
    }
}
