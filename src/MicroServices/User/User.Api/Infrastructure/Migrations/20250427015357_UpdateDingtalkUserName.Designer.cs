// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using User.Api.Infrastructure;

#nullable disable

namespace User.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250427015357_UpdateDingtalkUserName")]
    partial class UpdateDingtalkUserName
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("User.Api.Model.AgencyUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(40)");

                    b.Property<bool>("EmailStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("RealName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "UserName")
                        .IsUnique();

                    b.ToTable("AgencyUser", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.CustomerUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencySource")
                        .HasColumnType("bigint");

                    b.Property<string>("Avatar")
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("Gender")
                        .HasColumnType("tinyint");

                    b.Property<string>("NickName")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<sbyte>("RegisterSource")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "AgencySource");

                    b.HasIndex("TenantId", "PhoneNumber")
                        .IsUnique();

                    b.ToTable("CustomerUser", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.CustomerUserTag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CustomerUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("TagId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("CustomerUserTag", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.CustomerUserVip", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CustomerUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("VipLevelId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CustomerUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("CustomerUserVip", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenBonus", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AmountReceivable")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("AmountReceived")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BonusType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("BonusValue")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("CreateOrderTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("InvoiceAmount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<sbyte>("IsFixedAmount")
                        .HasColumnType("tinyint");

                    b.Property<int>("OrderType")
                        .HasColumnType("int");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ProductSkuName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UnderUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "BaseOrderId");

                    b.HasIndex("TenantId", "PhoneNumber");

                    b.ToTable("DarenBonus", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenProductCommission", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("DevelopBonus")
                        .HasColumnType("decimal(18,4)");

                    b.Property<bool>("IsFixedAmount")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("ShareBonus")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("ProductId", "ProductSkuId")
                        .IsUnique();

                    b.ToTable("DarenProductCommission", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("BindRule")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Intro")
                        .HasColumnType("text");

                    b.Property<bool>("IsDarenDevelopOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPublicDisplay")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DarenSetting", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenShareFollow", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FollowerOpenId")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("SuperiorUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DarenShareFollow", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenUnderling", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BindSource")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("SuperiorUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DarenUnderling", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("SuperiorUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DarenUser", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenUserBankCard", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DarenUserBankCard", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenWithdrawals", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("BankCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("BankFees")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FailureReason")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "PhoneNumber");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("DarenWithdrawals", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DarenWithdrawalsBonus", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("DarenBonusId")
                        .HasColumnType("bigint");

                    b.Property<long>("DarenWithdrawalsId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("DarenWithdrawalsBonus", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.DingtalkUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("DingtalkDeptId")
                        .HasColumnType("varchar(256)");

                    b.Property<bool>("DingtalkIsDeptLeader")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("DingtalkLeaderDeptId")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("DingtalkTitle")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("DingtalkUnionid")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("DingtalkUserId")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("JobId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("DingtalkUser", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.HuiZhiBinding", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("PartnerId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("SysRole")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Code");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("HuiZhiBinding", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.ManageUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Account")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("JobId")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NickName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Account")
                        .IsUnique();

                    b.ToTable("ManageUser", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 901019344460090000L,
                            Account = "admin",
                            CreateTime = new DateTime(2021, 10, 22, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Enabled = true,
                            Name = "管理员",
                            Password = "E19D5CD5AF0378DA05F63F891C7467AF"
                        });
                });

            modelBuilder.Entity("User.Api.Model.ManageUserRole", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ManageRoleId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "ManageRoleId")
                        .IsUnique();

                    b.ToTable("ManageUserRole", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.OperationLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Agent")
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("Body")
                        .HasColumnType("mediumtext");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("DataContentJson")
                        .HasColumnType("mediumtext");

                    b.Property<string>("Host")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Ip")
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("KeyId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OperationType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("OperationUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OperationUserName")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Query")
                        .HasColumnType("text");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("System")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TabLogType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Url")
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.ToTable("OperationLog", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.ReceiverAddress", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(64)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(32)");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiverAddress", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.SupplierUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Password")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("RealName")
                        .HasColumnType("varchar(16)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(16)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "UserName")
                        .IsUnique();

                    b.ToTable("SupplierUser", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.Tag", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Tag", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.TenantUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Account")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVaildEmail")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsVaildPhoneNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("JobId")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NickName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Qrcode")
                        .HasColumnType("varchar(512)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Account", "TenantId")
                        .IsUnique();

                    b.ToTable("TenantUser", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.TenantUserRole", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantRoleId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId", "TenantRoleId")
                        .IsUnique();

                    b.ToTable("TenantUserRole", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.TenantUserSecurityVerification", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Device")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FingerPrint")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("IP")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UA")
                        .HasColumnType("varchar(400)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TenantUserSecurityVerification", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.UserBinding", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("PlatformType")
                        .HasColumnType("int");

                    b.Property<int>("SysRole")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Code");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("UserBinding", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.Vip", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Intro")
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Vip", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.VipLevel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Cover")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FontColor")
                        .HasColumnType("varchar(20)");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("VipLevel", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.VipLevelRights", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("DetailIntro")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("decimal(2,1)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("SimpleIntro")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("VipLevelId")
                        .HasColumnType("bigint");

                    b.Property<long>("VipRightsId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("VipLevelRights", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.VipLevelRightsCoupon", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Amount")
                        .HasColumnType("int");

                    b.Property<long>("CouponId")
                        .HasColumnType("bigint");

                    b.Property<long>("VipLevelRightsId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("VipLevelRightsId");

                    b.ToTable("VipLevelRightsCoupon", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.VipRights", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Cover")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte>("RightsType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("VipRights", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.VipRightsRange", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ParticipateType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ProductType")
                        .HasColumnType("int");

                    b.Property<long>("VipRightsId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("VipRightsId");

                    b.ToTable("VipRightsRange", (string)null);
                });

            modelBuilder.Entity("User.Api.Model.VipRightsRangeValue", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("PriceStrategyIds")
                        .HasColumnType("text");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketsIds")
                        .HasColumnType("text");

                    b.Property<long>("VipRightsRangeId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("VipRightsRangeId");

                    b.ToTable("VipRightsRangeValue", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
