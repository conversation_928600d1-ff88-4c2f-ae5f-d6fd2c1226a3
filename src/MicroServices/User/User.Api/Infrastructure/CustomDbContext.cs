using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace User.Api.Infrastructure;

public class CustomDbContext : DbContextBase
{
    public CustomDbContext(DbContextOptions dbContextOptions, ITenantIdentify tenantIdentify) : base(dbContextOptions, tenantIdentify)
    {
    }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }

    public DbSet<CustomerUser> CustomerUsers { get; set; }
    public DbSet<ManageUser> ManageUsers { get; set; }
    public DbSet<TenantUser> TenantUsers { get; set; }
    public DbSet<UserBinding> UserBindings { get; set; }
    public DbSet<ReceiverAddress> ReceiverAddresses { get; set; }
    public DbSet<Vip> Vips { get; set; }
    public DbSet<VipLevel> VipLevels { get; set; }
    public DbSet<VipLevelRights> VipLevelRights { get; set; }
    public DbSet<VipLevelRightsCoupon> VipLevelRightsCoupons { get; set; }
    public DbSet<VipRights> VipRights { get; set; }
    public DbSet<VipRightsRange> VipRightsRanges { get; set; }
    public DbSet<VipRightsRangeValue> VipRightsRangeValues { get; set; }
    public DbSet<CustomerUserVip> CustomerUserVips { get; set; }
    public DbSet<DarenProductCommission> DarenProductCommissions { get; set; }
    public DbSet<DarenShareFollow> DarenShareFollows { get; set; }
    public DbSet<DarenUnderling> DarenUnderlings { get; set; }
    public DbSet<DarenUserBankCard> DarenUserBankCards { get; set; }
    public DbSet<DarenWithdrawals> DarenWithdrawals { get; set; }
    public DbSet<DarenWithdrawalsBonus> DarenWithdrawalsBonus { get; set; }
    public DbSet<DarenSetting> DarenSettings { get; set; }
    public DbSet<DarenUser> DarenUsers { get; set; }
    public DbSet<DarenBonus> DarenBonus { get; set; }
    public DbSet<HuiZhiBinding> HuiZhiBindings { get; set; }
    public DbSet<SupplierUser> SupplierUsers { get; set; }
    public DbSet<Tag> Tags { get; set; }
    public DbSet<CustomerUserTag> CustomerUserTags { get; set; }
    public DbSet<AgencyUser> AgencyUsers { get; set; }

    public DbSet<TenantUserRole> TenantUserRoles { get; set; }

    public DbSet<TenantUserSecurityVerification> TenantUserSecurityVerifications { get; set; }

    public DbSet<OperationLog> OperationLogs { get; set; }

    public DbSet<ManageUserRole> ManageUserRoles { get; set; }

    public DbSet<DingtalkUser> DingtalkUsers { get; set; }
}
