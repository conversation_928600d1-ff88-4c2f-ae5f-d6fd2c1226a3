using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace User.Api.Infrastructure.EntityConfigurations
{
    public class DarenUserEntityTypeConfiguration : TenantBaseConfiguration<Model.DarenUser>, IEntityTypeConfiguration<Model.DarenUser>
    {
        public void Configure(EntityTypeBuilder<Model.DarenUser> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.SuperiorUserId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
                    }
    }
}
