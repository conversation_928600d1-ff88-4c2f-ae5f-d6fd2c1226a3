using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace User.Api.Infrastructure.EntityConfigurations
{
    public class VipLevelEntityTypeConfiguration : TenantBaseConfiguration<Model.VipLevel>, IEntityTypeConfiguration<Model.VipLevel>
    {
        public void Configure(EntityTypeBuilder<Model.VipLevel> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Level)
                .HasColumnType("int");
            
            builder.Property(s => s.Name)
                .HasColumnType("varchar(20)")
                .IsRequired();
            
            builder.Property(s => s.Cover)
                .HasColumnType("varchar(200)")
                .IsRequired();
            
            builder.Property(s => s.FontColor)
                .HasColumnType("varchar(20)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
        }
    }
}
