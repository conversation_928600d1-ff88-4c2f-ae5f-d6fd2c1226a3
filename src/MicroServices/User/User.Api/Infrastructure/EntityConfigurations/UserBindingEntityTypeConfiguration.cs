using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace User.Api.Infrastructure.EntityConfigurations
{
    public class UserBindingEntityTypeConfiguration : TenantBaseConfiguration<UserBinding>, IEntityTypeConfiguration<UserBinding>
    {
        public void Configure(EntityTypeBuilder<UserBinding> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");

            builder.Property(s => s.Code)
                .HasColumnType("varchar(100)")
                .IsRequired();

            builder.Property(s => s.PlatformType)
                .HasColumnType("int");

            builder.Property(s => s.SysRole)
                .HasColumnType("int");

            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime")
                .IsRequired();

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");


            builder.HasIndex(s => s.UserId);
            builder.HasIndex(s => s.Code);
        }
    }
}
