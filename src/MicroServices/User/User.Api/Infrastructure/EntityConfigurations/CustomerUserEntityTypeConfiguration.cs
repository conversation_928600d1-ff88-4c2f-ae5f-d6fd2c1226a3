using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace User.Api.Infrastructure.EntityConfigurations
{
    public class CustomerUserEntityTypeConfiguration : TenantBaseConfiguration<Model.CustomerUser>, IEntityTypeConfiguration<Model.CustomerUser>
    {
        public void Configure(EntityTypeBuilder<Model.CustomerUser> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.PhoneNumber)
                .HasColumnType("varchar(32)")
                .IsRequired();

            builder.Property(s => s.NickName)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.Avatar)
                .HasColumnType("varchar(512)");

            builder.Property(s => s.Birthday)
                .HasColumnType("datetime");

            builder.Property(s => s.Gender)
                .HasColumnType("tinyint");

            builder.Property(s => s.RegisterSource)
                .HasColumnType("tinyint");

            builder.Property(s => s.AgencySource);

            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.InviteCodeAgencySource)
                .HasColumnType("bigint"); ;

            builder.Property(s => s.InviteCode)
                .HasColumnType("varchar(6)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.HasIndex(s => new { s.TenantId, s.PhoneNumber }).IsUnique();
            builder.HasIndex(x => new { x.TenantId, x.AgencySource });
        }
    }
}
