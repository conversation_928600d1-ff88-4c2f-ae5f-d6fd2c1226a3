using Common.Jwt;
using Common.Swagger;
using Contracts.Common.User.DTOs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using User.Api.Services.Interfaces;

namespace User.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class ReceiverAddressController : ControllerBase
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IReceiverAddressService _receiverAddressService;

        public ReceiverAddressController(IHttpContextAccessor httpContextAccessor,
            IReceiverAddressService receiverAddressService)
        {
            _httpContextAccessor = httpContextAccessor;
            _receiverAddressService = receiverAddressService;
        }

        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> Create(CreateReceiverAddressInput input)
        {
            var currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
            await _receiverAddressService.Create(currentUser.userid, input);
            return Ok();
        }

        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> Update(UpdateReceiverAddressInput input)
        {
            var currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
            await _receiverAddressService.Update(currentUser.userid, input);
            return Ok();
        }

        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<UpdateReceiverAddressInput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetAll()
        {
            var currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
            var result = await _receiverAddressService.GetAllByUserId(currentUser.userid);
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> Delete(long id)
        {
            var currentUser = _httpContextAccessor.HttpContext.GetCurrentUser();
            await _receiverAddressService.Delete(id, currentUser.userid);
            return Ok();
        }
    }
}
