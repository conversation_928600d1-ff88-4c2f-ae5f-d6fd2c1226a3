using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using User.Api.Services.Interfaces;

namespace User.Api.Controllers;

/// <summary>
/// 钉钉用户
/// </summary>
[Route("[controller]/[action]")]
[ApiController]
public class DingtalkUserController : ControllerBase
{
    private readonly IDingtalkUserService _dingtalkUserService;

    public DingtalkUserController(IDingtalkUserService dingtalkUserService)
    {
        _dingtalkUserService = dingtalkUserService;
    }



    #region CapSubscribe
    /// <summary>
    /// 订阅 - 保存钉钉用户
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    [NonAction]
    //[HttpPost]
    [CapSubscribe(CapTopics.User.SyncDingtalkUser)]
    public async Task SyncDingtalkUserId(DingtalkUserSaveMessage receive)
    {
        await _dingtalkUserService.SyncUserSave(receive);
    }
    #endregion

}
