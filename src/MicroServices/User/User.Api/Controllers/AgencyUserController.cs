using Common.Jwt;
using Common.Swagger;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using User.Api.Services.Interfaces;

namespace User.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class AgencyUserController : ControllerBase
{
    private readonly IAgencyUserService _agencyUserService;

    public AgencyUserController(IAgencyUserService agencyUserService)
    {
        _agencyUserService = agencyUserService;
    }

    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.User.AccountIsExist)]
    public async Task<IActionResult> Add(AddInput input)
    {
        var res = await _agencyUserService.Add(input);
        await _agencyUserService.AddLog(input, res, 0);
        return Ok(res.Id);
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(UpdateInput input)
    {
        await _agencyUserService.Update(input);

        return Ok();
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> OnOrOff(OnOrOffInput input)
    {
        await _agencyUserService.OnOrOff(input);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> RestPassword(RestPasswordInput input)
    {
        var newPwd = await _agencyUserService.RestPassword(input);
        return Ok(newPwd);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<AgencyUserDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        var result = await _agencyUserService.Search(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<AgencyUserDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> List(SearchInput input)
    {
        var result = await _agencyUserService.List(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<AgencyUserDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchPhone(List<string> phones)
    {
        var result = await _agencyUserService.SearchPhone(phones);
        return Ok(result);
    }

    /// <summary>
    /// 获取分销商用户信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(AgencyUserDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDetail(long id)
    {
        var result = (await _agencyUserService.GetDetails(new GetDetailsInput
        {
            UserIds = new[] { id }
        })).FirstOrDefault();
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<AgencyUserDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDetails(params long[] ids)
    {
        var result = await _agencyUserService.GetDetails(new GetDetailsInput
        {
            UserIds = ids
        });
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<AgencyUserDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetDetailByUserId(GetDetailsInput input)
    {
        var result = await _agencyUserService.GetDetails(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<AgencyUserDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetUsers(GetDetailsInput input)
    {
        var result = await _agencyUserService.GetDetails(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<AgencyUserDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetUserInfo(GetDetailsInput input)
    {
        var result = await _agencyUserService.GetUserInfo(input);
        return Ok(result);
    }

    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.User.PasswordError)]
    public async Task<IActionResult> UpdatePassword(UpdatePasswordInput input)
    {
        //var user = HttpContext.GetCurrentUser();
        await _agencyUserService.UpdatePassword(input);
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> FindOne(AgencyUserFindOneInput input)
    {
        var result = await _agencyUserService.FindOne(input);
        return Ok(result);
    }

    /// <summary>
    /// 校验分销商唯一性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AgencyUsersAny(AgencyUserVerifyInput input)
    {
        var result = await _agencyUserService.AgencyUsersAnyAsync(input);
        return Ok(result);
    }

    /// <summary>
    /// 设置 分销商 邮箱验证状态
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetEmailStatus(AgencyUserEmailStatusInput input)
    {
        await _agencyUserService.SetEmailStatusAsync(input);
        return Ok();
    }

    #region CapSubscribe

    [NonAction]
    [CapSubscribe(CapTopics.User.AddAgencyUser)]
    public async Task AddAgencyUser(AddAgencyUserMessage message)
    {
        var input = new AddInput
        {
            AgencyId = message.AgencyId,
            Password = message.Password,
            PhoneNumber = message.PhoneNumber,
            RealName = message.RealName,
            UserName = message.UserName,
            AgencyAcls = message.AgencyAcls,
            CountryDialCode = message.CountryDialCode,
            Email = message.Email,
        };
        var res = await _agencyUserService.Add(input);
        await _agencyUserService.AddLog(input, res, 1);
    }

    #endregion
}
