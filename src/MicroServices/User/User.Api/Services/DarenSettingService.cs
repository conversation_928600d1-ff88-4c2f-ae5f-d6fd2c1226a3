using Contracts.Common.User.DTOs.Daren;
using Contracts.Common.User.Enums;
using Microsoft.EntityFrameworkCore;
using User.Api.Services.Interfaces;

namespace User.Api.Services;

public class DarenSettingService : IDarenSettingService
{
    private readonly CustomDbContext _dbContext;
    private readonly IConfiguration _configuration;

    public DarenSettingService(CustomDbContext dbContext,
        IConfiguration configuration)
    {
        _dbContext = dbContext;
        _configuration = configuration;
    }

    public async Task<bool> AddOrUpdate(DarenSettingDto dto)
    {
        var entity = await _dbContext.DarenSettings.FirstOrDefaultAsync();
        if (entity == null)
        {
            _dbContext.DarenSettings.Add(new DarenSetting
            {
                IsPublicDisplay = dto.IsPublicDisplay,
                BindRule = dto.BindRule.Sum(s => (int)s),
                IsDarenDevelopOpen = dto.IsDarenDevelopOpen,
                Intro = dto.Intro,
            });
        }
        else
        {
            entity.IsPublicDisplay = dto.IsPublicDisplay;
            entity.BindRule = dto.BindRule.Sum(s => (int)s);
            entity.IsDarenDevelopOpen = dto.IsDarenDevelopOpen;
            entity.Intro = dto.Intro;
        }
        var rows = await _dbContext.SaveChangesAsync();
        return rows > 0;
    }

    public async Task<DarenSettingDto> Get()
    {
        var entity = await _dbContext.DarenSettings.FirstOrDefaultAsync();
        entity ??= new DarenSetting();
        var bindRules = new List<DarenBindRule> { DarenBindRule.PlaceOrder }; //默认下单选定
        if (entity.BindRule != 0)
            bindRules = Enum.GetValues<DarenBindRule>().Where(s => (entity.BindRule & (int)s) == (int)s).ToList();
        return new DarenSettingDto
        {
            IsPublicDisplay = entity.IsPublicDisplay,
            IsDarenDevelopOpen = entity.IsDarenDevelopOpen,
            Intro = entity.Intro ?? string.Empty,
            BindRule = bindRules,
            MallHost = _configuration["SysSetting:H5"]
        };
    }
}
