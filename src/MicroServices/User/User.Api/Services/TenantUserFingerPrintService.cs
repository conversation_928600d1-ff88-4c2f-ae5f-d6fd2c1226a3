using AutoMapper;
using Cit.Storage.Redis;
using Contracts.Common.User.DTOs.TenantUserSecurityVerification;
using Contracts.Common.User.Enums;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using User.Api.Model;
using User.Api.Services.Interfaces;

namespace User.Api.Services;

public class TenantUserFingerPrintService :ITenantUserSecurityVerificationService
{
    private readonly IMapper _mapper;
    private readonly IRedisClient _redisClient;
    private readonly CustomDbContext _dbContext;

    public TenantUserFingerPrintService(CustomDbContext dbContext, 
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<bool> CheckFingerPrint(CheckSecurityVerificationInput input)
    {
        var now = DateTime.Now;
        var entity = await _dbContext.TenantUserSecurityVerifications.IgnoreQueryFilters().AsNoTracking()
            .Where(x=> x.UserId == input.UserId && x.FingerPrint == input.FingerPrint)
            .WhereIF(input.TenantId.HasValue, x=> x.TenantId == input.TenantId)
            .FirstOrDefaultAsync();
        if (entity is null)
            return true;

        //登录48小时外，不满足3条件时，触发重新登录
        if (entity.UpdateTime.AddDays(2) < now)
            return true;

        return false;
    }

    public async Task Save(SaveTenantUserSecurityVerificationInput input)
    {
        var entity = _dbContext.TenantUserSecurityVerifications.FirstOrDefault(x => x.UserId == input.UserId && x.FingerPrint == input.FingerPrint);
        if (entity == null)
        {
            entity = _mapper.Map<TenantUserSecurityVerification>(input);
            _dbContext.TenantUserSecurityVerifications.Add(entity);
        }
        else
        {
            _mapper.Map(input, entity);
            entity.UpdateTime = DateTime.Now;
        }
        //只保存5条设备数据
        var existVerifications = _dbContext.TenantUserSecurityVerifications
            .OrderBy(x => x.UpdateTime)
            .Where(x => x.UserId == input.UserId)
            .ToList();
        if (existVerifications.Count() >= 5)
        {
            for (int i = 0; i <= existVerifications.Count() - 5; i++)
            {
                _dbContext.TenantUserSecurityVerifications.Remove(existVerifications[i]);
            }
        }

        await _dbContext.SaveChangesAsync();
    }
}
