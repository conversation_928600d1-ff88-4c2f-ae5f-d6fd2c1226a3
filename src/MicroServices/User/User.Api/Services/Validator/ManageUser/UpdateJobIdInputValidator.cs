using Contracts.Common.User.DTOs.ManageUser;
using FluentValidation;

namespace User.Api.Services.Validator.ManageUser;

public class UpdateJobIdInputValidator : AbstractValidator<UpdateJobIdInput>
{
    public UpdateJobIdInputValidator()
    {
        RuleFor(s => s.UserId).NotNull().NotEmpty();
        RuleFor(s => s.Name).NotNull().NotEmpty();
        RuleFor(s => s.JobId).NotNull().NotEmpty();
        RuleFor(s => s.DingtalkUserId).NotNull().NotEmpty();
    }
}
