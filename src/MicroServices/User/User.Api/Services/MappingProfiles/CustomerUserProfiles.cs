using AutoMapper;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.CustomerUser;

namespace User.Api.Services.MappingProfiles
{
    public class CustomerUserProfiles : Profile
    {
        public CustomerUserProfiles()
        {
            CreateMap<CustomerUser, UserInfoOutput>()
                .ReverseMap();

            CreateMap<CustomerUser, CustomerUserDTO>()
                .ReverseMap();
        }
    }
}
