using EfCoreExtensions.EntityBase;

namespace User.Api.Model
{
    public class DarenShareFollow : TenantBase
    {
        /// <summary>
        /// 上级Id/被关注者Id
        /// </summary>
        public long SuperiorUserId { get; set; }

        /// <summary>
        /// 关注者微信OpenId
        /// </summary>
        public string? FollowerOpenId { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
