using EfCoreExtensions.EntityBase;

namespace User.Api.Model
{
    public class ManageUser : KeyBase
    {
        /// <summary>
        /// 账号
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string? NickName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool Enabled { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 邮箱
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        public string? JobId { get; set; }

        /// <summary>
        /// 钉钉用户Id
        /// </summary>
        public string? DingtalkUserId { get; set; }

        /// <summary>
        /// 邮箱密码
        /// </summary>
        public string? EmailPassword { get; set; }
    }
}
