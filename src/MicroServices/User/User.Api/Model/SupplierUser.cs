using EfCoreExtensions.EntityBase;

namespace User.Api.Model
{
    public class SupplierUser : TenantBase
    {
        /// <summary>
        /// 供应商Id
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
