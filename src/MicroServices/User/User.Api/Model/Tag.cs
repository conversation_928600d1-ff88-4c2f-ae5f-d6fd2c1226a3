using EfCoreExtensions.EntityBase;

namespace User.Api.Model;

/// <summary>
/// 标签
/// </summary>
public class Tag : TenantBase
{
    /// <summary>
    /// 标签名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 标签说明
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
}