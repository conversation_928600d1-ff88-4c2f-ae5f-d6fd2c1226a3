using AutoMapper;
using Cit.Storage.Redis;
using Contracts.Common.Scenic.DTOs.OpenChannel;
using Contracts.Common.Scenic.DTOs.TicketChannelSetting;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Scenic.Api.Services.Interfaces;
using Scenic.Api.Services.OpenPlatform;
using Scenic.Api.Services.OpenPlatform.Contracts.Supplier;
using Scenic.Api.Services.OpenPlatform.Interfaces;

namespace Scenic.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class ThirdCalendarPriceController : ControllerBase
{
    private readonly IThirdCalendarPriceService _thirdCalendarPriceService;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly IRedisClient _redisClient;
    
    public ThirdCalendarPriceController(
        IThirdCalendarPriceService thirdCalendarPriceService,
        IOpenSupplierService openSupplierService,
        IRedisClient redisClient)
    {
        _thirdCalendarPriceService = thirdCalendarPriceService;
        _openSupplierService = openSupplierService;
        _redisClient = redisClient;
    }

    /// <summary>
    /// 第三方同步日历价库更新
    /// </summary>
    /// <param name="input"></param>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(UpdateThirdCalendarPriceInput input)
    {
        await _thirdCalendarPriceService.Update(input);
        _ = _thirdCalendarPriceService.UpdateThirdTicketMinPrice(new SetThirdTicketMinPriceInput
        {
            TicketIds = new List<long>{ input.TicketsId }
        });

        Console.WriteLine("Update");
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> NotifySyncPrice(NotifySyncThirdPriceInput input)
    {
        var output =  await _thirdCalendarPriceService.NotifySyncPrice(input);
        if (output.TicketIds.Any())
        {
            _ = _thirdCalendarPriceService.UpdateThirdTicketMinPrice(new SetThirdTicketMinPriceInput
            {
                TicketIds = output.TicketIds
            });
        }
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> NotifySyncThirdInventory(NotifySyncThirdInventoryInput input)
    {
        var openSupplierType = input.OpenSupplierType.ToString().ToLowerInvariant();
        var lockSecret = Guid.NewGuid().ToString();
        var lockKey = $"sync:{openSupplierType}:{input.SkuId}";
        try
        {
            await _redisClient.LockTakeWaitingAsync(lockKey, lockSecret, TimeSpan.FromMinutes(1));
            await _thirdCalendarPriceService.NotifySyncThirdInventory(input);
        }
        finally
        {
            await _redisClient.LockReleaseAsync(lockKey, lockSecret);
        }
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> NewOrderNotifyTriggerSyncStocks(OpenChannelNotifySyncStocksInput input)
    {
        await _thirdCalendarPriceService.NewOrderNotifyTriggerSyncStocks(input);
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> SetChannelSettingSyncPriceStocks(SetChannelSettingSyncPriceStocksInput input)
    {
        await _thirdCalendarPriceService.SetChannelSettingSyncPriceStocks(input);
        return Ok();
    }

    #region CapSubscribe

    /// <summary>
    /// 同步价库到渠道端
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Scenic.SyncChannelPriceStock)]
    public async Task<IActionResult> SyncChannelPriceStock(SyncChannelPriceStockUploadMessage receive)
    {
        await _thirdCalendarPriceService.SyncChannelPriceStock(receive);
        return Ok();
    }

    /// <summary>
    /// 处理日历价同步数据
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Scenic.SyncByCalendarPriceData)]
    public async Task<IActionResult> SyncByCalendarPriceData(SyncChannelByCalendarPriceMessage receive)
    {
        await _thirdCalendarPriceService.SyncByCalendarPriceData(receive);
        return Ok();
    }
    

    /// <summary>
    /// 触发供货端同步
    /// <remarks>查询时间，出行日期当天、明天、后天三天的价库</remarks>
    /// </summary>
    [NonAction]
    [CapSubscribe(CapTopics.Scenic.TriggerOpenChannelSync)]
    public async Task<IActionResult> TriggerOpenChannelSync(TriggerOpenChannelSyncMessage receive)
    {
        await _thirdCalendarPriceService.TriggerOpenChannelSync(receive);
        return Ok();
    }
    #endregion
}