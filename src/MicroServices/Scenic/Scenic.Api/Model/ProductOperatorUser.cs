using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Scenic.Api.Model;

/// <summary>
/// 产品平台运营人信息
/// </summary>
public class ProductOperatorUser : TenantBase
{
    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 运营助理
    /// </summary>
    public long? OperatorAssistantUserId { get; set; }

    public DateTime? CreateTime { get; set; } = DateTime.Now;
}
