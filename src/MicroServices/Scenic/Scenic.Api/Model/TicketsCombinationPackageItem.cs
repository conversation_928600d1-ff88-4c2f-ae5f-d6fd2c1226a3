using EfCoreExtensions.EntityBase;

namespace Scenic.Api.Model;

/// <summary>
/// 门票组合套餐-配置项
/// <value>由组合配置池子根据规则自动生成关联关系</value>
/// </summary>
public class TicketsCombinationPackageItem : TenantBase
{
    /// <summary>
    /// 组合产品id
    /// </summary>
    public long TicketsCombinationId { get; set; }

    /// <summary>
    /// 组合产品套餐Id
    /// </summary>
    public long TicketsCombinationPackageId { get; set; }
    
    /// <summary>
    /// 门票id
    /// </summary>
    public long TicketsId { get; set; }

    /// <summary>
    /// 时段id
    /// </summary>
    public long? TimeSlotId { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}