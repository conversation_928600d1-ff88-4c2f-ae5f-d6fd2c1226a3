using Common.GlobalException;
using Common.ServicesHttpClient;
using Common.Utils;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.DTOs.CitOpenTenantConfig;
using Contracts.Common.Tenant.Enums;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Scenic.Api.Extensions;
using Scenic.Api.Services.OpenPlatform.Contracts;
using Scenic.Api.Services.OpenPlatform.Interfaces;

namespace Scenic.Api.Services.OpenPlatform;

public class OpenPlatformBaseService : IOpenPlatformBaseService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    public OpenPlatformBaseService(
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddressOptions)
    {
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddressOptions.Value;
    }
    
    public SupplierApiType [] OpenSupplierApiTypes => new []
    {
        SupplierApiType.GuestRoutePlay,
        SupplierApiType.Globaltix,
        SupplierApiType.TreepFlyingEarth,
        SupplierApiType.Exoz,
        SupplierApiType.CA,
        SupplierApiType.Mozio,
        SupplierApiType.CaoYueTianKong,
        SupplierApiType.Sctt,
        SupplierApiType.Eyounz,
        SupplierApiType.ToursGroup,
    };
    
    
    public async Task<GetCitOpenTenantConfigOutput> GetCitOpenTenantConfig(long tenantId)
    {
        //查询租户信息
        var header = new  List<KeyValuePair<string,string>>
        {
            new("tenant",tenantId.ToString())
        };
        var request = new GetCitOpenTenantConfigInput
        {
            TenantId = tenantId
        };
        var httpContent = new StringContent(JsonConvert.SerializeObject(request),
            Encoding.UTF8, "application/json");
        var configInfo = await _httpClientFactory.InternalPostAsync<GetCitOpenTenantConfigOutput>(
            requestUri: _servicesAddress.Tenant_Tenant_GetCitOpenTenantConfigInfo(),
            headers: header,
            httpContent: httpContent);
        return configInfo;
    }

    public async Task<ApiBaseRequest<T>> CreateRequestBody<T>(T data, long tenantId)
    {
        var citOpenConfig = await GetCitOpenTenantConfig(tenantId);
        var appKey = citOpenConfig.CitOpenAppKey;
        var appSecret = citOpenConfig.CitOpenAppSecret;
        var generateSign = GenerateSign(appKey, appSecret);
        var requestBody = new ApiBaseRequest<T>
        {
            AppKey = appKey,
            RequestTime = generateSign.requestTime,
            Sign = generateSign.sign,
            Data = data
        };
        return requestBody;
    }
    
    public (string requestTime, string sign) GenerateSign(string appKey, string appSecret)
    {
        //生成签名算法：MD5("{AppKey}&{AppSecret}&{RequestTime}"),UTC时间
        var requestTime = DateTime.Now.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
        var sign = SecurityUtil.MD5Encrypt($"{appKey}&{appSecret}&{requestTime}", Encoding.UTF8);
        return (requestTime, sign);
    }

    public (OpenSupplierType openSupplierType, PriceInventorySource priceInventorySource, string openSupplierTypeStr)
        ConvertSupplierType(SupplierApiType supplierApiType)
    {
        OpenSupplierType openSupplierType;
        PriceInventorySource priceInventorySource;
        
        switch (supplierApiType)
        {
            case SupplierApiType.GuestRoutePlay:
                openSupplierType = OpenSupplierType.Klook;
                priceInventorySource = PriceInventorySource.Klook;
                break;
            case SupplierApiType.Globaltix:
                openSupplierType = OpenSupplierType.GlobalTix;
                priceInventorySource = PriceInventorySource.GlobalTix;
                break;
            case SupplierApiType.TreepFlyingEarth:
                openSupplierType = OpenSupplierType.TreepFlyingEarth;
                priceInventorySource = PriceInventorySource.TreepFlyingEarth;
                break;
            case SupplierApiType.Exoz:
                openSupplierType = OpenSupplierType.Experienceoz;
                priceInventorySource = PriceInventorySource.Experienceoz;
                break;
            case SupplierApiType.CA:
                openSupplierType = OpenSupplierType.CA;
                priceInventorySource = PriceInventorySource.CA;
                break;
            case SupplierApiType.CaoYueTianKong:
                openSupplierType = OpenSupplierType.CaoYueTianKong;
                priceInventorySource = PriceInventorySource.CaoYueTianKong;
                break;
            case SupplierApiType.Sctt:
                openSupplierType = OpenSupplierType.Sctt;
                priceInventorySource = PriceInventorySource.Sctt;
                break;
            case SupplierApiType.Eyounz:
                openSupplierType = OpenSupplierType.Eyounz;
                priceInventorySource = PriceInventorySource.Eyounz;
                break;
            case SupplierApiType.ToursGroup:
                openSupplierType = OpenSupplierType.ToursGroup;
                priceInventorySource = PriceInventorySource.ToursGroup;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(supplierApiType), supplierApiType, null);
        }
        
        string openSupplierTypeStr = openSupplierType.ToString().ToLowerInvariant();
        return (openSupplierType, priceInventorySource, openSupplierTypeStr);
    }

    public PriceInventorySource MapOpenSupplierTypeToPriceInventorySource(OpenSupplierType openSupplierType)
    {
        var priceInventorySource = openSupplierType
            switch
            {
                OpenSupplierType.GlobalTix => PriceInventorySource.GlobalTix,
                OpenSupplierType.Klook => PriceInventorySource.Klook,
                OpenSupplierType.TreepFlyingEarth => PriceInventorySource.TreepFlyingEarth,
                OpenSupplierType.Experienceoz => PriceInventorySource.Experienceoz,
                OpenSupplierType.CA => PriceInventorySource.CA,
                OpenSupplierType.CaoYueTianKong => PriceInventorySource.CaoYueTianKong,
                OpenSupplierType.Sctt => PriceInventorySource.Sctt,
                OpenSupplierType.Eyounz => PriceInventorySource.Eyounz,
                OpenSupplierType.ToursGroup => PriceInventorySource.ToursGroup,
                _ => throw new BusinessException("系统价格库存来源不支持同步")
            };

        return priceInventorySource;
    }
    
    public CalendarPriceChannelType MappingOtaChannelTypeToCalendarPriceChannelType(OtaChannelType otaChannelType)
    {
        return otaChannelType switch
        {
            OtaChannelType.AliTrip => CalendarPriceChannelType.Fliggy,
            OtaChannelType.Meituan => CalendarPriceChannelType.Meituan,
            OtaChannelType.Ctrip => CalendarPriceChannelType.Ctrip,
            OtaChannelType.DouYin => CalendarPriceChannelType.TikTok,
            _ => throw new ArgumentOutOfRangeException(nameof(otaChannelType), otaChannelType, null)
        };
    }
    
    public SupplierApiType MapPriceInventorySourceToSupplierApiType(PriceInventorySource priceInventorySource)
    {
        SupplierApiType supplierApiType;
        switch (priceInventorySource)
        {
            case PriceInventorySource.GlobalTix:
                supplierApiType = SupplierApiType.Globaltix;
                break;
            case PriceInventorySource.Klook:
                supplierApiType = SupplierApiType.GuestRoutePlay;
                break;
            case PriceInventorySource.TreepFlyingEarth:
                supplierApiType = SupplierApiType.TreepFlyingEarth;
                break;
            case PriceInventorySource.Experienceoz:
                supplierApiType = SupplierApiType.Exoz;
                break;
            case PriceInventorySource.CA:
                supplierApiType = SupplierApiType.CA;
                break;
            case PriceInventorySource.CaoYueTianKong:
                supplierApiType = SupplierApiType.CaoYueTianKong;
                break;
            case PriceInventorySource.Sctt:
                supplierApiType = SupplierApiType.Sctt;
                break;
            case PriceInventorySource.Eyounz:
                supplierApiType = SupplierApiType.Eyounz;
                break;
            case PriceInventorySource.ToursGroup:
                supplierApiType = SupplierApiType.ToursGroup;
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
        return supplierApiType;
    }

    public TouristInfoType MapTravelerInfoTypeToTouristInfoType(OpenSupplierProductTravelerInfoType travelerInfoType)
    {
        TouristInfoType touristInfoType;
        switch (travelerInfoType)
        {
            case OpenSupplierProductTravelerInfoType.NotNeed:
                touristInfoType = TouristInfoType.None;
                break;
            case OpenSupplierProductTravelerInfoType.JustOne:
                touristInfoType = TouristInfoType.One;
                break;
            case OpenSupplierProductTravelerInfoType.EveryOne:
                touristInfoType = TouristInfoType.Every;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(travelerInfoType), travelerInfoType, null);
        }

        return touristInfoType;
    }
    
    public TEnum GetSaasEnumValue<TEnum>(string value) where TEnum : struct, Enum
    {
        if (Enum.TryParse(value, true, out TEnum type))
        {
            return type;
        }
        throw new BusinessException("未知的数据类型");
    }
}