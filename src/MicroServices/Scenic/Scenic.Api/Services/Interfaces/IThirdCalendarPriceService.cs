using Contracts.Common.Scenic.DTOs.OpenChannel;
using Contracts.Common.Scenic.DTOs.TicketChannelSetting;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.Messages;

namespace Scenic.Api.Services.Interfaces;

public interface IThirdCalendarPriceService
{
    /// <summary>
    /// 价格设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Update(UpdateThirdCalendarPriceInput input);

    /// <summary>
    /// 门票新增,编辑-同步价库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SyncThirdPriceInventory(SyncThirdPriceInventoryInput input);

    /// <summary>
    /// 供应端价格变更通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<NotifySyncThirdPriceOutput> NotifySyncPrice(NotifySyncThirdPriceInput input);

    /// <summary>
    /// 供应端库存变更通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<NotifySyncThirdInventoryOutput> NotifySyncThirdInventory(NotifySyncThirdInventoryInput input);

    /// <summary>
    /// 更新产品最低价
    /// </summary>
    /// <param name="ticketIds"></param>
    /// <returns></returns>
    Task UpdateThirdTicketMinPrice(SetThirdTicketMinPriceInput input);

    /// <summary>
    /// 同步第三方OTA渠道价
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task SyncChannelPriceStock(SyncChannelPriceStockUploadMessage receive);

    
    /// <summary>
    /// 处理日历价同步数据
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    Task SyncByCalendarPriceData(SyncChannelByCalendarPriceMessage message);

    /// <summary>
    /// 下单后-触发ota渠道同步
    /// </summary>
    /// <remarks>查询时间，出行日期当天、明天、后天三天的价库</remarks>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task TriggerOpenChannelSync(TriggerOpenChannelSyncMessage receive);

    /// <summary>
    /// 渠道新订 - 触发ota渠道同步
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task NewOrderNotifyTriggerSyncStocks(OpenChannelNotifySyncStocksInput input);

    /// <summary>
    /// 配置渠道设置.同步价库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetChannelSettingSyncPriceStocks(SetChannelSettingSyncPriceStocksInput input);
}