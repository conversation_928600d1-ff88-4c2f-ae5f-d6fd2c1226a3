using Contracts.Common.Scenic.DTOs.FeatureTag;
using Contracts.Common.Scenic.DTOs.ScenicSpot;
using EfCoreExtensions.Abstract;

namespace Scenic.Api.Services.Interfaces;

public interface IScenicSpotService
{
    /// <summary>
    /// 查询景区数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchScenicSpotsOutput>> Search(SearchScenicSpotsInput input);

    /// <summary>
    /// 获取租户已添加景区数据
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GetScenicSpotSimpleInfo>> GetAll();

    /// <summary>
    /// 添加景区
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Add(AddScenicSpotInput input);

    /// <summary>
    /// 批量获取景区详情
    /// </summary>
    Task<List<GetScenicSpotDetailOutput>> GetByIds(List<long> ids);
    
    Task<List<ScenicSpot>> GetScenicSpots(params long[] scenicSpotIds);

    /// <summary>
    /// 编辑景区数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Edit(EditScenicSpotInput input);

    /// <summary>
    /// 切换景区上下架状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SwitchEnabled(SwitchScenicSpotEnabledInput input);

    /// <summary>
    /// 获取已添加景区的城市列表
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GetScenicSpotCitiesOutput>> GetCities(long tenantId, bool isCache, bool needAvailable);

    /// <summary>
    /// 获取景区的景点特色
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<IEnumerable<GetFeatureTagsOutput>> GetTags(List<long> ids);

    /// <summary>
    /// 获取景区图片
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<IEnumerable<ScenicSpotPhotos>> GetPhotos(List<long> ids);

    /// <summary>
    /// 检查景区资源是否已经添加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<CheckExistOutput>> CheckExist(CheckExistInput input);

    /// <summary>
    /// 获取供应商景区(景区下有门票才返回)
    /// </summary>
    /// <param name="supplierId"></param>
    /// <returns></returns>
    Task<IEnumerable<GetBySupplierOutput>> GetBySupplier(long supplierId);

    /// <summary>
    /// 同步saas端的景点信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateByResourceId(SyncScenicSpotInput input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="scenicSpotId"></param>
    /// <returns></returns>
    Task<List<GetScenicSpotSupplierOutput>> GetScenicSpotSuppliers(long scenicSpotId);
}