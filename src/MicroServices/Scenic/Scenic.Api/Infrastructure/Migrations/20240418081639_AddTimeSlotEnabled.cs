using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Scenic.Api.Infrastructure.Migrations
{
    public partial class AddTimeSlotEnabled : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Enabled",
                table: "TicketsTimeSlot",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdateTime",
                table: "TicketsTimeSlot",
                type: "datetime",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Enabled",
                table: "TicketsTimeSlot");

            migrationBuilder.DropColumn(
                name: "UpdateTime",
                table: "TicketsTimeSlot");
        }
    }
}
