using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Scenic.Api.Infrastructure.Migrations
{
    public partial class AddPriceChannelType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<sbyte>(
                name: "PriceChannelType",
                table: "TicketsCalendarPrice",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PriceChannelType",
                table: "TicketsCalendarPrice");
        }
    }
}
