using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Scenic.Api.Infrastructure.Migrations
{
    public partial class TicketsCalendarPriceAddTicketsIdIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_TicketsCalendarPrice_TicketsId",
                table: "TicketsCalendarPrice",
                column: "TicketsId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TicketsCalendarPrice_TicketsId",
                table: "TicketsCalendarPrice");
        }
    }
}
