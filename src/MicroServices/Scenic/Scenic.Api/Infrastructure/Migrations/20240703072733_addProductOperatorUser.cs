using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Scenic.Api.Infrastructure.Migrations
{
    public partial class addProductOperatorUser : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ProductOperatorUser",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    ProductType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    ProductId = table.Column<long>(type: "bigint", nullable: false),
                    SellingPlatform = table.Column<sbyte>(type: "tinyint", nullable: false),
                    OperatorUserId = table.Column<long>(type: "bigint", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductOperatorUser", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_ProductOperatorUser_ProductId_ProductType",
                table: "ProductOperatorUser",
                columns: new[] { "ProductId", "ProductType" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductOperatorUser_TenantId",
                table: "ProductOperatorUser",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProductOperatorUser");
        }
    }
}
