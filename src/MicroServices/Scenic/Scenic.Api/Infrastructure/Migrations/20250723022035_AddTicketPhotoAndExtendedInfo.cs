using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Scenic.Api.Infrastructure.Migrations
{
    public partial class AddTicketPhotoAndExtendedInfo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "BusinessHours",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "CancellationPolicy",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Highlights",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "OtherInstructions",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "Precautions",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ProductDetails",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ScenicNotice",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "UsageInstructions",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ValidityDescription",
                table: "Tickets",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "TicketPhotos",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    ScenicSpotId = table.Column<long>(type: "bigint", nullable: false),
                    TicketId = table.Column<long>(type: "bigint", nullable: false),
                    MediaType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Path = table.Column<string>(type: "varchar(255)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Sort = table.Column<int>(type: "int", nullable: false),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TicketPhotos", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_TicketPhotos_TenantId",
                table: "TicketPhotos",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_TicketPhotos_TicketId",
                table: "TicketPhotos",
                column: "TicketId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TicketPhotos");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "BusinessHours",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "CancellationPolicy",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "Highlights",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "OtherInstructions",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "Precautions",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "ProductDetails",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "ScenicNotice",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "UsageInstructions",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "ValidityDescription",
                table: "Tickets");
        }
    }
}
