// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Scenic.Api.Infrastructure;

#nullable disable

namespace Scenic.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250210022739_AddTicketCostDiscountRate")]
    partial class AddTicketCostDiscountRate
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Scenic.Api.Model.FeatureTags", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("FeatureTags", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.ProductInformationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductTemplateType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TemplateId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductInformationTemplate", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.ProductOperatorUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("ProductId", "ProductType");

                    b.ToTable("ProductOperatorUser", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.ScenicSpot", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ENName")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Intro")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OpeningTime")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("OperatingModel")
                        .HasColumnType("tinyint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ScenicSpotResourceId")
                        .HasColumnType("bigint");

                    b.Property<int>("Star")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("VideoPath")
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicSpot", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.ScenicSpotFeatures", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("FeatureTagId")
                        .HasColumnType("bigint");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ScenicSpotId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicSpotFeatures", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.ScenicSpotPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ScenicSpotId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicSpotPhotos", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.ScenicSpotRedundantData", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("HasCommission")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ScenicSpotId");

                    b.ToTable("ScenicSpotRedundantData", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.SupplySetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsSupply")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SupplySetting", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketOpenSupplierSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("PriceInventorySyncType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)4);

                    b.Property<int>("SyncDateRange")
                        .HasColumnType("int");

                    b.Property<int>("SyncInterval")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketId");

                    b.ToTable("TicketOpenSupplierSetting", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketOtaChannelSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelProductId")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("PriceInventorySyncChannelType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PriceInventorySyncType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)4);

                    b.Property<string>("SupplierProductId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketArea")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TicketId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketId");

                    b.ToTable("TicketOtaChannelSetting", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketOtaChannelSettingItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("SyncSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketOtaChannelSettingId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketId");

                    b.ToTable("TicketOtaChannelSettingItem", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.Tickets", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActivityId")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("AfterPurchaseDays")
                        .HasColumnType("int");

                    b.Property<bool>("AutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("AutoRefundRate")
                        .HasColumnType("int");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("BuyDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("BuyTimeInAdvance")
                        .HasColumnType("time");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("CostDiscountRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte?>("CredentialSourceType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ExchangeLocation")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ExchangeNote")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("ExchangeProof")
                        .HasColumnType("int");

                    b.Property<string>("FeeNotNote")
                        .HasColumnType("text");

                    b.Property<string>("FeeNote")
                        .HasColumnType("text");

                    b.Property<decimal?>("FeiZhuChannelPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsOpenPriceInventorySync")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOpenTimeSlotInventory")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal?>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("NeedToBuyInAdvance")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedToExchange")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OtherNote")
                        .HasColumnType("text");

                    b.Property<string>("PackageId")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte?>("PriceInventorySource")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<int?>("PriceInventorySyncChannelTypes")
                        .HasColumnType("int");

                    b.Property<sbyte?>("PriceInventoryType")
                        .HasColumnType("tinyint");

                    b.Property<int>("RefundDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<int>("RefundRate")
                        .HasColumnType("int");

                    b.Property<TimeOnly>("RefundTimeInAdvance")
                        .HasColumnType("time");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SkuId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .HasColumnType("varchar(128)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketsType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("TouristIDRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("TouristInfoType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("TouristNameRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("TouristPhoneRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("ValidityType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "ScenicSpotId");

                    b.ToTable("Tickets", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketsCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("PriceChannelType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketsId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TimeSlotId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketsId");

                    b.HasIndex("TenantId", "TicketsId");

                    b.HasIndex("TicketsId", "Date");

                    b.ToTable("TicketsCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketsCalendarPricePreview", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("BeginDate")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("PriceChannelType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Stock")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketsId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TimeSlotId")
                        .HasColumnType("bigint");

                    b.Property<string>("Week")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "TicketsId");

                    b.ToTable("TicketsCalendarPricePreview", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketsCombination", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Describe")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Name", "TenantId")
                        .IsUnique();

                    b.ToTable("TicketsCombination", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketsCombinationSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketsCombinationId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketsId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketsCombinationSetting", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.TicketsTimeSlot", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketsId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan>("Time")
                        .HasColumnType("time");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "TicketsId");

                    b.ToTable("TicketsTimeSlot", (string)null);
                });

            modelBuilder.Entity("Scenic.Api.Model.Travelers", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CardValidDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Gender")
                        .HasColumnType("tinyint");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("IdCardType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<string>("InternationalDialingCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("Travelers", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
