using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Scenic.Api.Infrastructure;

public class CustomDbContext : DbContextBase
{
    public CustomDbContext(DbContextOptions dbContextOptions, ITenantIdentify tenantIdentify) : base(dbContextOptions, tenantIdentify)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }

    public DbSet<FeatureTags> FeatureTags { get; set; }
    public DbSet<ScenicSpotFeatures> ScenicSpotFeatures { get; set; }
    public DbSet<ScenicSpot> ScenicSpots { get; set; }
    public DbSet<ScenicSpotPhotos> ScenicSpotPhotos { get; set; }
    public DbSet<Tickets> Tickets { get; set; }
    public DbSet<ScenicSpotRedundantData> ScenicSpotRedundantDatas { get; set; }
    public DbSet<Travelers> Travelers { get; set; }
    public DbSet<TicketsCalendarPrice> TicketsCalendarPrices { get; set; }
    public DbSet<TicketsCalendarPricePreview> TicketsCalendarPricePreviews { get; set; }
    public DbSet<SupplySetting> SupplySettings { get; set; }
    public DbSet<TicketsCombination> TicketsCombinations { get; set; }
    public DbSet<TicketsCombinationSetting> TicketsCombinationSettings { get; set; }

    public DbSet<TicketsTimeSlot> TicketsTimeSlots { get; set; }
    public DbSet<TicketOtaChannelSetting> TicketOtaChannelSettings { get; set; }

    public DbSet<ProductOperatorUser> ProductOperatorUsers { get; set; }

    public DbSet<ProductInformationTemplate> ProductInformationTemplates { get; set; }
    public DbSet<TicketOpenSupplierSetting> TicketOpenSupplierSettings { get; set; }
    public DbSet<TicketOtaChannelSettingItem> TicketOtaChannelSettingItems { get; set; }

    public DbSet<TicketsCombinationPackage> TicketsCombinationPackages { get; set; }
    public DbSet<TicketsCombinationPackageItem> TicketsCombinationPackageItems { get; set; }
    public DbSet<TicketPhotos> TicketPhotos { get; set; }
}
