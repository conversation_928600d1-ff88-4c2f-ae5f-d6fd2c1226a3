using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Scenic.Api.Infrastructure.EntityConfigurations
{
    public class FeatureTagsEntityTypeConfiguration : TenantBaseConfiguration<Model.FeatureTags>, IEntityTypeConfiguration<Model.FeatureTags>
    {
        public void Configure(EntityTypeBuilder<Model.FeatureTags> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Name)
                .HasColumnType("varchar(20)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
        }
    }
}
