using Contracts.Common.Scenic.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Scenic.Api.Infrastructure.EntityConfigurations
{
    public class TicketOtaChannelSettingEntityTypeConfiguration :
        TenantBaseConfiguration<Model.TicketOtaChannelSetting>, IEntityTypeConfiguration<Model.TicketOtaChannelSetting>
    {
        public void Configure(EntityTypeBuilder<Model.TicketOtaChannelSetting> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.TicketId)
                .HasColumnType("bigint");

            builder.Property(s => s.SettingType)
                .HasColumnType("tinyint")
                .HasDefaultValue(OpenChannelSettingType.PriceInventorySync);
            
            builder.Property(s => s.PriceInventorySyncChannelType)
                .HasColumnType("tinyint");

            builder.Property(s => s.ChannelProductId)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.SupplierProductId)
                .HasColumnType("varchar(100)");
            
            builder.Property(s => s.TicketArea)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.ZeroStockThreshold)
                .HasColumnType("int");
            
            builder.Property(s => s.PriceInventorySyncType)
                .HasColumnType("tinyint")
                .HasDefaultValue(PriceInventorySyncType.SyncAll);

            builder.Property(s => s.TimelinessTriggerType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            // Indexes
            builder.HasIndex(s => s.TicketId);
        }
    }
}