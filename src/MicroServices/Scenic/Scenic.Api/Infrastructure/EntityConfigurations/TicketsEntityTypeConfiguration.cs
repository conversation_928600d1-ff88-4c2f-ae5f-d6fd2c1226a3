using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Scenic.Api.Infrastructure.EntityConfigurations
{
    public class TicketsEntityTypeConfiguration : TenantBaseConfiguration<Model.Tickets>, IEntityTypeConfiguration<Model.Tickets>
    {
        public void Configure(EntityTypeBuilder<Model.Tickets> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ScenicSpotId)
                .HasColumnType("bigint");

            builder.Property(s => s.TicketsType)
                .HasColumnType("tinyint");

            builder.Property(s => s.Name)
                .HasColumnType("varchar(200)")
                .IsRequired();

            builder.Property(s => s.EnName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.TouristInfoType)
                .HasColumnType("tinyint");

            builder.Property(s => s.TouristNameRequired)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.TouristIDRequired)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.TouristPhoneRequired)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.FeeNote)
                .HasColumnType("text");

            builder.Property(s => s.FeeNotNote)
                .HasColumnType("text");

            builder.Property(s => s.OtherNote)
                .HasColumnType("text");

            builder.Property(s => s.NeedToBuyInAdvance)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.BuyDaysInAdvance)
                .HasColumnType("int");

            builder.Property(s => s.BuyTimeInAdvance)
                .HasColumnType("time");

            builder.Property(s => s.NeedToExchange)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.ExchangeLocation)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.ExchangeProof)
                .HasColumnType("int");

            builder.Property(s => s.ExchangeNote)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.IsSupportRefund)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.RefundDaysInAdvance)
                .HasColumnType("int");

            builder.Property(s => s.RefundTimeInAdvance)
                .HasColumnType("time");

            builder.Property(s => s.RefundRate)
                .HasColumnType("int");

            builder.Property(s => s.AutoRefundAfterExpiration)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.AutoRefundRate)
                .HasColumnType("int");

            builder.Property(s => s.ValidityType)
                .HasColumnType("tinyint");

            builder.Property(s => s.ValidityBegin)
                .HasColumnType("datetime");

            builder.Property(s => s.ValidityEnd)
                .HasColumnType("datetime");

            builder.Property(s => s.AfterPurchaseDays)
                .HasColumnType("int");

            builder.Property(s => s.CostPrice)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(s => s.SellingPrice)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(s => s.LinePrice)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);
            
            builder.Property(s => s.PriceBasisType)
                .HasColumnType("tinyint")
                .HasDefaultValue(PriceBasisType.Designated);

            builder.Property(s => s.PriceAdjustmentType)
                .HasColumnType("tinyint")
                .HasDefaultValue(PriceAdjustmentType.Designated);

            builder.Property(s => s.PriceAdjustmentValue)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");

            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.CostCurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString())
                .IsRequired();

            builder.Property(s => s.SaleCurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString())
                .IsRequired();

            builder.Property(s => s.CredentialSourceType)
                .HasColumnType("tinyint")
                .IsRequired(false);

            builder.Property(s => s.FeiZhuChannelPrice)
                .HasColumnType("decimal(18,2)")
                .IsRequired(false);

            builder.Property(s => s.ActivityId)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.PackageId)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.SkuId)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.B2bSellingStatus)
               .HasColumnType("tinyint(1)");

            builder.Property(s => s.IsOpenTimeSlotInventory)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.IsOpenPriceInventorySync)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.PriceInventorySyncChannelTypes)
                .HasColumnType("int");

            builder.Property(s => s.PriceInventoryType)
                .HasColumnType("tinyint");

            builder.Property(s => s.PriceInventorySource)
                .HasColumnType("tinyint")
                .HasDefaultValue(PriceInventorySource.System);

            builder.Property(s => s.DevelopUserId)
             .HasColumnType("bigint");

            builder.Property(s => s.OperatorUserId)
             .HasColumnType("bigint");

            builder.Property(s => s.SupplierName)
               .HasColumnType("varchar(128)");
            
            builder.Property(s => s.CostDiscountRate)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.SupplierIsSale)
                .HasColumnType("tinyint(1)")
                .ValueGeneratedNever();//控制赋值

            builder.Property(s => s.IsCompensation)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.IsChannelTimeliness)
                .HasColumnType("tinyint(1)");
            
            builder.Property(s => s.TimelinessChannelTypes)
                .HasColumnType("int");

            builder.Property(s => s.SupplierServiceInfoId)
                .HasColumnType("bigint");

            builder.Property(s => s.SupplierServiceInfoName)
              .HasColumnType("varchar(128)");
            
            builder.Property(s => s.Highlights)
                .HasColumnType("text");
            
            builder.Property(s => s.BusinessHours)
                .HasColumnType("text");
            
            builder.Property(s => s.Highlights)
                .HasColumnType("text");
            
            builder.Property(s => s.ScenicNotice)
                .HasColumnType("text");
            
            builder.Property(s => s.Address)
                .HasColumnType("text");
            
            builder.Property(s => s.ProductDetails)
                .HasColumnType("text");
            
            builder.Property(s => s.ValidityDescription)
                .HasColumnType("text");
            
            builder.Property(s => s.Precautions)
                .HasColumnType("text");
            
            builder.Property(s => s.UsageInstructions)
                .HasColumnType("text");
            
            builder.Property(s => s.OtherInstructions)
                .HasColumnType("text");
            
            builder.Property(s => s.CancellationPolicy)
                .HasColumnType("text");

            builder.HasIndex(s => new { s.TenantId, s.ScenicSpotId });
        }
    }
}
