using Contracts.Common.User.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Scenic.Api.Infrastructure.EntityConfigurations
{
    public class TravelersEntityTypeConfiguration : TenantBaseConfiguration<Model.Travelers>, IEntityTypeConfiguration<Model.Travelers>
    {
        public void Configure(EntityTypeBuilder<Model.Travelers> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.Name)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.PhoneNumber)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.IDCard)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.InternationalDialingCode)
                .HasColumnType("varchar(20)");
            
            builder.Property(s => s.Email)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.IdCardType)
                .HasColumnType("tinyint")
                .HasDefaultValue(UserIdCardType.IdCard);
            
            builder.Property(s => s.Gender)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.CardValidDate)
                .HasColumnType("datetime");
            
            builder.Property(s => s.Birthday)
                .HasColumnType("datetime");


            builder.HasIndex(s => new { s.TenantId, s.UserId });
        }
    }
}
