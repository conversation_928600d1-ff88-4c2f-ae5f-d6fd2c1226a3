using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Scenic.Api.Infrastructure.EntityConfigurations
{
    public class TicketsCombinationPackageItemEntityTypeConfiguration :
        TenantBaseConfiguration<Model.TicketsCombinationPackageItem>,
        IEntityTypeConfiguration<Model.TicketsCombinationPackageItem>
    {
        public void Configure(EntityTypeBuilder<Model.TicketsCombinationPackageItem> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.TicketsCombinationId)
                .HasColumnType("bigint");

            builder.Property(s => s.TicketsCombinationPackageId)
                .HasColumnType("bigint");

            builder.Property(x => x.TicketsId)
                .HasColumnType("bigint");

            builder.Property(x => x.TimeSlotId)
                .HasColumnType("bigint");

            builder.Property(x => x.Quantity)
                .HasColumnType("int");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            // index
            builder.HasIndex(s => new {s.TicketsCombinationPackageId, s.TenantId});
        }
    }
}