using Contracts.Common.Resource.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Scenic.Api.Infrastructure.EntityConfigurations
{
    public class ScenicSpotEntityTypeConfiguration : TenantBaseConfiguration<Model.ScenicSpot>, IEntityTypeConfiguration<Model.ScenicSpot>
    {
        public void Configure(EntityTypeBuilder<Model.ScenicSpot> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ScenicSpotResourceId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.OperatingModel)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.Name)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.Star)
                .HasColumnType("int");
            
            builder.Property(s => s.ContactNumber)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.CountryCode)
                .HasColumnType("int");
            
            builder.Property(s => s.CountryName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.ProvinceCode)
                .HasColumnType("int");
            
            builder.Property(s => s.ProvinceName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.CityCode)
                .HasColumnType("int");
            
            builder.Property(s => s.CityName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.DistrictCode)
                .HasColumnType("int");
            
            builder.Property(s => s.DistrictName)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.Address)
                .HasColumnType("varchar(100)")
                .IsRequired();

            builder.Property(s => s.Location)
                .HasColumnType("point")
                .IsRequired();

            builder.Property(s => s.VideoPath)
                .HasColumnType("varchar(256)");
            
            builder.Property(s => s.OpeningTime)
                .HasColumnType("varchar(100)")
                .IsRequired();

            builder.Property(s => s.Intro)
                .HasColumnType("text")
                .IsRequired();

            builder.Property(s => s.Enabled)
                .HasColumnType("tinyint(1)");

            builder.Property(s => s.ENName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.CoordinateType)
                .HasColumnType("int")
                .HasDefaultValue(CoordinateType.BD09);
            
            builder.Property(s => s.IsCompensation)
                .HasColumnType("tinyint(1)");
        }
    }
}
