namespace Hangfire.Server.Filters;

public class ServerExceptionFilter : IServerExceptionFilter
{
    public void OnServerException(ServerExceptionContext filterContext)
    {
        var logger = Logging.LogProvider.GetCurrentClassLogger();
        logger.Log(Logging.LogLevel.Error, () => filterContext.Exception.Message, filterContext.Exception);
        //filterContext.ExceptionHandled = true;
    }
}
