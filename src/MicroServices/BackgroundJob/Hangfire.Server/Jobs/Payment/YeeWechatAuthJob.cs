using Common.ServicesHttpClient;
using Hangfire.Server.Extensions;
using HangfireClient.Jobs.Payment;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace Hangfire.Server.Jobs.Payment
{
    public class YeeWechatAuthJob : IYeeWechatAuthJob
    {
        private readonly ILogger<YeeWechatAuthJob> _logger;
        private readonly IOptions<ServicesAddress> _servicesAddress;
        private readonly IHttpClientFactory _httpClientFactory;

        public YeeWechatAuthJob(ILogger<YeeWechatAuthJob> logger,
            IOptions<ServicesAddress> servicesAddress,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            _servicesAddress = servicesAddress;
            _httpClientFactory = httpClientFactory;
        }

        public async Task YeeWechatAuthQuery(long tenantId)
        {
            var uri = new Uri(new Uri(_servicesAddress.Value.Payment_YeeWechatAuthQuery()), $"?TenantId={tenantId}");
            var result = await _httpClientFactory.InternalGetAsync<JObject>(uri.ToString());

            var authorizeState = result["authorizeState"]?.ToString();
            if (authorizeState == "AUTHORIZE_STATE_AUTHORIZED")
            {
                return;//已授权
            }

            var applymentState = result["applymentState"].ToString();
            applymentState = applymentState.Replace("APPLYMENT_STATE_", "");
            switch (applymentState)
            {
                case "WAITTING_FOR_AUDIT"://WAITTING_FOR_AUDIT【审核中】
                case "EDITTING"://EDITTING【编辑中】可用同一个业务申请编号重新提交
                case "WAITTING_FOR_CONFIRM_CONTACT"://WAITTING_FOR_CONFIRM_CONTACT【待确认联系信息】扫描返回图片;
                case "WAITTING_FOR_CONFIRM_LEGALPERSON"://WAITTING_FOR_CONFIRM_LEGALPERSON【待账户验证】扫描完成验证;
                    {
                        //1.4 查询申请单审核结果：SAAS服务商/平台商提交商家资料后，建议每隔五分钟调用“查询微信实名认证状态” 查询申请单审核结果。
                        //12h再次查询实名认证结果
                        BackgroundJob.Schedule<IYeeWechatAuthJob>(a => a.YeeWechatAuthQuery(tenantId),
                            TimeSpan.FromHours(2));
                    }
                    break;
                //case "REJECTED"://REJECTED【审核驳回】根据原因更换请求号，重新提交申请
                //case "FREEZED"://FREEZED【已冻结】查看驳回原因，扫描完成授权流程
                //case "CANCELED"://CANCELED【已作废】已被撤销，不可操作
                //case "PASSED"://PASSED【审核通过】扫描完成授权
                default:
                    break;
            }
        }
    }
}
