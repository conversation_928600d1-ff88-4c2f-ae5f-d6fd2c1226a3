using Common.ServicesHttpClient;
using HangfireClient.Jobs.Order;
using Hangfire.Server.Filters;
using Microsoft.Extensions.Options;
using HangfireClient.Jobs;
using Hangfire.Server.Extensions;
using Contracts.Common.Tenant.DTOs.Agency;
using Newtonsoft.Json;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.Enums;
using Common.GlobalException;

namespace Hangfire.Server.Jobs.Order;

public class ReceiptOrderJob : IReceiptOrderJob
{
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;

    public ReceiptOrderJob(IOptions<ServicesAddress> servicesAddress,
            IHttpClientFactory httpClientFactory)
    {
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
    }

    [RecurringJob("Order.自动生成收款结算单", "0 20 3 * * ?")]
    public async Task<IJobResult> AutoCreate()
    {
        var now = DateTime.Today;
        var result = BackgroundJob.Enqueue(() => AutoCreate(now));
        return new JobResult { Data = result };
    }

    /// <summary>
    /// 周期核算分销商等级
    /// </summary>
    /// <param name="date"></param>
    /// <returns></returns>
    public async Task<JobResult> AutoCreate(DateTime now)
    {
        //查询分销商用户
        var request = new GetAgenciesByReceiptOrderInput
        {
            AgencyRecencyStatus = new List<AgencyRecencyStatus> {
                AgencyRecencyStatus.WaitRepurchase,
                AgencyRecencyStatus.Normal,
                AgencyRecencyStatus.Silent,
            }
        };
        using var httpContent = new StringContent(JsonConvert.SerializeObject(request),
            encoding: System.Text.Encoding.UTF8, "application/json");
        var requestUrl = _servicesAddress.Value.Tenant_GetAgenciesByReceiptOrder();
        var agencies = await _httpClientFactory.InternalPostAsync<List<GetAgenciesByReceiptOrderOutput>>(requestUrl,
            httpContent: httpContent);
        if (agencies.Any() is false)
            return new JobResult { Message = "自动生成收款结算单-不存在生成收款结算单分销商" };

        var result = new AutoCreateResult(new List<long>(), new List<long>());
        //月结和半月结
        if (now.Day == 1)
        {
            var semiMonthlyResult = await AutoCreateBySettlementPeriod(agencies, now, SupplierSettlementPeriod.SemiMonthly);
            result = MergeAutoCreateResult(result, semiMonthlyResult);
            var monthlyResult = await AutoCreateBySettlementPeriod(agencies, now, SupplierSettlementPeriod.Monthly);
            result = MergeAutoCreateResult(result, monthlyResult);
        }
        else if (now.Day == 16)//半月结
        {
            var semiMonthlyResult = await AutoCreateBySettlementPeriod(agencies, now, SupplierSettlementPeriod.SemiMonthly);
            result = MergeAutoCreateResult(result, semiMonthlyResult);
        }
        //周结
        if (now.DayOfWeek == DayOfWeek.Monday)
        {
            var weeklyResult = await AutoCreateBySettlementPeriod(agencies, now, SupplierSettlementPeriod.Weekly);
            result = MergeAutoCreateResult(result, weeklyResult);
        }

        return new JobResult
        {
            Message = $"自动生成收款结算单-执行完成,success:{result.SuccessAgencies.Count},failed:{result.FailAgencies.Count}",
            Data = result
        };
    }

    private async Task<AutoCreateResult> AutoCreateBySettlementPeriod(List<GetAgenciesByReceiptOrderOutput> agencies,
        DateTime billingCycleEnd, 
        SupplierSettlementPeriod supplierSettlementPeriod)
    {
        var result = new AutoCreateResult(new List<long>(),new List<long>());
        DateTime billingCycleBegin;

        if (supplierSettlementPeriod == SupplierSettlementPeriod.Single)
            return result;
        else if (supplierSettlementPeriod == SupplierSettlementPeriod.Weekly)
        {
            billingCycleEnd = billingCycleEnd.AddDays(-1);
        }
        else if (supplierSettlementPeriod == SupplierSettlementPeriod.SemiMonthly)
        {
            if (billingCycleEnd.Day == 1)
                billingCycleEnd = billingCycleEnd.AddDays(-1);
            else
                billingCycleEnd = billingCycleEnd.AddDays(-1);
        }
        else
        {
            billingCycleEnd = billingCycleEnd.AddDays(-1);
        }
        //账期统一从2024.10.1开始
        billingCycleBegin = new DateTime(2024,10,1);

        var agenciesBySettlementPeriod = agencies.Where(x => x.SettlementPeriod == supplierSettlementPeriod).ToList();
        foreach (var agencyGroup in agenciesBySettlementPeriod.GroupBy(x=>x.TenantId))
        {
            foreach (var agency in agencyGroup)
            {
                var headers = new List<KeyValuePair<string, string>>(1) {
                    new("tenant", agency.TenantId.ToString())
                };
                var requestUrlBySetting = _servicesAddress.Value.Ordert_ReceiptOrderSetting();
                var setting = await _httpClientFactory.InternalGetAsync<ReceiptOrderSettingOutput>(requestUrlBySetting, headers: headers);
                if (setting != null &&
                    setting.TenantDepartmentIds.Any() is true &&
                    ((agency.TenantDepartmentId.HasValue && !setting.TenantDepartmentIds.Contains(agency.TenantDepartmentId.Value))
                        || !agency.TenantDepartmentId.HasValue))
                {
                    continue;
                }
                try
                {
                    //查询分销商用户
                    var request = new ReceiptOrderAutoCreateInput
                    {
                        AgencyId = agency.Id,
                        BillingCycleBegin = billingCycleBegin,
                        BillingCycleEnd = billingCycleEnd,
                        HotelOrderDateType = GetReceiptOrderDateType(agency.DimensionType, OrderType.Hotel),
                        TicketOrderDateType = GetReceiptOrderDateType(agency.DimensionType, OrderType.Ticket),
                        ReservationOrderDateType = ReceiptDateTye.Finish,
                        ScenicTicketOrderDateType = GetReceiptOrderDateType(agency.DimensionType, OrderType.ScenicTicket),
                        LineOrderDateType = GetReceiptOrderDateType(agency.DimensionType, OrderType.TravelLineOrder),
                        CarProductOrderDateType = GetReceiptOrderDateType(agency.DimensionType, OrderType.CarProduct),
                        CreditReChargeOrderDateType = ReceiptDateTye.ChargeCreate,
                    };

                    using var httpContent = new StringContent(JsonConvert.SerializeObject(request),
                        encoding: System.Text.Encoding.UTF8, "application/json");
                    var requestUrl = _servicesAddress.Value.Ordert_ReceiptOrderAutoCreate();
                    var data = await _httpClientFactory.InternalPostAsync<ReceiptSettlementOrderCreateOutput>(requestUrl,
                        httpContent: httpContent, headers: headers);
                    if (data == null)
                        continue;
                    if (data.SettlementOrderIds.Any())
                        result.SuccessAgencies.Add(agency.Id);
                }
                catch (Exception ex)
                {
                    result.FailAgencies.Add(agency.Id);
                    continue;
                }
            }
        }

        return result;
    }

    private AutoCreateResult MergeAutoCreateResult(AutoCreateResult org, AutoCreateResult target)
    {
        if (target.SuccessAgencies.Any())
            org.SuccessAgencies.AddRange(target.SuccessAgencies);
        if (target.FailAgencies.Any())
            org.FailAgencies.AddRange(target.FailAgencies);

        return org;
    }

    private ReceiptDateTye GetReceiptOrderDateType(SettlementDimensionType type,OrderType orderType)
    {
        if (orderType == OrderType.Hotel)
        {
            return type switch
            {
                SettlementDimensionType.BookingTime => ReceiptDateTye.Create,
                SettlementDimensionType.TravelTime => ReceiptDateTye.CheckIn,
                SettlementDimensionType.FinishTime => ReceiptDateTye.CheckOut,
                _ => throw new BusinessException($"{nameof(type)} is error")
            };
        }
        else if (orderType == OrderType.Ticket || orderType == OrderType.ScenicTicket)
        {
            return type switch
            {
                SettlementDimensionType.BookingTime => ReceiptDateTye.Create,
                SettlementDimensionType.TravelTime => ReceiptDateTye.Create,
                SettlementDimensionType.FinishTime => ReceiptDateTye.Finish,
                _ => throw new BusinessException($"{nameof(type)} is error")
            };
        }
        else if (orderType == OrderType.TravelLineOrder || orderType == OrderType.CarProduct)
        {
            return type switch
            {
                SettlementDimensionType.BookingTime => ReceiptDateTye.Create,
                SettlementDimensionType.TravelTime => ReceiptDateTye.TravelBeginDate,
                SettlementDimensionType.FinishTime => ReceiptDateTye.Finish,
                _ => throw new BusinessException($"{nameof(type)} is error")
            };
        }

        return ReceiptDateTye.Create;
    }

    record AutoCreateResult(List<long> SuccessAgencies, List<long> FailAgencies);
}
