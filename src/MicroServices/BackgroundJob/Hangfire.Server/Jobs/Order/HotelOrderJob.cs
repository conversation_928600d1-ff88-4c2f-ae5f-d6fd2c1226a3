using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common;
using Contracts.Common.Order.DTOs.HotelApiOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.Messages;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Hangfire.Server.Extensions;
using HangfireClient.Jobs;
using HangfireClient.Jobs.Order;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Text;

namespace Hangfire.Server.Jobs.Order
{
    public class HotelOrderJob : IHotelOrderJob
    {
        private readonly IOptions<ServicesAddress> _servicesAddress;
        private readonly IHttpClientFactory _httpClientFactory;

        public HotelOrderJob(IOptions<ServicesAddress> servicesAddress,
            IHttpClientFactory httpClientFactory)
        {
            _servicesAddress = servicesAddress;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<IJobResult> AutoCheckIn(long hotelOrderId, long tenantId)
        {
            var result = new JobResult();
            var requestUrl = _servicesAddress.Value.Order_HotelOrderCheckIn();
            var headers = new List<KeyValuePair<string, string>>(1) {
                new("tenant", tenantId.ToString())
            };
            var body = new HotelOrderInput
            {
                Id = hotelOrderId,
            };
            using var httpContent = new StringContent(JsonConvert.SerializeObject(body), System.Text.Encoding.UTF8, "application/json");
            try
            {
                await _httpClientFactory.InternalPostAsync(requestUrl, headers, httpContent);
            }
            catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
            {
                result.Message = ex.BusinessErrorType + " - " + ex.Message;
            }
            catch
            {
                throw;
            }
            return result;
        }

        public async Task<IJobResult> AutoFinished(long hotelOrderId, long tenantId)
        {
            var result = new JobResult();
            var requestUrl = _servicesAddress.Value.Order_HotelOrderCheckOut();
            var headers = new List<KeyValuePair<string, string>>(1) {
                new("tenant", tenantId.ToString())
            }; var body = new HotelOrderInput
            {
                Id = hotelOrderId,
            };
            using var httpContent = new StringContent(JsonConvert.SerializeObject(body), System.Text.Encoding.UTF8, "application/json");
            try
            {
                await _httpClientFactory.InternalPostAsync(requestUrl, headers, httpContent);
            }
            catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
            {
                result.Message = ex.BusinessErrorType + " - " + ex.Message;
            }
            catch
            {
                throw;
            }
            return result;
        }

        public async Task<IJobResult> ApiOrderConfirmTimeoutCancel(long baseOrderId, long tenantId)
        {
            //查询订单
            var requestUrl = _servicesAddress.Value.Order_HotelApiOrderQueryAndHandleResult();
            var headers = new List<KeyValuePair<string, string>>(1) {
                new("tenant", tenantId.ToString())
            };
            QueryHotelApiOrderMessage input = new()
            {
                BaseOrderId = baseOrderId,
                TenantId = tenantId
            };
            var httpContent = new StringContent(JsonConvert.SerializeObject(input), System.Text.Encoding.UTF8, "application/json");

            var result = await _httpClientFactory.InternalPostAsync<QueryAndHandleResultOutput>(requestUrl, headers, httpContent);

            //待确认 尝试执行取消API订单 非直采 HOP订单
            if (result.Status == Contracts.Common.Resource.Enums.SupplierApiOrderStatus.WaitForConfirm
                && result.SupplierApiType == Contracts.Common.Tenant.Enums.SupplierApiType.Hop
                && !result.UnionOrderId.HasValue
                && (result.IsDirect is not true))
            {
                var cancelOrderInput = new CancelOrderInput()
                {
                    TenantId = result.TenantId,
                    SupplierApiType = result.SupplierApiType,
                    BaseOrderId = result.BaseOrderId,
                    SupplierOrderId = result.SupplierOrderId,
                };
                var requestUri = _servicesAddress.Value.Resource_ThirdHotelCancelOrder();
                using var content = new StringContent(JsonConvert.SerializeObject(cancelOrderInput), Encoding.UTF8, "application/json");
                var cancelOrderOutput = await _httpClientFactory.InternalPostAsync<CancelOrderOutput>(requestUri, httpContent: content);
                //var stringContent = new StringContent(JsonConvert.SerializeObject(new HotelApiOrderCancelInput
                //{
                //    BaseOrderId = baseOrderId,
                //    TenantId = tenantId
                //}), System.Text.Encoding.UTF8, "application/json");
                //var res = await _httpClientFactory.InternalPostAsync<bool>(_servicesAddress.Value.Order_HotelApiOrderCancel(), headers, stringContent);

                //查询api订单并处理更新订单
                result = await _httpClientFactory.InternalPostAsync<QueryAndHandleResultOutput>(requestUrl, headers, httpContent);

                //取消成功 检查查询结果是否一致
                if (cancelOrderOutput.IsSuccessed)
                {
                    if (result.Status is not (Contracts.Common.Resource.Enums.SupplierApiOrderStatus.Closed
                        or Contracts.Common.Resource.Enums.SupplierApiOrderStatus.Refunded
                        or Contracts.Common.Resource.Enums.SupplierApiOrderStatus.Refused))
                    {
                        throw new BusinessException("取消API采购订单成功，查询订单详情采购状态不符合条件");
                    }
                }
            }
            return new JobResult() { Data = result };
        }

        public async Task<IJobResult> GDSAutoConfirmed(long hotelOrderId, long tenantId, int supplierApiType)
        {
            var result = new JobResult();
            var requestUrl = _servicesAddress.Value.Order_GDSHotelOrderConfirmed();
            var headers = new List<KeyValuePair<string, string>>(1) {
                new("tenant", tenantId.ToString())
            };
            var body = new GDSGetBookingMessage
            {
                HotelOrderId = hotelOrderId,
                SupplierApiType = (Contracts.Common.Tenant.Enums.SupplierApiType)supplierApiType
            };
            using var httpContent = new StringContent(JsonConvert.SerializeObject(body), System.Text.Encoding.UTF8, "application/json");
            try
            {
                await _httpClientFactory.InternalPostAsync(requestUrl, headers, httpContent);
            }
            catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
            {
                result.Message = ex.BusinessErrorType + " - " + ex.Message;
            }
            catch
            {
                throw;
            }
            return result;
        }
    }
}
