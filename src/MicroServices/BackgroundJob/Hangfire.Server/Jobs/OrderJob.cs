using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.OrderDelayed;
using Contracts.Common.Order.DTOs.ReservationOrder;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.DTOs.TicketsCombinationOrder;
using Contracts.Common.Tenant.DTOs.AgencyRecencyOrder;
using Contracts.Common.Tenant.DTOs.SupplierApiFields;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Hangfire.Server.Extensions;
using Hangfire.Server.Filters;
using HangfireClient.Jobs;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Net.Http;
using System.Text;

namespace Hangfire.Server.Jobs.Resource;

public class OrderJob : IOrderJob
{
    private readonly IOptions<ServicesAddress> _servicesAddress;
    private readonly IHttpClientFactory _httpClientFactory;

    public OrderJob(IOptions<ServicesAddress> servicesAddress,
        IHttpClientFactory httpClientFactory)
    {
        _servicesAddress = servicesAddress;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<IJobResult> CloseTimeoutOrder(long baseOrderId, long tenantId)
    {
        var result = new JobResult();
        var requestUrl = _servicesAddress.Value.Order_CloseTimeoutOrder(baseOrderId);
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        try
        {
            await _httpClientFactory.InternalPostAsync(requestUrl, headers);
        }
        catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
        {
            result.Message = ex.Message;
        }
        catch
        {
            throw;
        }
        return result;
    }

    public async Task<IJobResult> CloseTimeoutReservationOrder(long reservationOrderId, long tenantId)
    {
        var result = new JobResult();
        var requestUrl = _servicesAddress.Value.Order_CloseTimeOutReservationOrder(reservationOrderId);
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        try
        {
            await _httpClientFactory.InternalPostAsync(requestUrl, headers);
        }
        catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
        {
            result.Message = ex.Message;
        }
        catch
        {
            throw;
        }
        return result;
    }

    public async Task<IJobResult> OrderDelayedTimeoutCancel(long baseOrderId, long tenantId)
    {
        var result = new JobResult();
        var requestUrl = _servicesAddress.Value.Order_OrderDelayedTimeoutCancel();
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        OrderDelayedCancelInput input = new()
        {
            BaseOrderId = baseOrderId,
            OperationUser = new Contracts.Common.Order.DTOs.OperationUserDto
            {
                UserType = Contracts.Common.Order.Enums.UserType.None
            }
        };
        using var httpContent = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        try
        {
            await _httpClientFactory.InternalPostAsync(requestUrl, headers, httpContent);
        }
        catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
        {
            result.Message = ex.Message;
        }
        catch
        {
            throw;
        }
        return result;
    }


    public async Task<IJobResult> OrderDelayedPayNotify(long baseOrderId, long tenantId)
    {
        var result = new JobResult();
        var requestUrl = _servicesAddress.Value.Order_OrderDelayedPayNotify();
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        DelayedPayNotifyInput input = new()
        {
            BaseOrderId = baseOrderId
        };
        using var httpContent = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
        await _httpClientFactory.InternalPostAsync(requestUrl, headers, httpContent);
        return result;
    }

    public async Task<IJobResult> CloseAgencyCreditChargeOrder(long id, long tenantId)
    {
        var result = new JobResult();
        var requestUrl = _servicesAddress.Value.Order_CloseAgencyCreditChargeOrder(id);
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        try
        {
            await _httpClientFactory.InternalPostAsync(requestUrl, headers);
        }
        catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
        {
            result.Message = ex.Message;
        }
        catch
        {
            throw;
        }
        return result;
    }

    public async Task<IJobResult> ScenicTicketCombinationOrderDelivery(long ticketsCombinationOrderId, long tenantId)
    {
        var result = new JobResult();
        var requestUrl = _servicesAddress.Value.Order_ScenicTicketCombinationOrderDelivery(ticketsCombinationOrderId);
        var headers = new List<KeyValuePair<string, string>> { new("tenant", tenantId.ToString()) };
        try
        {
            var response =  await _httpClientFactory.InternalPostAsync<CombinationOrderDeliveryOutput>(requestUrl, headers);
            result.Data = response;
        }
        catch (BusinessException ex) when (ex.BusinessErrorType == ErrorTypes.Common.NotSupportedOperation.ToString())
        {
            result.Message = ex.Message;
        }
        catch
        {
            throw;
        }
        return result;
    }

    
    [RecurringJob("Order.完结电商订单", "0 1 1 * * ?")]
    public async Task<IJobResult> CompletedMailOrder()
    {
        var requestUrl = _servicesAddress.Value.Order_CompletedMailOrder();
        var response = await _httpClientFactory.InternalPostAsync<IEnumerable<long>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.券类订单过期自动完结", "0 7 3 * * ?")]
    public async Task<IJobResult> FinishExpirationTicketOrder()
    {
        var requestUrl = _servicesAddress.Value.Order_FinishExpirationTicketOrder();
        var response = await _httpClientFactory.InternalPostAsync<IEnumerable<FinishExpirationTicketOrderOutput>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.预约单过期自动完结", "0 11 2 * * ?")]
    public async Task<IJobResult> FinishExpirationReservationOrder()
    {
        var requestUrl = _servicesAddress.Value.Order_FinishExpirationReservationOrder();
        var response = await _httpClientFactory.InternalPostAsync<IEnumerable<FinishExpirationReservationOrderOutput>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.景点门票过期自动退", "0 12 3 * * ?")]
    public async Task<IJobResult> ScenicTicketOrderAutoRefundAfterExpiration()
    {
        var requestUrl = _servicesAddress.Value.Order_ScenicTicketOrderAutoRefundAfterExpiration();
        var response = await _httpClientFactory.InternalPostAsync<IEnumerable<long>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.券类订单时间提醒", "0 0 19 * * ? ")]
    public async Task<IJobResult> TicketOrderAutoTimeReminder()
    {
        var requestUrl = _servicesAddress.Value.Order_TicketOrderAutoTimeReminder();
        var response = await _httpClientFactory.InternalPostAsync<List<long>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.预约单时间提醒", "0 0 19 * * ? ")]
    public async Task<IJobResult> ReservationOrderAutoTimeReminder()
    {
        var requestUrl = _servicesAddress.Value.Order_ReservationOrderAutoTimeReminder();
        var response = await _httpClientFactory.InternalPostAsync<List<long>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.酒店入住时间提醒", "0 0 19 * * ? ")]
    public async Task<IJobResult> HotelOrderCheckInTimeReminder()
    {
        var requestUrl = _servicesAddress.Value.Order_HotelOrderCheckInTimeReminder();
        var response = await _httpClientFactory.InternalPostAsync<List<long>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.景点门票时间提醒", "0 0 19 * * ? ")]
    public async Task<IJobResult> ScenicTicketOrderAutoTimeReminder()
    {
        var requestUrl = _servicesAddress.Value.Order_ScenicTicketOrderAutoTimeReminder();
        var response = await _httpClientFactory.InternalPostAsync<List<long>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.线路出行时间提醒", "0 0 19 * * ? ")]
    public async Task<IJobResult> TravelLineOrderAutoTimeReminder()
    {
        var requestUrl = _servicesAddress.Value.Order_TravelLineOrderAutoTimeReminder();
        var response = await _httpClientFactory.InternalPostAsync<List<long>>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.批量更新base订单的连续单号", "0 20 1 * * ? ")]
    public async Task<IJobResult> BatchUpdateBaseOrderSeriesNumberReminder()
    {
        var requestUrl = _servicesAddress.Value.Order_BatchUpdateBaseOrderSeriesNumber();
        var response = await _httpClientFactory.InternalPostAsync<long>(requestUrl);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.更新希望保保险产品", "0 50 1 * * ? ")]
    public async Task<IJobResult> UpdateThirdInsureProductReminder()
    {
        //获取希望保供应商
        var supplierApiSettingInfoUrl = _servicesAddress.Value.Tenant_GetSupplierApiSettingInfos();
        var input = new GetApiSettingInfosInput() { SupplierApiType = Contracts.Common.Tenant.Enums.SupplierApiType.PingAnHopeInsurance };
        var stringContent = new StringContent(JsonConvert.SerializeObject(input),
            Encoding.UTF8, "application/json");
        var supplierApiSettings = await _httpClientFactory.InternalPostAsync<List<SupplierApiSettingDto>>(supplierApiSettingInfoUrl, httpContent: stringContent);
        var supplierApiSetting = supplierApiSettings.FirstOrDefault();
        var userName = supplierApiSetting?.SupplierApiSettingFields?
             .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.PingAnHopeInsuranceCode.PAHInsuranceUserName)?.FieldValue;
        var password = supplierApiSetting?.SupplierApiSettingFields?
              .FirstOrDefault(x => x.FieldCode == SupplierApiFieldCodeManager.PingAnHopeInsuranceCode.PAHInsurancePassword)?.FieldValue;
        if (string.IsNullOrEmpty(userName) ||
            string.IsNullOrEmpty(password))
            return new JobResult { };

        var upateThirdInsureProductUrl = _servicesAddress.Value.Order_UpateThirdInsureProduct();
        var upateThirdInsureProductInput = new SearchSupplierInsureProductInput { 
            SupplierId = supplierApiSetting.SupplierId,
            Password = password,
            UserName = userName,
        };

        var upateThirdInsureProductContent = new StringContent(JsonConvert.SerializeObject(upateThirdInsureProductInput), Encoding.UTF8, "application/json");
        var response = await _httpClientFactory.InternalPostAsync<long>(upateThirdInsureProductUrl, httpContent: upateThirdInsureProductContent);
        return new JobResult { Data = response };
    }

    [RecurringJob("Order.批量投保", "0 30 23 * * ? ")]
    public async Task<IJobResult> BatchInsurePolicyReminder()
    {
        var requestUrl = _servicesAddress.Value.Order_GetNeedInsureOrders();
        var insureOrders = await _httpClientFactory.InternalPostAsync<List<GetNeedInsureOrderOutput>>(requestUrl);

        var insureByOrderUrl = _servicesAddress.Value.Order_InsureByOrder();
        foreach (var insureOrder in insureOrders)
        {
            var input = new NeedInsureOrderInput() 
            { 
                BaseOrderId = insureOrder.BaseOrderId,
                InsureProductId = insureOrder.InsureProductId,
            };
            var stringContent = new StringContent(JsonConvert.SerializeObject(input),
                Encoding.UTF8, "application/json");

            try
            {
                await _httpClientFactory.InternalPostAsync<bool>(insureByOrderUrl, httpContent: stringContent);
            }
            catch { }
        }

        return new JobResult { };
    }
}
