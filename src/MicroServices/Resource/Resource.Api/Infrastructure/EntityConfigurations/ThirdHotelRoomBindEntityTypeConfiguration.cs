using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Resource.Api.Infrastructure.EntityConfigurations
{
    public class ThirdHotelRoomBindEntityTypeConfiguration : KeyBaseConfiguration<Model.ThirdHotelRoomBind>, IEntityTypeConfiguration<Model.ThirdHotelRoomBind>
    {
        public void Configure(EntityTypeBuilder<Model.ThirdHotelRoomBind> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.SupplierApiType)
                .HasColumnType("tinyint");

            builder.Property(s => s.ResourceRoomId)
                .HasColumnType("bigint");

            builder.Property(s => s.ThirdRoomId)
                .HasColumnType("varchar(64)");

            builder.HasIndex(s => new { s.SupplierApiType, s.ResourceRoomId }).IsUnique();
        }
    }
}
