using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Resource.Api.Infrastructure.EntityConfigurations
{
    public class ScenicSpotPhotosEntityTypeConfiguration : KeyBaseConfiguration<Model.ScenicSpotPhotos>, IEntityTypeConfiguration<Model.ScenicSpotPhotos>
    {
        public void Configure(EntityTypeBuilder<Model.ScenicSpotPhotos> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ScenicSpotId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.Path)
                .HasColumnType("varchar(256)")
                .IsRequired();

            builder.Property(s => s.Sort)
                .HasColumnType("int");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            builder.HasIndex(s => s.ScenicSpotId);
        }
    }
}
