using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Resource.Api.Infrastructure.EntityConfigurations;

public class AirportEntityConfiguration : KeyBaseConfiguration<Airport>, IEntityTypeConfiguration<Airport>
{
    public void Configure(EntityTypeBuilder<Airport> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(x => x.CountryCode)
            .HasColumnType("int")
            .IsRequired();

        builder.Property(x => x.CityCode)
            .HasColumnType("int")
            .IsRequired();

        builder.Property(x => x.IATA)
            .HasColumnType("varchar(10)")
            .IsRequired();

        builder.Property(x => x.ICAO)
            .HasColumnType("varchar(10)")
            .IsRequired();

        builder.Property(x => x.AirportZHName)
            .HasColumnType("varchar(100)")
            .IsRequired();

        builder.Property(x => x.AirportENName)
            .HasColumnType("varchar(100)")
            .IsRequired();

        builder.Property(x => x.CoordinateType)
            .HasColumnType("tinyint")
            .IsRequired();

        builder.Property(x => x.Location)
            .HasColumnType("point")
            .IsRequired();

        builder.Property(x => x.GooglePalceId)
            .HasColumnType("varchar(100)")
            .IsRequired();

        builder.Property(x => x.Address)
            .HasColumnType("varchar(200)")
            .IsRequired();

        builder.Property(x => x.Intro)
            .HasColumnType("text")
            .IsRequired();

        builder.Property(x => x.CreateTime)
            .HasColumnType("datetime");

        builder.Property(x => x.UpdateTime)
            .HasColumnType("datetime");


        builder.HasIndex(x => x.IATA);
        builder.HasIndex(x => x.CityCode);
    }
}
