using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Resource.Api.Infrastructure.Migrations
{
    public partial class add_GDSThirdHotelBind : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "GDSHotelExtend");

            migrationBuilder.AddColumn<string>(
                name: "BrandCode",
                table: "GDSHotel",
                type: "varchar(20)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ChainCode",
                table: "GDSHotel",
                type: "varchar(20)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ChainName",
                table: "GDSHotel",
                type: "varchar(100)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ENBrand",
                table: "GDSHotel",
                type: "varchar(100)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ENChainName",
                table: "GDSHotel",
                type: "varchar(100)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ExclusivePrivileges",
                table: "GDSHotel",
                type: "varchar(500)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "GDSThirdHotelBind",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    SupplierApiType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    GDSHotelId = table.Column<long>(type: "bigint", nullable: false),
                    ThirdHotelId = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GDSThirdHotelBind", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_GDSThirdHotelBind_GDSHotelId",
                table: "GDSThirdHotelBind",
                column: "GDSHotelId");

            migrationBuilder.CreateIndex(
                name: "IX_GDSThirdHotelBind_SupplierApiType_GDSHotelId",
                table: "GDSThirdHotelBind",
                columns: new[] { "SupplierApiType", "GDSHotelId" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "GDSThirdHotelBind");

            migrationBuilder.DropColumn(
                name: "BrandCode",
                table: "GDSHotel");

            migrationBuilder.DropColumn(
                name: "ChainCode",
                table: "GDSHotel");

            migrationBuilder.DropColumn(
                name: "ChainName",
                table: "GDSHotel");

            migrationBuilder.DropColumn(
                name: "ENBrand",
                table: "GDSHotel");

            migrationBuilder.DropColumn(
                name: "ENChainName",
                table: "GDSHotel");

            migrationBuilder.DropColumn(
                name: "ExclusivePrivileges",
                table: "GDSHotel");

            migrationBuilder.CreateTable(
                name: "GDSHotelExtend",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CanCheckInChildrenType = table.Column<int>(type: "int", nullable: true),
                    CheckInFrom = table.Column<TimeSpan>(type: "time", nullable: true),
                    CheckInTo = table.Column<TimeSpan>(type: "time", nullable: true),
                    CheckOutFrom = table.Column<TimeSpan>(type: "time", nullable: true),
                    CheckOutTo = table.Column<TimeSpan>(type: "time", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    DepositCurrency = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ExistingBedMax = table.Column<int>(type: "int", nullable: false),
                    ExtraBedMax = table.Column<int>(type: "int", nullable: false),
                    FrequencyType = table.Column<sbyte>(type: "tinyint", nullable: true),
                    HotelApplicabilityType = table.Column<sbyte>(type: "tinyint", nullable: true),
                    HotelExtraBedType = table.Column<int>(type: "int", nullable: true),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    IsHasBreakfast = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsHasChildrenExistingBed = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    IsHasDeposit = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    MaxAge = table.Column<int>(type: "int", nullable: true),
                    MixAge = table.Column<int>(type: "int", nullable: true),
                    PayTypes = table.Column<int>(type: "int", nullable: true),
                    RefundTimeType = table.Column<sbyte>(type: "tinyint", nullable: true),
                    RefundType = table.Column<sbyte>(type: "tinyint", nullable: true),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GDSHotelExtend", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_GDSHotelExtend_HotelId",
                table: "GDSHotelExtend",
                column: "HotelId");
        }
    }
}
