using Contracts.Common.Resource.Enums;
using EfCoreExtensions.EntityBase;

namespace Resource.Api.Model;

/// <summary>
/// 酒店早餐政策-儿童
/// </summary>
public class HotelMealChildPolicy : KeyBase
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 范围值 小
    /// </summary>
    public int Min { get; set; }

    /// <summary>
    /// 范围值 大
    /// </summary>
    public int Max { get; set; }

    /// <summary>
    /// 限制类型
    /// </summary>
    public HotelChildExistingBedPolicyType LimitType { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
