using Contracts.Common.Hotel.Enums;
using EfCoreExtensions.EntityBase;

namespace Resource.Api.Model;

public class GDSTag : KeyBase
{
    /// <summary>
    /// 标签名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 标签说明
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 标签英文名称
    /// </summary>
    public string? EnName { get; set; }

    /// <summary>
    /// 展示页面类别
    /// </summary>
    public TagShowPageType? ShowPageTypes { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }
}
