using Contracts.Common.Resource.Enums;
using EfCoreExtensions.EntityBase;
using NetTopologySuite.Geometries;
using Newtonsoft.Json;

namespace Resource.Api.Model;

public class TradingArea : TenantBase
{
    /// <summary>
    /// 城市code
    /// </summary>
    public long CityCode { get; set; }

    public TradingAreaType TradingAreaType { get; set; }

    public string Name { get; set; }

    public string Address { get; set; }

    /// <summary>
    /// 经纬度坐标
    /// </summary>
    [JsonIgnore]
    public Point Location { get; private set; }
    
    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType CoordinateType { get; set; }

    /// <summary>
    /// 设置经纬度
    /// </summary>
    /// <param name="lon">经度</param>
    /// <param name="lat">纬度</param>
    public void SetLocation(double lon, double lat)
    {
        //错误的地理坐标
        if (Math.Abs(lon) > 180)
            throw new ArgumentOutOfRangeException(nameof(lon));
        if (Math.Abs(lat) > 90)
            throw new ArgumentOutOfRangeException(nameof(lat));

        var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);
        Location = geometryFactory.CreatePoint(new Coordinate(lon, lat));
    }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
