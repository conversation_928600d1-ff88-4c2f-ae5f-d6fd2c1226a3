using EfCoreExtensions.EntityBase;

namespace Resource.Api.Model;

/// <summary>
/// 特色
/// </summary>
public class HotelIntroFeature : KeyBase
{
    public long HotelId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    public string? EnName { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? EnDescription { get; set; }
}
