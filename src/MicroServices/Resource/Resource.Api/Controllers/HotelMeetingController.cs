using AutoMapper;
using Contracts.Common.Resource.DTOs.HotelMeeting;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class HotelMeetingController : ControllerBase
{
    private readonly IHotelMeetingService _hotelMeetingService;
    private readonly IMapper _mapper;

    public HotelMeetingController(
        IHotelMeetingService hotelMeetingService,
        IMapper mapper)
    {
        _hotelMeetingService = hotelMeetingService;
        _mapper = mapper;
    }

    /// <summary>
    /// 搜索
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchHotelMeetingOuput>), (int)HttpStatusCode.OK)]
    public async Task<ActionResult> Search([FromBody] SearchHotelMeetingInput input)
    {
        var result = await _hotelMeetingService.Search(input);
        return Ok(result);
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(AddHotelMeetingInput input)
    {
        var result = await _hotelMeetingService.Add(input);
        return Ok(result);
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(UpdateHotelMeetingInput input)
    {
        await _hotelMeetingService.Update(input);
        return Ok();
    }


    /// <summary>
    /// 详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(HotelMeetingDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var result = await _hotelMeetingService.Detail(id);
        return Ok(result);
    }


    /// <summary>
    /// 删除-软删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Delete(List<long> ids)
    {
        await _hotelMeetingService.Delete(ids);
        return Ok();
    }


}
