using Contracts.Common.Resource.DTOs.StandardHotelRoomCode;
using Microsoft.AspNetCore.Mvc;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Controllers;

/// <summary>
/// 标准房型Code
/// </summary>
[ApiController]
[Route("[controller]/[action]")]
public class StandardHotelRommCodeController : ControllerBase
{
    private readonly IStandardHotelRoomCodeService _standardHotelRommCodeService;
    public StandardHotelRommCodeController(
        IStandardHotelRoomCodeService standardHotelRommCodeService)
    {
        _standardHotelRommCodeService = standardHotelRommCodeService;
    }

    /// <summary>
    /// 查询所有标准房型Codes
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<SearchStandardHotelRoomCodeOutput>),(int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search()
    {
        var result = await _standardHotelRommCodeService.Search();
        return Ok(result);
    }

    /// <summary>
    /// 查询第三方关联的房型数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<GetThirdCodeMappingOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetThirdCodeMapping(GetThirdCodeMappingInput input)
    {
        var result = await _standardHotelRommCodeService.GetThirdCodeMapping(input);
        return Ok(result);
    }
}