using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Resource.DTOs.EsPopularCity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class EsPopularCityController : ControllerBase
{
    private readonly IEsPopularCityService _esPopularCityService; 
    public EsPopularCityController(
        IEsPopularCityService esPopularCityService)
    {
        _esPopularCityService = esPopularCityService;
    }

    /// <summary>
    /// es城市查询
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<SearchEsPopularCityOutput>),(int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchEsPopularCityInput input)
    {
        var tenantId = HttpContext.GetTenantId();
        var result = await _esPopularCityService.Search(input,tenantId);
        return Ok(result);
    }

    /// <summary>
    /// es热门城市获取
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<SearchEsPopularCityOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get()
    {
        var tenantId = HttpContext.GetTenantId();
        var result = await _esPopularCityService.Search(new SearchEsPopularCityInput
        {
            IsPopular = true
        },tenantId);
        return Ok(result);
    }
    
}