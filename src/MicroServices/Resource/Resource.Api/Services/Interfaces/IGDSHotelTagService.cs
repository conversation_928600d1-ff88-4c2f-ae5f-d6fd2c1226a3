using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.GDSTag;
using EfCoreExtensions.Abstract;

namespace Resource.Api.Services.Interfaces;

public interface IGDSHotelTagService
{
    /// <summary>
    /// 酒店标签添加(勾选多个用户)
    /// </summary>
    /// <param name="input"></param>
    Task Add(SetGDSHotelTagsInput input);

    /// <summary>
    /// 酒店标签删除(勾选多个用户)
    /// </summary>
    /// <param name="input"></param>
    Task Delete(SetGDSHotelTagsInput input);

    /// <summary>
    /// 获取酒店的标签列表
    /// </summary>
    /// <param name="apiHotelIds"></param>
    /// <returns></returns>
    Task<IEnumerable<GetGDSHotelTagsOutput>> GetByHotelIds(GetGDSHotelTagsInput input);
}
