using Newtonsoft.Json;

namespace Resource.Api.Services.ThirdPlatformHotel.HopOpenApi;

public partial class HotelSearchResponse : BaseResponse
{
    /// <summary>
    /// 入住日期，入住日期，格式：yyyy-MM-dd
    /// </summary>
    [JsonProperty("checkIn")]
    public DateTime CheckIn { get; set; }

    /// <summary>
    /// 离店日期，离店日期，格式：yyyy-MM-dd
    /// </summary>
    [JsonProperty("checkOut")]
    public DateTime CheckOut { get; set; }

    /// <summary>
    /// 币种，币种，例如：USD、CNY
    /// </summary>
    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("hotels")]
    public Hotel[] Hotels { get; set; }
}

public partial class Hotel
{
    /// <summary>
    /// 酒店Id，酒店Id
    /// </summary>
    [JsonProperty("hotelId", NullValueHandling = NullValueHandling.Ignore)]
    public string? HotelId { get; set; }

    /// <summary>
    /// 酒店名称，酒店名称
    /// </summary>
    [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
    public string Name { get; set; }

    /// <summary>
    /// 酒店英文名称，酒店英文名称
    /// </summary>
    [JsonProperty("nameEn", NullValueHandling = NullValueHandling.Ignore)]
    public string NameEn { get; set; }

    [JsonProperty("rooms", NullValueHandling = NullValueHandling.Ignore)]
    public Room[] Rooms { get; set; }
}

public partial class Room
{
    /// <summary>
    /// 房型名称，房型名称
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// 房型英文名称，房型英文名称
    /// </summary>
    [JsonProperty("nameEn")]
    public string NameEn { get; set; }

    [JsonProperty("rates")]
    public Rate[] Rates { get; set; }

    /// <summary>
    /// 房型Id，房型Id
    /// </summary>
    [JsonProperty("roomId")]
    public string RoomId { get; set; }
}

public partial class Rate
{
    /// <summary>
    /// 成人数，每间房入住成人数
    /// </summary>
    [JsonProperty("adults")]
    public int Adults { get; set; }

    /// <summary>
    /// 餐食代码，详见餐食代码
    /// </summary>
    [JsonProperty("boardCode")]
    public BoardCodeType BoardCode { get; set; }

    /// <summary>
    /// 餐食数量，餐食数量
    /// </summary>
    [JsonProperty("boardCount")]
    public int BoardCount { get; set; }

    /// <summary>
    /// 取消政策，取消政策，如为空表示取消收取全额或不可取消，过了入住日期后不可取消
    /// </summary>
    [JsonProperty("cancellationPolicies", NullValueHandling = NullValueHandling.Ignore)]
    public CancellationPolicy[] CancellationPolicies { get; set; }

    /// <summary>
    /// 儿童年龄列表，每间房入住儿童的年龄列表，列表数量为儿童的入住人数
    /// </summary>
    [JsonProperty("childrenAges", NullValueHandling = NullValueHandling.Ignore)]
    public long[] ChildrenAges { get; set; }

    /// <summary>
    /// 确认时长，非立即确认报价预计确认时长，单位：分钟
    /// </summary>
    [JsonProperty("confirmByMins")]
    public int? ConfirmByMins { get; set; }

    /// <summary>
    /// 每日价格，每日价格
    /// </summary>
    [JsonProperty("dailyPrices")]
    public DailyPrice[] DailyPrices { get; set; }

    /// <summary>
    /// 到店付税费
    /// </summary>
    [JsonProperty("fees", NullValueHandling = NullValueHandling.Ignore)]
    public Fee[] Fees { get; set; }

    /// <summary>
    /// 是否为立即确认，报价计划是否为立即确认，true为立即确认
    /// </summary>
    [JsonProperty("isInstantConfirm")]
    public bool IsInstantConfirm { get; set; }

    /// <summary>
    /// 报价计划名称，请务直接展示给客人，收益可能会填写一些敏感信息
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// 国籍约束，国籍约束List，国家ISO 3166-2代码
    /// </summary>
    [JsonProperty("nationalityCodes", NullValueHandling = NullValueHandling.Ignore)]
    public string[] NationalityCodes { get; set; }

    /// <summary>
    /// 客人国籍约束规则，为空无约束
    /// 1：该价格仅适用于nationalityCodes的客人
    /// 2：该价格不适用于nationalityCodes的客人
    /// </summary>
    [JsonProperty("nationalityRule", NullValueHandling = NullValueHandling.Ignore)]
    public long? NationalityRule { get; set; }

    /// <summary>
    /// 报价计划Code，报价计划Code
    /// </summary>
    [JsonProperty("ratePlanCode")]
    public string RatePlanCode { get; set; }

    /// <summary>
    /// 销售\预订场景
    /// </summary>
    [JsonProperty("saleScenario")]
    public SaleScenario SaleScenario { get; set; }

    /// <summary>
    /// 总价格，总价格
    /// </summary>
    [JsonProperty("totalPrice")]
    public decimal TotalPrice { get; set; }
}


/// <summary>
/// 销售\预订场景
/// </summary>
public partial class SaleScenario
{
    /// <summary>
    /// 保留房回收时间数，保留房回收时间数
    /// </summary>
    [JsonProperty("cutoffHour")]
    public int? CutoffHour { get; set; }

    /// <summary>
    /// 是否直采价格，是否直采价格
    /// </summary>
    [JsonProperty("isDirectly")]
    [Obsolete]
    public bool IsDirectly { get; set; }

    /// <summary>
    /// 是否团房，是否团房
    /// </summary>
    [JsonProperty("isReunionRoom")]
    [Obsolete]
    public bool IsReunionRoom { get; set; }

    /// <summary>
    /// 最大连住天数，最大连住天数
    /// </summary>
    [JsonProperty("maxDays")]
    public int? MaxDays { get; set; }

    /// <summary>
    /// 提前预订小时数，提前预订小时数
    /// </summary>
    [JsonProperty("minAdvHours")]
    public int? MinAdvHours { get; set; }

    /// <summary>
    /// 最小连住天数，最小连住天数
    /// </summary>
    [JsonProperty("minDays")]
    public int MinDays { get; set; }

    /// <summary>
    /// 团房起订间数，团房起订间数
    /// </summary>
    [JsonProperty("minReunionRooms")]
    public int MinReunionRooms { get; set; }

    /// <summary>
    /// 尊享套餐说明，尊享套餐说明
    /// </summary>
    [JsonProperty("premium")]
    public string Premium { get; set; }

    /// <summary>
    /// 敏感度
    /// </summary>
    [JsonProperty("sensitivityLevel")]
    public int? SensitivityLevel { get; set; }

    /// <summary>
    /// 是否连住倍数，是否连住倍数，0-否，1-是
    /// </summary>
    [JsonProperty("stayMult")]
    public int? StayMult { get; set; }

    /// <summary>
    /// 供应商渠道类型 1-直采 2-EBK 3-CM渠道 4-直连
    /// </summary>
    [JsonProperty("channelType")]
    public ChannelType ChannelType { get; set; }

    /// <summary>
    /// 报价计划类型 0-日历房 1-商旅 2-高德 3-团房 4-直销
    /// </summary>
    [JsonProperty("saleType")]
    public SaleType? SaleType { get; set; }
}