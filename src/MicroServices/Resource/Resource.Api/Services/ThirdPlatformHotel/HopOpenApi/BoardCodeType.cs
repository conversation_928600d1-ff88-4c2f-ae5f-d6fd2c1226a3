namespace Resource.Api.Services.ThirdPlatformHotel.HopOpenApi;

/// <summary>
/// 餐食类型
/// </summary>
public enum BoardCodeType
{
    /// <summary>
    /// 不含餐食
    /// </summary>
    RO,
    /// <summary>
    /// 含早
    /// </summary>
    BB,
    /// <summary>
    /// 半餐	早餐，晚餐
    /// </summary>
    HB,
    /// <summary>
    /// 全餐	早餐，午餐，晚餐
    /// </summary>
    FB,
    /// <summary>
    /// 午餐
    /// </summary>
    BL,
    /// <summary>
    /// 晚餐
    /// </summary>
    BD,
    /// <summary>
    /// 早餐+午餐	早餐，午餐
    /// </summary>
    HBBL,

    /// <summary>
    /// 午餐+晚餐	午餐，晚餐
    /// </summary>
    HBLD,

    /// <summary>
    /// 全包	早餐，午餐，晚餐，饮料/点心
    /// </summary>
    AI,
}
