using Newtonsoft.Json;

namespace Resource.Api.Services.ThirdPlatformHotel.HopOpenApi;

public class SearchBookingRequest
{
    /// <summary>
    /// 分销商唯一订单号
    /// </summary>
    [JsonProperty("agentOrderId", NullValueHandling = NullValueHandling.Ignore)]
    public string AgentOrderId { get; set; }

    /// <summary>
    /// 汇智订单号
    /// </summary>
    [JsonProperty("orderId", NullValueHandling = NullValueHandling.Ignore)]
    public string? OrderId { get; set; }
}
