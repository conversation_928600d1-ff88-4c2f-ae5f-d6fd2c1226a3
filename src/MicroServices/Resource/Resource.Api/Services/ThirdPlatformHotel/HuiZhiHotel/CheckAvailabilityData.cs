namespace Resource.Api.Services.ThirdPlatformHotel.HuiZhiHotel;

public class CheckAvailabilityData
{
    /// <summary>
    /// 酒店Id（汇智国际旅游）
    /// </summary>
    public string hid { get; set; }
    /// <summary>
    /// 房型Id
    /// </summary>
    public string rid { get; set; }
    /// <summary>
    /// 报价计划code（汇智国际旅游）
    /// </summary>
    public string rpid { get; set; }
    /// <summary>
    /// 入住日期yyyy-mm-dd
    /// </summary>
    public string checkin { get; set; }
    /// <summary>
    /// 离店日期yyyy-mm-dd
    /// </summary>
    public string checkout { get; set; }
    /// <summary>
    /// 房间数量
    /// </summary>
    public int roomnum { get; set; }
    /// <summary>
    /// 每间房成人数量
    /// </summary>
    public int adultnum { get; set; }

    /// <summary>
    /// 每间儿童数
    /// </summary>
    public List<int> childrenAges { get; set; }
    /// <summary>
    /// 客人国籍，不传默认中国国籍
    /// </summary>
    public string nationality { get; set; }
    /// <summary>
    /// 成本价格集合
    /// 每间夜的价格集合，切勿乘房量
    /// </summary>
    public string costs { get; set; }
    /// <summary>
    /// 成本总价(间夜总价)
    /// </summary>
    public decimal totalprice { get; set; }
}
