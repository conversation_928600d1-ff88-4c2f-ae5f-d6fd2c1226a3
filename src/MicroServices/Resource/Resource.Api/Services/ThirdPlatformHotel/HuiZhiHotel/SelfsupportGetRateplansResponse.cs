namespace Resource.Api.Services.ThirdPlatformHotel.HuiZhiHotel;

public class SelfsupportGetRateplansResponse
{
    public SelfsupportGetRateplanResult[] Rateplans { get; set; }
}

public class SelfsupportGetRateplanResult
{
    public int Rid { get; set; }
    public int Rpid { get; set; }
    public int Breakfast_count { get; set; }
    public CancelPolicy[] Cancel_policy { get; set; }
    public string Cutoff_hour { get; set; }
    public string Enname { get; set; }
    public int Max_days { get; set; }
    public int Max_occupancy { get; set; }
    public int Min_adv_hours { get; set; }
    public int Min_days { get; set; }
    public string Name { get; set; }
    public string National_codes { get; set; }
    public string National_names { get; set; }
}


public class CancelPolicy
{
    public string Charge { get; set; }
    public string Startwindowhours { get; set; }
    public string Nightcount { get; set; }
    public string Percent { get; set; }
    public string Amount { get; set; }
    public string Currencycode { get; set; }
}
