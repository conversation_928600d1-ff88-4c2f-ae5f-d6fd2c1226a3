using AutoMapper;
using Contracts.Common.Marketing.DTOs.Lottery;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.Airport;
using Contracts.Common.Resource.DTOs.StandardHotelRoomCode;
using EfCoreExtensions.Abstract;

namespace Resource.Api.Services.MappingProfiles
{
    public class StandardHotelRommCodeProfiles : Profile
    {
        public StandardHotelRommCodeProfiles()
        {
            CreateMap<StandardThirdHotelRoomCode, GetThirdCodeMappingOutput>();
            CreateMap<StandardHotelRoomCode, SearchStandardHotelRoomCodeOutput>();
        }
    }
}