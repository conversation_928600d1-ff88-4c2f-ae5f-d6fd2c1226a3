using AutoMapper;
using Contracts.Common.Resource.DTOs.PopularCity;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Resource.Api.Services.Interfaces;

namespace Resource.Api.Services;

public class PopularCityService : IPopularCityService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;

    public PopularCityService(CustomDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<PagingModel<SearchPopularCityDto>> Search(SearchPopularCityInput input)
    {
        var popularCities = await _dbContext.PopularCities.AsNoTracking()
            .WhereIF(input.CountryCode is not null, x => x.CountryCode.Equals(input.CountryCode))
            .WhereIF(input.ProvinceCode is not null, x => x.ProvinceCode.Equals(input.ProvinceCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.CityName), x => x.CityName.Contains(input.CityName!))
            .OrderByDescending(c => c.CountryCode)
            .ThenByDescending(c => c.ProvinceCode)
            .ThenByDescending(c => c.CityCode)
            .PagingAsync(input.PageIndex, input.PageSize);
        var res = _mapper.Map<PagingModel<SearchPopularCityDto>>(popularCities);
        if (res.Data.Any())
        {
            var query = from city in _dbContext.Cities
                        join province in _dbContext.Provinces on city.ProvinceCode equals province.ProvinceCode
                        join country in _dbContext.Countries on city.CountryCode equals country.CountryCode
                        select new
                        {
                            CityCode = city.CityCode,
                            CityName = city.ZHName,
                            CityEnName = city.ENName,
                            CountryCode = city.CountryCode,
                            CountryName = country.ZHName,
                            CountryEnName = country.ENName,
                            ProvinceCode = city.ProvinceCode,
                            ProvinceName = province.ZHName,
                            ProvinceEnName = province.ENName,
                        };
            var cityCodes = res.Data.Select(x => x.CityCode);
            var cities = await query.Where(x => cityCodes.Contains(x.CityCode)).ToListAsync();
            foreach (var pcity in res.Data)
            {
                var city = cities.FirstOrDefault(x => x.CityCode == pcity.CityCode);
                pcity.EnProvinceName = city?.ProvinceEnName;
                pcity.EnCityName = city?.CityEnName;
                pcity.EnCountryName = city?.CountryEnName;
            }
        }
        return res;
    }

    public async Task Delete(long id)
    {
        var popularCity = await _dbContext.PopularCities.FindAsync(id);
        if (popularCity is null) return;
        _dbContext.Remove(popularCity);
        await _dbContext.SaveChangesAsync();
    }

    public async Task Add(List<PopularCityDto> dtos)
    {
        var popularCitys = _mapper.Map<List<PopularCity>>(dtos);
        await _dbContext.AddRangeAsync(popularCitys);
        await _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// 获取已经添加城市
    /// </summary>
    public async Task<List<int>> GetCities(int countryCode)
    {
        var alreadyAddedCityCodes = await _dbContext.PopularCities.AsNoTracking()
            .Where(x => x.CountryCode == countryCode)
            .Select(x => x.CityCode)
            .ToListAsync();
        return alreadyAddedCityCodes;
    }

}
