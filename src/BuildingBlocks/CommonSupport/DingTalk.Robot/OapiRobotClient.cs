using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Text;

namespace DingTalk.Robot
{
    public class OapiRobotClient
    {
        private static readonly Lazy<HttpClient> _httpClient = new(() => new HttpClient());
        private readonly OapiRobotConfig _oapiRobotConfig;

        public OapiRobotClient(OapiRobotConfig oapiRobotConfig)
        {
            _oapiRobotConfig = oapiRobotConfig;
        }

        public void SendText(string content)
        {
            OapiRobotSendTextInput input = new()
            {
                MsgType = "text",
                Text = new OapiRobotSendText { Content = content?.Replace("\r\n", "\n") ?? string.Empty },
                At = new OapiRobotSendTextAt
                {
                    AtMobiles = _oapiRobotConfig.AtMobiles,
                    IsAtAll = _oapiRobotConfig.IsAtAll
                }
            };
            SendText(input).Wait();
        }

        /// <summary>
        /// 发送文本消息
        /// </summary>
        /// <param name="oapiRobotConfig"></param>
        /// <param name="input"></param>
        public async Task SendText(OapiRobotSendTextInput input)
        {
            var str = JsonConvert.SerializeObject(input,
                new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });
            using var httpContent = new StringContent(str, Encoding.UTF8, "application/json");
            var url = GetServerUrl();
            var response = await _httpClient.Value.PostAsync(url, httpContent);
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadAsStringAsync();
        }

        private string GetServerUrl()
        {
            var timestamp = Convert.ToInt64(DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalMilliseconds).ToString();
            var sign = OapiRobotHelper.Getsign(timestamp + "\n" + _oapiRobotConfig.Secret, _oapiRobotConfig.Secret);
            var serverUrl = string.Format("{0}&timestamp={1}&sign={2}", _oapiRobotConfig.Webhook, timestamp, sign);
            return serverUrl;
        }

    }
}
