
using Microsoft.Extensions.Hosting;

namespace Extensions;
public static class EnvironmentEnvExtensions
{
    /// <summary>
    /// Production、kdwl
    /// </summary>
    /// <param name="hostEnvironment"></param>
    /// <returns></returns>
    /// <exception cref="System.ArgumentNullException"></exception>
    public static bool IsSaasProduction(this IHostEnvironment hostEnvironment)
    {
        if(hostEnvironment is null)
            throw new System.ArgumentNullException(nameof(hostEnvironment));

        return hostEnvironment.IsEnvironment(Environments.Production) 
            || hostEnvironment.IsEnvironment("kdwl");
    }

    /// <summary>
    /// Testing
    /// </summary>
    /// <param name="hostEnvironment"></param>
    /// <returns></returns>
    /// <exception cref="System.ArgumentNullException"></exception>
    public static bool IsTesting(this IHostEnvironment hostEnvironment)
    {
        return hostEnvironment.IsEnvironment("Testing");
    }
}
