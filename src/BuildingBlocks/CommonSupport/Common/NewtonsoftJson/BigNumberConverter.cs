using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.NewtonsoftJson
{
    /// <summary>
    /// 大数字序列化器
    /// </summary>
    public class BigNumberConverter : JsonConverter
    {
        private readonly static Type[] _bigNumTypes = new Type[] { typeof(long), typeof(double), typeof(long?), typeof(double?) };

        public override bool CanConvert(Type objectType)
        {
            return _bigNumTypes.Contains(objectType);
        }

        /// <summary>
        /// 反序列化时不转换
        /// </summary>
        public override bool CanRead => false;

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            writer.WriteValue(value.ToString());
        }
    }
}
