using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Common.FluentValidation
{
    public static class FluentValidationExtensions
    {
        public static IMvcBuilder AddCustomFluentValidation(this IMvcBuilder mvcBuilder)
        {
            var assemblies = mvcBuilder.PartManager.ApplicationParts
                .Where(s => s is AssemblyPart s1 && s1.Assembly.EntryPoint is not null)
                .Select(s => (s as AssemblyPart).Assembly);
            mvcBuilder.AddFluentValidation(options=>
            {
                options.RegisterValidatorsFromAssemblies(assemblies);
                options.DisableDataAnnotationsValidation = true;
            });

            mvcBuilder.Services.Configure<ApiBehaviorOptions>(options =>
            {
                options.InvalidModelStateResponseFactory = (context) =>
                {
                    var errors = context.ModelState.Values
                        .SelectMany(x => x.Errors.Select(p => p.ErrorMessage))
                        .ToList();
                    var result = new ObjectResult(new { Message = errors.FirstOrDefault() })
                    {
                        StatusCode = (int)HttpStatusCode.BadRequest
                    };
                    return result;
                };
            });

            return mvcBuilder;
        }
    }
}
