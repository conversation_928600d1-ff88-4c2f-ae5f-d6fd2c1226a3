using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Common.GlobalException
{
    public class GlobalExceptionFilter : IExceptionFilter
    {
        private readonly IWebHostEnvironment _env;
        private readonly ILogger<GlobalExceptionFilter> _logger;

        public GlobalExceptionFilter(IWebHostEnvironment env,
            ILogger<GlobalExceptionFilter> logger)
        {
            _env = env;
            _logger = logger;
        }

        public void OnException(ExceptionContext context)
        {
            var errorResponse = new ErrorResponse(null);
            var statusCode = 0;
            if (context.Exception is BusinessException businessException)
            {
                _logger.LogError(context.Exception, "GlobalException BusinessException: {message}", context.Exception.Message);
                errorResponse.BusinessErrorType = businessException.BusinessErrorType;
                errorResponse.Message = businessException.Message;
                errorResponse.Data = businessException.Data;
                statusCode = (int)HttpStatusCode.BadRequest;
            }
            else if (context.Exception is UnauthorizedAccessException)
            {
                statusCode = (int)HttpStatusCode.Unauthorized;
            }
            else if (context.Exception is ForbiddenException)
            {
                statusCode = (int)HttpStatusCode.Forbidden;
            }
            else if (context.Exception is NotFoundException)
            {
                statusCode = (int)HttpStatusCode.NotFound;
            }
            else
            {
                _logger.LogError(context.Exception, "GlobalException ServerError: {message}", context.Exception.Message);
                if (_env.IsDevelopment())
                    errorResponse.Message = context.Exception.Message;
                else
                    errorResponse.Message = "server error";
                statusCode = (int)HttpStatusCode.InternalServerError;
            }
            context.ExceptionHandled = true;
            context.Result = new ObjectResult(errorResponse) { StatusCode = statusCode };
        }
    }
}
