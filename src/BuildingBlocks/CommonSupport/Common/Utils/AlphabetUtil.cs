using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Utils
{
    public class AlphabetUtil
    {
        private const string _numbers = "123456789";
        private const string _upperCase = "ABCDEFGHJKLMNPRSTWXY";
        private const string _lowerCase = "abcdefghjklmnprstwxy";

        /// <summary>
        /// 字符随机生成器
        /// </summary>
        /// <param name="alphabetType">支持位运算如：AlphabetType.Number | AlphabetType.LowerCase</param>
        /// <param name="length">字符长度</param>
        /// <returns></returns>
        public static string Generate(AlphabetType alphabetType = AlphabetType.Number, int length = 6)
        {
            var values = Enum.GetValues<AlphabetType>();
            var selectValues = values.Where(s => (alphabetType & s) == s).ToList();
            var codeChars = string.Empty;
            foreach (var item in selectValues)
            {
                switch (item)
                {
                    case AlphabetType.Number:
                        codeChars += _numbers;
                        break;
                    case AlphabetType.UpperCase:
                        codeChars += _upperCase;
                        break;
                    case AlphabetType.LowerCase:
                        codeChars += _lowerCase;
                        break;
                }
            }

            string code = string.Empty;
            Random rd = new();
            for (int i = 0; i < length; i++)
                code += codeChars.Substring(rd.Next(0, codeChars.Length), 1);
            return code;
        }
    }

    /// <summary>
    /// 验证码类型
    /// </summary>
    public enum AlphabetType
    {
        /// <summary>
        /// 纯数字
        /// </summary>
        Number = 1,

        /// <summary>
        /// 大写字母
        /// </summary>
        UpperCase = 2,

        /// <summary>
        /// 小写字母
        /// </summary>
        LowerCase = 4
    }
}
