using Agency.OpenAPI.Areas.v1.Models.Hotel;
using Agency.OpenAPI.Areas.v1.Models.RequestModel;
using Agency.OpenAPI.Callers;
using Agency.OpenAPI.Enums;
using Agency.OpenAPI.Services.Interfaces;
using Agency.OpenAPI.v1.Models.PriceStrategy;
using AutoMapper;
using Common.GlobalException;
using Contracts.Common;
using ProductDTO = Contracts.Common.Product.DTOs;
using PaymentDTO = Contracts.Common.Payment.DTOs;
using NuGet.Configuration;
using System.Diagnostics;
using Agency.OpenAPI.Areas.v1.Models.PriceStrategy;
using System.IO.Pipelines;
using HotelDTO = Contracts.Common.Hotel.DTOs;
using Hangfire.States;

namespace Agency.OpenAPI.Services;

public class HotelPriceStrategyService : IHotelPriceStrategyService
{
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly CurrentUser _currentUser;

    public HotelPriceStrategyService(IPaymentApiCaller paymentApiCaller,
        ITenantApiCaller tenantApiCaller,
        CurrentUser currentUser,
        IProductApiCaller productApiCaller,
        IHotelApiCaller hotelApiCaller)
    {
        _paymentApiCaller = paymentApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _currentUser = currentUser;
        _productApiCaller = productApiCaller;
        _hotelApiCaller = hotelApiCaller;
    }

    public async Task<GetPriceInventoryStatusOutput> GetPriceStrategy(GetPriceInventoryStatusInput input)
    {
        //校验分销商信息
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(_currentUser.AgencyId);
        if (agencyInfo.PriceGroupId is null or <= 0)
            return null;
        var priceGroupId = agencyInfo.PriceGroupId.Value;
        //校验分组启用状态
        var priceGroup = await _tenantApiCaller.GetPriceGroupDetail(priceGroupId);
        if (!priceGroup.Enable)
            return null;

        #region 查询该价格分组下已配置的酒店数据

        var settingRequest = new ProductDTO.AgencyChannelPriceSettings.QueryChannelPriceInput
        {
            PriceGroupId = priceGroupId,
            ProductType = new[] { Contracts.Common.Product.Enums.ChannelProductType.Hotel },
            ProductIds = new[] { input.HotelId }
        };
        if (input.PriceStrategyIds.Any() is true)
        {
            settingRequest.SkuIds = input.PriceStrategyIds.ToArray();
        }
        var settingResponse = await _productApiCaller.QueryAgencyChannelPrices(settingRequest);
        if (settingResponse.Any() is false)
            return null;

        input.PriceStrategyIds = settingResponse
            .Where(x => x.ProductType == Contracts.Common.Product.Enums.ChannelProductType.Hotel)
            .Select(x => x.SkuId)
            .Distinct()
            .Union(input.PriceStrategyIds)
            .ToList();
        #endregion

        //该接口因历史问题，校验和获取信息在同一接口，会报错，因此价格策略的可售筛选在后面进行
        var strategies = await _hotelApiCaller.GetAgencyStrategies(new Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice.AgencyGetInput
        {
            HotelId = input.HotelId,
            PriceStrategyIds = input.PriceStrategyIds,
            LiveDate = input.BeginDate,
            LeaveDate = input.EndDate,
            VerifySale = false
        });

        #region 计算汇率

        var getExchangeRateInput = new List<PaymentDTO.Currency.GetExchangeRatesInput>();
        var costToSaleExchangeRateInput = strategies
            .SelectMany(x => x.PriceStrategies)
            .GroupBy(x => new { x.CostCurrencyCode, x.SaleCurrencyCode })
            .Select(x => new PaymentDTO.Currency.GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key.CostCurrencyCode,
                TargetCurrencyCode = x.Key.SaleCurrencyCode
            })
            .ToList();
        var saleToB2BExchangeRateInput = strategies
            .SelectMany(x => x.PriceStrategies)
            .GroupBy(x => x.SaleCurrencyCode)
            .Select(x => new PaymentDTO.Currency.GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key,
                TargetCurrencyCode = agencyInfo.CurrencyCode
            })
            .ToList();

        getExchangeRateInput.AddRange(costToSaleExchangeRateInput);
        getExchangeRateInput.AddRange(saleToB2BExchangeRateInput);
        var exchangeRateList = await GetCurrencyExchangeRateList(getExchangeRateInput);

        #endregion

        //过滤空数据和IsSale = true
        var result = new GetPriceInventoryStatusOutput();
        result.HotelId = input.HotelId;
        result.Rooms = new List<PriceInventoryStatusRoomsInfo>();
        foreach (var strategy in strategies)
        {
            if (!strategy.PriceStrategies.Any())
                continue;

            var roomInfo = new PriceInventoryStatusRoomsInfo();
            roomInfo.RoomId = strategy.RoomId;
            roomInfo.RoomName = strategy.RoomZHName;
            roomInfo.PriceStrategies = new List<PriceInventoryStatusBaseInfo>();
            foreach (var priceStrategy in strategy.PriceStrategies)
            {
                if (priceStrategy.IsSale is false && input.IsSale)
                    continue;
                if(priceStrategy.MaximumOccupancy < input.MaximumOccupancy)
                    continue;

                var strategyPrice = new PriceInventoryStatusBaseInfo
                {
                    OverSaleable = priceStrategy.OverSaleable,
                    PriceStrategyId = priceStrategy.Id,
                    Name = priceStrategy.Name,
                    NumberOfBreakfast = priceStrategy.NumberOfBreakfast,
                };
                var dataPrices = priceStrategy.CalendarPrices.Select(x => new Agency.OpenAPI.Areas.v1.Models.DatePrice
                {
                    Price = GetPrice(new GetB2BPriceInput
                    {
                        ExchangeRateList = exchangeRateList,
                        Setting = settingResponse.First(s => s.SkuId == priceStrategy.Id),
                        SalePrice = x.SalePrice,
                        CostPrice = x.CostPrice,
                        CostCurrencyCode = priceStrategy.CostCurrencyCode,
                        SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
                        AgencyCurrencyCode = agencyInfo.CurrencyCode,
                    }),
                    Quantity = x.AvailableQuantity,
                    Date = x.Date,
                    Status = x.Enabled,
                    CurrencyCode = priceStrategy.SaleCurrencyCode,
                }).ToList();

                if (input.IsSale)
                    dataPrices = dataPrices.Where(x => x.Status == true).ToList();
                if(input.RoomNum.HasValue)
                    dataPrices = dataPrices.Where(x => x.Quantity >= input.RoomNum).ToList();
                if (dataPrices.Any() is false)
                    continue;
                strategyPrice.DatePrices = dataPrices;

                roomInfo.PriceStrategies.Add(strategyPrice);
            }

            if(roomInfo.PriceStrategies.Any())
                result.Rooms.Add(roomInfo);
        }

        return result;
    }

    public async Task<List<PaymentDTO.Currency.GetExchangeRateOutput>> GetCurrencyExchangeRateList(List<PaymentDTO.Currency.GetExchangeRatesInput> input)
    {
        var result = new List<PaymentDTO.Currency.GetExchangeRateOutput>();

        //相同币种之前的汇率
        var sameCurrencyExchangeRateList =
            (from item in input
             let baseEqualsTarget =
                 item.BaseCurrencyCode.Equals(item.TargetCurrencyCode, StringComparison.OrdinalIgnoreCase)
             where baseEqualsTarget
             select new PaymentDTO.Currency.GetExchangeRateOutput
             {
                 BaseCurrencyCode = item.BaseCurrencyCode,
                 TargetCurrencyCode = item.TargetCurrencyCode,
                 ExchangeRate = 1
             }).ToList();
        result.AddRange(sameCurrencyExchangeRateList);

        //不同币种之前的汇率
        var differentCurrencyInput =
            (from item in input
             let baseEqualsTarget =
                 item.BaseCurrencyCode.Equals(item.TargetCurrencyCode, StringComparison.OrdinalIgnoreCase)
             where !baseEqualsTarget
             select item).ToList();
        if (differentCurrencyInput.Any())
        {
            var diffCurrencyExchangeRateList = await _paymentApiCaller.GetExchangeRates(differentCurrencyInput);
            result.AddRange(diffCurrencyExchangeRateList);
        }

        return result;
    }

    public decimal GetPrice(GetB2BPriceInput input)
    {
        //采购价=>售价汇率
        var costToSaleExchangeRate = input.ExchangeRateList.First(e =>
            e.BaseCurrencyCode == input.CostCurrencyCode &&
            e.TargetCurrencyCode == input.SaleCurrencyCode).ExchangeRate;

        //售价=>B2B价汇率
        var saleToB2BExchangeRate = input.ExchangeRateList.First(e =>
            e.BaseCurrencyCode == input.SaleCurrencyCode &&
            e.TargetCurrencyCode == input.AgencyCurrencyCode).ExchangeRate;

        var channelPrice = CalcChannelPrice(input.Setting, input.SalePrice, input.CostPrice,
            costToSaleExchangeRate);
        var b2BPrice = channelPrice is null
            ? 0
            : Math.Round(channelPrice.Value * saleToB2BExchangeRate, 2);

        return b2BPrice;
    }

    public decimal? CalcChannelPrice(ProductDTO.AgencyChannelPriceSettings.AgencyChannelPriceSettingOutput setting, decimal? salePrice,
        decimal? costPrice, decimal costToSaleExchangeRate)
    {
        if (setting == null || (setting.BasePriceType == Contracts.Common.Product.Enums.ChannelBasePriceType.SalePrice && salePrice is null)
            || (setting.BasePriceType == Contracts.Common.Product.Enums.ChannelBasePriceType.CostPrice && costPrice is null))
        {
            return null;
        }

        var calcValue = setting.BasePriceType == Contracts.Common.Product.Enums.ChannelBasePriceType.SalePrice
            ? salePrice.Value
            : Math.Round(costPrice.Value * costToSaleExchangeRate, 2);

        var value = setting.PriceSettingType switch
        {
            Contracts.Common.Product.Enums.ChannelPriceSettingType.AddValue => Math.Round(calcValue + setting.PriceSettingValue, 2),
            Contracts.Common.Product.Enums.ChannelPriceSettingType.SubtractValue => Math.Round(calcValue - setting.PriceSettingValue, 2),
            Contracts.Common.Product.Enums.ChannelPriceSettingType.AddRate => Math.Round(calcValue * (1 + setting.PriceSettingValue * 0.01m), 2),
            Contracts.Common.Product.Enums.ChannelPriceSettingType.SubtractRate => Math.Round(calcValue * (1 - setting.PriceSettingValue * 0.01m),
                2),
            _ => 0
        };

        if (value < 0) value = 0;

        return value;
    }
}
