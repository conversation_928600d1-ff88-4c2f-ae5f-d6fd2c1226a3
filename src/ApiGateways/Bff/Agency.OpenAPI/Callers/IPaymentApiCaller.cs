using Agency.OpenAPI.Areas.v1.Models.Hotel;
using Common.Caller;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.Currency;

namespace Agency.OpenAPI.Callers;

public interface IPaymentApiCaller : IHttpCallerBase
{
    #region Currency

    /// <summary>
    /// 查询货币汇率
    /// </summary>
    /// <param name="inputs"></param>
    /// <returns></returns>
    Task<List<GetExchangeRateOutput>> GetExchangeRates(IEnumerable<GetExchangeRatesInput> inputs);

    #endregion

    #region PayOrder

    /// <summary>
    /// 分销商额度支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<OrderPayOutput> AgencyCreditPay(AgencyCreditOrderPayInput input);

    #endregion
}