using Common.ServicesHttpClient;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.AgencyPriceGroup;
using Microsoft.Extensions.Options;

namespace Agency.OpenAPI.Callers.HttpImplements;

public class ProductApiCaller : Common.Caller.HttpCallerBase, IProductApiCaller
{
    public ProductApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.Product, httpClientFactory)
    {
    }

    #region PriceSettings
    public Task<List<AgencyChannelPriceSettingOutput>> QueryAgencyChannelPrices(QueryChannelPriceInput input)
    {
        var relativePath = "/agencychannelpricesettings/query";
        return PostAsync<QueryChannelPriceInput, List<AgencyChannelPriceSettingOutput>>(relativePath, input);
    }

    #endregion
}
