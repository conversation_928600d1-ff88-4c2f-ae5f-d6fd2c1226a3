using System.ComponentModel;

namespace Agency.OpenAPI.Enums;

/// <summary>
/// 取消类型
/// </summary>
public enum CancelRulesType
{
    /// <summary>
    /// 免费取消
    /// </summary>
    [Description("免费取消")]
    FreeCancel = 0,

    /// <summary>
    /// 不可取消
    /// </summary>
    [Description("不可取消")]
    CannotCancel = 1,

    /// <summary>
    /// 限时取消
    /// </summary>
    [Description("限时取消")]
    LimitedTimeCancel = 2,
}