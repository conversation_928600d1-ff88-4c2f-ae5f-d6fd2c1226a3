using Common.Swagger;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Agency.OpenAPI.Areas.v2.Controllers;

[Route("[area]/[controller]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v2))]
[ApiController]
[Area(nameof(ApiVersion.v2))]
public class ProductController : ControllerBase
{
    private readonly ILogger<ProductController> _logger;

    public ProductController(ILogger<ProductController> logger)
    {
        _logger = logger;
    }

    [HttpPost]
    [Route("search/byid")]
    [ProducesResponseType(typeof(ResultModel<string>), 200)]
    public string Search(SearchByIdInput input)
    {
        _logger.LogInformation("test log template {@data}", new { x = 1, y = 2 });
        return "v2";
    }

    [HttpPost]
    [Route("search/paging")]
    [ProducesResponseType(typeof(ResultModel<string>), 200)]
    public string Search(SearchInput input)
    {
        return "v2";
    }
}

public class SearchByIdInput
{
    public long Id { get; set; }
}

public class SearchInput
{
    public long Id { get; set; }

    public bool f { get; set; }
}
