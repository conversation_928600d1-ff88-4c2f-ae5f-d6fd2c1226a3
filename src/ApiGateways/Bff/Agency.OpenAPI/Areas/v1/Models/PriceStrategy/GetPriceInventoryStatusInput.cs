using Agency.OpenAPI.Enums;

namespace Agency.OpenAPI.v1.Models.PriceStrategy;

/// <summary>
/// 通过酒店Id、价格策略Id、时间区间 获取价格，库存，房态
/// </summary>
public class GetPriceInventoryStatusInput
{
    /// <summary>
    /// 酒店Id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 价格策略Ids
    /// </summary>
    public List<long> PriceStrategyIds { get; set; } = new();

    /// <summary>
    /// 日期区间 - 起
    /// </summary>
    public DateTime BeginDate { get; set; }

    /// <summary>
    /// 日期区间 - 止
    /// </summary>
    public DateTime EndDate { get; set; }
    
    /// <summary>
    /// 是否只查可售
    /// </summary>
    public bool IsSale { get; set; }

    /// <summary>
    /// 价格策略可住人数
    /// </summary>
    public int? MaximumOccupancy { get; set; }

    /// <summary>
    /// 房间数
    /// </summary>
    public int? RoomNum { get; set; }
}