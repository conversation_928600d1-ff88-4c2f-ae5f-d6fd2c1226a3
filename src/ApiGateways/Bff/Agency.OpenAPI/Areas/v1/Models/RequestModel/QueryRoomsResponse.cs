using Agency.OpenAPI.Enums;

namespace Agency.OpenAPI.Areas.v1.Models.RequestModel;

public class QueryRoomsResponse
{
    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }
    /// <summary>
    /// 酒店房间
    /// </summary>
    public List<HotelRoomsOutput> HotelRooms { get; set; }
}

public class HotelRoomsOutput
{
    
    public long Id { get; set; }

    public long HotelId { get; set; }
    
    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? ENName { get; set; }


    /// <summary>
    /// 房型
    /// </summary>
    public List<BedType> BedType { get; set; }

    /// <summary>
    /// 窗户
    /// </summary>
    public WindowType WindowType { get; set; }

    /// <summary>
    /// 面积 - 最小
    /// </summary>
    public decimal AreaMin { get; set; }

    /// <summary>
    /// 面积 - 最大
    /// </summary>
    public decimal AreaMax { get; set; }

    /// <summary>
    /// 楼层 - 最低
    /// </summary>
    public int FloorMin { get; set; }

    /// <summary>
    /// 楼层 - 最高
    /// </summary>
    public int FloorMax { get; set; }

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaximumOccupancy { get; set; }

    /// <summary>
    /// 房间数量
    /// </summary>
    public int RoomQuantity { get; set; }
    
    /// <summary>
    /// 房型图片
    /// </summary>
    public IEnumerable<string> Photos { get; set; }
}

public class HotelResponse
{
    public string ZHName { get; set; }
}

public class PriceCountResponse
{
    public long HotelRoomId { get; set; }
}