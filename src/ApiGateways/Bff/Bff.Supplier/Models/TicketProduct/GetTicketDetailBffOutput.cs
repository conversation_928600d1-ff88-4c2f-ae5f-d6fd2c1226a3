using Contracts.Common.Product.DTOs.TicketProduct;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;

namespace Bff.Supplier.Models.TicketProduct;

public class GetTicketDetailBffOutput
{
    /// <summary>
    /// 券类产品id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 票券业务类型
    /// </summary>
    public TicketBusinessType TicketBusinessType { get; set; }

    /// <summary>
    /// 售卖类型 0-团购 1-预订
    /// </summary>
    public TicketSaleType TicketSaleType { get; set; }

    /// <summary>
    /// 是否指定售卖时间
    /// </summary>
    public bool IsSetSellingDate { get; set; }

    /// <summary>
    /// 资源
    /// </summary>
    public List<TicketProductResourceBffOutput> ResourceInfos { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 卖点描述
    /// </summary>
    public string SellPointDescribe { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ProductPictures { get; set; }

    /// <summary>
    /// 视频
    /// </summary>
    public List<string> ProductVideos { get; set; }

    /// <summary>
    /// 海报
    /// </summary>
    public List<string> ProductPosters { get; set; }

    /// <summary>
    /// 分组
    /// </summary>
    public List<long> GroupIds { get; set; }

    /// <summary>
    /// 使用说明
    /// </summary>
    public string Instructions { get; set; }

    /// <summary>
    /// 温馨提示
    /// </summary>
    public string KindReminder { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 是否需要核销
    /// </summary>
    public bool NeedWriteOff { get; set; }

    /// <summary>
    /// 是否需要预约
    /// </summary>
    public bool NeedReservation { get; set; }

    /// <summary>
    /// 是否需要确认预约
    /// </summary>
    public bool NeedConfirmReservation { get; set; }

    /// <summary>
    /// 提前几天预约
    /// </summary>

    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal RefundRate { get; set; }

    /// <summary>
    /// 是否过期自动退款
    /// </summary>
    public bool AutoRefundAfterExpiration { get; set; }

    /// <summary>
    /// 过期自动退款比例
    /// </summary>
    public decimal AutoRefundRate { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    #region 出行人信息

    /// <summary>
    /// 游客信息类型
    /// </summary>
    public TouristInfoType TouristInfoType { get; set; }

    /// <summary>
    /// 游客姓名必填
    /// </summary>
    public bool TouristNameRequired { get; set; }

    /// <summary>
    /// 游客身份证必填
    /// </summary>
    public bool TouristIDRequired { get; set; }

    /// <summary>
    /// 游客手机号必填
    /// </summary>
    public bool TouristPhoneRequired { get; set; }

    #endregion
}

public class TicketProductResourceBffOutput
{
    /// <summary>
    /// 资源类型
    /// </summary>
    public ProductResourceType Type { get; set; }
    
    /// <summary>
    /// 资源ID
    /// </summary>
    public long Id { get; set; }
}