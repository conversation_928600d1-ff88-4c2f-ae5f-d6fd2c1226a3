using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Supplier.Models.TicketProduct;

public class UpdateTicketBffInput
{
    /// <summary>
    /// 券类产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long Id { get; set; }

    /// <summary>
    /// 资源
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<TicketProductResourceBffInput> ResourceInfos { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string Title { get; set; }

    /// <summary>
    /// 卖点描述
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string SellPointDescribe { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<string> ProductPictures { get; set; }

    /// <summary>
    /// 视频
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<string> ProductVideos { get; set; }

    /// <summary>
    /// 海报
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<string> ProductPosters { get; set; }

    /// <summary>
    /// 分组
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<long> GroupIds { get; set; } = new();

    /// <summary>
    /// 使用说明
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string Instructions { get; set; }

    /// <summary>
    /// 温馨提示
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string KindReminder { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string Content { get; set; }

    /// <summary>
    /// 是否需要核销
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool NeedWriteOff { get; set; }

    /// <summary>
    /// 是否需要预约
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool NeedReservation { get; set; }

    /// <summary>
    /// 是否需要确认预约
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool NeedConfirmReservation { get; set; }

    /// <summary>
    /// 提前几天预约
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 是否支持退款
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public TimeSpan? RefundTravelDateTime { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal RefundRate { get; set; }

    /// <summary>
    /// 是否过期自动退款
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool AutoRefundAfterExpiration { get; set; }

    /// <summary>
    /// 过期自动退款比例
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal AutoRefundRate { get; set; }

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool Enabled { get; set; }
    
    /// <summary>
    /// 售前客服
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long PreSaleStaff { get; set; }

    /// <summary>
    /// 售后客服
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long AfterSaleStaff { get; set; }
    
    /// <summary>
    /// 游客信息类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public TouristInfoType TouristInfoType { get; set; }

    /// <summary>
    /// 游客姓名必填
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool TouristNameRequired { get; set; }

    /// <summary>
    /// 游客身份证必填
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool TouristIDRequired { get; set; }

    /// <summary>
    /// 游客手机号必填
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool TouristPhoneRequired { get; set; }
}