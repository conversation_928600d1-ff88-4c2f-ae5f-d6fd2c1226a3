using Contracts.Common.Product.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Supplier.Models.LineProduct;

public class UpdateLineSkuCalendarPriceBffInput
{
    /// <summary>
    /// 线路产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }

    /// <summary>
    /// 线路产品规格id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductSkuId { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 周日到周六   0-6
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<DayOfWeek> Week { get; set; } = new List<DayOfWeek>();

    /// <summary>
    /// 扩展日期集合
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public IEnumerable<DateTime> ExtendDates { get; set; } = Enumerable.Empty<DateTime>();

    /// <summary>
    /// 库存
    /// 不填则不变
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public int? TotalQuantity { get; set; }

    /// <summary>
    /// 售卖状态
    /// null -保持不变
    /// 0 - 关
    /// 1 - 开
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? Enabled { get; set; }

    /// <summary>
    /// 日历价信息
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<LineProductSkuCalendarPriceBffInfo> SkuCalendarPrices { get; set; }
}

public class LineProductSkuCalendarPriceBffInfo
{
    /// <summary>
    /// 线路价格类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime Date { get; set; }

    /// <summary>
    /// 成本价(采购价)
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public decimal? CostPrice { get; set; }

}