using Contracts.Common.Product.DTOs.LineProduct;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Supplier.Models.LineProduct;

public class AddLineProductBffInput
{  
    /// <summary>
    /// 标题
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string Title { get; set; }

    /// <summary>
    /// 卖点描述
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string SellPointDescribe { get; set; }

    /// <summary>
    /// 出发国家id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int DepartureCountryId { get; set; }
    
    /// <summary>
    /// 出发国家名称
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string DepartureCountryName { get; set; }

    /// <summary>
    /// 出发地城市Id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int DepartureCityId { get; set; }

    /// <summary>
    /// 出发地城市名称
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string DepartureCityName { get; set; }

    /// <summary>
    /// 目的地国家id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int DestinationCountryId { get; set; }
    
    /// <summary>
    /// 目的地国家名称
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string DestinationCountryName { get; set; }

    /// <summary>
    /// 目的地城市Id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int DestinationCityId { get; set; }

    /// <summary>
    /// 目的地城市名称
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string DestinationCityName { get; set; }

    /// <summary>
    /// 成人标准说明
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string AdultsStandard { get; set; }

    /// <summary>
    /// 儿童标准说明
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ChildrenStandard { get; set; }

    /// <summary>
    /// 使用说明
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string Instructions { get; set; }

    /// <summary>
    /// 温馨提示
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string KindReminder { get; set; }

    /// <summary>
    /// 图文内容
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string Content { get; set; }

    #region 预订规则

    /// <summary>
    /// 提前几天预订
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 提前预订时间点
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public TimeSpan ReservationTimeInAdvance { get; set; }

    #endregion

    #region 退款规则

    /// <summary>
    /// 是否支持退款
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public TimeSpan? RefundTravelDateTime { get; set; }

    #endregion

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<string> ProductPictures { get; set; } = new();

    /// <summary>
    /// 视频
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<string> ProductVideos { get; set; } = new();

    /// <summary>
    /// 海报
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<string> ProductPosters { get; set; } = new();

    /// <summary>
    /// 上车点
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<RallyPointItem> RallyPoints { get; set; } = new List<RallyPointItem>();

    /// <summary>
    /// 产品分组
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<long> GroupIds { get; set; } = new();
    
    /// <summary>
    /// 天数
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int Days { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int Nights { get; set; }
    
    /// <summary>
    /// 售前客服
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long PreSaleStaff { get; set; }

    /// <summary>
    /// 售后客服
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long AfterSaleStaff { get; set; }
}