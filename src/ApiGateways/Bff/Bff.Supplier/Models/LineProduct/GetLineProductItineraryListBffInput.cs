using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Supplier.Models.LineProduct;

public class GetLineProductItineraryListBffInput
{
    /// <summary>
    /// 产品Id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }

    /// <summary>
    /// 产品套餐Id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductSkuId { get; set; }
}