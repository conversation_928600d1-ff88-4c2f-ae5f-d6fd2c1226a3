using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;

namespace Bff.Supplier.Models.TravelLineOrder;

public class GetOrderDetailOutput
{
    public BaseOrderDetailOutput BaseOrder { get; set; }

    public TravelLineOrderDetailOutput TravelLineOrder { get; set; }

    public IList<OrderTravelerOutput> OrderTravelers { get; set; }

    public IList<TravelLineOrderPriceOutput> OrderPrices { get; set; }
}

public class BaseOrderDetailOutput
{

    public long Id { get; set; }

    /// <summary>
    /// 下单用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 下单用户昵称
    /// </summary>
    public string UserNickName { get; set; }

    /// <summary>
    /// 会员等级id
    /// </summary>
    public long VipLevelId { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactsEmail { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 规格名称
    /// </summary>
    public string ProductSkuName { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BaseOrderStatus Status { get; set; }

    /// <summary>
    /// 留言
    /// </summary>
    public string Message { get; set; }

    public DateTime CreateTime { get; set; }
}

public class TravelLineOrderDetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 入住房间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    /// <summary>
    /// 预订日期
    /// </summary>
    public DateTime TravelBeginDate { get; set; }

    /// <summary>
    /// 预订结束日期
    /// </summary>
    public DateTime TravelEndDate { get; set; }

    /// <summary>
    /// 认领人Id
    /// </summary>
    public long? ClaimantId { get; set; }

    /// <summary>
    /// 认领人名称
    /// </summary>
    public string ClaimantName { get; set; }

    /// <summary>
    /// 集结点时间
    /// </summary>
    public TimeSpan? RallyPointTime { get; set; }

    /// <summary>
    /// 集结点地址
    /// </summary>
    public string? RallyPointAddress { get; set; }

    /// <summary>
    /// 车牌号
    /// </summary>
    public string TourGuideCarNumber { get; set; }

    /// <summary>
    /// 导游名称
    /// </summary>
    public string TourGuideName { get; set; }

    /// <summary>
    /// 导游电话
    /// </summary>
    public string TourGuidePhoneNumber { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public TravelLineOrderStatus Status { get; set; }

    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 线路产品SkuId
    /// </summary>
    public long LineProductSkuId { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int Days { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int Nights { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    public DateTime CreateTime { get; set; }

    #region 退款规则

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    #endregion
}

public class OrderTravelerOutput
{
    /// <summary>
    /// 1-成人 2-儿童
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IDCard { get; set; }

    public string Phone { get; set; }
}

public class TravelLineOrderPriceOutput
{
    /// <summary>
    /// 1-成人 2-儿童 3-房差价
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 成本币种 =>供应商币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 成本价(采购价) 实际成本价格
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }
}
