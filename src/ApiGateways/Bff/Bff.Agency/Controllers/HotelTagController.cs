using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.HotelTag;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.HotelTag;
using Contracts.Common.Hotel.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 酒店标签
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelTagController : ControllerBase
{
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IMapper _mapper;

    public HotelTagController(IHotelApiCaller hotelApiCaller, IMapper mapper)
    {
        _hotelApiCaller = hotelApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 根据汇智酒店id获取标签
    /// </summary>
    /// <param name="apiHotelIds"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(IEnumerable<GetHotelTagsOutput>))]
    public async Task<IActionResult> GetByApiHotelIds(GetHotelTagsBffInput input)
    {
        var getByApiHotelInput = _mapper.Map<GetHotelTagsInput>(input);
        var result = await _hotelApiCaller.GetByApiHotelIds(getByApiHotelInput);
        return Ok(result);
    }

    /// <summary>
    /// 获取标签
    /// </summary>
    /// <param name="showPageType"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(List<GetHotelTagsBffOutput>))]
    public async Task<IActionResult> GetTags(TagShowPageType showPageType, string language = "zh")
    {
        var tags = await _hotelApiCaller.SearchTag(new Contracts.Common.Hotel.DTOs.Tag.SearchTagInput { 
            ShowPageTypes = new List<TagShowPageType> { showPageType }
        });

        var result = _mapper.Map<List<GetHotelTagsBffOutput>>(tags);
        if (language.ToLower().Equals("en"))
        {
            result.ForEach(o =>
            {
                o.Name = tags.FirstOrDefault(x => x.Id == o.Id)?.EnName;
            });
        }
        return Ok(result);
    }
}
