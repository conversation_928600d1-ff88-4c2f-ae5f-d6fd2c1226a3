using Bff.Agency.Callers;
using Bff.Agency.Models.Tenant;
using Common.Jwt;
using Common.Swagger;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class TenantController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;

    public TenantController(ITenantApiCaller tenantApiCaller)
    {
        _tenantApiCaller = tenantApiCaller;
    }

    /// <summary>
    /// 商户基本信息
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetTenantOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get()
    {
        var tenantId = HttpContext.GetTenantId();
        var tenant = await _tenantApiCaller.GetTenant(tenantId);
        if (tenant == null)
            return Ok();
        GetTenantOutput output = new()
        {
            TenantId = tenant.TenantId,
            FullName = tenant.FullName,
            ShortName = tenant.ShortName
        };
        return Ok(output);
    }
}
