using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.Fields;
using Common.Swagger;
using Contracts.Common.Product.DTOs.Fields;
using Contracts.Common.Product.DTOs.ProductDefaultInformationTemplate;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 字段
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class FieldsController : ControllerBase
{
    private readonly IProductApiCaller _productApiCaller;
    private readonly IMapper _mapper;
    public FieldsController(IProductApiCaller productApiCaller, IMapper mapper)
    {
        _productApiCaller = productApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 字段列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<BffFieldsOutput>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> List(SearchFieldsInput input)
    {
        var data = await _productApiCaller.FieldsList(input);
        var result = _mapper.Map<List<BffFieldsOutput>>(data);
        return Ok(result);
    }


}
