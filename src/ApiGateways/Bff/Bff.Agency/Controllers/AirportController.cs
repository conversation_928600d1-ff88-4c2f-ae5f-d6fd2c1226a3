using Bff.Agency.Callers;
using Common.Swagger;
using Contracts.Common.Resource.DTOs.Airport;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 机场资源
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AirportController : ControllerBase
{
    private readonly IResourceApiCaller _resourceApiCaller;

    public AirportController(IResourceApiCaller resourceApiCaller)
    {
        _resourceApiCaller = resourceApiCaller;
    }

    /// <summary>
    /// 机场资源详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(AirportDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var result = await _resourceApiCaller.GetAirportDetail(id);
        return Ok(result);
    }
}
