using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.LineProductSku;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 线路产品套餐
/// </summary>
[Authorize]
[ApiController]
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
public class LineProductSkuController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IProductApiCaller _productApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IInventoryApiCaller _inventoryApiCaller;
    
    public LineProductSkuController(
        IMapper mapper,
        IProductApiCaller productApiCaller,
        ITenantApiCaller tenantApiCaller,
        IInventoryApiCaller inventoryApiCaller)
    {
        _mapper = mapper;
        _productApiCaller = productApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _inventoryApiCaller = inventoryApiCaller;
    }

    /// <summary>
    /// 查询线路产品套餐信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(List<GetLineProductSkuBffOutput>))]
    public async Task<IActionResult> GetSkus(GetLineProductSkuBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(agencyId);
        var priceGroupId = agencyInfo.PriceGroupId;
        var agencyCurrencyCode = agencyInfo.CurrencyCode;
        var skusRequest = new AgencyGetLineProductSkuInput
        {
            ProductId = input.ProductId,
            AgencyId = agencyId,
            PriceGroupId = priceGroupId
        };
        var skusResponse = await _productApiCaller.GetPriceGroupLineProductSkus(skusRequest);
        var result = _mapper.Map<List<GetLineProductSkuBffOutput>>(skusResponse);

        //处理出行日期价库
        var travelBeginDate = input.TravelDate ?? DateTime.Today;
        var travelEndDate = input.TravelDate ?? DateTime.Today.AddDays(179);//无日期默认180天
        
        //查询最低价&库存信息
        var skuScheduleResponse = await _productApiCaller.GetPriceGroupLineProductSkuPricesByProductId(
            new AgencyGetInput
            {
                ProductId = input.ProductId,
                AgencyId = agencyId,
                PriceGroupId = priceGroupId,
                AgencyCurrencyCode = agencyCurrencyCode,
                StartDate = travelBeginDate,
                EndDate = travelEndDate
            });

        foreach (var skuItem in result)
        {
            var priceForSale = true;
            var invForSale = true;
            
            #region 套餐价格处理

            var skuSchedule = skuScheduleResponse
                .Where(x => x.LineProductSkuId == skuItem.Id)
                .SelectMany(x => x.PriceAndQuantityInfos)
                .ToList();
            var skuPrices = skuSchedule.SelectMany(x => x.SkuCalendarPriceInfos)
                .ToList();
            
            //币种
            skuItem.SaleCurrencyCode = agencyCurrencyCode;
            //成人最低价
            var adultMinPrice = skuPrices.Where(x => x.Type == LineSkuPriceType.Adult)
                .Min(x => x.ChannelPrice);
            //长者最低价
            var elderlyMinPrice = skuPrices.Where(x => x.Type == LineSkuPriceType.Elderly)
                .Min(x => x.ChannelPrice);
            //儿童最低价
            var childMinPrice = skuPrices.Where(x => x.Type == LineSkuPriceType.Child)
                .Min(x => x.ChannelPrice);
            //婴儿最低价
            var babyMinPrice = skuPrices.Where(x => x.Type == LineSkuPriceType.Baby)
                .Min(x => x.ChannelPrice);
            //其他最低价
            var otherMinPrice = skuPrices.Where(x => x.Type == LineSkuPriceType.Other)
                .Min(x => x.ChannelPrice);

            //价格取值优先级：成人>长者>儿童>婴儿 > 其他
            skuItem.MinChannelPrice = adultMinPrice ??
                                      elderlyMinPrice ??
                                      childMinPrice ??
                                      babyMinPrice ??
                                      otherMinPrice;

            priceForSale = skuItem.MinChannelPrice != null;

            #endregion
            
            #region 套餐库存处理
            
            invForSale = skuSchedule.Any(x => x is {Enabled: true, AvailableQuantity: > 0});

            #endregion
            
            //判断是否可售
            skuItem.IsForSale = priceForSale && invForSale;
        }

        return Ok(result);
    }
}