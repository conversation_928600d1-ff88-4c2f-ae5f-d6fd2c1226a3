using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.InvoiceTitle;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.DTOs.InvoiceTitle;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 发票抬头
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class InvoiceTitleController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IMapper _mapper;

    public InvoiceTitleController(IOrderApiCaller orderApiCaller, IMapper mapper)
    {
        _orderApiCaller = orderApiCaller;
        _mapper = mapper;
    }

    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(List<GetBffOutput>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.InvoiceTitleNotExists)]
    public async Task<IActionResult> GetList()
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var userId = currentUser.UserId;
        var invoiceTitles = await _orderApiCaller.GetInvoiceTitleList(userId);
        var result = _mapper.Map<List<GetBffOutput>>(invoiceTitles);
        //获取默认抬头
        var agencyInvoiceTitle = (await _orderApiCaller.GetInvoiceTitleList(currentUser.Provider)).FirstOrDefault();
        if (agencyInvoiceTitle is not null) result.Insert(0, _mapper.Map<GetBffOutput>(agencyInvoiceTitle));
        return Ok(result);
    }

    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(GetInvoiceTitleOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.InvoiceTitleNotExists)]
    public async Task<IActionResult> Get(long id)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var userId = currentUser.UserId;
        //获取默认抬头
        var result = (await _orderApiCaller.GetInvoiceTitleList(currentUser.Provider)).FirstOrDefault(x => x.Id.Equals(id));
        if (result is not null) return Ok(result);
        result = (await _orderApiCaller.GetInvoiceTitleList(userId)).FirstOrDefault(x => x.Id.Equals(id));
        return Ok(result);
    }

    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(AddBffInput bffInput)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var user = currentUser.UserId;
        //选择默认开票主体
        var invoiceConfigs = await _orderApiCaller.GetInvoiceConfig();
        var invoiceConfig = invoiceConfigs.FirstOrDefault(x => x.IsDefault)
            ?? throw new BusinessException(ErrorTypes.Order.InvoiceConfigNotExists);
        var input = _mapper.Map<AddInvoiceTitleInput>(bffInput);
        input.InvoiceConfigId = invoiceConfig.Id;
        input.UserId = user;
        input.UserType = Contracts.Common.Order.Enums.InvoiceTitleUserType.Agency;
        var result = await _orderApiCaller.AddInvoiceTitle(input);
        return Ok(result);
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> Update(UpdateInvoiceTitleInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agencyId = currentUser.Provider;
        var agencies = (await _orderApiCaller.GetInvoiceTitleList(agencyId));

        if (agencies is null || agencies.Any() is false)
            return Ok();

        //不允许修改默认抬头
        if (agencies.FirstOrDefault().Id.Equals(input.Id))
            return Ok();

        await _orderApiCaller.UpdateInvoiceTitle(input);
        return Ok();
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> Delete(DeleteInput input)
    {
        await _orderApiCaller.DeleteInvoiceTitle(input);
        return Ok();
    }
}
