using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.TicketProduct;
using Common.Swagger;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Product.DTOs.TicketProduct;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 券类产品
/// </summary>
[Authorize]
[ApiController]
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
public class TicketProductController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IMarketingApiCaller _marketingApiCaller;
    
    public TicketProductController(
        IMapper mapper,
        IProductApiCaller productApiCaller,
        IMarketingApiCaller marketingApiCaller)
    {
        _mapper = mapper;
        _productApiCaller = productApiCaller;
        _marketingApiCaller = marketingApiCaller;
    }

    /// <summary>
    /// 券类产品列表查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200,typeof(PagingModel<TicketProductAgencySearchOutput>))]
    public async Task<IActionResult> Search(TicketProductAgencySearchInput input)
    {
        var searchInput = _mapper.Map<SearchByAgencyInput>(input);
        //查询优惠券包含信息
        if (input.CouponId.HasValue)
        {
            var couponList = await _marketingApiCaller.GetCouponInfos(new List<long> {input.CouponId.Value});
            var couponInfo = couponList.FirstOrDefault();
            var rangeItem =
                couponInfo?.UserCouponRanges.FirstOrDefault(x => x.ProductType == LimitProductType.Ticket);
            if (rangeItem is {RangeType: CouponRangeType.Part})
            {
                searchInput.GroupIds = rangeItem.GroupItems;
            }
        }
        
        //查询线路产品列表
        var searchPage = await _productApiCaller.AgencySearchTicketProduct(searchInput);
        var result = _mapper.Map<PagingModel<TicketProductAgencySearchOutput>>(searchPage);
        return Ok(result);
    }
}