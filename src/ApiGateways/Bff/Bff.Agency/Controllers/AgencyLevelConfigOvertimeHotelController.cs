using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.AgencyLevelConfigHotel;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfigHotel;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfigOvertimeHotel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 会员等级酒店赔付信息
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AgencyLevelConfigOvertimeHotelController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    public AgencyLevelConfigOvertimeHotelController(IMapper mapper,
        ITenantApiCaller tenantApiCaller,
        IHotelApiCaller hotelApiCaller
        )
    {
        _mapper = mapper;
        _tenantApiCaller = tenantApiCaller;
        _hotelApiCaller = hotelApiCaller;
    }

    /// <summary>
    /// 通过酒店Id获取赔付信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<AgencyLevelConfigOvertimeHotelOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetByHotelIds(GetAgencyLevelConfigHotelInput input)
    {
        var searchApiHotelInput = new Contracts.Common.Hotel.DTOs.ApiHotel.SearchInput()
        {
            StaffTag = true,
            ApiHotelIds = input.HotelIds,
            PageIndex = 1,
            PageSize = input.HotelIds.Count,
        };
        var apiHotels = await _hotelApiCaller.ApiHotelSearchV2(searchApiHotelInput);
        var result = new List<AgencyLevelConfigOvertimeHotelOutput>();
        if (apiHotels.Data.Any())
        {
            var hotelIds = apiHotels.Data.Select(x => x.Id).ToList();
            CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
            var search = new AgencyLevelConfigOvertimeHotelInput()
            {
                AgencyId = currentUser.Provider,
                HotelIds = hotelIds,
                TenantId = currentUser.Tenant
            };
            result = await _tenantApiCaller.GetConfigOvertimeHotelByHotelIds(search);
        }
        return Ok(result);
    }

}
