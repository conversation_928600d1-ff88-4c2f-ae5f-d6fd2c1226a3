using Bff.Agency.Callers;
using Common.Swagger;
using Common.Swagger.Header;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.DTOs.Huizhi;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 汇智微信
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HuizhiController : ControllerBase
{
    private readonly IWechatApiCaller _wechatApiCaller;

    public HuizhiController(IWechatApiCaller wechatApiCaller)
    {
        _wechatApiCaller = wechatApiCaller;
    }

    /// <summary>
    /// 获取公众号网页授权地址
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreTenantHeader]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetAuthorizeUri(GetHuizhiAuthorizeUriInput input)
    {
        var result = await _wechatApiCaller.GetHuizhiAuthorizeUri(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取汇智JS SDK Package
    /// </summary>
    /// <param name="pageurl"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetJsSdkPackageOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetJsSdkPackage(string pageurl)
    {
        var result = await _wechatApiCaller.GetHuizhiJsSdkPackage(pageurl);
        return Ok(result);
    }

    /// <summary>
    /// 根据code获取汇智openid
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GetOauth2AccessTokenOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.WeChat.CodeInvalid, ErrorTypes.WeChat.CodeBeenUsed)]
    public async Task<IActionResult> GetOauth2AccessToken(GetOauth2AccessTokenInput input)
    {
        var output = await _wechatApiCaller.GetHuiZhiOauth2AccessToken(input);
        return Ok(output);
    }

    /// <summary>
    /// 获取小程序码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetWxacodeUnlimit(GetWxacodeUnlimitInput input)
    {
        var result = await _wechatApiCaller.GetHuiZhiWxacodeUnlimit(input);
        return Ok(result);
    }
}
