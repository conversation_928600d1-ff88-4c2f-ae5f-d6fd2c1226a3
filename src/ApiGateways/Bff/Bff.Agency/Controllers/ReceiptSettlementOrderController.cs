using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.ReceiptSettlementOrder;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.TenantBankAccount;
using Contracts.Common.Tenant.DTOs.Agency;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 收款结算单信息
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class ReceiptSettlementOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IMapper _mapper;

    public ReceiptSettlementOrderController(
        IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller,
        IPaymentApiCaller paymentApiCaller,
        IMapper mapper)
    {
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 收款结算单查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<SearchReceiptSettlementOrderBffOutput>))]
    public async Task<IActionResult> Search(SearchReceiptSettlementOrderBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        //对账中数据默认展示B2B设置增加默认收款账号
        var searchRequest = _mapper.Map<SearchInput>(input);
        searchRequest.AgencyId = currentUser.Provider;
        searchRequest.TenantId = currentUser.Tenant;
        var searchResponse = await _orderApiCaller.SearchReceiptSettlementOrder(searchRequest);
        var pageResult = _mapper.Map<PagingModel<SearchReceiptSettlementOrderBffOutput>>(searchResponse);
        if (pageResult.Data.Any())
        {
            var receivableAccount = await GetReceivableAccount();
            foreach (var item in pageResult.Data
                         .Where(x => x.Status != ReceiptSettlementOrderStatus.ReceiptComplete))
            {
                item.AccountName = receivableAccount.AccountName;
                item.BankAccount = receivableAccount.AccountNo;
                item.BankName = receivableAccount.BankName;
                item.BankCode = receivableAccount.BankCode;
            }
        }

        return Ok(pageResult);
    }

    /// <summary>
    /// 收款结算单查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(PagingModel<SearchReceiptSettlementOrderBffOutput>))]
    public async Task<IActionResult> Detail(SearchReceiptSettlementOrderBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        //对账中数据默认展示B2B设置增加默认收款账号
        var searchRequest = _mapper.Map<SearchInput>(input);
        searchRequest.AgencyId = currentUser.Provider;
        searchRequest.TenantId = currentUser.Tenant;
        var searchResponse = await _orderApiCaller.SearchReceiptSettlementOrder(searchRequest);
        var pageResult = _mapper.Map<PagingModel<SearchReceiptSettlementOrderBffOutput>>(searchResponse);
        if (pageResult.Data.Any())
        {
            var receivableAccount = await GetReceivableAccount();
            foreach (var item in pageResult.Data
                         .Where(x => x.Status != ReceiptSettlementOrderStatus.ReceiptComplete))
            {
                item.AccountName = receivableAccount.AccountName;
                item.BankAccount = receivableAccount.AccountNo;
                item.BankName = receivableAccount.BankName;
                item.BankCode = receivableAccount.BankCode;
            }
        }

        return Ok(pageResult);
    }

    /// <summary>
    /// 收款单-确认
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Confirm(ConfirmReceiptSettlementOrderBffInput input)
    {
        await _orderApiCaller.ConfirmReceiptSettlementOrder(new ConfirmInput
        {
            SettlementOrderId = input.SettlementOrderId
        });
        return Ok();
    }

    /// <summary>
    /// 收款结算单明细导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(ExportDetailBffOutput))]
    public async Task<IActionResult> ExportDetail(ExportDetailBffInput input)
    {
        var exportResponse = await _orderApiCaller.ExportReceiptSettlementOrderDetail(new ExportDetailInput
        {
            SettlementOrderId = input.SettlementOrderId
        });
        if (exportResponse.Status != ReceiptSettlementOrderStatus.ReceiptComplete)
        {
            //默认展示配置的收款账户
            var receivableAccount = await GetReceivableAccount();
            exportResponse.BankName = receivableAccount.BankName;
            exportResponse.BankCode = receivableAccount.BankCode;
            exportResponse.BankAccount = receivableAccount.AccountNo;
            exportResponse.AccountName = receivableAccount.AccountName;
        }
        var result = _mapper.Map<ExportDetailBffOutput>(exportResponse);

        //查询充值单数据
        var reChargeOrderIds = result.CreditRechargeOrder?.Order.Select(x => x.OrderId).ToArray();
        if (reChargeOrderIds != null && reChargeOrderIds.Any())
        {
            var reChargeOrders = (await _tenantApiCaller.AgencyCreditChargeDetails(reChargeOrderIds)).ToList();
            foreach (var item in result.CreditRechargeOrder.Order)
            {
                var reChargeOrder = reChargeOrders.FirstOrDefault(x => x.Id == item.OrderId);
                if (reChargeOrder == null) continue;
                item.CreateOrderDate = reChargeOrder.CreateTime;
                item.PayType = reChargeOrder.PayType;
                item.FinishTime = reChargeOrder.ConfirmTime;
                item.PaymentCurrencyCode = reChargeOrder.CurrencyCode;
            }
        }

        //分销商数据补充
        var agencies = await _tenantApiCaller.GetAgencyDetail(result.AgencyId);
        result.AgencyName = agencies?.FullName;

        return Ok(result);
    }

    /// <summary>
    /// 获取配置的收款结算账户
    /// </summary>
    /// <returns></returns>
    private async Task<GetTenantBankAccountOutput> GetReceivableAccount()
    {
        var receivableAccount = new GetTenantBankAccountOutput();
        //查询B2B设置增加默认收款账号信息
        var financialSetting = await _tenantApiCaller.GetFinancialSetting();
        if (long.TryParse(financialSetting?.ReceivablesAccounts, out long receivablesAccountId))
        {
            //查询收款账号基础信息
            receivableAccount = await _paymentApiCaller.GetTenantBankAccount(receivablesAccountId);
        }
        return receivableAccount;
    }

    /// <summary>
    /// 收款结算单-账单提醒
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(ReceiptSettlementOrderRemindOutput))]
    public async Task<IActionResult> Remind()
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        var datas = await _orderApiCaller.RemindReceiptSettlementOrder(new ReceiptSettlementOrderRemindInput
        {
            AgencyId = currentUser.Provider
        });
        if (datas.Any() is false)
            return Ok();
        var result = datas.OrderByDescending(x => x.CreateTime).FirstOrDefault();

        return Ok(result);
    }

    /// <summary>
    /// 收款提醒-标记为已阅读提醒
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> ReadRemind()
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        await _orderApiCaller.ReadRemindReceiptSettlementOrder(new ReadRemindDataInput
        {
            AgencyId = currentUser.Provider,
            UserId = currentUser.UserId,
        });

        return Ok();
    }

    /// <summary>
    /// 获取收款记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(IEnumerable<GetReceiptSettlementOrderRecordsOutput>), 200)]
    public async Task<IActionResult> GetRecords(GetReceiptSettlementOrderRecordsInput input)
    {
        var datas = await _orderApiCaller.GetReceiptSettlementOrderRecords(new GetReceiptSettlementOrderRecordsInput { 
            SettlementOrderIds = input.SettlementOrderIds,
        });
        var result = _mapper.Map<List<GetReceiptSettlementOrderRecordsBffOutput>>(datas);
        return Ok(result);
    }
}