using Bff.Agency.Models.Invoice;
using FluentValidation;

namespace Bff.Agency.Validators.Invoice;

public class ApplyBffInputValidator : AbstractValidator<ApplyBffInput>
{
    public ApplyBffInputValidator()
    {
        RuleFor(x => x.EmailAddress).NotEmpty();
        RuleFor(x => x.OrderId).NotEmpty();
        RuleFor(x => x.InvoiceAmount).NotEmpty();
        RuleFor(x => x.InvoiceTitleDto)
            .NotEmpty();
        RuleFor(x => x.InvoiceTitleDto.Title)
            .NotEmpty()
            .When(x => x.InvoiceTitleDto is not null);
    }
}
