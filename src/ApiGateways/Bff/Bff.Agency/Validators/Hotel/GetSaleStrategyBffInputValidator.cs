using Bff.Agency.Models.Hotel;
using FluentValidation;

namespace Bff.Agency.Validators.Hotel;

public class GetSaleStrategyBffInputValidator : AbstractValidator<GetSaleStrategyBffInput>
{
    public GetSaleStrategyBffInputValidator()
    {
        RuleFor(x => x.HotelId).NotEmpty();
        RuleFor(x => x.MaximumOccupancy).GreaterThanOrEqualTo(1);
        RuleFor(x => x.RoomNum).GreaterThanOrEqualTo(1);
    }
}