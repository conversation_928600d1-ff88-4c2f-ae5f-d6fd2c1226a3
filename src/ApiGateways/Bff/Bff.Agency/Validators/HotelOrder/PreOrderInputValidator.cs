using Bff.Agency.Models.HotelOrder;
using FluentValidation;

namespace Bff.Agency.Validators.HotelOrder;

public class PreOrderInputValidator : AbstractValidator<PreOrderInput>
{
    public PreOrderInputValidator()
    {
        RuleFor(x => x.HotelId).GreaterThan(0);
        RuleFor(x => x.PriceStrategyId).NotEmpty();
        RuleFor(x => x.SupplierApiType).IsInEnum();
        RuleFor(x => x.AdultNum).GreaterThan(0);
        RuleFor(x => x.RoomCount).GreaterThan(0);
        RuleFor(x => x.CheckIn).NotNull().GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.CheckOut).NotNull().GreaterThan(x => x.CheckIn);
    }
}
