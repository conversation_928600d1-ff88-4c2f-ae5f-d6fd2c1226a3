using Bff.Agency.Models.HotelGroupBookingOrder;
using Contracts.Common.Order.Enums;
using FluentValidation;

namespace Bff.Agency.Validators.HotelGroupBookingOrder;

public class SubmitInputValidator : AbstractValidator<SubmitInput>
{
    public SubmitInputValidator()
    {
        RuleFor(x => x.SellingPlatform).IsInEnum()
                .Must(x => x == SellingPlatform.B2BWeb || x == SellingPlatform.B2BApplet);

        RuleFor(x => x.GroupBookingApplicationFormId).NotNull()
                .GreaterThan(0);
        RuleFor(x => x.GroupBookingPreOrderId).NotNull()
            .GreaterThan(0);

        RuleFor(x => x.ContactsName).NotNull()
            .Length(1, 50);
        RuleFor(x => x.ContactsPhoneNumber)
            .Length(1, 50);
        RuleFor(x => x.ContactsEmail)
            .EmailAddress()
            .When(x => !string.IsNullOrEmpty(x.ContactsEmail));

        RuleFor(x => x.Message)
            .MaximumLength(500);
    }
}
