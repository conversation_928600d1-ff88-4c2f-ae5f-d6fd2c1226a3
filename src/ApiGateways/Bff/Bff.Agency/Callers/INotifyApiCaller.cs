using Common.Caller;
using EfCoreExtensions.Abstract;
using SiteMessage = Contracts.Common.Notify.DTOs.SiteMessage;

namespace Bff.Agency.Callers;

public interface INotifyApiCaller : IHttpCallerBase
{
    #region SiteMessage

    Task<SiteMessage.DetailOutput> SiteMessageRead(SiteMessage.ReadInput input);

    Task<SiteMessage.DetailOutput> SiteMessageDetail(SiteMessage.DetailInput input);

    Task<PagingModel<SiteMessage.SearchOutput>> SiteMessageSearch(SiteMessage.SearchInput input);

    Task SiteMessageBatchRead(SiteMessage.BatchReadInput input);

    #endregion
}
