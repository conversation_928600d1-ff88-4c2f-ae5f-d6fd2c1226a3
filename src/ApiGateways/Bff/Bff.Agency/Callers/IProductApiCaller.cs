using Common.Caller;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.DTOs.CarServiceItem;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.DTOs.ProductResource;
using Contracts.Common.Product.DTOs.ProductSku;
using Contracts.Common.Product.DTOs.TicketProduct;
using FunProduct=Contracts.Common.Product.DTOs.FunProduct;
using EfCoreExtensions.Abstract;
using Contracts.Common.Product.DTOs.ProductPhoto;
using Contracts.Common.Product.DTOs.CarTypeGrade;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.DTOs.Fields;
using Contracts.Common.Product.DTOs.DataFinder;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;

namespace Bff.Agency.Callers;

public interface IProductApiCaller : IHttpCallerBase
{
    #region PriceSettings

    /// <summary>
    /// 查询分销商渠道价格设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<AgencyChannelPriceSettingOutput>> QueryAgencyChannelPrices(QueryChannelPriceInput input);

    /// <summary>
    /// 查询汇智酒店渠道价格设置
    /// </summary>
    /// <param name="priceGroupId"></param>
    /// <returns></returns>
    Task<GetHuiZhiHotelChannelPriceOutput> GetHuiZhiHotelChannelPrice(long priceGroupId);

    #endregion

    #region LineProduct

    /// <summary>
    /// 线路-分销商分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<AgencySearchOutput>> AgencySearchLineProduct(AgencySearchInput input);

    /// <summary>
    /// 获取线路产品信息
    /// </summary>
    /// <param name="productId"></param>
    /// <returns></returns>
    Task<GetLineProductDto> GetLineProduct(long productId);

    /// <summary>
    /// 获取线路产品信息
    /// (因为 GetLineProduct 返回的对象和底层不一致，而且在订单业务里使用，暂新开一个接口接原来参数)
    /// </summary>
    /// <param name="productId"></param>
    /// <returns></returns>
    Task<GetLineProductOutput> GetLineProductV2(long productId);

    /// <summary>
    /// 获取线路日历价格信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetLineProductSkuDto> GetLineProductSkuCalendarPriceBySkuId(GetLineProductSkuCalendarPriceInput input);

    /// <summary>
    /// 获取线路产品规格信息
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<GetLineProductSkuDetailOutput>> GetLineProductSkusByIds(params long[] ids);

    #endregion

    #region LineProductSku

    /// <summary>
    /// 获取价格分组关联的线路产品规格列表
    /// </summary>
    /// <param name="input">通过产品id查询和价格分组id查询</param>
    /// <returns></returns>
    Task<List<GetLineProductSkuDetailOutput>> GetPriceGroupLineProductSkus(AgencyGetLineProductSkuInput input);

    #endregion

    #region LineProductSkuCalendarPrice

    /// <summary>
    /// 获取通过产品id线路产品规格渠道日历价格信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetLineProductSkuCalendarPriceOutput>> GetPriceGroupLineProductSkuPricesByProductId(AgencyGetInput input);

    #endregion

    #region TicketProduct

    /// <summary>
    /// 券类-分销商分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchByAgencyOutput>> AgencySearchTicketProduct(SearchByAgencyInput input);

    /// <summary>
    /// 查询券类规格信息
    /// </summary>
    /// <param name="skuIds"></param>
    /// <returns></returns>
    Task<List<GetProductSkusOutput>> GetTicketSkuByIds(List<long> skuIds);

    #endregion

    #region AgencyChannelPriceSetting
    /// <summary>
    /// 查询分销商渠道价格配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<AgencyChannelPriceSettingOutput>> QueryAgencyChannelPriceSettings(QueryChannelPriceInput input);
    #endregion

    #region ProdcuctResource

    /// <summary>
    /// 获取产品关联资源的位置信息
    /// </summary>
    /// <param name="productIds"></param>
    /// <returns></returns>
    Task<IEnumerable<GetProductResourceLocationOutput>> GetProductResourceLocations(IEnumerable<long> productIds);

    #endregion

    #region CarProduct
    Task<CarProductDetailCalendarPricesOutput> GetCarProductPriceDetails(CarProductDetailCalendarPricesInput input);

    Task<CarProductSelectOutput> GetCarProductSelects(GetProductSelectsInput input);

    #endregion

    #region CarProductSku
    Task<List<CarProductSkuOutput>> GetCarProductSkDetails(CarProductSkuInput input);
    #endregion

    #region CarServiceItem
    Task<List<CarServiceItemOutput>> GetCarServiceItemByIds(List<long> ids); 
    #endregion

    #region ProductPhoto

    /// <summary>
    /// 批量获取产品首图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<BatchProductFirstOutput>> BatchProductFirstPhoto(BatchProductFirstInput input);

    #endregion

    #region CarTypeGrade

    /// <summary>
    /// 获取车型等级列表
    /// </summary>
    /// <returns></returns>
    Task<List<CarTypeGradeOuput>> GetCarTypeGrades();

    #endregion 

    #region FunProduct 玩乐

    /// <summary>
    /// 玩乐 用车列表查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<FunProduct.CarProductSearchOutput>> FunCarProductSearch(FunProduct.SearchInput input);

    /// <summary>
    /// 玩乐 线路产品列表查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<FunProduct.LineProductSearchOutput>> FunLineProductSearch(FunProduct.SearchInput input);

    #endregion

    #region ProductInformationTemplate

    /// <summary>
    /// 更小颗粒度上配置的模版优先，套餐层模版设置 > 产品层模版设置 > 默认模版设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetProductTempFieldsDetailOutput> ProductInformationTemplateGetProductTempFieldsDetail(GetProductTempFieldsDetailInput input);
    #endregion

    #region Fields
    Task<List<FieldsOutput>> FieldsList(SearchFieldsInput input);
    #endregion

    #region DataFinder

    Task EventCorrelation(IEnumerable<EventCorrelationDTO> eventCorrelations);

    #endregion

    #region LineProductSkuTypeItem

    Task<List<QueryLineSkuTypeItemOutput>> QueryLineSkuTypeItems(QueryLineSkuTypeItemInput input);

    #endregion
    
    #region openSupplierProduct

    /// <summary>
    /// 查询供应端产品详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetOpenSupplierProductDetailOutput> GetOpenSupplierProductDetail(GetOpenSupplierProductDetailInput input);

    #endregion

    #region AgencyChannelCommissionSettings
    Task<List<QueryAgencyChannelCommissionOutput>> QueryAgencyChannelCommissionSettings(QueryAgencyChannelCommissionInput input); 
    #endregion
}
