using AutoMapper;
using CarProductOrderDto= Contracts.Common.Order.DTOs.CarProductOrder;

namespace Bff.Agency.Models.CarProductOrder;

public class DetailOutputProfile:Profile
{
    public DetailOutputProfile() {
        CreateMap<CarProductOrderDto.BaseOrderDetailOutput, BaseOrderDetailOutput>();
        CreateMap<CarProductOrderDto.CarProductOrderDetailOutput, CarProductOrderDetailOutput>();
        CreateMap<CarProductOrderDto.CarServiceItemOrderOutput, CarServiceItemOrderOutput>();
        CreateMap<CarProductOrderDto.CarProductOrderPriceOutput, CarProductOrderPriceOutput>();
        CreateMap<CarProductOrderDto.GetDetailOutput, DetailOutput>();
        CreateMap<CarProductOrderDto.DistributionChannelInfo, DistributionChannelBffInfo>();
        CreateMap<CarProductOrderDto.SupplyChannelInfo, SupplyChannelBffInfo>();
        CreateMap<CarProductOrderDto.DeliveryResult, DeliveryBffResult>();
    }
}
