using Contracts.Common.Order.Enums;

namespace Bff.Agency.Models.WorkOrder;

public class SearchBffOutput
{
    public long Id { get; set; }

    public DateTime CreateTime { get; set; }

    /// <summary>
    /// HOP工单号
    /// </summary>
    public string? HopWorkOrderNumber { get; set; }

    /// <summary>
    /// 工单类型
    /// </summary>
    public string WorkOrderType { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public WorkOrderStatus Status { get; set; }

    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    public string EnHotelName { get; set; }

    /// <summary>
    /// 最新一条回复
    /// </summary>
    public string LatestReplyContent { get; set; }

    /// <summary>
    /// 能否评价
    /// </summary>
    public bool CanItBeEvaluated { get; set; }
}
