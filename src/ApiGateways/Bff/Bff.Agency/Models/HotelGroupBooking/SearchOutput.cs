using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Bff.Agency.Models.HotelGroupBooking;

public class SearchOutput
{/// <summary>
 /// 申请单号
 /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 申请人id
    /// </summary>
    public long? ApplicantId { get; set; }

    /// <summary>
    /// 申请人
    /// </summary>
    public string? Applicant { get; set; }

    /// <summary>
    /// 申请时间
    /// </summary>
    public DateTime ApplicationTime { get; set; }

    /// <summary>
    /// 完结时间
    /// </summary>
    public DateTime? FinishTime { get; set; }

    public GroupBookingApplicationFormStatus Status { get; set; }

    /// <summary>
    /// 合同是否已签署
    /// </summary>
    public bool? ContractSigned { get; set; }

    #region

    /// <summary>
    /// 申请 预订单状态
    /// </summary>
    public GroupBookingFormPreOrderStatus? GroupBookingFormPreOrderStatus { get; set; }

    /// <summary>
    /// 团房单数 当值为0时表示没有团房订单
    /// </summary>
    public int GroupBookingOrderCount { get; set; }

    /// <summary>
    /// 预订单状态
    /// </summary>
    public GroupPreOrderStatus? GroupPreOrderStatus { get; set; }

    /// <summary>
    /// 最小预订单id
    /// </summary>
    public long? GroupBookingPreOrderId { get; set; }

    public long? GroupBookingOrderId { get; set; }

    /// <summary>
    /// 团房最小订单状态
    /// </summary>
    public HotelOrderStatus? HotelOrderStatus { get; set; }

    /// <summary>
    /// 入住人信息excel文件路径
    /// </summary>
    public string? HotelOrderGuestFilePath { get; set; }

    /// <summary>
    /// 是否允许添加入住人信息
    /// </summary>
    public bool GuestAddable { get; set; }

    #endregion

    /// <summary>
    /// 目的地信息
    /// </summary>
    public List<SearchDemandOutput> Demands { get; set; }

    /// <summary>
    /// 关联子单单号集合
    /// </summary>
    public long[]? BaseOrderIds { get; set; }

    /// <summary>
    /// 最新操作记录
    /// </summary>
    public OperationLogOutput LatestOperationLog { get; set; }

    /// <summary>
    /// 筛选项 状态
    /// </summary>
    public GroupBookingFormPreStatus? GroupBookingFormPreStatus { get; set; }
}

public class SearchDemandOutput
{
    public long Id { get; set; }
    public int? CountryCode { get; set; }

    public string? CountryName { get; set; }
    public string? EnCountryName { get; set; }

    public int? ProvinceCode { get; set; }

    public string? ProvinceName { get; set; }
    public string? EnProvinceName { get; set; }

    public int? CityCode { get; set; }

    public string? CityName { get; set; }
    public string? EnCityName { get; set; }

    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long? ResourceHotelId { get; set; }

    /// <summary>
    /// 指定酒店名称
    /// </summary>
    public string? HotelName { get; set; }
    public string? EnHotelName { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime? CheckOutDate { get; set; }

    #region 住宿

    /// <summary>
    /// 需要住宿
    /// </summary>
    public bool NeedHotelRoom { get; set; }

    /// <summary>
    /// 成人数
    /// </summary>
    public int? AdultNum { get; set; }

    /// <summary>
    /// 儿童数
    /// </summary>
    public int? ChildNum { get; set; }

    /// <summary>
    /// 间夜单价预算
    /// </summary>
    public decimal? UnitPrice { get; set; }

    public string? CurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 床型信息
    /// </summary>
    public GroupBookingApplicationFormBedTypeDto[] BedTypes { get; set; }

    /// <summary>
    /// 其他需求
    /// </summary>
    public string? OtherRequirement { get; set; }

    #endregion

    #region 会议

    public bool NeedMeetingRoom { get; set; }

    /// <summary>
    /// 参会人数
    /// </summary>
    public int? MeetingsNum { get; set; }

    /// <summary>
    /// 会议室预算
    /// </summary>
    public decimal? MeetingBudget { get; set; }

    /// <summary>
    /// 会议时间信息
    /// </summary>
    public GroupBookingApplicationFormMeetingTimeDto[] MeetingTimes { get; set; }

    /// <summary>
    /// 会议详细需求
    /// </summary>
    public string? MeetingRequirement { get; set; }

    #endregion
}

public class SearchSupplement
{
    public int Count { get; set; }
    public GroupBookingFormPreStatus Status { get; set; }
}

public enum GroupBookingFormPreStatus
{
    /// <summary>
    /// 已提交
    /// </summary>
    Submitted = 1,

    /// <summary>
    /// 报价待确认
    /// </summary>
    QuotationWaitforConfirm = 2,

    /// <summary>
    /// 预订创建中
    /// </summary>
    PreOrderCreating = 3,

    /// <summary>
    /// 预订单待付款
    /// </summary>
    PreOrderPendingPayment = 4,

    /// <summary>
    /// 预订已付款
    /// </summary>
    PreOrderConfirmedPayment = 5,

    /// <summary>
    /// 预订单 已完成
    /// </summary>
    PreOrderFinished = 6,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 7,
}