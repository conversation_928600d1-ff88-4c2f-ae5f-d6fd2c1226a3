using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Agency.Models.AgencyUsers;

public class AddInput
{
    /// <summary>
    /// 用户名 6-50位
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string UserName { get; set; }

    /// <summary>
    /// 姓名 2-10位
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string RealName { get; set; }

    /// <summary>
    /// 手机号 
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 密码必须为包含数字和字母的8-20位
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string Password { get; set; }

    /// <summary>
    /// 邮箱 如有填则是正确邮箱格式
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string? Email { get; set; }

    /// <summary>
    /// AclKey
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public IEnumerable<string> AclKeys { get; set; }

    /// <summary>
    /// 国家区号
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string? CountryDialCode { get; set; }
}
