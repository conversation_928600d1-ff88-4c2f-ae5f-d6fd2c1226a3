using Bff.Agency.Models.ProductInformationTemplate;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Bff.Agency.Models.TravelLineOrder;

public class CreateInput : OrderCreateInput
{
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactsEmail { get; set; }

    /// <summary>
    /// 来源订单号
    /// </summary>
    public string ChannelOrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 用户优惠券id
    /// </summary>
    public long? UserCouponId { get; set; }

    /// <summary>
    /// 订单字段信息
    /// </summary>
    public new List<SaveOrderFieldInformationTypeDto> OrderFields { get; set; } = new();
}

