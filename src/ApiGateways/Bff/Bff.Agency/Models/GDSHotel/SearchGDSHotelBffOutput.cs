using Bff.Agency.Models.Hotel;

namespace Bff.Agency.Models.GDSHotel;

public class SearchGDSHotelBffOutput
{
    /// <summary>
    /// 高定酒店id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? ENName { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }
    public string CountryName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }
    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }
    public string CityName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public int DistrictCode { get; set; }
    public string? DistrictName { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? ENAddress { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    /// <summary>
    /// 是否置顶
    /// </summary>
    public bool OnTop { get; set; }

    /// <summary>
    /// 酒店品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 品牌英文名
    /// </summary>
    public string? ENBrand { get; set; }

    /// <summary>
    /// 集团英文名
    /// </summary>
    public string? ENChainName { get; set; }

    /// <summary>
    /// 集团中文名
    /// </summary>
    public string? ChainName { get; set; }

    /// <summary>
    /// 首图
    /// </summary>
    public string FirstPhoto { get; set; }

    /// <summary>
    /// 基础设施
    /// </summary>
    public List<HotelFacilitiesBffItem> Facilities { get; set; }

    /// <summary>
    /// 酒店权益
    /// </summary>
    public List<GDSHotelExclusivePrivilegesBffOutput>? ExclusivePrivileges { get; set; }
}
