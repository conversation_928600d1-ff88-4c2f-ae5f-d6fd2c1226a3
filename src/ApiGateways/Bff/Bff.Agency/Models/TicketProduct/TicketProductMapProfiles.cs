using AutoMapper;
using Contracts.Common.Product.DTOs.TicketProduct;
using EfCoreExtensions.Abstract;

namespace Bff.Agency.Models.TicketProduct;

public class TicketProductMapProfiles : Profile
{
    public TicketProductMapProfiles()
    {
        CreateMap<TicketProductAgencySearchInput, SearchByAgencyInput>();
        CreateMap<AgencySearchTicketSkuInfo,TicketProductAgencySearchSkuInfo>();
        CreateMap<SearchByAgencyOutput,TicketProductAgencySearchOutput>();
        CreateMap<PagingModel<SearchByAgencyOutput>,PagingModel<TicketProductAgencySearchOutput>>();
    }
}