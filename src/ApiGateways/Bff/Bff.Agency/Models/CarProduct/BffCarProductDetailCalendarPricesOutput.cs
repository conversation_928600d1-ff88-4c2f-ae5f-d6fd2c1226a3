using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarServiceItem;
using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;

namespace Bff.Agency.Models.CarProduct;

public class BffCarProductDetailCalendarPricesOutput
{
    public BffCarProductDetailOutput Product { get; set; }

    public List<BffCarProductSkuDetailCalendarPricesOutput> Skus { get; set; } = new();
}

public class BffCarProductDetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 是否在售卖时间内
    /// </summary>
    public bool InSellingDate { get; set; }

    /// <summary>
    /// 产品标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 英文标题
    /// </summary>
    public string EnTitle { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public CarProductType CarProductType { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public string CountryName { get; set; }

    public string EnCountryName { get; set; }

    /// <summary>
    /// 城市 接送机为机场所在城市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }
    public string EnCityName { get; set; }

    /// <summary>
    /// 机场id
    /// </summary>
    public long? AirportId { get; set; }

    public string? AirportName { get; set; }

    public string? EnAirportName { get; set; }

    #region 预订须知

    /// <summary>
    /// 提前几天预订
    /// </summary>
    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 预订须知
    /// </summary>
    public string? Instructions { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeInclude { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeExclude { get; set; }

    /// <summary>
    /// 是否可取消
    /// </summary>
    public CarProductCancelType CancelType { get; set; }

    /// <summary>
    /// 退改规则
    /// </summary>
    public string? CancelRule { get; set; }

    #endregion

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ProductPictures { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 移动版 商品详情
    /// </summary>
    public string? MobileDesc { get; set; }

    /// <summary>
    /// PC版 商品详情
    /// </summary>
    public string? PCDesc { get; set; }

    /// <summary>
    /// 采购价币种 (供应商币种)
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种 (商户币种)
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType? CoordinateType { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// Google位置Id
    /// </summary>
    public string? GooglePalceId { get; set; }

    /// <summary>
    /// 采购来源类型
    /// </summary>
    public CarProductPurchaseSourceType PurchaseSourceType { get; set; }

    /// <summary>
    /// 支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }
}
