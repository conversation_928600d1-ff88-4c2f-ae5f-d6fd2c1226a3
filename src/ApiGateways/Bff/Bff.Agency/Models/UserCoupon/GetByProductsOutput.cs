using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Enums;

namespace Bff.Agency.Models.UserCoupon;

public class GetByProductsOutput
{
    /// <summary>
    /// 用户优惠券id
    /// </summary>
    public long UserCouponId { get; set; }

    /// <summary>
    /// 优惠券类型
    /// </summary>
    public CouponType CouponType { get; set; }

    /// <summary>
    /// 优惠券名称
    /// </summary>
    public string CouponName { get; set; }

    /// <summary>
    /// 面额 折扣券:[0,1)  满减券:[0,999999]；
    /// 例子：9折：0.9 
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 满多少元可用
    /// </summary>
    public decimal LimitMinAmt { get; set; }

    /// <summary>
    /// 最大优惠金额
    /// </summary>
    public decimal MaxDiscountAmt { get; set; }

    /// <summary>
    /// 开始有效期
    /// </summary>
    public DateTime ValidBeginDate { get; set; }

    /// <summary>
    /// 结束有效期
    /// </summary>
    public DateTime ValidEndDate { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int? Nights { get; set; }

    /// <summary>
    /// 房间数
    /// </summary>
    public int? NumberOfRoom { get; set; }

    /// <summary>
    /// 剩余有效期天数 
    /// </summary>
    public int LeftDays
    {
        get
        {
            var val = (int)ValidEndDate.Subtract(DateTime.Today).TotalDays;
            if (val < 0) val = 0;
            return val;
        }
    }

    /// <summary>
    /// 适用的产品集合
    /// </summary>
    public IEnumerable<EnableOrderProductInfoOutput> EnableProductInfos { get; set; } = Enumerable.Empty<EnableOrderProductInfoOutput>();
}
