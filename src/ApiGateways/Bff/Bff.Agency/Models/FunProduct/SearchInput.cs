using Contracts.Common.Product.Enums;

namespace Bff.Agency.Models.FunProduct;

public class SearchInput : PagingInput
{
    /// <summary>
    /// 出行日期
    /// </summary>
    public DateTime? TravelDate { get; set; }

    /// <summary>
    /// 目的地城市
    /// </summary>
    public int? CityCode { get; set; }

    /// <summary>
    /// 最低价
    /// </summary>
    public int? MinPrice { get; set; }

    /// <summary>
    /// 最高价
    /// </summary>
    public int? MaxPrice { get; set; }

    /// <summary>
    /// 产品类型 5-景区 6-线路 8-用车
    /// </summary>
    public ProductType[]? ProductTypes { get; set; }

    /// <summary>
    /// 二级子类型 81-接送车 82-点对点 83-包车
    /// </summary>
    public SubFunProductType[]? SubProductTypes { get; set; }

    /// <summary>
    /// 目标地点距离 单位km
    /// </summary>
    public int? TargetDistance { get; set; }

    /// <summary>
    /// 目标地点纬度
    /// </summary>
    public double? TargetLatitude { get; set; }

    /// <summary>
    /// 目标地点经度
    /// </summary>
    public double? TargetLongitude { get; set; }

    /// <summary>
    /// 产品关键字搜索 景点名称 产品名 接送车名称
    /// </summary>
    public string? Keyword { get; set; }
}

/// <summary>
/// 玩乐产品二级类型
/// </summary>
public enum SubFunProductType
{
    #region CarProduct

    /// <summary>
    /// 接送机
    /// </summary>
    AirportTransfer = 81,

    /// <summary>
    /// 点对点接送
    /// </summary>
    PointToPointTransfer = 82,

    /// <summary>
    /// 包车
    /// </summary>
    CarChartered = 83,

    #endregion
}