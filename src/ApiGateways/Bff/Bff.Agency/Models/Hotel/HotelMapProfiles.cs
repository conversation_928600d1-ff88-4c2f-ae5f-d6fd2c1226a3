using AutoMapper;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Marketing.DTOs.Coupon;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Newtonsoft.Json;

namespace Bff.Agency.Models.Hotel;

public class HotelMapProfiles : Profile
{
    public HotelMapProfiles()
    {
        CreateMap<GetHotelDetailsOutput, GetHotelDetailsBffOutput>();
        CreateMap<HotelDetailOutput, GetHotelDetailsBffOutput>()
            .ForMember(x => x.ZHName, m => m.MapFrom(f => f.Name));
        CreateMap<CouponInfo, GetByHotelIdsOutput>().ReverseMap();

        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelPolicyOutput, HotelPolicyBffOutput>();
        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelExtendOutput, HotelExtendBffOutput>()
            .ForMember(x=> x.CheckInFrom, m => m.MapFrom(f=> GetTimeSpanStr(f.CheckInFrom, false)))
            .ForMember(x => x.CheckInTo, m => m.MapFrom(f => GetTimeSpanStr(f.CheckInTo, true)))
            .ForMember(x => x.CheckOutFrom, m => m.MapFrom(f => GetTimeSpanStr(f.CheckOutFrom, false)))
            .ForMember(x => x.CheckOutTo, m => m.MapFrom(f => GetTimeSpanStr(f.CheckOutTo, true)))
            .ReverseMap();
        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelExtraBedPolicyOutput, HotelExtraBedPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelMealChildPolicyOutput, HotelMealChildPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelMealPolicyOutput, HotelMealPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelPolicyExtendOutput, HotelPolicyExtendBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelRoomExtraBedPolicyOutput, HotelRoomExtraBedPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Hotel.DTOs.HotelExtend.HotelMealBusinessHourInfoModel, HotelMealBusinessHourInfoModel>()
            .ReverseMap();

        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelPolicyOutput, HotelPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelExtendOutput, HotelExtendBffOutput>()
            .ForMember(x => x.CheckInFrom, m => m.MapFrom(f => GetTimeSpanStr(f.CheckInFrom, false)))
            .ForMember(x => x.CheckInTo, m => m.MapFrom(f => GetTimeSpanStr(f.CheckInTo, true)))
            .ForMember(x => x.CheckOutFrom, m => m.MapFrom(f => GetTimeSpanStr(f.CheckOutFrom, false)))
            .ForMember(x => x.CheckOutTo, m => m.MapFrom(f => GetTimeSpanStr(f.CheckOutTo, true)))
            .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelExtraBedPolicyOutput, HotelExtraBedPolicyBffOutput>()
               .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelExtraBedPolicyOutput, HotelRoomExtraBedPolicyBffOutput>()
               .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelMealChildPolicyOutput, HotelMealChildPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelMealPolicyOutput, HotelMealPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelPolicyExtendOutput, HotelPolicyExtendBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelRoomExtraBedPolicyOutput, HotelRoomExtraBedPolicyBffOutput>()
            .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.HotelExtend.HotelMealBusinessHourInfoModel, HotelMealBusinessHourInfoModel>()
            .ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.Hotel.BedType, Contracts.Common.Hotel.DTOs.Hotel.BedType>().ReverseMap();
        CreateMap<Contracts.Common.Resource.DTOs.Hotel.SubModel, Contracts.Common.Hotel.DTOs.Hotel.SubModel>().ReverseMap();

        CreateMap<GetSaleStrategyBffInput, SearchGDSHotelPriceStrategyInput>()
            .ForMember(x => x.Adults, m => m.MapFrom(f => f.MaximumOccupancy))
            .ReverseMap();
        CreateMap<GDSGuaranteeInfo, GuaranteeBffInfo>().ReverseMap();
        CreateMap<GuaranteePaymentCardInfo, GuaranteePaymentCardBffInfo>().ReverseMap();
        CreateMap<GDSCommissionInfo, CommissionBffInfo>().ReverseMap();
        CreateMap<GDSHotelTaxInfo, TaxBffInfo>().ReverseMap();
        CreateMap<GDSHotelFeeInfo, FeeBffInfo>().ReverseMap();
        CreateMap<GDSHotelExclusivePrivilegesOutput, HotelExclusivePrivilegesBffInfo>().ReverseMap();
        CreateMap<GDSHotelExclusivePrivilegesDetailOutput, HotelExclusivePrivilegesDetailBffInfo>().ReverseMap();
        CreateMap<GDSHotelRoomInfo, GetSaleStrategyBffOutput>().ReverseMap();
        CreateMap<GDSHotelCalendarPriceInfo, SaleStrategyPriceInventoryBffItem>()
            .ForMember(x => x.ChannelPrice, m => m.MapFrom(f => f.Price))
            .ReverseMap();
        CreateMap<SearchGDSHotelPriceStrategyOutput, GetSaleStrategyBffOutput>().ReverseMap();
        CreateMap<GDSHotelPriceStrategyInfo, SaleStrategyBffItem>()
            .ForMember(x => x.TotalChannelPrice, m => m.MapFrom(f => f.AmountAfterTax))
            .ForMember(x => x.SumChannelPrice, m => m.MapFrom(f => f.AmountAfterTax))
            .ForMember(x => x.MinChannelPrice, m => m.MapFrom(f => f.AverageNightlyAfterTax))
            .ReverseMap();

        CreateMap<GetSaleStrategyBffInput, GDSHotelPriceCheckInput>().ReverseMap();
        CreateMap<SearchGDSHotelPriceStrategyOutput, GetSaleStrategyBffOutput>().ReverseMap();
        CreateMap<RoomInfo, GetSaleStrategyBffOutput>()
            .ForMember(x=> x.RoomId, m => m.MapFrom(f => f.Id))
            .ForMember(x => x.RoomZHName, m => m.MapFrom(f => f.ZHName))
            .ForMember(x => x.RoomENName, m => m.MapFrom(f => f.ENName))
            .ForMember(x => x.BedType, m => m.MapFrom(f => JsonConvert.DeserializeObject<List<BedType>>(f.BedType)))
            .ReverseMap();
        CreateMap<GDSHotelPriceCheckOutput, GDSHotelPriceCheckBffOutput>().ReverseMap();
    }

    string GetTimeSpanStr(TimeSpan? timeSpan, bool isTo = false)
    {
        if (timeSpan.HasValue)
        {
            if (isTo && timeSpan == new TimeSpan(0, 0, 0))
            {
                return "24:00";
            }
            return timeSpan.Value.ToString(@"hh\:mm");
        }

        return null;
    }
}