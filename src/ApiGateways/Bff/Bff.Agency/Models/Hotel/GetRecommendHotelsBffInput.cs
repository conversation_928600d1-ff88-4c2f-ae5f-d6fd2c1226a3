using Contracts.Common.Hotel.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Agency.Models.Hotel;

/// <summary>
/// 推荐酒店数据
/// </summary>
public class GetRecommendHotelsBffInput
{
    /// <summary>
    /// 出行日期
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime? TravelDate { get; set; } = DateTime.Today;

    /// <summary>
    /// 城市编码
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int CityCode { get; set; }

    /// <summary>
    /// 目标地点最大距离 单位km 默认50KM
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public int? TargetDistance { get; set; }

    /// <summary>
    /// 目标地点纬度
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public double TargetLatitude { get; set; }

    /// <summary>
    /// 目标地点经度
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public double TargetLongitude { get; set; }

    /// <summary>
    /// 最大推荐数量
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public int? RecommendCount { get; set; } = 10;

    /// <summary>
    /// 推荐页面类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public B2BHotelRecommendPageType RecommendPageType { get; set; }

    /// <summary>
    /// 酒店标签展示页面类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public TagShowPageType? TagShowPageType { get; set; }
    
    /// <summary>
    /// 星级
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal StarLevel { get; set; }
    
    /// <summary>
    /// 均价区间-最高
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public decimal? AverageMaxPrice { get; set; }

    /// <summary>
    /// 均价区间-最低
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public decimal? AverageMinPrice { get; set; }

    /// <summary>
    /// 酒店详情页过滤酒店id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long? FilterHotelId { get; set; }
}