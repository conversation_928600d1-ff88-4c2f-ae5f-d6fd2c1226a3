using Contracts.Common.Order.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Agency.Models.ScenicTicketOrder;

public class SearchScenicTicketOrderBffInput : PagingInput
{
    /// <summary>
    /// 下单时间-开始
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? DateBegin { get; set; }

    /// <summary>
    /// 下单时间-结束
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? DateEnd { get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ProductName { get; set; }

    /// <summary>
    /// 主订单Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? BaseOrderId { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 主订单订单状态
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public BaseOrderStatus? Status { get; set; }

    /// <summary>
    /// 子订单状态
    /// 待发货状态: 只需要传该值
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public ScenicTicketOrderStatus? TicketOrderStatus { get; set; }
    
    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ChannelOrderNo { get; set; }
}