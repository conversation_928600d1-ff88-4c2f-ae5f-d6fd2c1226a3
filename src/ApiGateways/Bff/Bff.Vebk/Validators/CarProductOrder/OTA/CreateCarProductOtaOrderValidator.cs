using Bff.Vebk.Models.CarProductOrder.OTA;
using Contracts.Common.Product.Enums;
using FluentValidation;

namespace Bff.Vebk.Validators.CarProductOrder.OTA;

/// <summary>
/// 开放平台-渠道端用车产品下单验证器
/// </summary>
public class CreateCarProductOtaOrderValidator : AbstractValidator<CreateCarProductOtaOrderValidatorDto>
{
    public CreateCarProductOtaOrderValidator()
    {
        RuleFor(x => x.ChannelOrderNo).NotEmpty()
            .WithMessage("渠道订单号不能为空");
        RuleFor(x=>x.CarProductSkuId).NotEmpty()
            .WithMessage("渠道商品未配置商家编码");
        RuleFor(x => x.TravelDate)
           .NotEmpty()
           .WithMessage("出行时间不正确")
           .GreaterThanOrEqualTo(DateTime.Today.AddDays(-1))
           .WithMessage("出行日期必须大于等于今天");
        RuleFor(x => x.Quantity).GreaterThan(0)
           .WithMessage("用车数量必须大于0");
        RuleFor(x => x.CarProductType).IsInEnum();
        //RuleFor(x => x.AirportTransferType)
        //    .Must(x => x == AirportTransferType.PickUp || x == AirportTransferType.DropOff)
        //    .When(x => x.CarProductType == CarProductType.AirportTransfer, ApplyConditionTo.CurrentValidator);
        //RuleFor(x => x.DepartureAddress).MaximumLength(200)
        //    .NotEmpty()
        //    .When(x => (x.CarProductType == CarProductType.AirportTransfer && x.AirportTransferType == AirportTransferType.DropOff)
        //               || x.CarProductType == CarProductType.PointToPointTransfer
        //               || x.CarProductType == CarProductType.CarChartered
        //        , ApplyConditionTo.CurrentValidator)
        //    .WithMessage("出发地地址不能为空");
        //RuleFor(x => x.DestinationAddress).MaximumLength(200)
        //    .NotEmpty()
        //    .When(x => (x.CarProductType == CarProductType.AirportTransfer && x.AirportTransferType == AirportTransferType.PickUp)
        //               || x.CarProductType == CarProductType.PointToPointTransfer
        //        , ApplyConditionTo.CurrentValidator)
        //    .WithMessage("目的地地址不能为空");
        //RuleFor(x => x.FlightNumber).MaximumLength(10);
        //RuleFor(x => x.AirportTerminal).MaximumLength(10);
    
        //RuleFor(x => x.Passengers).GreaterThan(0)
        //    .WithMessage("乘客数量必须大于0");
        //RuleFor(x => x.Baggages).GreaterThanOrEqualTo(0)
        //    .WithMessage("行李数量必须大于等于0");
        //RuleFor(x => x.Time).NotNull()
        //    .WithMessage("用车时间不能为空");
        //RuleFor(x => x.ContactsName).NotEmpty()
        //    .WithMessage("联系人姓名不能为空");
        //RuleFor(x => x.ContactsPhoneNumber).NotEmpty()
        //    .WithMessage("联系人手机号码不能为空");
    }
}