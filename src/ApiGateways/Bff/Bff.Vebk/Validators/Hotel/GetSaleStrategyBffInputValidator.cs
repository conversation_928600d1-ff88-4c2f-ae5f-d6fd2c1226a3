using Bff.Vebk.Models.Hotel;
using FluentValidation;

namespace Bff.Vebk.Validators.Hotel;

public class GetSaleStrategyBffInputValidator : AbstractValidator<GetSaleStrategyBffInput>
{
    public GetSaleStrategyBffInputValidator()
    {
        RuleFor(x => x.AgencyId).GreaterThan(0);
        RuleFor(x => x.HotelId).GreaterThan(0);
        RuleFor(x => x.LiveDate).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.LeaveDate).GreaterThan(x => x.LiveDate);
    }
}
