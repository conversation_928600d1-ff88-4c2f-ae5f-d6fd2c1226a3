using Contracts.Common.Hotel.DTOs.HotelCombination;
using FluentValidation;

namespace Bff.Vebk.Validators.HotelCombination;

public class ChannelMarkupSaveInputValidator : AbstractValidator<ChannelMarkupSaveInput>
{
    public ChannelMarkupSaveInputValidator()
    {
        RuleFor(x => x.HotelCombinationId).GreaterThan(0);
        RuleFor(x => x.SkuId).GreaterThan(0);
        RuleFor(x => x.SellingChannel).IsInEnum();
        RuleForEach(x => x.MarkupRules).SetValidator(new MarkupRuleOutputValidator());
    }
}

public class MarkupRuleOutputValidator : AbstractValidator<MarkupRuleOutput>
{
    public MarkupRuleOutputValidator()
    {
        RuleFor(x => x.BeginDate).NotEmpty();
        RuleFor(x => x.EndDate).NotEmpty();
        RuleFor(x => x.ValidWeeks).NotEmpty();
        RuleFor(x => x.PriceType).IsInEnum();
        RuleFor(x => x.MarkupType).IsInEnum();
        RuleFor(x => x.Value).GreaterThanOrEqualTo(0);
    }
}