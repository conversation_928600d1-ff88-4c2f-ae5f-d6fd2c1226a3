using Bff.Vebk.Models.HotelOrder;
using Bff.Vebk.Models.Insure;
using Contracts.Common.Order.DTOs.Insure;
using FluentValidation;

namespace Bff.Vebk.Validators.InsureProduct;

public class AddOrUpdateInsurePolicyHolderInputValidator : AbstractValidator<AddOrUpdateInsurePolicyHolderInput>
{
    public AddOrUpdateInsurePolicyHolderInputValidator()
    {
        RuleFor(x => x.CNName).NotEmpty();
        RuleFor(x => x.ENName).NotEmpty();
        RuleFor(x => x.Gender).NotEmpty();
        RuleFor(x => x.Phone).NotEmpty();
        RuleFor(x => x.Type).NotEmpty();
        RuleFor(x => x.IDCard).NotEmpty();
        RuleFor(x => x.Birthday).NotNull();
        RuleFor(x => x.Email)
            .EmailAddress()
            .When(x => !string.IsNullOrEmpty(x.Email));
    }
}
