using Contracts.Common.Tenant.DTOs.SignSubject;
using FluentValidation;

namespace Bff.Vebk.Validators.SignSubject;

public class SignSubjectAddInputValidator : AbstractValidator<SignSubjectDto>
{
    public SignSubjectAddInputValidator()
    {
        RuleFor(s => s.Name).NotEmpty();
        RuleFor(s => s.CurrencyCode).NotEmpty();
        RuleFor(s => s.CountryCode).GreaterThan(0);
        RuleFor(s => s.ProvinceCode).GreaterThan(0);
        RuleFor(s => s.CityCode).GreaterThan(0);
        RuleFor(s => s.CountryName).NotEmpty();
        RuleFor(s => s.ProvinceName).NotEmpty();
        RuleFor(s => s.CityName).NotEmpty();
    }
}
