using Bff.Vebk.Models.LineProduct;
using FluentValidation;

namespace Bff.Vebk.Validators.LineProduct;

public class UpdateOpenSupplierSyncSettingBffInputValidator : AbstractValidator<UpdateOpenSupplierSyncSettingBffInput>
{
    public UpdateOpenSupplierSyncSettingBffInputValidator()
    {
        RuleFor(x => x.LineProductId).NotEmpty();
        RuleFor(x => x.PriceInventorySyncType).IsInEnum();
        RuleFor(x => x.SyncInterval).GreaterThanOrEqualTo(1);
        RuleFor(x => x.SyncDateRange).GreaterThanOrEqualTo(1);
    }
}