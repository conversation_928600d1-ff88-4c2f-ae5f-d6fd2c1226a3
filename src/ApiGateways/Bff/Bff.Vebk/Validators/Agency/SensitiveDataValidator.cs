using FluentValidation.Validators;

namespace Bff.Vebk.Validators.Agency;

public class SensitiveDataValidator : PropertyValidator
{
    public SensitiveDataValidator()
        : base()
    {
    }

    protected override bool IsValid(PropertyValidatorContext context)
    {
        if (context.PropertyValue == null)
            return true;
        return !context.PropertyValue.ToString().Contains("*");
    }
}
