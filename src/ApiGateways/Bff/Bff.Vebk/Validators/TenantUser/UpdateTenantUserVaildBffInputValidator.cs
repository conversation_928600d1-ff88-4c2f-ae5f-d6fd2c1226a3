using Bff.Vebk.Models.TenantUser;
using FluentValidation;

namespace Bff.Vebk.Validators.TenantUser;

public class UpdateTenantUserVaildBffInputValidator : AbstractValidator<UpdateTenantUserVaildBffInput>
{
    public UpdateTenantUserVaildBffInputValidator()
    {       
        RuleFor(x => x.VerifyCode)
            .NotEmpty();

        RuleFor(x => x.ContactType)
            .NotEmpty();

        RuleFor(x => x.ContactTypeValue)
            .NotEmpty()
            .Matches(@"\d{11}")
            .WithMessage("输入正确的手机号")
            .When(x => x.ContactType == Contracts.Common.Tenant.Enums.TenantUserVaildContactType.Phone);

        RuleFor(x => x.ContactTypeValue)
            .NotEmpty()
            .EmailAddress()
            .WithMessage("输入正确的邮箱")
            .When(x => x.ContactType == Contracts.Common.Tenant.Enums.TenantUserVaildContactType.Email);
    }
}
