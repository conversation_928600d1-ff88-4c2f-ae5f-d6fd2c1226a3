using Bff.Vebk.Models.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSku;
using FluentValidation;

namespace Bff.Vebk.Validators.LineProductSku;

public class EditInputValidator : AbstractValidator<EditInput>
{
    public EditInputValidator()
    {
        RuleFor(x => x.LineProductId).GreaterThan(0).WithMessage("线路产品Id错误");
        RuleFor(x => x.Name).NotEmpty().WithMessage("套餐名称不能为空");
        RuleFor(x => x.FeeIncludes)
            .Must(x => x.Count <= 10).WithMessage("费用包含最多可添加10条");
        When(x => x.FeeIncludes.Count is > 0 and <= 10, () =>
        {
            RuleForEach(x => x.FeeIncludes).ChildRules(c =>
            {
                c.RuleFor(f => f.FeeType).NotEmpty().WithMessage("费用类型不能为空")
                    .Length(0, 20).WithMessage("费用类型长度最多为20个字");
                c.RuleFor(f => f.Description).Length(0, 100).WithMessage("费用描述最多为100个字");
            });
        });
        RuleFor(x => x.InsureProductSku).SetValidator(new InsureProductRelation.SaveInsureProductSkuBffInputValidator());
    }
}
