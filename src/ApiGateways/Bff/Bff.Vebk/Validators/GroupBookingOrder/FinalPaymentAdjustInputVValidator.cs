using Bff.Vebk.Models.HotelGroupBookingOrder;
using FluentValidation;

namespace Bff.Vebk.Validators.GroupBookingOrder;

public class FinalPaymentAdjustInputVValidator : AbstractValidator<FinalPaymentAdjustInput>
{
    public FinalPaymentAdjustInputVValidator()
    {
        RuleFor(x => x.GroupBookingOrderPaymentId).GreaterThan(0);

        RuleFor(x => x.BaseOrderId).GreaterThan(0);
        RuleFor(x => x.WorkOrderId).GreaterThan(0);
        RuleFor(x => x.AdjustAmount).NotEqual(0);
        RuleFor(x => x.Remark).MaximumLength(200);
    }
}