using Bff.Vebk.Models.ScenicTicket;
using Contracts.Common.Tenant.Enums;
using FluentValidation;

namespace Bff.Vebk.Validators.ScenicTicket;

public class GetOpenSupplierProductDetailBffInputValidator : AbstractValidator<GetOpenSupplierProductDetailBffInput>
{
    public GetOpenSupplierProductDetailBffInputValidator()
    {
        //ActivityId 和 PackageId 和 SkuId 3个不能同时为空
        RuleFor(x => x.ActivityId)
            .NotEmpty()
            .When(x => string.IsNullOrWhiteSpace(x.PackageId) && string.IsNullOrWhiteSpace(x.SkuId));

        RuleFor(x => x.PackageId)
            .NotEmpty()
            .When(x => string.IsNullOrWhiteSpace(x.ActivityId) && string.IsNullOrWhiteSpace(x.SkuId));

        RuleFor(x => x.SkuId)
            .NotEmpty()
            .When(x => string.IsNullOrWhiteSpace(x.ActivityId) && string.IsNullOrWhiteSpace(x.PackageId));
    }
}