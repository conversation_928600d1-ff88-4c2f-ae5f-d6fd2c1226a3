using Contracts.Common.Marketing.DTOs.PromotionTrace;
using Contracts.Common.Marketing.Enums;
using FluentValidation;

namespace Bff.Vebk.Validators.PromotionTrace;

public class CheckRefInputValidator : AbstractValidator<List<CheckRefInput>>
{
    public CheckRefInputValidator()
    {
        RuleForEach(x => x)
            .ChildRules(c =>
            {
                c.RuleFor(r => r.TargetId).NotEmpty()
                .When(r => r.TraceType is PromotionTraceType.Product
                || r.TraceType is PromotionTraceType.Lottery
                || r.TraceType is PromotionTraceType.FlashSale);
            });
    }
}
