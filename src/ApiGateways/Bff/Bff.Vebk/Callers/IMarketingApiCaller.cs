using Bff.Vebk.Models.BackstageGiftRecord;
using Bff.Vebk.Models.PromotionPosition;
using Bff.Vebk.Models.PromotionTrace;
using Common.Caller;
using Contracts.Common.Marketing.DTOs.BackstageGiftRecord;
using Contracts.Common.Marketing.DTOs.Coupon;
using Contracts.Common.Marketing.DTOs.CouponActivity;
using Contracts.Common.Marketing.DTOs.PromotionCustomerRemark;
using Contracts.Common.Marketing.DTOs.PromotionPosition;
using Contracts.Common.Marketing.DTOs.PromotionTrace;
using Contracts.Common.Marketing.DTOs.PromotionTraceRecord;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Callers;

public interface IMarketingApiCaller : IHttpCallerBase
{
    #region Coupon

    /// <summary>
    /// 添加优惠券
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddCoupon(AddCouponInput input);
    
    /// <summary>
    /// 优惠券详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetCouponOutput> GetCouponDetail(long id);

    /// <summary>
    /// 更新优惠券
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateCoupon(UpdateCouponInput input);
    
    /// <summary>
    /// 获取待使用的券码
    /// </summary>
    /// <param name="couponActivityId"></param>
    /// <returns></returns>
    Task<List<string>> GetUnUsedCouponRedemptionCodes(long couponActivityId);

    /// <summary>
    /// 优惠券搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchCouponOutput>> CouponSearch(SearchCouponInput input);

    /// <summary>
    /// 根据活动Id获取优惠券详情信息
    /// </summary>
    /// <param name="couponActivityId"></param>
    /// <returns></returns>
    Task<List<GetCouponInfoOutput>> GetCouponDetailByActivityId(long couponActivityId);

    /// <summary>
    /// 优惠券活动搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchCouponActivityOutput>> CouponActivitySearch(SearchCouponActivityInput input);

    /// <summary>
    /// 根据优惠券获取活动详情信息
    /// </summary>
    /// <param name="couponId"></param>
    /// <returns></returns>
    Task<List<GetActivityInfoOutput>> GetActivityInfoByCouponId(long couponId);

    #endregion

    #region CouponActivity

    /// <summary>
    /// 添加优惠券活动
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> AddCouponActivity(AddCouponActivityInput input);

    /// <summary>
    /// 获取优惠券活动信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetCouponActivityOutput> GetCouponActivity(long id);

    #endregion

    #region BackstageGiftRecord

    /// <summary>
    /// 后台赠送给分销商
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ErrorTypes.Tenant.TenantIsNotCNYCurrency"></exception>
    /// <exception cref="ErrorTypes.Marketing.B2BNotSupportedCouponType"></exception>
    Task BackstageGiftRecordAddToB2B(AddToB2BInput input);

    /// <summary>
    /// 后台赠送B2B查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchToB2BBffOutput>> BackstageGiftRecordSearchToB2B(SearchGiftRecordInput input);

    #endregion

    #region PromotionPosition
    /// <summary>
    /// 投放位置-分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchPromotionPositionBffOutput>> PromotionPositionSearch(SearchPromotionPositionInput input);

    /// <summary>
    /// 投放位置-新增
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<long> PromotionPositionAdd(AddPromotionPositionDto dto);

    /// <summary>
    /// 投放位置-启用/停用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task PromotionPositionSetEnabled(SetEnabledInput input);

    /// <summary>
    /// 投放位置-编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task PromotionPositionUpdate(UpdatePromotionPositionInput input);

    /// <summary>
    /// 投放位置-获取下拉标签
    /// </summary>
    /// <returns></returns>
    Task<List<string>> PromotionPositionGetSelectionTags();
    #endregion

    #region PromotionTrace
    /// <summary>
    /// 推广活码-分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchPromotionTraceBffOutput>> PromotionTraceSearch(SearchPromotionTraceInput input);

    /// <summary>
    /// 推广活码-新增
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    Task<long> PromotionTraceAdd(AddPromotionTraceDto dto);

    /// <summary>
    /// 推广活码-编辑
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task PromotionTraceUpdate(UpdatePromotionTraceInput input);

    /// <summary>
    /// 检查是或否存在对应活码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CheckRefOutput>> PromotionTraceCheckRef(List<CheckRefInput> input);

    /// <summary>
    /// 获取推广码列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<PromotionTraceListDto>> PromotionTraceList(ListPromotionTraceInput input);


    #endregion

    #region PromotionTraceRecord

    /// <summary>
    /// 营销统计数据查询
    /// </summary>
    Task<PagingModel<SearchTraceStatisticsOutput, List<SummarizedTraceStatisticsOutput>>> SearchTraceStatistics(SearchTraceStatisticsInput input);

    /// <summary>
    /// 导出营销统计数据
    /// </summary>
    Task<List<ExportTraceStatisticsOutput>> ExportTraceStatistics(ExportTraceStatisticsInput input);

    /// <summary>
    /// 用户索引列表
    /// </summary>
    Task<PagingModel<SearchCustomerCluesOutput>> SearchCustomerClues(SearchCustomerCluesInput input);


    /// <summary>
    /// 用户线索详情
    /// </summary>
    Task<GetCustomerCluesDetailOutput> GetCustomerCluesDetail(GetCustomerCluesDetailInput input);

    /// <summary>
    /// 用户足迹
    /// </summary>
    Task<PagingModel<GetCustomerTraceListOutput>> GetCustomerTraceList(GetCustomerTraceListInput input);

    /// <summary>
    /// 新增跟进记录
    /// </summary>
    Task AddPromotionCustomerRemark(AddPromotionCustomerRemarkInput input);

    /// <summary>
    /// 跟进记录查询
    /// </summary>
    Task<PagingModel<SearchPromotionCustomerRemarkOutput>> SearchPromotionCustomerRemark(SearchPromotionCustomerRemarkInput input);

    #endregion
}
