using Bff.Vebk.Models.BackstageGiftRecord;
using Bff.Vebk.Models.PromotionPosition;
using Bff.Vebk.Models.PromotionTrace;
using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Marketing.DTOs.BackstageGiftRecord;
using Contracts.Common.Marketing.DTOs.Coupon;
using Contracts.Common.Marketing.DTOs.CouponActivity;
using Contracts.Common.Marketing.DTOs.PromotionCustomerRemark;
using Contracts.Common.Marketing.DTOs.PromotionPosition;
using Contracts.Common.Marketing.DTOs.PromotionTrace;
using Contracts.Common.Marketing.DTOs.PromotionTraceRecord;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;

namespace Bff.Vebk.Callers.HttpImplements;

public class MarketingApiCaller : HttpCallerBase, IMarketingApiCaller
{
    public MarketingApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.Marketing, httpClientFactory)
    {
    }

    #region Coupon

    public Task<long> AddCoupon(AddCouponInput input)
    {
        var relativePath = "/Coupon/Add";
        return PostAsync<AddCouponInput, long>(relativePath, input);
    }

    public Task<GetCouponOutput> GetCouponDetail(long id)
    {
        var relativePath = $"/Coupon/Get?id={id}";
        return GetAsync<GetCouponOutput>(relativePath);
    }

    public Task UpdateCoupon(UpdateCouponInput input)
    {
        var relativePath = "/Coupon/Update";
        return PostAsync<UpdateCouponInput>(relativePath, input);
    }

    public Task<List<string>> GetUnUsedCouponRedemptionCodes(long couponActivityId)
    {
        var relativePath = "/CouponActivity/GetUnUsedCouponRedemptionCodes?couponActivityId=" + couponActivityId;
        return GetAsync<List<string>>(relativePath);
    }

    public Task<PagingModel<SearchCouponOutput>> CouponSearch(SearchCouponInput input)
    {
        var relativePath = "/Coupon/Search";
        return PostAsync<SearchCouponInput, PagingModel<SearchCouponOutput>>(relativePath, input);
    }

    public Task<List<GetCouponInfoOutput>> GetCouponDetailByActivityId(long couponActivityId)
    {
        var relativePath = $"/Coupon/GetCouponInfoByActivityId?couponActivityId={couponActivityId}";
        return GetAsync<List<GetCouponInfoOutput>>(relativePath);
    }

    public Task<PagingModel<SearchCouponActivityOutput>> CouponActivitySearch(SearchCouponActivityInput input)
    {
        var relativePath = "/CouponActivity/Search";
        return PostAsync<SearchCouponActivityInput, PagingModel<SearchCouponActivityOutput>>(relativePath, input);
    }

    public Task<List<GetActivityInfoOutput>> GetActivityInfoByCouponId(long couponId)
    {
        var relativePath = $"/CouponActivity/GetActivityInfo?couponId={couponId}";
        return GetAsync<List<GetActivityInfoOutput>>(relativePath);
    }

    #endregion

    #region CouponActivity

    public Task<long> AddCouponActivity(AddCouponActivityInput input)
    {
        var relativePath = "/CouponActivity/Add";
        return PostAsync<AddCouponActivityInput, long>(relativePath, input);
    }

    public Task<GetCouponActivityOutput> GetCouponActivity(long id)
    {
        var relativePath = $"/CouponActivity/Get?id={id}";
        return GetAsync<GetCouponActivityOutput>(relativePath);
    }

    #endregion

    #region BackstageGiftRecord
    public async Task BackstageGiftRecordAddToB2B(AddToB2BInput input)
    {
        var relativePath = "/BackstageGiftRecord/AddToB2B";
        await PostAsync(relativePath, input);
    }

    public async Task<PagingModel<SearchToB2BBffOutput>> BackstageGiftRecordSearchToB2B(SearchGiftRecordInput input)
    {
        var relativePath = "/BackstageGiftRecord/SearchToB2B";
        var result = await PostAsync<SearchGiftRecordInput, PagingModel<SearchToB2BBffOutput>>(relativePath, input);
        return result;
    }

    #endregion

    #region PromotionPosition
    public async Task<PagingModel<SearchPromotionPositionBffOutput>> PromotionPositionSearch(SearchPromotionPositionInput input)
    {
        var relativePath = "/PromotionPosition/Search";
        return await PostAsync<SearchPromotionPositionInput, PagingModel<SearchPromotionPositionBffOutput>>(relativePath, input);
    }

    public async Task<long> PromotionPositionAdd(AddPromotionPositionDto dto)
    {
        var relativePath = "/PromotionPosition/Add";
        var result = await PostAsync<AddPromotionPositionDto, long>(relativePath, dto);
        return result;
    }

    public async Task PromotionPositionSetEnabled(SetEnabledInput input)
    {
        var relativePath = "/PromotionPosition/SetEnabled";
        await PostAsync(relativePath, input);
    }

    public async Task PromotionPositionUpdate(UpdatePromotionPositionInput input)
    {
        var relativePath = "/PromotionPosition/Update";
        await PostAsync(relativePath, input);
    }

    public async Task<List<string>> PromotionPositionGetSelectionTags()
    {
        var relativePath = "/PromotionPosition/GetSelectionTags";
        return await GetAsync<List<string>>(relativePath);
    }
    #endregion

    #region PromotionTrace
    public async Task<PagingModel<SearchPromotionTraceBffOutput>> PromotionTraceSearch(SearchPromotionTraceInput input)
    {
        var relativePath = "/PromotionTrace/Search";
        return await PostAsync<SearchPromotionTraceInput, PagingModel<SearchPromotionTraceBffOutput>>(relativePath, input);
    }

    public async Task<long> PromotionTraceAdd(AddPromotionTraceDto dto)
    {
        var relativePath = "/PromotionTrace/Add";
        return await PostAsync<AddPromotionTraceDto, long>(relativePath, dto);
    }

    public async Task PromotionTraceUpdate(UpdatePromotionTraceInput input)
    {
        var relativePath = "/PromotionTrace/Update";
        await PostAsync<UpdatePromotionTraceInput, long>(relativePath, input);
    }

    public async Task<List<CheckRefOutput>> PromotionTraceCheckRef(List<CheckRefInput> input)
    {
        var relativePath = "/PromotionTrace/CheckRef";
        return await PostAsync<List<CheckRefInput>, List<CheckRefOutput>>(relativePath, input);
    }

    public async Task<List<PromotionTraceListDto>> PromotionTraceList(ListPromotionTraceInput input)
    {
        var relativePath = "/PromotionTrace/List";
        return await PostAsync<ListPromotionTraceInput, List<PromotionTraceListDto>>(relativePath, input);
    }

    #endregion

    #region PromotionTraceRecord

    public async Task<PagingModel<SearchTraceStatisticsOutput, List<SummarizedTraceStatisticsOutput>>>
        SearchTraceStatistics(
            SearchTraceStatisticsInput input)
    {
        var relativePath = "/PromotionTraceRecord/SearchTraceStatistics";
        return await
            PostAsync<SearchTraceStatisticsInput,
                PagingModel<SearchTraceStatisticsOutput, List<SummarizedTraceStatisticsOutput>>>(relativePath, input);
    }

    public async Task<List<ExportTraceStatisticsOutput>> ExportTraceStatistics(ExportTraceStatisticsInput input)
    {
        var relativePath = "/PromotionTraceRecord/ExportTraceStatistics";
        return await
            PostAsync<ExportTraceStatisticsInput, List<ExportTraceStatisticsOutput>>(relativePath, input);
    }

    public async Task<PagingModel<SearchCustomerCluesOutput>> SearchCustomerClues(SearchCustomerCluesInput input)
    {
        var relativePath = "/PromotionTraceRecord/SearchCustomerClues";
        return await PostAsync<SearchCustomerCluesInput, PagingModel<SearchCustomerCluesOutput>>(relativePath, input);
    }

    public async Task<GetCustomerCluesDetailOutput> GetCustomerCluesDetail(GetCustomerCluesDetailInput input)
    {
        var relativePath = "/PromotionTraceRecord/GetCustomerCluesDetail";
        return await PostAsync<GetCustomerCluesDetailInput, GetCustomerCluesDetailOutput>(relativePath, input);
    }

    public async Task<PagingModel<GetCustomerTraceListOutput>> GetCustomerTraceList(GetCustomerTraceListInput input)
    {
        var relativePath = "/PromotionTraceRecord/GetCustomerTraceList";
        return await PostAsync<GetCustomerTraceListInput, PagingModel<GetCustomerTraceListOutput>>(relativePath, input);
    }

    public async Task AddPromotionCustomerRemark(AddPromotionCustomerRemarkInput input)
    {
        var relativePath = "/PromotionCustomerRemark/Add";
        await PostAsync<AddPromotionCustomerRemarkInput>(relativePath, input);
    }

    public async Task<PagingModel<SearchPromotionCustomerRemarkOutput>> SearchPromotionCustomerRemark(
        SearchPromotionCustomerRemarkInput input)
    {
        var relativePath = "/PromotionCustomerRemark/Search";
        return await PostAsync<SearchPromotionCustomerRemarkInput, PagingModel<SearchPromotionCustomerRemarkOutput>>(
            relativePath, input);
    }

    #endregion
}
