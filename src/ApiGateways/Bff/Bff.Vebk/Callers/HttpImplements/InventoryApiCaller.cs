using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Product.DTOs.SkuCalendarPrice;
using Microsoft.Extensions.Options;

namespace Bff.Vebk.Callers.HttpImplements;

public class InventoryApiCaller : HttpCallerBase, IInventoryApiCaller
{
    public InventoryApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.Inventory, httpClientFactory)
    {

    }


    public async Task<IEnumerable<CapCalendarInventory>> GetCalendarInventories(GetCalendarInventoryInput input)
    {
        var relativePath = "/Calendar/GetInventories";
        return await PostAsync<GetCalendarInventoryInput, IEnumerable<CapCalendarInventory>>(
            relativePath,
            input);
    }

    public async Task<IEnumerable<GeneralInventoryOutput>> GeneralGetInventories(GetGeneralInventoryInput input)
    {
        var relativePath = "/General/GetInventories";
        return await PostAsync<GetGeneralInventoryInput, IEnumerable<GeneralInventoryOutput>>(relativePath, input);
    }

    public async Task<IEnumerable<GeneralInventoryOutput>> GeneralBatchGetInventories(List<GetGeneralInventoryInput> input)
    {
        var relativePath = "/General/BatchGetInventories";
        return await PostAsync<List<GetGeneralInventoryInput>, IEnumerable<GeneralInventoryOutput>>(relativePath, input);
    }

    public async Task<CheckInventoryEarlyWarningOutput> CheckCalendarInventoryEarlyWarning(CheckInventoryEarlyWarningInput input)
    {
        var relativePath = "/Calendar/CheckEarlyWarning";
        return await PostAsync<CheckInventoryEarlyWarningInput, CheckInventoryEarlyWarningOutput>(relativePath, input);
    }

    #region TimeSlotCalendarInventory

    public async Task<List<GetTimeSlotInventoryOutput>> GetTimeSlotCalendarInventories(GetTimeSlotInventoryInput input)
    {
        var relativePath = "/TimeSlotCalendar/GetInventories";
        return await PostAsync<GetTimeSlotInventoryInput,List<GetTimeSlotInventoryOutput>>(relativePath,input);
    }

    public async Task<CheckInventoryEarlyWarningOutput> CheckTimeSlotInventoryEarlyWarning(CheckInventoryEarlyWarningInput input)
    {
        var relativePath = "/TimeSlotCalendar/CheckEarlyWarning";
        return await PostAsync<CheckInventoryEarlyWarningInput, CheckInventoryEarlyWarningOutput>(relativePath, input);
    }

    #endregion

}