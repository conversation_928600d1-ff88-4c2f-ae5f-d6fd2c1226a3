using Bff.Vebk.Callers;
using Bff.Vebk.Models.Hotel;
using Bff.Vebk.Models.HotelMapping;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.HotelMapping;
using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NetTopologySuite.IO;

namespace Bff.Vebk.Controllers;
/// <summary>
/// 酒店售卖绑定
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelMappingController : ControllerBase
{
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;

    public HotelMappingController(IHotelApiCaller hotelApiCaller, ITenantApiCaller tenantApiCaller)
    {
        _hotelApiCaller = hotelApiCaller;
        _tenantApiCaller = tenantApiCaller;
    }

    /// <summary>
    /// 检查酒店是否可匹配
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpPost]
    [ProducesResponseType(typeof(CheckHotelMappingableOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CheckMappingable(CheckHotelMappingableInput input)
    {
        AgencyApiType agencyApiType = input.SellingPlatform switch
        {
            SellingPlatform.Ctrip => AgencyApiType.CtripHotel,
            _ => throw new NotImplementedException()
        };
        var apiSetting = await _tenantApiCaller.GetAgencyApiSetting(agencyApiType);
        var result = new CheckHotelMappingableOutput
        {
            AgencyApiSetting = apiSetting is not null,
            Mappingables = input.HotelIds.Select(x => new CheckHotelMappingableDto { HotelId = x, Mappingable = false })
        };
        if (apiSetting != null)
        {
            var hotelMappings = await _hotelApiCaller.GetHotelMappings(new HotelMappingInput
            {
                SellingPlatform = input.SellingPlatform,
                HotelIds = input.HotelIds
            });
            result.Mappingables = input.HotelIds.Select(x =>
            {
                var hotelMapping = hotelMappings.Where(m => m.HotelId == x).FirstOrDefault();
                return new CheckHotelMappingableDto
                {
                    HotelId = x,
                    Mappingable = hotelMapping is null || hotelMapping.MappingStatus == Contracts.Common.Hotel.Enums.MappingStatus.Failure
                };
            });
        }
        return Ok(result);
    }

    /// <summary>
    /// 酒店匹配信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<MappingInfoOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchHotelMappingsInput input)
    {
        var result = await _hotelApiCaller.HotelMappingSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 酒店匹配详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(DetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Detail(HotelMappingDetailInput input)
    {
        var result = await _hotelApiCaller.HotelMappingDetail(input);

        if (result is null) return Ok();

        var detailOutput = new DetailOutput
        {
            HotelMapping = result.HotelMapping,
            RoomMappings = result.RoomMappings,
            PriceStrategyMappings = result.PriceStrategyMappings.Select(x => new PriceStrategyMappingDetailOutput
            {
                HotelId = x.HotelId,
                HotelRoomId = x.HotelRoomId,
                RoomName = result.RoomMappings.FirstOrDefault(r => r.HotelRoomId == x.HotelRoomId)?.ZHName,
                Name = x.Name,
                PriceStrategyId = x.PriceStrategyId,
                PriceStrategyMapping = x.PriceStrategyMapping
            })
        };
        return Ok(detailOutput);
    }

    /// <summary>
    /// 酒店匹配推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.HotelMappingHasSuccessed, ErrorTypes.Hotel.HotelMappingIsProcessing)]
    public async Task<IActionResult> Add(AddHotelMappingInput input)
    {
        await _hotelApiCaller.AddHotelMapping(input);
        return Ok();
    }

    /// <summary>
    /// 酒店房型匹配推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> SetRoomMapping(SetHotelRoomMappingInput input)
    {
        await _hotelApiCaller.SetHotelRoomMapping(input);
        return Ok();
    }

    /// <summary>
    /// 酒店价格策略匹配推送
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> SetPriceStrategyMapping(SetPriceStrategyMappingInput input)
    {
        await _hotelApiCaller.SetPriceStrategyMapping(input);
        return Ok();
    }

    /// <summary>
    /// 更新酒店匹配情况
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.ExternalServiceRequestException)]
    public async Task<IActionResult> HotelMappingPushQueryHandle(PushQueryHandleInput input)
    {
        await _hotelApiCaller.HotelMappingPushQueryHandle(new HotelMappingPushQueryInput
        {
            SellingPlatform = input.SellingPlatform,
            HotelIds = new[] { input.HotelId }
        });
        return Ok();
    }
}
