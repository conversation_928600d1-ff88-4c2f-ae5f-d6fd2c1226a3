using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.SupplierApi;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Tenant.DTOs.SupplierApi;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class SupplierApiController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IMapper _mapper;
    public SupplierApiController(ITenantApiCaller tenantApiCaller,
       IMapper mapper)
    {
        _tenantApiCaller = tenantApiCaller;
        _mapper = mapper;
    }


    /// <summary>
    /// 查询Api供应商列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<SupplierApiListBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> List(SupplierApiListBffInput input)
    {
        var result = new List<SupplierApiListBffOutput>();
        if (input.IsTenantConfig)
        {
            var user = HttpContext.User.ParseUserInfo<CurrentUser>();
            var tenantApiSupplier = await _tenantApiCaller.TenantApiSupplierList(new QueryTenantApiSupplierConfigListInput
            {
                TenantId = user.Tenant
            });
            result = _mapper.Map<List<SupplierApiListBffOutput>>(tenantApiSupplier.Where(x=>x.Enabled));
        }
        else
        {
            var supplierApiList = await _tenantApiCaller.SupplierApiList();
            result = _mapper.Map<List<SupplierApiListBffOutput>>(supplierApiList);
        }
        return Ok(result);
    }
}
