using AutoMapper;
using Bff.Vebk.Callers;
using Common.Swagger;
using Contracts.Common.Tenant.DTOs.SupplierServiceInfo;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 供应商
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
[Authorize]
public class SupplierServiceInfoController: ControllerBase
{
    private readonly IMapper _mapper;
    private readonly ITenantApiCaller _tenantApiCaller;
    public SupplierServiceInfoController(
        IMapper mapper,
        ITenantApiCaller tenantApiCaller)
    {
        _mapper = mapper;
        _tenantApiCaller = tenantApiCaller;
    }

    /// <summary>
    /// 获取供应商服务信息下拉列表
    /// </summary>
    /// <param name="supplierId"></param>
    /// <returns></returns>
    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(List<SupplierServiceInfoDetailDto>))]
    public async Task<IActionResult> GetDetail(long supplierId)
    {
        var result = await _tenantApiCaller.GetSupplierServiceInfoDetail(supplierId);
        return Ok(result);
    }

    /// <summary>
    /// 获取供应商服务信息
    /// </summary>
    /// <param name="supplierInfoId"></param>
    /// <returns></returns>
    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(SupplierServiceInfoDto))]
    public async Task<IActionResult> Get(long supplierInfoId)
    {
        var result = await _tenantApiCaller.GetSupplierServiceInfo(supplierInfoId);
        return Ok(result);
    }

    /// <summary>
    /// 根据供应商获取服务信息列表
    /// </summary>
    /// <param name="supplierId"></param>
    /// <returns></returns>
    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK,typeof(List<SupplierServiceInfoDto>))]
    public async Task<IActionResult> GetList(long supplierId)
    {
        var result = await _tenantApiCaller.GetSupplierServiceInfoList(supplierId);
        return Ok(result);
    }

    /// <summary>
    /// 添加供应商服务信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(AddSupplierServiceInfoDto input)
    {
        await _tenantApiCaller.AddSupplierServiceInfo(input);
        return Ok();
    }

    /// <summary>
    /// 编辑供应商服务信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Edit(SupplierServiceInfoDto input)
    {
        await _tenantApiCaller.UpdateSupplierServiceInfo(input);
        return Ok();
    }

    /// <summary>
    /// 删除供应商服务信息
    /// </summary>
    /// <param name="supplierInfoId"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Delete(DeleteSupplierServiceInfoInput input)
    {
        await _tenantApiCaller.DeleteSupplierServiceInfo(input);
        return Ok();
    }
}
