using Bff.Vebk.Callers;
using Bff.Vebk.Models.B2BPage;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Tenant.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace Bff.Vebk.Controllers;

/// <summary>
/// B2B配置
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class B2BPageController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IScenicSpotApiCaller _scenicSpotApiCaller;
    
    public B2BPageController(
        ITenantApiCaller tenantApiCaller,
        IHotelApiCaller hotelApiCaller,
        IProductApiCaller productApiCaller,
        IScenicSpotApiCaller scenicSpotApiCaller)
    {
        _tenantApiCaller = tenantApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _productApiCaller = productApiCaller;
        _scenicSpotApiCaller = scenicSpotApiCaller;
    }

    /// <summary>
    /// 获取B2B配置数据
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200,typeof(GetB2BPageDetailOutput))]
    public async Task<IActionResult> GetDetail()
    {
        var result = new GetB2BPageDetailOutput();
        var b2BPageDetail = await _tenantApiCaller.GetB2BPageDetail();
        if (b2BPageDetail.Components.Any() is false) return Ok(result);

        ConcurrentBag<B2BPageComponentBffDto> b2BPageComponent = new();
        await Parallel.ForEachAsync(
            b2BPageDetail.Components,
            new ParallelOptions {MaxDegreeOfParallelism = 5},
            async (component, _) =>
            {
                var b2BPageComponentItem = new B2BPageComponentBffDto
                {
                    Id = component.Id, 
                    ComponentType = component.ComponentType, 
                    Content = component.Content
                };
                if (component.ComponentType == B2BComponentType.ProductList)
                {
                    //处理无效产品
                    b2BPageComponentItem.Content = await CheckProductValidity(component.Content);
                }

                b2BPageComponent.Add(b2BPageComponentItem);
            });
        result.Components = b2BPageComponent.OrderBy(x => x.Id).ToList();
        return Ok(result);
    }


    private async Task<string> CheckProductValidity(string content)
    {
        var productList = JsonConvert.DeserializeObject<Contracts.Common.Tenant.DTOs.B2BPage.ProductList>(content);
        var productIds = productList.Contents.Select(x => x.Value).ToList()
            .ConvertAll(long.Parse);
        var validProductIds = new List<long>();
        switch (productList.ChooseType)
        {
            case B2BLinkChooseType.Hotel:
                var hotelCheckResponse = await _hotelApiCaller.CheckHotelIsEnabled(
                    new CheckIsEnabledInput
                    {
                        HotelIds = productIds
                    });
                validProductIds = hotelCheckResponse
                    .Where(x => x.Exists)
                    .Select(x => x.Id)
                    .ToList();
                break;
            case B2BLinkChooseType.HOPHotel:
                var apiHotelCheckResponse = await _hotelApiCaller.GetApiHotelDetail(productIds.ToArray());
                validProductIds = apiHotelCheckResponse.Select(x => x.Id).ToList();
                break;
            case B2BLinkChooseType.HotelPackages:
            case B2BLinkChooseType.RoomVoucher:
            case B2BLinkChooseType.Catering:
                var ticketProductCheckResponse = await _productApiCaller.CheckTicketProductValidity(productIds.ToArray());
                validProductIds = ticketProductCheckResponse.Select(x => x.Id).ToList();
                break;
            case B2BLinkChooseType.Line:
                var lineProductCheckResponse = await _productApiCaller.GetLineProductDetail(productIds.ToArray());
                validProductIds = lineProductCheckResponse.Select(x => x.ProductId).ToList();
                break;
            case B2BLinkChooseType.ScenicTicket:
                var scenicSpotCheckResponse = await _scenicSpotApiCaller.CheckScenicSpotValidity(productIds.ToArray());
                validProductIds = scenicSpotCheckResponse.Select(x => x.Id).ToList();
                break;
        }
        
        productList.Contents = productList.Contents
            .Where(x => validProductIds.Contains(long.Parse(x.Value)))
            .ToList();
        var validContent = JsonConvert.SerializeObject(productList);
        return validContent;
    }
    
}