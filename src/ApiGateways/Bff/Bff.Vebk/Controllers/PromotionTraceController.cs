using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.PromotionTrace;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Marketing.DTOs.PromotionTrace;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.User.DTOs.SupplierUser;
using Contracts.Common.User.DTOs.TenantUser;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 推广活码管理
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class PromotionTraceController : ControllerBase
{
    private readonly IMarketingApiCaller _marketingApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IMapper _mapper;

    public PromotionTraceController(IMarketingApiCaller marketingApiCaller,
        IMapper mapper,
        IUserApiCaller userApiCaller)
    {
        _marketingApiCaller = marketingApiCaller;
        _mapper = mapper;
        _userApiCaller = userApiCaller;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="bffInput"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(PagingModel<SearchPromotionTraceBffOutput>))]
    public async Task<IActionResult> Search(SearchPromotionTraceBffInput bffInput)
    {
        var input = _mapper.Map<SearchPromotionTraceInput>(bffInput);
        var result = await _marketingApiCaller.PromotionTraceSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <param name="bffInput"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(long))]
    public async Task<IActionResult> Add(PromotionTraceBffDto dto)
    {

        #region 判断是否已存在活码
        var checkRefOutputs = await _marketingApiCaller.PromotionTraceCheckRef(new List<CheckRefInput>
        {
            new CheckRefInput
            {
                TargetId = dto.TargetId,
                TraceType = dto.TraceType,
                PromotionPositionId = dto.PromotionPositionId
            }
        });
        if (checkRefOutputs.Any(x => x.IsExist))
        {
            return Ok(checkRefOutputs.First().Id);
        }
        #endregion

        var addDto = _mapper.Map<AddPromotionTraceDto>(dto);
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var tenantUserIds = new List<long>()
        {
            currentUser.UserId
        };
        if (dto.SalespersonId.HasValue)
        {
            tenantUserIds.Add(dto.SalespersonId!.Value);
        }
        var tenantUsers = await _userApiCaller.SearchTenantUsers(new SearchTenantUsersInput
        {
            Ids = tenantUserIds.ToArray()
        });

        addDto.PromoterType = PromoterType.TenantStaff;
        addDto.PromoterId = currentUser.UserId;
        addDto.PromoterName = currentUser.NickName;
        addDto.PromoterPhoneNumber = tenantUsers.FirstOrDefault(x => x.Id == currentUser.UserId).PhoneNumber;
        //租户手机号必填
        if (string.IsNullOrEmpty(addDto.PromoterPhoneNumber))
        {
            throw new BusinessException(ErrorTypes.Marketing.PromoterPhoneNumberMissing);
        }
        if (dto.SalespersonId.HasValue)
        {
            addDto.SalespersonName = tenantUsers.FirstOrDefault(x => x.Id == dto.SalespersonId!.Value)?.Name;
        }

        await _marketingApiCaller.PromotionTraceAdd(addDto);
        return Ok();
    }

    /// <summary>
    /// 编辑
    /// </summary>
    /// <param name="bffInput"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(UpdatePromotionTraceBffInput bffInput)
    {
        var input = _mapper.Map<UpdatePromotionTraceInput>(bffInput);
        if (input.SalespersonId.HasValue)
        {
            var tenantUserInfo = await _userApiCaller.TenantUserFindOne(new FindOneInput
            {
                UserId = input.SalespersonId
            });
            input.SalespersonName = tenantUserInfo?.Name;
        }
        await _marketingApiCaller.PromotionTraceUpdate(input);
        return Ok();
    }

    /// <summary>
    /// 检查是或否存在对应活码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(List<CheckRefOutput>))]
    public async Task<IActionResult> CheckRef(List<CheckRefBffInput> input)
    {
        var checkRefInputs = _mapper.Map<List<CheckRefInput>>(input);
        //vebk默认只查询 推广人=租户后台类型 的记录
        checkRefInputs.ForEach(x => x.PromoterType = PromoterType.TenantStaff);
        var result = await _marketingApiCaller.PromotionTraceCheckRef(checkRefInputs);
        return Ok(result);
    }


    /// <summary>
    /// 获取下拉列表
    /// </summary>
    /// <param name="bffInput"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(List<PromotionTraceListDto>))]
    public async Task<IActionResult> List(ListPromotionTraceInput input)
    {
        var result = await _marketingApiCaller.PromotionTraceList(input) ?? new List<PromotionTraceListDto>();
        result.Add(new PromotionTraceListDto
        {
            Id = 0,
            Title = "常规"
        });
        return Ok(result);
    }

}
