using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.Hotel;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System.Collections.Concurrent;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 酒店信息
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelController : ControllerBase
{
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IHotelService _hotelService;
    private readonly IAgencyChannelPriceService _agencyChannelPriceService;
    private readonly IPaymentService _paymentService;
    private readonly IMapper _mapper;

    public HotelController(
        IHotelApiCaller hotelApiCaller,
        IResourceApiCaller resourceApiCaller,
        ITenantApiCaller tenantApiCaller,
        IHotelService hotelService,
        IAgencyChannelPriceService agencyChannelPriceService,
        IPaymentService paymentService,
        IMapper mapper)
    {
        _hotelApiCaller = hotelApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _hotelService = hotelService;
        _agencyChannelPriceService = agencyChannelPriceService;
        _paymentService = paymentService;
        _mapper = mapper;
    }

    /// <summary>
    /// 酒店搜索列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<Models.Hotel.SearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchHotelsInput input)
    {

        var data = await _hotelApiCaller.HotelSearch(input);
        var result = _mapper.Map<PagingModel<Models.Hotel.SearchOutput>>(data);
        if (result.Data.Any())
        {
            var cityCodes = result.Data.Select(x => x.CityCode).Distinct().ToArray();
            var cities = await _resourceApiCaller.QueryCities(new QueryInput { CityCodes = cityCodes });
            foreach (var d in result.Data)
            {
                var city = cities.FirstOrDefault(x => x.CityCode == d.CityCode);
                if (city is null) continue;
                d.EnCityName = city.ENName;
                d.CityName = city.ZHName;
                d.CountryCode = city.CountryCode;
                d.EnCountryName = city.CountryEnName;
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 获取酒店营业信息
    /// </summary>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetOperateInfoBffOutput))]
    public async Task<IActionResult> GetOperateInfo([SwaggerParameter(Required = true)] long hotelId)
    {
        var result = new GetOperateInfoBffOutput();
        var esHotel = (await _hotelApiCaller.GetEsHotelDetailV2(hotelId)).FirstOrDefault();
        if (esHotel is null) return Ok(result);
        //优先展示本地酒店
        var localHotelInfo = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        if (localHotelInfo != null)
        {//本地酒店
            var localHotel = await _hotelApiCaller.GetHotelDetail(localHotelInfo.HotelId);
            result.ZHName = localHotel.ZHName;
            result.ENName = localHotel.ENName;
            result.CityName = localHotel.CityName;
            result.CountryName = localHotel.CountryName;
            result.ServiceTimeBegin = localHotel.ServiceTimeBegin;
            result.ServiceTimeEnd = localHotel.ServiceTimeEnd;
            result.ServiceEndtimeInNextDay = localHotel.ServiceEndtimeInNextDay;
            result.OperatingModel = localHotel.OperatingModel;
        }
        else
        {//第三方酒店
            var thirdPartyHotel = await _resourceApiCaller.GetHotelDetail(esHotel.ResourceHotelId);
            result.ZHName = thirdPartyHotel.Name;
            result.ENName = thirdPartyHotel.EnName;
            result.CityName = thirdPartyHotel.CityName;
            result.CountryName = thirdPartyHotel.CountryName;
            result.ServiceTimeBegin = thirdPartyHotel.ServiceTimeBegin;
            result.ServiceTimeEnd = thirdPartyHotel.ServiceTimeEnd;
            result.ServiceEndtimeInNextDay = thirdPartyHotel.ServiceEndtimeInNextDay;
            result.OperatingModel = OperatingModel.Agency;
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取酒店房型价格策略
    /// </summary>
    /// <param name="hotelId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetHotelRoomPriceStrategyBffOutput))]
    public async Task<IActionResult> GetHotelRoomPriceStrategy([SwaggerParameter(Required = true)] long hotelId)
    {
        var result = new GetHotelRoomPriceStrategyBffOutput
        {
            HotelId = hotelId
        };

        var tenantId = HttpContext.GetTenantId();
        var esHotel = (await _hotelApiCaller.GetEsHotelDetailV2(hotelId)).FirstOrDefault();
        if (esHotel is null) return Ok(result);
        var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        if (localHotel != null)
        {
            var localHotelPriceStrategies = await _hotelApiCaller.GetSimpleInfoByHotelIds(
                new GetSimpleInfoByHotelIdsInput
                {
                    HotelIds = new List<long> { localHotel.HotelId },
                    NeedPrice = false
                });
            var localHotelPriceStrategy = localHotelPriceStrategies.FirstOrDefault();
            if (localHotelPriceStrategy is null)
            {
                return Ok(result);
            }

            foreach (var room in localHotelPriceStrategy.Rooms)
            {
                var roomInfo = new RoomBffInfo()
                {
                    RoomId = room.RoomId,
                    RoomName = room.RoomName
                };
                foreach (var priceStrategy in room.PriceStrategies)
                {
                    roomInfo.PriceStrategies.Add(new PriceStrategyBffInfo()
                    {
                        Id = priceStrategy.Id.ToString(),
                        Name = priceStrategy.Name,
                        SupplierId = priceStrategy.SupplierId
                    });
                }
                result.Rooms.Add(roomInfo);
            }

        }
        else
        {
            var thirdPartyHotelPriceStrategy = await _resourceApiCaller.GetThirdHotelPrice(
                new GetThirdHotelPriceInput
                {
                    TenantId = tenantId,
                    CheckIn = DateTime.Today,
                    CheckOut = DateTime.Today.AddDays(1),
                    SupplierApiTypes = new SupplierApiType[] { SupplierApiType.Hop },
                    ResourceHotelId = esHotel.ResourceHotelId
                });
            foreach (var room in thirdPartyHotelPriceStrategy.Rooms)
            {
                var roomInfo = new RoomBffInfo()
                {
                    RoomId = room.ResourceRoomId,
                    RoomName = room.ZHName
                };
                foreach (var priceStrategy in room.Pricestrategies)
                {
                    roomInfo.PriceStrategies.Add(new PriceStrategyBffInfo()
                    {
                        Id = priceStrategy.PricestrategyId,
                        Name = priceStrategy.Name,
                        SupplierId = priceStrategy.SupplierId
                    });
                }
                result.Rooms.Add(roomInfo);
            }
        }

        return Ok(result);
    }

    #region B2B

    /// <summary>
    /// 酒店详情
    /// </summary>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetHotelDetailsBffOutput))]
    public async Task<IActionResult> Detail(long hotelId)
    {
        var result = new GetHotelDetailsBffOutput();
        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(hotelId);
        if (esHotelInfo.Any() is false)
        {
            return Ok(result);
        }
        var esHotel = esHotelInfo.First();
        //优先展示本地酒店
        var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        if (localHotel != null)
        {//查询本地酒店
            var localHotelDetail = await _hotelApiCaller.GetHotelDetail(localHotel.HotelId);
            result = _mapper.Map<GetHotelDetailsBffOutput>(localHotelDetail);
            result.Id = hotelId;
        }
        else
        {//查询资源库酒店信息
            var resourceHotelDetail = await _resourceApiCaller.GetHotelDetail(esHotel.ResourceHotelId);
            result = _mapper.Map<GetHotelDetailsBffOutput>(resourceHotelDetail);
            result.Id = hotelId;
            result.ResourceHotelId = resourceHotelDetail.Id;
        }

        var cityCode = result.CityCode;
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = new int[] { cityCode },
        });
        var city = cities.FirstOrDefault();
        result.EnCityName = city?.ENName;
        result.EnProvinceName = city?.ProvinceEnName;
        result.EnCountryName = city?.CountryEnName;

        return Ok(result);
    }

    /// <summary>
    /// 查询可售价格策略
    /// </summary>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200, typeof(List<GetSaleStrategyBffOutput>))]
    [SwaggerResponseExt(default, ErrorTypes.Inventory.GetInventoryFail)]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.PricesNotEnough,
        ErrorTypes.Hotel.EndRoomNotBook,
        ErrorTypes.Hotel.HourRoomNotBook,
        ErrorTypes.Hotel.StayLongDiscountNotBook,
        ErrorTypes.Hotel.CalendarNotEnable)]
    public async Task<IActionResult> GetSaleStrategies(GetSaleStrategyBffInput input)
    {
        var result = new List<GetSaleStrategyBffOutput>();
        if (input.LiveDate == input.LeaveDate)
            return Ok(result);
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        //校验分销商信息
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(input.AgencyId);
        if (agencyInfo.PriceGroupId is null or <= 0) return Ok(result);
        var priceGroupId = agencyInfo.PriceGroupId.Value;

        //校验分组启用状态
        var priceGroup = await _tenantApiCaller.GetPriceGroupDetail(priceGroupId);
        if (!priceGroup.Enable)
            return Ok(result);

        var checkPriceSetting = await _hotelService.CheckAgencyPriceSetting(priceGroupId);
        if (checkPriceSetting.localHotelIds.Any() is false &&
            checkPriceSetting.queryAgencyThirdHotel is false)
            return Ok(result);

        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(input.HotelId);
        if (esHotelInfo.Any() is false)
        {
            return Ok(result);
        }

        var esHotel = esHotelInfo.First();
        var localHotelInfo = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
        var thirdHotelInfo = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop);

        //判断第三方酒店是否配置了指定酒店配置
        var thirdHotelAgencyChannelPriceSettings = new List<AgencyChannelPriceSettingOutput>();
        if (checkPriceSetting.queryAgencyThirdHotel is false)
        {
            thirdHotelInfo = null;
        }
        else
        {
            if (thirdHotelInfo != null)
            {
                var checkHuiZhiSellChannelSettings = _hotelService.CheckHuiZhiSellChannelSettings(
                    checkPriceSetting.huiZhiPriceSettings,
                    thirdHotelInfo.HotelId,
                    true,
                    true,
                    Enum.GetValues<SellHotelTag>().ToList());

                thirdHotelAgencyChannelPriceSettings = checkHuiZhiSellChannelSettings.agencyChannelPriceSetting;
                if (checkHuiZhiSellChannelSettings.thirdHotelChannelPriceSettings.Any() is false)
                {
                    //无本地酒店和无汇智酒店
                    if (localHotelInfo == null && checkHuiZhiSellChannelSettings.defaultChannelHotelPriceSettings.Any() is false)
                    {
                        return Ok(result);
                    }

                    //无汇智酒店
                    if (checkHuiZhiSellChannelSettings.defaultChannelHotelPriceSettings.Any() is false)
                        thirdHotelInfo = null;
                }
            }
        }

        if (localHotelInfo != null)
        {
            //查询本地酒店
            input.HotelId = localHotelInfo.HotelId;
            result = await GetLocalHotelPriceStrategy(input,
                currentUser,
                agencyInfo);
        }

        if (thirdHotelInfo != null)
        {
            //查询第三方酒店
            input.HotelId = thirdHotelInfo.HotelId;
            var thirdSaleData = await GetThirdPartHotelPriceStrategy(input,
                thirdHotelInfo.SupplierApiType,
                currentUser,
                agencyInfo,
                thirdHotelAgencyChannelPriceSettings,
                esHotel);

            if (result.Any())
            {
                //本地有引用该酒店时,房型排序根据商户侧的排序规则
                //相同房型的合并一起，同一房型下，排序上优先显示本地酒店的价格策略
                var localHotelRooms = await _hotelApiCaller.GetHotelRooms(new GetHotelRoomsInput { HotelId = localHotelInfo.HotelId });

                //合并结果
                var combineResult = new List<GetSaleStrategyBffOutput>();
                foreach (var localHotelRoom in localHotelRooms.HotelRooms.OrderBy(x => x.Sort))
                {
                    var combineItem = new GetSaleStrategyBffOutput();
                    var saleByLocal = result
                        .FirstOrDefault(x => x.ResourceRoomId == localHotelRoom.ResourceRoomId);
                    var saleByThird = thirdSaleData
                        .FirstOrDefault(x => x.ResourceRoomId == localHotelRoom.ResourceRoomId);

                    if (saleByLocal == null && saleByThird == null)
                        continue;

                    if (saleByLocal != null)
                    {
                        combineItem = saleByLocal;
                    }

                    if (saleByThird != null)
                    {
                        if (combineItem.PriceStrategies.Any())
                        {
                            combineItem.PriceStrategies.AddRange(saleByThird.PriceStrategies);
                        }
                        else
                        {
                            combineItem = saleByThird;
                            combineItem.FirstPhoto = localHotelRoom.Photos.FirstOrDefault();
                        }
                    }

                    combineResult.Add(combineItem);
                }

                result = combineResult;
            }
            else
            {
                result.AddRange(thirdSaleData);
            }
        }

        //过滤
        var filterResult = new List<GetSaleStrategyBffOutput>();
        if (input.RoomId is > 0 || !string.IsNullOrEmpty(input.BedType))
        {
            if (input.RoomId is > 0)
            {
                result = result
                    .Where(x => x.ResourceRoomId == input.RoomId.Value)
                    .ToList();
            }

            if (!string.IsNullOrEmpty(input.BedType))
            {
                result = result
                    .Where(x => x.BedType.Any(b => b.main == input.BedType))
                    .ToList();
            }

            if (result.Any() is false)
                return Ok(result);
        }

        foreach (var item in result)
        {
            var saleStrategies = new List<SaleStrategyBffItem>();
            saleStrategies.AddRange(item.PriceStrategies);

            if (input.PriceStrategyType is > 0)
            {
                saleStrategies = saleStrategies
                    .Where(x => x.PriceStrategyType == input.PriceStrategyType)
                    .ToList();
            }

            if (input.MaximumOccupancy is >= 1)
            {
                //筛选满足的价格策略：价格策略可住人数×间数>入住人数
                saleStrategies = saleStrategies
                    .Where(x => x.MaximumOccupancy * input.RoomNum >= input.MaximumOccupancy)
                    .ToList();
            }

            if (input.CancelRulesType.HasValue)
            {
                //免费取消，可取消，限时取消；默认全部
                saleStrategies = saleStrategies
                    .Where(x => x.CancelRulesType == input.CancelRulesType.Value)
                    .ToList();
            }

            if (!string.IsNullOrEmpty(input.NationalCountryName))
            {
                saleStrategies = saleStrategies
                    .Where(x => x.NationalNames.Any() is false
                                || (x.NationalNames.Any() && x.NationalNames.Contains(input.NationalCountryName)))
                    .ToList();
            }

            if (input.IsBreakfast.HasValue)
            {
                //全部，不含早（=0时），含早（>0时）
                saleStrategies = input.IsBreakfast.Value
                    ? saleStrategies
                        .Where(x => x.NumberOfBreakfast > 0)
                        .ToList()
                    : saleStrategies
                        .Where(x => x.NumberOfBreakfast == 0)
                        .ToList();
            }

            if (input.StrategyConfirmType.HasValue)
            {
                //本地酒店
                //商家确认（设置不自动确认，设置自动确认且库存<=0）
                //立即确认（设置自动确认且库存>0）
                var localStrategies = input.StrategyConfirmType == PriceStrategyConfirmType.Auto
                    ? saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.None
                                    && x.IsAutoConfirm
                                    && x.CalendarPrices.All(c => c.AvailableQuantity > 0))
                        .ToList()
                    : saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.None
                                    && (x.IsAutoConfirm == false || (x.IsAutoConfirm
                                                                     && x.CalendarPrices.Any(c =>
                                                                         c.AvailableQuantity <= 0))))
                        .ToList();

                //汇智酒店
                //商家确认：直采酒店  库存为0 ；非直采酒店
                //立即确认（直采酒店  且有库存）
                var thirdStrategies = input.StrategyConfirmType == PriceStrategyConfirmType.Auto
                    ? saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.Hop
                                    && x.IsDirect!.Value && x.CalendarPrices.All(c => c.AvailableQuantity > 0))
                        .ToList()
                    : saleStrategies
                        .Where(x => x.SupplierApiType == SupplierApiType.Hop
                                    && (x.IsDirect!.Value == false || (x.IsDirect!.Value
                                                                && x.CalendarPrices.Any(c =>
                                                                    c.AvailableQuantity <= 0))))
                        .ToList();

                saleStrategies = new List<SaleStrategyBffItem>();
                saleStrategies.AddRange(localStrategies);
                saleStrategies.AddRange(thirdStrategies);
            }

            if (input.AverageMaxPrice.HasValue || input.AverageMinPrice.HasValue)
            {
                //均价区间
                var avgCheckStrategies = new List<SaleStrategyBffItem>();
                var nightNum = input.LeaveDate.Subtract(input.LiveDate).Days;
                foreach (var strategy in saleStrategies)
                {
                    var sumPrice = strategy.CalendarPrices
                        .Sum(x => x.ChannelPrice);
                    var avgPrice = Math.Round((decimal)(sumPrice / nightNum), 2);
                    bool minCheck = !(avgPrice < input.AverageMinPrice);
                    bool maxCheck = !(avgPrice > input.AverageMaxPrice);
                    if (minCheck && maxCheck)
                    {
                        avgCheckStrategies.Add(strategy);
                    }
                }
                saleStrategies = avgCheckStrategies;
            }

            if (saleStrategies.Any())
            {
                //同一个房型的价格策略排序规则
                //1）按照价格从低往高排 
                //2）有库存的往前排(库存≤0与库存＞0)
                saleStrategies = saleStrategies
                    .OrderBy(o => o.MinChannelPrice)
                    .ThenByDescending(o => o.MinAvailableQuantity is null)
                    .ThenByDescending(o => o.MinAvailableQuantity)
                    .ToList();

                var filterRoomInfo = item;

                foreach (var strategy in saleStrategies)
                {
                    #region 预订建议项

                    //1、起订量
                    if (input.RoomNum < strategy.NumberOfRooms)
                        strategy.BookingAdvices.Add(new(BookingAdviceType.NumberOfRooms, strategy.NumberOfRooms));
                    //2、提前预订天数
                    if (strategy.BookingHoursInAdvance > 0)
                        if (input.LiveDate.Date.AddDays(1).AddHours(-strategy.BookingHoursInAdvance) <= DateTime.Now)
                        {
                            strategy.BookingAdvices.Add(new(BookingAdviceType.BookingHoursInAdvance,
                                strategy.BookingHoursInAdvance));
                        }

                    //3、连住天数
                    var nights = input.LeaveDate.Subtract(input.LiveDate).Days;
                    if (nights < strategy.NumberOfNights)
                        strategy.BookingAdvices.Add(new(BookingAdviceType.NumberOfNights, strategy.NumberOfNights));
                    //4.最大连住天数限制
                    if (strategy.LimitNumberOfNights is > 0 && nights > strategy.LimitNumberOfNights.Value)
                    {
                        strategy.BookingAdvices.Add(new(BookingAdviceType.LimitNumberOfNights,
                            strategy.LimitNumberOfNights.Value));
                    }

                    #endregion
                };

                filterRoomInfo.PriceStrategies = saleStrategies;
                filterResult.Add(filterRoomInfo);
            }
        }
        //不同房型，按照最低价格策略价格从低往高排
        filterResult = filterResult
            .OrderBy(o => o.PriceStrategies.Min(m => m.MinChannelPrice))
            .ToList();
        return Ok(filterResult);
    }

    /// <summary>
    /// 本地酒店价格策略
    /// </summary>
    private async Task<List<GetSaleStrategyBffOutput>> GetLocalHotelPriceStrategy(
        GetSaleStrategyBffInput input,
        CurrentUser currentUser,
        GetAgenciesByIdsOutput agencyInfo)
    {
        var result = new List<GetSaleStrategyBffOutput>();
        //如果查询条件存在儿童，不查询本地酒店报价
        if (input.ChildrenAges?.Any() is true)
        {
            return result;
        }
        // var agencyInfo = await _tenantApiCaller.GetAgencyDetail(currentUser.Provider);
        // if (agencyInfo.PriceGroupId is null or <= 0) return result;
        var priceGroupId = agencyInfo.PriceGroupId;

        #region 查询该价格分组下已配置的酒店数据

        var settingRequest = new QueryChannelPriceInput
        {
            PriceGroupId = priceGroupId.Value,
            ProductType = new[] { ChannelProductType.Hotel },
            ProductIds = new[] { input.HotelId }
        };
        if (!string.IsNullOrEmpty(input.PriceStrategyId))
        {
            _ = long.TryParse(input.PriceStrategyId, out long strategyId);
            settingRequest.SkuIds = new[] { strategyId };
        }

        var settingResponse = await _agencyChannelPriceService.QueryPricesSettings(settingRequest);
        if (settingResponse.Any() is false) return result;

        var skuIds = settingResponse.Select(x => x.SkuId).Distinct();

        #endregion

        #region 查询分销商酒店价格策略

        var priceStrategies = await _hotelApiCaller.GetAgencyStrategies(new AgencyGetInput
        {
            HotelId = input.HotelId,
            LiveDate = input.LiveDate,
            LeaveDate = input.LeaveDate,
            PriceStrategyIds = skuIds,
            VerifySale = !string.IsNullOrEmpty(input.PriceStrategyId)
        });

        #endregion

        #region 计算汇率

        var getExchangeRateInput = new List<GetExchangeRatesInput>();
        var costToSaleExchangeRateInput = priceStrategies
            .SelectMany(x => x.PriceStrategies)
            .GroupBy(x => new { x.CostCurrencyCode, x.SaleCurrencyCode })
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key.CostCurrencyCode,
                TargetCurrencyCode = x.Key.SaleCurrencyCode
            })
            .ToList();
        var saleToB2BExchangeRateInput = priceStrategies
            .SelectMany(x => x.PriceStrategies)
            .GroupBy(x => x.SaleCurrencyCode)
            .Select(x => new GetExchangeRatesInput
            {
                BaseCurrencyCode = x.Key,
                TargetCurrencyCode = agencyInfo.CurrencyCode
            })
            .ToList();

        getExchangeRateInput.AddRange(costToSaleExchangeRateInput);
        getExchangeRateInput.AddRange(saleToB2BExchangeRateInput);
        var exchangeRateList = await _paymentService.GetCurrencyExchangeRateList(getExchangeRateInput);

        #endregion

        result = priceStrategies.OrderBy(x => x.Sort)
            .Select(x =>
            {
                var data = new GetSaleStrategyBffOutput
                {
                    RoomId = x.ResourceRoomId,
                    ResourceRoomId = x.ResourceRoomId,
                    RoomZHName = x.RoomZHName,
                    RoomENName = x.RoomENName,
                    FirstPhoto = x.FirstPhoto,
                    WindowType = x.WindowType,
                    RoomQuantity = x.RoomQuantity,
                    MaximumOccupancy = x.MaximumOccupancy,
                    AreaMax = x.AreaMax,
                    AreaMin = x.AreaMin,
                    FloorMax = x.FloorMax,
                    FloorMin = x.FloorMin,
                    BedType = x.BedType
                };

                foreach (var strategy in x.PriceStrategies)
                {
                    var strategyOutputItem = new SaleStrategyBffItem
                    {
                        Id = strategy.Id.ToString(),
                        Name = strategy.Name,
                        HotelId = strategy.HotelId,
                        PriceStrategyType = strategy.PriceStrategyType,
                        NumberOfBreakfast = strategy.NumberOfBreakfast,
                        CancelRulesType = strategy.CancelRulesType,
                        NumberOfNights = strategy.NumberOfNights,
                        NumberOfRooms = strategy.NumberOfRooms,
                        BookingHoursInAdvance = strategy.BookingHoursInAdvance,
                        IsAutoConfirm = strategy.IsAutoConfirm,
                        CancelRule = strategy.CancelRule,
                        NationalNames = strategy.Nationalities.Select(x => x.ZHName),
                        MaximumOccupancy = strategy.MaximumOccupancy,
                        SupplierId = strategy.SupplierId,
                    };

                    var settingItem = settingResponse.First(s => s.SkuId == strategy.Id);

                    foreach (var priceOutputItem in from price in strategy.CalendarPrices
                                                    let channelPrice = _agencyChannelPriceService.ConvertB2BPrice(exchangeRateList, settingItem,
                                                        price.SalePrice, price.CostPrice,
                                                        strategy.CostCurrencyCode, strategy.SaleCurrencyCode, agencyInfo.CurrencyCode)
                                                    select new SaleStrategyPriceInventoryBffItem
                                                    {
                                                        Date = price.Date,
                                                        Enabled = price.Enabled,
                                                        AvailableQuantity = price.AvailableQuantity,
                                                        TotalQuantity = price.TotalQuantity,
                                                        ChannelPrice = channelPrice,
                                                        OverSaleable = strategy.OverSaleable
                                                    })
                    {
                        strategyOutputItem.CalendarPrices.Add(priceOutputItem);
                    }

                    strategyOutputItem.SumChannelPrice = strategyOutputItem.CalendarPrices.Sum(s => s.ChannelPrice);
                    strategyOutputItem.MinChannelPrice = strategyOutputItem.CalendarPrices.Min(s => s.ChannelPrice);

                    var noOverSaleableCalendarPrice =
                        strategyOutputItem.CalendarPrices.Where(c => c.OverSaleable == false)
                            .ToList();
                    if (noOverSaleableCalendarPrice.Any())
                    {
                        //最低库存数量.只统计不可超售的日历库存
                        strategyOutputItem.MinAvailableQuantity =
                            noOverSaleableCalendarPrice.Min(c => c.AvailableQuantity);
                    }

                    data.PriceStrategies.Add(strategyOutputItem);
                }

                return data;
            }).ToList();

        return result;
    }

    /// <summary>
    /// 分销商第三方酒店价格策略
    /// </summary>
    private async Task<List<GetSaleStrategyBffOutput>> GetThirdPartHotelPriceStrategy(
        GetSaleStrategyBffInput input,
        SupplierApiType supplierApiType,
        CurrentUser currentUser,
        GetAgenciesByIdsOutput agencyInfo,
        List<AgencyChannelPriceSettingOutput> agencyChannelPriceSettings,
        SearchEsHotelOutput esHotel)
    {
        var result = new List<GetSaleStrategyBffOutput>();
        //是否验证可售
        var verifySale = !string.IsNullOrEmpty(input.PriceStrategyId);
        var resourceHotelId = esHotel.ResourceHotelId;

        //查询第三方价格策略信息
        var strategies = await _resourceApiCaller.GetThirdHotelPrice(new GetThirdHotelPriceInput
        {
            IsGroupBooking = input.IsGroupBooking,
            AdultNum = input.MaximumOccupancy,
            ChildrenAges = input.ChildrenAges,
            RoomNum = input.RoomNum,
            ResourceHotelId = resourceHotelId,
            SupplierApiTypes = new[] { supplierApiType },
            CheckIn = input.LiveDate,
            CheckOut = input.LeaveDate,
            TenantId = currentUser.Tenant
        });
        if (strategies.Rooms.Any() is false) return result;

        //查询资源酒店房型信息
        var resourceRooms = await _resourceApiCaller.GetRoomsByHotelIds(new GetRoomsByHotelIdsInput
        {
            HotelIds = new List<long>
            {
                resourceHotelId
            }
        });

        var resourceRoomInfos = resourceRooms
            .FirstOrDefault(x => x.HotelId == resourceHotelId);
        if (resourceRoomInfos == null) return result;

        //查询租户信息
        var tenantInfoTask = _tenantApiCaller.GetSysConfigByTenantIds(currentUser.Tenant);
        //查询Api供应商
        var apiSupplierTask = _tenantApiCaller.QuerySuppliers(new Contracts.Common.Tenant.DTOs.Supplier.QuerySuppliersInput
        {
            SupplierType = SupplierType.Api,
            SupplierApiParentType = SupplierApiParentType.Hotel,
            SupplierApiType = supplierApiType,
        });
        //查询房型图片
        var roomPhotosTask = _resourceApiCaller.GetHotelPhotos(resourceHotelId);
        Task.WaitAll(tenantInfoTask, apiSupplierTask, roomPhotosTask);

        var tenantInfo = tenantInfoTask.Result.FirstOrDefault();
        var apiSupplier = apiSupplierTask.Result;
        var roomPhotos = roomPhotosTask.Result;

        #region 计算汇率

        var costCurrencyCode = apiSupplier.First().CurrencyCode;
        var saleCurrencyCode = tenantInfo.CurrencyCode;
        var agencyCurrencyCode = agencyInfo.CurrencyCode;
        var getExchangeRateInput = new List<GetExchangeRatesInput>
        {
            new GetExchangeRatesInput {BaseCurrencyCode = costCurrencyCode, TargetCurrencyCode = saleCurrencyCode},
            new GetExchangeRatesInput {BaseCurrencyCode = saleCurrencyCode, TargetCurrencyCode = agencyCurrencyCode}
        };
        var exchangeRateList = await _paymentService.GetCurrencyExchangeRateList(getExchangeRateInput);

        #endregion

        //入住天数
        var stayDays = input.LeaveDate.Subtract(input.LiveDate).Days;
        foreach (var resourceRoomItem in resourceRoomInfos.Rooms)
        {
            var room = strategies.Rooms.FirstOrDefault(x => x.ResourceRoomId == resourceRoomItem.Id);
            if (room == null)
                continue;

            var roomPriceStrategies = room.Pricestrategies;
            if (!string.IsNullOrEmpty(input.PriceStrategyId))
            {
                if (room.Pricestrategies.All(x => x.PricestrategyId != input.PriceStrategyId))
                {
                    continue;
                }

                roomPriceStrategies = room.Pricestrategies
                    .Where(x => x.PricestrategyId == input.PriceStrategyId);
            }

            var roomPhoto = roomPhotos.Rooms.Where(x => x.Id == room.ResourceRoomId)
                .SelectMany(x => x.Photos)
                .FirstOrDefault();
            var roomBedType = !string.IsNullOrWhiteSpace(room.BedType)
                ? JsonConvert.DeserializeObject<List<BedType>>(room.BedType)
                : new List<BedType>();
            var resultItem = new GetSaleStrategyBffOutput
            {
                RoomId = room.ResourceRoomId,
                ResourceRoomId = room.ResourceRoomId,
                RoomZHName = room.ZHName,
                RoomENName = room.ENName,
                WindowType = room.WindowType,
                RoomQuantity = room.RoomQuantity,
                MaximumOccupancy = room.MaximumOccupancy,
                AreaMax = room.AreaMax,
                AreaMin = room.AreaMin,
                FloorMax = room.FloorMax,
                FloorMin = room.FloorMin,
                FirstPhoto = roomPhoto?.Url,
                BedType = roomBedType
            };

            foreach (var strategy in roomPriceStrategies)
            {
                if (!strategy.Enabled)
                    continue;

                //过滤日历价格和入住天数不匹配
                if (strategy.Calendars.Count() != stayDays)
                {
                    if (verifySale)
                    {
                        throw new BusinessException("PricesNotEnough", "PricesNotEnough");
                    }
                }

                if (verifySale)
                {
                    if (strategy.Calendars.Any(x => x.Enabled == false))
                        throw new BusinessException("CalendarNotEnable", "CalendarNotEnable");

                    //判断提前预订时间
                    var nowTime = DateTime.Now;
                    if (strategy.BookingHoursInAdvance > 0
                        && input.LiveDate.Date.AddHours(24 - strategy.BookingHoursInAdvance) < nowTime)
                    {
                        continue;
                    }
                }

                var strategyOutputItem = new SaleStrategyBffItem
                {
                    Id = strategy.PricestrategyId,
                    Name = strategy.Name,
                    HotelId = input.HotelId,
                    PreBookingCode = strategy.PreBookingCode,
                    PriceStrategyType = strategy.PriceStrategyType,
                    NumberOfNights = strategy.NumberOfNights,
                    LimitNumberOfNights = strategy.LimitNumberOfNights,
                    NumberOfRooms = strategy.NumberOfRooms,
                    NumberOfBreakfast = strategy.NumberOfBreakfast,
                    CancelRulesType = strategy.CancelRule?.CancelRulesType,
                    BookingHoursInAdvance = strategy.BookingHoursInAdvance,
                    SupplierApiType = strategy.SupplierApiType,
                    IsDirect = strategy.IsDirect,
                    MaximumOccupancy = strategy.MaxOccupancy,
                    Tag = strategy.Tag,
                    BookingBenefits = strategy.BookingBenefits,
                    SupplierId = strategy.SupplierId,
                };

                if (strategy.CancelRule != null)
                {
                    strategyOutputItem.CancelRule = new CancelRule
                    {
                        CancelChargeType = strategy.CancelRule.CancelChargeType,
                        CancelRulesType = strategy.CancelRule.CancelRulesType,
                        BeforeCheckInDays = strategy.CancelRule.BeforeCheckInDays,
                        BeforeCheckInTime = strategy.CancelRule.BeforeCheckInTime,
                        CheckInDateTime = strategy.CancelRule.CheckInDateTime,
                        ChargeValue = strategy.CancelRule.ChargeValue
                    };
                }

                strategyOutputItem.NationalNames = strategy.NationalNames;

                //查询符合价格策略售卖类型的汇智酒店配置
                var isReunionRoomTag = strategy.PriceStrategyType == PriceStrategyType.GroupRoom;
                var convertB2BPriceSetting = _hotelService.GetStrategyChannelPriceSetting(strategy.IsDirect,
                    isReunionRoomTag,
                    strategy.Tag,
                    agencyChannelPriceSettings);

                //查无配置跳过
                if (convertB2BPriceSetting == null) continue;

                foreach (var calendar in strategy.Calendars)
                {
                    var minChannelPrice = _agencyChannelPriceService.ConvertB2BPrice(exchangeRateList,
                        convertB2BPriceSetting,
                        calendar.CostPrice, calendar.CostPrice,
                        costCurrencyCode, tenantInfo.CurrencyCode, agencyInfo.CurrencyCode);
                    strategyOutputItem.CalendarPrices.Add(new SaleStrategyPriceInventoryBffItem
                    {
                        Date = calendar.Date,
                        Enabled = calendar.Enabled,
                        AvailableQuantity = calendar.Quantity,
                        ChannelPrice = minChannelPrice,
                        OverSaleable = calendar.OverSaleable
                    });
                }

                strategyOutputItem.SumChannelPrice = strategyOutputItem.CalendarPrices.Sum(s => s.ChannelPrice);
                strategyOutputItem.MinChannelPrice = strategyOutputItem.CalendarPrices.Min(s => s.ChannelPrice);

                var noOverSaleableCalendarPrice =
                    strategyOutputItem.CalendarPrices.Where(c => c.OverSaleable == false)
                        .ToList();
                if (noOverSaleableCalendarPrice.Any())
                {
                    //最低库存数量.只统计不可超售的日历库存
                    strategyOutputItem.MinAvailableQuantity =
                        noOverSaleableCalendarPrice.Min(c => c.AvailableQuantity);
                }
                resultItem.PriceStrategies.Add(strategyOutputItem);
            }

            result.Add(resultItem);
        }

        return result;
    }

    #endregion

    /// <summary>
    /// 按每天查询分销商汇智酒店B2B价格信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(GetB2BPricesOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetB2BPrices(GetB2BPricesInput input)
    {
        GetB2BPricesOutput result = new();

        //校验分销商信息
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(input.AgencyId);
        if (agencyInfo.PriceGroupId is null or <= 0) return Ok(result);
        var priceGroupId = agencyInfo.PriceGroupId.Value;

        //校验分组启用状态
        var priceGroup = await _tenantApiCaller.GetPriceGroupDetail(priceGroupId);
        if (!priceGroup.Enable)
            return Ok(result);

        var checkPriceSetting = await _hotelService.CheckAgencyPriceSetting(priceGroupId);
        if (checkPriceSetting.localHotelIds.Any() is false &&
            checkPriceSetting.queryAgencyThirdHotel is false)
            return Ok(result);

        var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(input.HotelId);
        if (esHotelInfo.Any() is false)
        {
            return Ok(result);
        }

        var esHotel = esHotelInfo.First();
        var thirdHotelInfo = esHotel?.HotelIdList?.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Hop);
        if (thirdHotelInfo is null)
        {
            return Ok(result);
        }

        var checkHuiZhiSellChannelSettings = _hotelService.CheckHuiZhiSellChannelSettings(
                   checkPriceSetting.huiZhiPriceSettings,
                   thirdHotelInfo.HotelId,
                   true,
                   true,
                   Enum.GetValues<SellHotelTag>().ToList());

        var agencyChannelPriceSettings = checkHuiZhiSellChannelSettings.agencyChannelPriceSetting;

        var tenantId = HttpContext.GetTenantId();
        var dates = new List<(DateTime checkIn, DateTime checkOut)>();
        var days = input.EndDate.Subtract(input.BeginDate).TotalDays;
        for (int i = 0; i <= days; i++)
        {
            var checkIn = input.BeginDate.AddDays(i);
            var checkOut = checkIn.AddDays(1);
            dates.Add(new(checkIn, checkOut));
        }
        ConcurrentBag<GetThirdHotelPriceOutput> thirdHotelPriceOutputs = new();
        await Parallel.ForEachAsync(dates, async (d, cancellationToken) =>
        {
            //查询第三方价格策略信息
            var strategies = await _resourceApiCaller.GetThirdHotelPrice(new GetThirdHotelPriceInput
            {
                AdultNum = input.Occupancy,
                RoomNum = input.RoomNum,
                ResourceHotelId = esHotel.ResourceHotelId,
                SupplierApiTypes = new[] { thirdHotelInfo.SupplierApiType },
                CheckIn = d.checkIn,
                CheckOut = d.checkOut,
                TenantId = tenantId
            });
            thirdHotelPriceOutputs.Add(strategies);
        });
        var thirdHotelRooms = thirdHotelPriceOutputs.SelectMany(r => r.Rooms)
           .GroupBy(r => new { r.ResourceRoomId, r.RoomId })
           .Select(r => new ThirdHotelRoomOutput
           {
               ResourceRoomId = r.Key.ResourceRoomId,
               RoomId = r.Key.RoomId,
               ZHName = r.First().ZHName,
               ENName = r.First().ENName,
               AreaMax = r.First().AreaMax,
               AreaMin = r.First().AreaMin,
               BedType = r.First().BedType,
               FloorMax = r.First().FloorMax,
               FloorMin = r.First().FloorMin,
               MaximumOccupancy = r.First().MaximumOccupancy,
               RoomQuantity = r.First().RoomQuantity,
               WindowType = r.First().WindowType,
               Pricestrategies = r.SelectMany(p => p.Pricestrategies)
                .GroupBy(p => new { p.PricestrategyId, p.PriceStrategyType, p.SupplierApiType, })
                .Select(p => new ThirdHotelPricestrategyOutput
                {
                    PricestrategyId = p.Key.PricestrategyId,
                    PriceStrategyType = p.Key.PriceStrategyType,
                    SupplierApiType = p.Key.SupplierApiType,
                    SupplierId = p.First().SupplierId,
                    Name = p.First().Name,
                    ENName = p.First().ENName,
                    CostCurrencyCode = p.First().CostCurrencyCode,
                    SaleCurrencyCode = p.First().SaleCurrencyCode,
                    NationalNames = p.First().NationalNames,
                    BookingHoursInAdvance = p.First().BookingHoursInAdvance,
                    Enabled = p.First().Enabled,
                    IsAutoConfirm = p.First().IsAutoConfirm,
                    NumberOfBreakfast = p.First().NumberOfBreakfast,
                    CancelRules = p.First().CancelRules,
                    IsDirect = p.First().IsDirect,
                    NumberOfNights = p.First().NumberOfNights,
                    NumberOfRooms = p.First().NumberOfRooms,
                    Calendars = p.SelectMany(c => c.Calendars).OrderBy(c => c.Date),
                    MaxOccupancy = p.First().MaxOccupancy,
                })
           })
           .ToList();
        if (thirdHotelRooms.Any() is not true)
            return Ok(result);
        #region 计算汇率

        var costCurrencyCode = thirdHotelRooms.FirstOrDefault().Pricestrategies.FirstOrDefault().CostCurrencyCode;
        var saleCurrencyCode = thirdHotelRooms.FirstOrDefault().Pricestrategies.FirstOrDefault().SaleCurrencyCode;
        var agencyCurrencyCode = agencyInfo.CurrencyCode;
        var getExchangeRateInput = new List<GetExchangeRatesInput>
        {
            new GetExchangeRatesInput {BaseCurrencyCode = costCurrencyCode, TargetCurrencyCode = saleCurrencyCode},
            new GetExchangeRatesInput {BaseCurrencyCode = saleCurrencyCode, TargetCurrencyCode = agencyCurrencyCode}
        };
        var exchangeRateList = await _paymentService.GetCurrencyExchangeRateList(getExchangeRateInput);

        #endregion

        var rooms = thirdHotelRooms.Select(r =>
           {
               var roomOutput = new B2BPriceRoomOutput
               {
                   ResourceRoomId = r.ResourceRoomId,
                   ENName = r.ENName,
                   ZHName = r.ZHName,
               };
               roomOutput.Pricestrategies = r.Pricestrategies.Select(p =>
               {
                   var pricestrategyOutput = new B2BPricestrategyOutput
                   {
                       PricestrategyId = p.PricestrategyId,
                       Name = p.Name,
                       ENName = p.ENName,
                       Enabled = p.Enabled,
                       Calendars = dates.Select(d =>
                       {
                           var date = d.checkIn;
                           var pCalendar = p.Calendars.FirstOrDefault(x => x.Date == date);
                           var r = new B2BCalendarPriceOutput
                           {
                               Date = date,
                               Quantity = pCalendar?.Quantity ?? 0,
                               OverSaleable = pCalendar?.OverSaleable ?? false,
                               Enabled = pCalendar?.Enabled ?? false,
                               CostCurrencyCode = p.CostCurrencyCode,
                               CostPrice = pCalendar?.CostPrice,
                               CurrencyCode = agencyCurrencyCode,
                           };
                           if (pCalendar is null)
                               return r;
                           //查询符合价格策略售卖类型的汇智酒店配置
                           var isReunionRoomTag = p.PriceStrategyType == PriceStrategyType.GroupRoom;
                           var convertB2BPriceSetting = _hotelService.GetStrategyChannelPriceSetting(p.IsDirect,
                               isReunionRoomTag,
                               p.Tag,
                               agencyChannelPriceSettings);

                           if (convertB2BPriceSetting is not null)
                           {
                               r.ChannelPrice = _agencyChannelPriceService.ConvertB2BPrice(exchangeRateList,
                               convertB2BPriceSetting,
                               pCalendar.CostPrice, pCalendar.CostPrice,
                               costCurrencyCode, saleCurrencyCode,
                               agencyCurrencyCode);
                           }
                           return r;
                       })
                   };
                   return pricestrategyOutput;
               });
               return roomOutput;
           });

        result.Rooms = rooms;

        return Ok(result);
    }

    /// <summary>
    /// 详情
    /// </summary>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(List<BffFacilitiesOutput>), (int)HttpStatusCode.OK)]
    public async Task<ActionResult> Facilities()
    {
        var result = await _resourceApiCaller.Facilities();
        var outRes = _mapper.Map<List<BffFacilitiesOutput>>(result);
        return Ok(outRes);
    }


    /// <summary>
    /// 查询已添加酒店的城市列表
    /// </summary>
    /// <param name="isCache">是否取缓存</param>
    /// <param name="needAvailable">是否取上架景区</param>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(IEnumerable<BffGetCitiesOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCitiese(bool? isCache, bool? needAvailable)
    {
        if (!isCache.HasValue) isCache = true;
        if (!needAvailable.HasValue) needAvailable = false;

        var result = await _hotelApiCaller.GetHotelCities(isCache.Value, needAvailable.Value);
        var outRes = _mapper.Map<List<BffGetCitiesOutput>>(result);
        if (outRes.Any())
        {
            var cityCodes = outRes.Select(x => x.CityCode).ToArray();
            var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
            {
                CityCodes = cityCodes
            });
            outRes.ForEach(item =>
            {
                var city = cities.FirstOrDefault(x => x.CityCode == item.CityCode);
                item.EnCityName = city?.ENName;
                item.EnCountryName = city?.CountryEnName;
            });
        }
        return Ok(outRes);
    }


    /// <summary>
    /// 添加酒店
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> Add(AddHotelInput input)
    {
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = new int[] { input.CityCode }
        });
        var city = cities.FirstOrDefault(x => x.CityCode == input.CityCode);
        input.CityName = city?.ZHName;
        input.ProvinceName = city?.ProvinceName;
        input.CountryName = city?.CountryName;
        await _hotelApiCaller.AddHotel(input);
        return Ok();
    }


    /// <summary>
    /// 修改酒店详情
    /// </summary>
    [HttpPost]
    [Authorize]
    public async Task<IActionResult> Update(UpdateHotelDetailsInput input)
    {
        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = new int[] { input.CityCode }
        });
        var city = cities.FirstOrDefault(x => x.CityCode == input.CityCode);
        input.CityName = city?.ZHName;
        input.ProvinceName = city?.ProvinceName;
        input.CountryName = city?.CountryName;
        await _hotelApiCaller.UpdateHotel(input);
        return Ok();
    }
}