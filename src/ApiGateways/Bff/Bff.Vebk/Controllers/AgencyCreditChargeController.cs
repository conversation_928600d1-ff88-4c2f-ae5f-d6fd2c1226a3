using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.AgencyCreditCharge;
using Bff.Vebk.Models.Common;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Tenant.DTOs.AgencyCreditCharge;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 分销商预收款充值，原 分销商额度充值
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AgencyCreditChargeController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IMapper _mapper;

    public AgencyCreditChargeController(ITenantApiCaller tenantApiCaller,
        IPaymentApiCaller paymentApiCaller,
        IMapper mapper)
    {
        _tenantApiCaller = tenantApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 根据条件搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<ChargeSearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(AgencyCreditChargeSearchInput input)
    {
        var data = await _tenantApiCaller.AgencyCreditChargeSearch(input);

        PagingModel<ChargeSearchOutput> result = new()
        {
            PageIndex = data.PageIndex,
            PageSize = data.PageSize,
            Total = data.Total,
            Data = _mapper.Map<List<ChargeSearchOutput>>(data.Data)
        };
        var refundChargeIds = result.Data
            .Where(x => x.ChargeStatus == AgencyCreditChargeStatus.Refunded || x.ChargeStatus == AgencyCreditChargeStatus.Refunding)
            .Select(x => x.Id)
            .ToArray();
        if (refundChargeIds.Any())
        {
            var refunds = await _paymentApiCaller.GetReceiptPrepaymentRefundList(new Contracts.Common.Payment.DTOs.ReceiptPrepayment.GetRefundListInput
            {
                ChargeIds = refundChargeIds
            });
            foreach (var item in result.Data)
            {
                if (item.AccountInfo is not null)
                {
                    item.AccountInfo.AccountNo = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto
                    {
                        Id = item.AccountInfo.TenantBankAccountId,
                        DataType = SensitiveDataType.BankAccount,
                        IdType = SensitiveDataIdType.BankAccount,
                        Data = item.AccountInfo.AccountNo,
                    });
                }
                var refund = refunds.FirstOrDefault(x => x.ChargeId == item.Id);
                if (refund != null)
                {
                    if (refund.AccountInfo is not null)
                    {
                        refund.AccountInfo.AccountNo = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto
                        {
                            Id = refund.AccountInfo.TenantBankAccountId,
                            DataType = SensitiveDataType.BankAccount,
                            IdType = SensitiveDataIdType.BankAccount,
                            Data = refund.AccountInfo.AccountNo,
                        });
                    }
                    item.ChargeRefund = new ChargeRefundDto
                    {
                        AccountInfo = refund.AccountInfo,
                        RefundAmount = refund.RefundAmount,
                        Proof = refund.Proof,
                        Remark = refund.Remark,
                        FinishTime = refund.FinishTime,
                    };
                }
            }
        }

        return Ok(result);
    }
    /// <summary>
    /// 分销商充值记录 导出数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<ChargeSearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportData(AgencyCreditChargeQueryInput input)
    {
        var data = await _tenantApiCaller.AgencyCreditChargeExportData(input);

        List<ChargeSearchOutput> result = _mapper.Map<List<ChargeSearchOutput>>(data);

        var refundChargeIds = result
            .Where(x => x.ChargeStatus == AgencyCreditChargeStatus.Refunded || x.ChargeStatus == AgencyCreditChargeStatus.Refunding)
            .Select(x => x.Id)
            .ToArray();
        if (refundChargeIds.Any())
        {
            var refunds = await _paymentApiCaller.GetReceiptPrepaymentRefundList(new Contracts.Common.Payment.DTOs.ReceiptPrepayment.GetRefundListInput
            {
                ChargeIds = refundChargeIds
            });
            foreach (var item in result)
            {
                if (item.AccountInfo is not null)
                {
                    item.AccountInfo.AccountNo = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto
                    {
                        Id = item.AccountInfo.TenantBankAccountId,
                        DataType = SensitiveDataType.BankAccount,
                        IdType = SensitiveDataIdType.BankAccount,
                        Data = item.AccountInfo.AccountNo,
                    });
                }
                var refund = refunds.FirstOrDefault(x => x.ChargeId == item.Id);
                if (refund != null)
                {
                    if (refund.AccountInfo is not null)
                    {
                        refund.AccountInfo.AccountNo = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto
                        {
                            Id = refund.AccountInfo.TenantBankAccountId,
                            DataType = SensitiveDataType.BankAccount,
                            IdType = SensitiveDataIdType.BankAccount,
                            Data = refund.AccountInfo.AccountNo,
                        });
                    }
                    item.ChargeRefund = new ChargeRefundDto
                    {
                        AccountInfo = refund.AccountInfo,
                        RefundAmount = refund.RefundAmount,
                        Proof = refund.Proof,
                        Remark = refund.Remark,
                        FinishTime = refund.FinishTime,
                    };
                }
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 充值处理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Handle(HandleInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        await _tenantApiCaller.AgencyCreditChargeHandle(new AgencyCreditChargeHandleInput
        {
            Id = input.Id,
            ChargeStatus = input.ChargeStatus,
            Operator = currentUser.NickName,
            OperatorId = currentUser.UserId
        });
        return Ok();
    }

    /// <summary>
    /// 充值退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation,
        ErrorTypes.Tenant.AgencyChargeOverChagreAmount,
        ErrorTypes.Tenant.ReceiptPrepaymentRefundIsExists,
        ErrorTypes.Tenant.ReceiptPrepaymentInsufficientBalance)]
    public async Task<IActionResult> Refund(AgencyCreditChargeRefundInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        await _tenantApiCaller.AgencyCreditChargeRefund(input);
        return Ok();
    }

    /// <summary>
    /// 线下收款类充值确认退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> OfflineRefundConfirm(ChargeRefundConfirmInput input)
    {
        var currentUser=HttpContext.User.ParseUserInfo<CurrentUser>();
        var tenantBankAccount = await _paymentApiCaller.GetTenantBankAccount(input.TenantBankAccountId);
        OfflineRefundConfirmInput offlineRefundConfirmInput = new()
        {
            ChargeId = input.ChargeId,
            AccountInfo = new Contracts.Common.Payment.DTOs.ReceiptPrepayment.TenantAccountInfoDto
            {
                TenantBankAccountId = tenantBankAccount.Id,
                TenantBankAccountType = tenantBankAccount.TenantBankAccountType,
                AccountName = tenantBankAccount.AccountName,
                AccountNo = tenantBankAccount.AccountNo,
                BankCode = tenantBankAccount.BankCode,
                BankName = tenantBankAccount.BankName,
                BranchName = tenantBankAccount.BranchName,
            },
            PayTime = input.PayTime,
            Proof = input.Proof,
            OperationUser = new Contracts.Common.Tenant.DTOs.OperationUserDto
            {
                UserType = UserType.Merchant,
                UserId = currentUser.UserId,
                Name = currentUser.NickName,
            }
        };
        await _tenantApiCaller.AgencyCreditChargeOfflineRefundConfirm(offlineRefundConfirmInput);
        return Ok();
    }
}
