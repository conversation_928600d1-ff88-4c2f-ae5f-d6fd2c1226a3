using Bff.Vebk.Callers;
using Common.Swagger;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.Country;
using Contracts.Common.Resource.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class CountryController : ControllerBase
{
    private readonly IResourceApiCaller _resourceApiCaller;
    public CountryController(IResourceApiCaller resourceApiCaller)
    {
        _resourceApiCaller = resourceApiCaller;
    }

    /// <summary>
    /// 搜索国家列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<CountrySearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(CountrySearchInput input)
    {
        var result = await _resourceApiCaller.SearchCountries(input);
        return Ok(result);
    }

    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(IEnumerable<MixedCountryDto>))]
    public async Task<IActionResult> GetMixedCountries()
    {
        //国籍数据加入 中国大陆,中国香港，中国澳门，中国台湾
        var mixedCountries = await MixCountries();
        return Ok(mixedCountries);
    }

    private async Task<IEnumerable<MixedCountryDto>> MixCountries()
    {
        var countries = await _resourceApiCaller.GetCountries();
        var result = countries.Select(x => new MixedCountryDto
        {
            CountryCode = x.CountryCode,
            ZHName = x.ZHName,
            ENName = x.ENName,
            IsoCode = x.IsoCode,
        })
        .Where(x => x.CountryCode > 10)//排除其他
        .ToList();

        //添加 中国大陆,中国香港，中国澳门，中国台湾
        result.ApendMixedCountry();

        return result.OrderBy(s => s.CountryCode);
    }
}
