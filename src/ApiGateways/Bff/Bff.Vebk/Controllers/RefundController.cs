using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.Order;
using Bff.Vebk.Models.Refund;
using Bff.Vebk.Services.Interfaces;
using Common.Swagger;
using Common.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class RefundController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IBaseOrderService _baseOrderService;
    private readonly IMapper _mapper;

    public RefundController(IOrderApiCaller orderApiCaller
        , IUserApiCaller userApiCaller
        , IMapper mapper
        , IBaseOrderService baseOrderService)
    {
        _orderApiCaller = orderApiCaller;
        _userApiCaller = userApiCaller;
        _mapper = mapper;
        _baseOrderService = baseOrderService;
    }

    /// <summary>
    /// 获取主单号关联的退款单
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(List<RefundOrderDetailBffOutPut>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetRefundsByBaseOrderId(long baseOrderId)
    {
        var refundOrderDetailOutDtos = await _orderApiCaller.GetRefundsByBaseOrderId(baseOrderId);
        var result = _mapper.Map<List<RefundOrderDetailBffOutPut>>(refundOrderDetailOutDtos);
        var refundOrderPersonsInfo = result.Select(x => new QueryRefundOrderRelatedPersonsInfoBffInput
        {
            RefundUserId = x.UserId,
            RefundUserType = x.UserType
        })
        .ToList();

        var orderRelatedPersons = await _baseOrderService.GetRelatedPersonsInfo(new QueryOrderRelatedPersonsInfoBffInput
        {
            RefundOrderRelatedPersonsInfos = refundOrderPersonsInfo,
            IsDataSecrecy = true,
            BaseOrderId = baseOrderId,
        });

        foreach (var item in result)
        {
            var relatedPerson =
                orderRelatedPersons.RefundOrderRelatedPersonsInfos.FirstOrDefault(x =>
                    x.RefundUserId == item.UserId);
            item.UserPhoneNumber = relatedPerson?.RefundUserPhoneNumber; ;
        }

        return Ok(result);
    }
}
