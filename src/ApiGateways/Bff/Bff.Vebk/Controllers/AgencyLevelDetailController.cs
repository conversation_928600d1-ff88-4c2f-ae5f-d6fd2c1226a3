using AutoMapper;
using Bff.Vebk.Callers;
using Common.Swagger;
using Contracts.Common.Tenant.DTOs.AgencyLevelDetail;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 分销商等级明细
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AgencyLevelDetailController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly ITenantApiCaller _tenantApiCaller;
    public AgencyLevelDetailController(IMapper mapper,
        ITenantApiCaller tenantApiCaller
        )
    {
        _mapper = mapper;
        _tenantApiCaller = tenantApiCaller;
    }

    /// <summary>
    /// 调整分销商成长值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.NoVipAgency,
                                 ErrorTypes.Tenant.AgencyLevelDetailNotExist)]
    public async Task<IActionResult> IncreaseAgencyGrowUpValue(IncreaseAgencyGrowUpValueInput input)
    {
        var result = await _tenantApiCaller.IncreaseAgencyGrowUpValue(input);
        return Ok(result);
    }

}
