using Bff.Vebk.Callers;
using Common.Swagger;
using Contracts.Common.Order.DTOs.Invoice;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 产品日历
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class InvoiceConfigController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;

    public InvoiceConfigController(IOrderApiCaller orderApiCaller)
    {
        _orderApiCaller = orderApiCaller;
    }

    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(List<GetInvoiceConfigOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get()
    {
        var result = await _orderApiCaller.GetInvoiceConfig();
        return Ok(result);
    }

    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Delete(long id)
    {
        await _orderApiCaller.DeleteInvoiceConfig(id);
        return Ok();
    }

    /// <summary>
    /// 修改电子发票设置
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.InvoiceConfigNotExists)]
    [SwaggerResponseExt(default, ErrorTypes.Order.InvoiceConfigIsDefault)]
    [SwaggerResponseExt(default, ErrorTypes.Order.InvoiceConfigIsExists)]
    public async Task<IActionResult> Set(SetInvoiceConfigInput input)
    {
        var result = await _orderApiCaller.SetInvoiceConfig(input);
        return Ok(result);
    }

    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetEnabled(bool enabled)
    {
        await _orderApiCaller.SetInvoiceConfigEnabled(enabled);
        return Ok();
    }
}
