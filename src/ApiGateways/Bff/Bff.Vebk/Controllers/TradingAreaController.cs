using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.TradingArea;
using Common.Swagger;
using Contracts.Common.Resource.DTOs.TradingArea;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class TradingAreaController : ControllerBase
{
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IMapper _mapper;
    public TradingAreaController(IResourceApiCaller resourceApiCaller, IMapper mapper)
    {
        _resourceApiCaller = resourceApiCaller;
        _mapper = mapper;
    }


    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<BffSearchTradingAreaDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchTradingAreaInput input)
    {
        var result = await _resourceApiCaller.SearchTradingArea(input);
        var outRes=_mapper.Map<PagingModel<BffSearchTradingAreaDto>>(result);
        return Ok(outRes);
    }
}
