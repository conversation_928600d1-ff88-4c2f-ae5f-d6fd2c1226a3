using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.Common;
using Bff.Vebk.Models.LineProductSkuCalendarPrice;
using Bff.Vebk.Models.OpenChannelOrder;
using Bff.Vebk.Models.OpenChannelSyncFailOrder;
using Bff.Vebk.Models.OpenSupplierOrder;
using Bff.Vebk.Models.Order;
using Bff.Vebk.Models.TravelLineOrder;
using Bff.Vebk.Models.TravelLineOrder.OTA;
using Bff.Vebk.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.OpenChannelOrder;
using Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.DTOs.Supplier;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Bff.Vebk.Controllers;
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class TravelLineOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly ITravelLineOrderService _travelLineOrderService;
    private readonly IMapper _mapper;
    private readonly IBaseOrderService _baseOrderService;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IOrderFieldInformationService _orderFieldInformationService;
    private readonly IOpenPlatformService _openPlatformService;
    private readonly IOpenSupplierOrderService _openSupplierOrderService;
    private readonly IOpenChannelOrderService _openChannelOrderService;

    public TravelLineOrderController(IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller,
        IPaymentApiCaller paymentApiCaller,
        ITravelLineOrderService travelLineOrderService,
        IMapper mapper,
        IBaseOrderService baseOrderService,
        IProductApiCaller productApiCaller,
        IOrderFieldInformationService orderFieldInformationService,
        IOpenPlatformService openPlatformService,
        IOpenSupplierOrderService openSupplierOrderService,
        IOpenChannelOrderService openChannelOrderService)
    {
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _travelLineOrderService = travelLineOrderService;
        _mapper = mapper;
        _baseOrderService = baseOrderService;
        _productApiCaller = productApiCaller;
        _orderFieldInformationService = orderFieldInformationService;
        _openPlatformService = openPlatformService;
        _openSupplierOrderService = openSupplierOrderService;
        _openChannelOrderService = openChannelOrderService;
    }

    /// <summary>
    /// 线路订单搜索列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<SearchLineOrderBffOutput, LineOrderStatusCountBffOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchLineOrderBffInput input)
    {
        var result = new PagingModel<SearchLineOrderBffOutput, LineOrderStatusCountBffOutput>
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize
        };

        //正常线路分页数据
        var searchLineOrderRequest = _mapper.Map<SearchInput>(input);

        //渠道失败异常订单
        var syncFailOrderRequest = new SearchOpenChannelSyncFailOrderInput
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            Id = input.OrderId,
            OrderTypes = new List<OrderType> { OrderType.TravelLineOrder },
            ChannelOrderNo = input.ChannelOrderNo,
            SellingPlatform = input.SellingPlatform,
            SyncStatus = new List<OpenChannelFailOrderSyncStatus> { OpenChannelFailOrderSyncStatus.SyncFail },
            TravelDateBegin = input.TravelBeginDate,
            TravelDateEnd = input.TravelEndDate,
            CreateDateBegin = input.CreateBeginDate,
            CreateDateEnd = input.CreateEndDate,
            AgencyId = input.AgencyId,
            SupplierId = input.SupplierId,
            ProductName = input.ProductName,
            ProductId = input.ProductId,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber
        };

        //异常订单没有采购单号,所以有值的时候不查询异常订单数据
        if (input.Status == SearchLineOrderBffStatus.ChannelAbnormal)
        {
            //异常订单没有采购单号,所以有值的时候不查询异常订单数据
            if (string.IsNullOrEmpty(input.SupplierOrderId))
            {
                //渠道异常订单分页数据
                var syncFailOrderSearchResponse =
                    await _orderApiCaller.SearchOpenChannelSyncFailOrder(syncFailOrderRequest);
                result.PageIndex = syncFailOrderSearchResponse.PageIndex;
                result.PageSize = syncFailOrderSearchResponse.PageSize;
                result.Total = syncFailOrderSearchResponse.Total;
                result.Data = new List<SearchLineOrderBffOutput>();
                result.Supplement = new LineOrderStatusCountBffOutput();
                var dataList = new List<SearchLineOrderBffOutput>();

                //查询线路产品id
                var productIds = syncFailOrderSearchResponse.Data
                    .Where(x => x.ProductId.HasValue)
                    .Select(x => x.ProductId!.Value)
                    .Distinct()
                    .ToArray();
                var lineProductResponse = await _productApiCaller.GetLineProductDetail(productIds);

                foreach (var item in syncFailOrderSearchResponse.Data)
                {
                    //处理渠道同步订单数据
                    var lineProduct = lineProductResponse.FirstOrDefault(x => x.ProductId == item.ProductId);

                    var resultDataItem = new SearchLineOrderBffOutput
                    {
                        BaseOrderId = item.Id,
                        ProductName = item.ProductName,
                        ProductSkuName = item.SkuName,
                        UserNickName = item.ContactsName,
                        ContactsName = item.ContactsName,
                        ContactsPhoneNumber = item.ContactsPhoneNumber,
                        ContactsEmail = item.ContactsEmail,
                        SellingPlatform = item.SellingPlatform,
                        CreateTime = item.CreateTime,
                        PaymentAmount = item.PaymentAmount,
                        PaymentCurrencyCode = item.CurrencyCode,
                        ChannelOrderNos = item.ChannelOrderNo,
                        SupplierId = item.SupplierId ?? 0,
                        TravelBeginDate = item.TravelDate,
                        OpenChannelSyncReason = item.Reason,
                        Status = TravelLineOrderMixStatus.ChannelAbnormal,
                        Days = lineProduct?.Days,
                        Nights = lineProduct?.Nights
                    };

                    dataList.Add(resultDataItem);
                }

                result.Data = dataList;
            }

            //线路订单的状态数量统计
            var searchLineOrderResponse = await _orderApiCaller.TravelLineOrderSearch(searchLineOrderRequest);
            result.Supplement = new LineOrderStatusCountBffOutput
            {
                TotalCount = searchLineOrderResponse.Supplement.TotalCount,
                WaitingForClaimCount = searchLineOrderResponse.Supplement.WaitingForClaimCount,
                WaitingForConfirmCount = searchLineOrderResponse.Supplement.WaitingForConfirmCount,
                UnFinishedCount = searchLineOrderResponse.Supplement.UnFinishedCount,
                FinishedCount = searchLineOrderResponse.Supplement.FinishedCount,
                ClosedCount = searchLineOrderResponse.Supplement.ClosedCount,
                WaitingForPayCount = searchLineOrderResponse.Supplement.WaitingForPayCount,
            };
        }
        else
        {
            var searchLineOrderResponse = await _orderApiCaller.TravelLineOrderSearch(searchLineOrderRequest);
            result.PageIndex = searchLineOrderResponse.PageIndex;
            result.PageSize = searchLineOrderResponse.PageSize;
            result.Total = searchLineOrderResponse.Total;
            result.Data = _mapper.Map<List<SearchLineOrderBffOutput>>(searchLineOrderResponse.Data);
            result.Supplement = new LineOrderStatusCountBffOutput
            {
                TotalCount = searchLineOrderResponse.Supplement.TotalCount,
                WaitingForClaimCount = searchLineOrderResponse.Supplement.WaitingForClaimCount,
                WaitingForConfirmCount = searchLineOrderResponse.Supplement.WaitingForConfirmCount,
                UnFinishedCount = searchLineOrderResponse.Supplement.UnFinishedCount,
                FinishedCount = searchLineOrderResponse.Supplement.FinishedCount,
                ClosedCount = searchLineOrderResponse.Supplement.ClosedCount,
                WaitingForPayCount = searchLineOrderResponse.Supplement.WaitingForPayCount,
            };
        }

        //补充异常订单状态统计数据
        //异常订单没有采购单号,所以有值的时候不查询异常订单数据
        if (string.IsNullOrEmpty(input.SupplierOrderId))
        {
            var syncFailOrderCountResponse =
                await _orderApiCaller.SearchOpenChannelSyncFailOrderStatusCount(syncFailOrderRequest);
            result.Supplement.OpenChannelSyncFailOrderCount = syncFailOrderCountResponse
                .FirstOrDefault(x => x.OrderType == OrderType.TravelLineOrder)?.SyncFailCount ?? 0;
        }

        return Ok(result);
    }

    /// <summary>
    /// 导出数据
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(ExportDataOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> ExportData(ExportDataInput input)
    {
        var searchResponse = await _orderApiCaller.TravelLineOrderDetail(new OrderDetailInput
        {
            BaseOrderId = input.BaseOrderId
        });
        List<ProductTemplateType> productTemplateTypes = new List<ProductTemplateType>() {
          ProductTemplateType.EachPerson,
           ProductTemplateType.EachAdult,
            ProductTemplateType.EachChild,
             ProductTemplateType.EachBaby,
              ProductTemplateType.EachElderly,
              ProductTemplateType.EachOther,
               ProductTemplateType.JustOnePerson,
        };
        var travelerFields = searchResponse.OrderFields.Where(x => x.TemplateType == TemplateType.Travel
                              && productTemplateTypes.Contains(x.ProductTemplateType)).ToList();
        var result = new ExportDataOutput
        {
            OrderTravelers = searchResponse.OrderTravelers
                .Select(x => new TravelerInfo
                {
                    IDCard = x.IDCard,
                    Name = x.Name,
                    Type = x.Type
                })
                .ToList(),
            BaseOrder = new BaseOrderInfo
            {
                BaseOrderId = searchResponse.BaseOrder.Id,
                CreateTime = searchResponse.BaseOrder.CreateTime,
                ProductName = searchResponse.BaseOrder.ProductName,
                ProductSkuName = searchResponse.BaseOrder.ProductSkuName,
                ContactsName = searchResponse.BaseOrder.ContactsName,
                ContactsPhoneNumber = searchResponse.BaseOrder.ContactsPhoneNumber,
                Remark = searchResponse.BaseOrder.Message,

                TravelBeginDate = searchResponse.TravelLineOrder.TravelBeginDate,
                TravelEndDate = searchResponse.TravelLineOrder.TravelEndDate,
                RallyPointTime = searchResponse.TravelLineOrder.RallyPointTime,
                RallyPointAddress = searchResponse.TravelLineOrder.RallyPointAddress
            },
            TravelerFields = travelerFields
        };

        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        await _orderApiCaller.CreateOrderLog(new Contracts.Common.Order.DTOs.OrderLog.CreateInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationType = OrderOperationType.ExportData,
            OrderLogType = OrderLogType.TravelLine,
            OperationRole = UserType.Merchant,
            UserId = currentUser.UserId,
            UserName = currentUser.NickName
        });
        return Ok(result);
    }

    /// <summary>
    /// 线路订单详情
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(GetTravelLineOrderDetailBffOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData)]
    public async Task<IActionResult> GetDetail(long baseOrderId)
    {
        GetTravelLineOrderDetailBffOutput result;

        //判断是否未处理的异常订单
        var syncFailOrderDetail = await SyncFailOrderDetailProcess(new GetOpenChannelSyncFailOrderInput
        {
            SyncFailOrderId = baseOrderId
        });
        if (syncFailOrderDetail is { SyncStatus: OpenChannelFailOrderSyncStatus.SyncFail })
        {
            result = ConvertFailOrderToNormalOrderDetail(syncFailOrderDetail);
        }
        else
        {
            var searchResponse = await _orderApiCaller.TravelLineOrderDetail(new OrderDetailInput
            {
                BaseOrderId = baseOrderId
            });
            // 获取保险采购单列表
            var insureRecord = await _orderApiCaller.GetInsureRecordByOrder(baseOrderId);

            result = _mapper.Map<GetTravelLineOrderDetailBffOutput>(searchResponse);
            result.InsurePurchaseRecord = insureRecord;

            //处理分销商信息
            if (result.DistributionChannelInfo.AgencyId > 0)
            {
                var agencyInfo = await _tenantApiCaller.GetAgencyDetail(result.DistributionChannelInfo.AgencyId);
                result.DistributionChannelInfo.AgencyType = agencyInfo?.AgencyType;
                result.DistributionChannelInfo.AgencyApiType = agencyInfo?.AgencyApiType;
            }

            if (result.TravelLineOrder.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
            {
                // 查询附加信息
                var orderExtraInfos = (await _orderApiCaller.QueryOpenSupplierOrderExtraInfo(baseOrderId)).FirstOrDefault();
                if (orderExtraInfos.OrderExtraInfos.Any())
                {
                    result.OrderExtraInfos =
                        orderExtraInfos.OrderExtraInfos.Select(x => new QueryOpenSupplierOrderExtraInfoBffOutput
                        {
                            DataType = x.DataType,
                            OptionKey = x.OptionKey,
                            OptionValue = x.OptionValue
                        }).ToList();
                }
                else
                {
                    // 供应端采购失败查询对应关联产品的采购失败信息
                    if (result.TravelLineOrder.Status == TravelLineOrderStatus.WaitingForConfirm)
                    {
                        var checkOrderRelatedExtraInfos = await _openSupplierOrderService.CheckOrderRelatedExtraInfos(
                            new CheckOrderRelatedExtraInfoBffInput
                            {
                                Items = new List<CheckOrderRelatedExtraInfoItem>
                                {
                                    new CheckOrderRelatedExtraInfoItem
                                    {
                                        BaseOrderId = baseOrderId,
                                        OrderType = OrderType.TravelLineOrder
                                    }
                                }
                            });
                        result.OrderExtraInfos = checkOrderRelatedExtraInfos
                            .GroupBy(x => x.DataType)
                            .Select(x => new QueryOpenSupplierOrderExtraInfoBffOutput
                            {
                                DataType = x.Key
                            }).ToList();
                    }
                }
            }
        }

        //脱敏数据
        if (result != null)
        {
            result.BaseOrder.ContactsPhoneNumber = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
            {
                Id = result.BaseOrder.Id,
                IdType = SensitiveDataIdType.TravelLineOrder,
                DataType = SensitiveDataType.OrderContactsPhone,
                Data = result.BaseOrder.ContactsPhoneNumber,
            });
            result.BaseOrder.ContactsEmail = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
            {
                Id = result.BaseOrder.Id,
                IdType = SensitiveDataIdType.TravelLineOrder,
                DataType = SensitiveDataType.OrderContactsEmail,
                Data = result.BaseOrder.ContactsEmail,
            });

            result.OrderTravelers.ForEach(x =>
            {
                x.IDCard = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                {
                    Id = result.BaseOrder.Id,
                    IdType = SensitiveDataIdType.TravelLineOrder,
                    DataType = SensitiveDataType.OrderTravelerIDCard,
                    Data = x.IDCard,
                });
                x.Email = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                {
                    Id = result.BaseOrder.Id,
                    IdType = SensitiveDataIdType.TravelLineOrder,
                    DataType = SensitiveDataType.OrderTravelerEmail,
                    Data = x.Email,
                });
                x.Phone = DataSecrecyHelper.DataSecrecyEncrypt(new SensitiveDataDto()
                {
                    Id = result.BaseOrder.Id,
                    IdType = SensitiveDataIdType.TravelLineOrder,
                    DataType = SensitiveDataType.OrderTravelerPhone,
                    Data = x.Phone,
                });
            });
        }

        #region 补充创建人，产品人，运营人，跟单人,创建人信息

        var orderRelatedPersons =
            await _baseOrderService.GetRelatedPersonsInfo(new QueryOrderRelatedPersonsInfoBffInput
            {
                IsDataSecrecy = true,
                SellingPlatform = result.BaseOrder.SellingPlatform,
                SalespersonId = result.BaseOrder.SalespersonId,
                DevelopUserId = result.BaseOrder.DevelopUserId,
                OperatorUserId = result.BaseOrder.OperatorUserId,
                TrackingUserId = result.BaseOrder.TrackingUserId,
                OperatorAssistantUserId = result.BaseOrder.OperatorAssistantUserId,
                UserId = result.BaseOrder.UserId,
                BaseOrderId = result.BaseOrder.Id,
                IdType = SensitiveDataIdType.TravelLineOrder
            });

        //填充关联人员名称
        result.BaseOrder.SalespersonName = orderRelatedPersons.SalespersonName;
        result.BaseOrder.DevelopUserName = orderRelatedPersons.DevelopUserName;
        result.BaseOrder.OperatorUserName = orderRelatedPersons.OperatorUserName;
        result.BaseOrder.TrackingUserName = orderRelatedPersons.TrackingUserName;
        result.BaseOrder.UserNickName = orderRelatedPersons.UserNickName;
        result.BaseOrder.OperatorAssistantUserName = orderRelatedPersons.OperatorAssistantUserName;

        //填充关联人员手机号码 
        result.BaseOrder.SalespersonPhoneNumber = orderRelatedPersons.SalespersonPhoneNumber;
        result.BaseOrder.DevelopUserPhoneNumber = orderRelatedPersons.DevelopUserPhoneNumber;
        result.BaseOrder.OperatorUserPhoneNumber = orderRelatedPersons.OperatorUserPhoneNumber;
        result.BaseOrder.TrackingUserPhoneNumber = orderRelatedPersons.TrackingUserPhoneNumber;
        result.BaseOrder.UserPhoneNumber = orderRelatedPersons.UserPhoneNumber;
        result.BaseOrder.OperatorAssistantUserPhoneNumber = orderRelatedPersons.OperatorAssistantUserPhoneNumber;

        #endregion

        result.OrderFields.ForEach(temp =>
        {
            temp.Fields.ForEach(x =>
            {
                x.FieldValue = _orderFieldInformationService.SensitiveData(new Services.FieldSensitiveDataDto(x, result.BaseOrder.Id,
                    SensitiveDataIdType.TravelLineOrder, temp.TemplateType));
            });
            // 订单详情不需要信息提示
            temp.Fields = temp.Fields.Where(x => x.FieldType != FieldsType.Label).OrderBy(x => x.Sort).ToList();
            temp.Fields.ForEach(x =>
            {
                x.FieldValue ??= "";
            });
        });
        return Ok(result);
    }

    /// <summary>
    /// 认领
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Claim(OrderClaimInput input)
    {
        var cliamInput = new ClaimInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = GetOperationUser()
        };
        await _orderApiCaller.TravelLineOrderClaim(cliamInput);
        return Ok();
    }

    /// <summary>
    /// 确认
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Confirm(OrderConfirmInput input)
    {

        var confirmInput = new ConfirmInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = GetOperationUser()
        };
        await _orderApiCaller.TravelLineOrderConfirm(confirmInput);
        return Ok();
    }

    /// <summary>
    /// 完结订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Finish(OrderFinishInput input)
    {
        var cliamInput = new FinishInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = GetOperationUser()
        };
        await _orderApiCaller.TravelLineOrderFinish(cliamInput);
        return Ok();
    }

    /// <summary>
    /// 发送确认短信
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation, ErrorTypes.Notify.NotifyNotOpen)]
    public async Task<IActionResult> SendConfirmSms(OrderSendConfirmSmsInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var sendConfirmSmsInput = new SendConfirmSmsInput
        {
            BaseOrderId = input.BaseOrderId,
            TrackingUserId = currentUser.UserId,
        };
        await _orderApiCaller.TravelLineOrderSendConfirmSms(sendConfirmSmsInput);
        return Ok();
    }

    /// <summary>
    /// 更新导游确认信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation, ErrorTypes.Order.OrderFieldValidater)]
    public async Task<IActionResult> UpdateTourGuide(UpdateOrderTourGuideInput input)
    {
        UpdateTourGuideInput updateTourGuideInput = new()
        {
            OperationUser = GetOperationUser(),
            OrderFields = input.OrderFields,
        };
        var tempResult = await _productApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(new GetProductTempFieldsDetailInput()
        {
            ProductId = input.LineProductId,
            ProductSkuId = input.LineProductSkuId,
            ProductType = ProductType.Line
        });
        var orderFields = _mapper.Map<List<SaveOrderFieldInformationTypeDto>>(input.OrderFields);
        List<ProductInformationTemplateDetail> checkTemplates = new List<ProductInformationTemplateDetail>();
        foreach (var orderField in orderFields)
        {
            var templates = tempResult.Templates.Where(x => x.ProductTemplateType == orderField.ProductTemplateType && x.TemplateType == orderField.TemplateType);
            foreach (var template in templates)
            {
                // 因为提交时，没改动的不会提交，所以排除没改动的
                var codes = orderField.Fields.Select(x => x.FieldCode).ToList();
                template.Fields = template.Fields.Where(x => codes.Contains(x.FieldCode)).ToList();
                checkTemplates.Add(template);
            }
        }
        // 验证模板参数
        _orderFieldInformationService.Validator(checkTemplates, orderFields, false);

        await _orderApiCaller.TravelLineOrderUpdateTourGuide(updateTourGuideInput);
        return Ok();
    }

    /// <summary>
    /// 可退款信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(OrderRefundableOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderCannotRefund, ErrorTypes.Order.RefundRefunseByOffsetOrder)]
    public async Task<IActionResult> OrderRefundable(RefundableInput input)
    {
        var result = await _orderApiCaller.TravelLineOrderRefundable(new OrderRefundableInput
        {
            BaseOrderId = input.BaseOrderId,
            OperationUser = GetOperationUser()
        });
        return Ok(result);
    }

    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.OrderCannotRefund, ErrorTypes.Order.RefundAmountInvalid)]
    public async Task<IActionResult> Refund(Models.TravelLineOrder.RefundInput input)
    {
        await _orderApiCaller.TravelLineOrderRefund(new OrderRefundInput
        {
            BaseOrderId = input.BaseOrderId,
            RefundAmount = input.RefundAmount,
            Reason = input.Reason,
            ProofImgs = input.ProofImgs,
            OperationUser = GetOperationUser()
        });
        return Ok();
    }

    /// <summary>
    /// 获取渠道订单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetLineChannelOrderInfoBffOutput))]
    public async Task<IActionResult> GetChannelOrderInfo(GetLineChannelOrderInfoBffInput input)
    {
        var result = await GetChannelOrderInfoProcess(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询线路同步失败异常订单详情
    /// </summary>
    /// <param name="syncFailOrderId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt(200, typeof(GetSyncFailLineOrderDetailBffOutput))]
    public async Task<IActionResult> GetOpenChannelSyncFailOrderDetail(long syncFailOrderId)
    {
        var result = await SyncFailOrderDetailProcess(new GetOpenChannelSyncFailOrderInput
        {
            SyncFailOrderId = syncFailOrderId
        });
        return Ok(result);
    }


    /// <summary>
    /// 创建手工单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default,
        ErrorTypes.Order.LineRallyPointNotExists,
        ErrorTypes.Order.NumberOfRoomsOutOfRange,
        ErrorTypes.Order.RoomDiffQuantityInvalid,
        ErrorTypes.Order.TravelerNotCompleted,
        ErrorTypes.Order.CalendarNotEnable,
        ErrorTypes.Product.ProductDisabled,
        ErrorTypes.Inventory.ProductInventoryNotEnough,
        ErrorTypes.Order.OrderFieldChange,
        ErrorTypes.Order.OrderFieldValidater,
        ErrorTypes.Order.SyncFailOrderStatusChanged)]
    public async Task<IActionResult> Create(CreateInput input)
    {
        var tempResult = await _productApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(new GetProductTempFieldsDetailInput()
        {
            ProductId = input.LineProductId,
            ProductSkuId = input.LineProductSkuId,
            ProductType = ProductType.Line
        });
        //检查校验提交参数
        CheckOrderCreateInput(new OrderCreateInput
        {
            LineProductId = input.LineProductId,
            LineProductSkuId = input.LineProductSkuId,
            LineProductRallyPointId = input.LineProductRallyPointId,
            NumberOfRooms = input.NumberOfRooms,
            OrderTravelers = input.OrderTravelers,
            TravelDate = input.TravelDate,
            OrderPrices = input.OrderPrices.Select(x => new OrderPriceInput
            {
                Type = x.Type,
                Quantity = x.Quantity
            }).ToList(),
            OrderFields = input.OrderFields,
            CheckTravelerInfo = false
        }, tempResult.Templates);

        // 验证模板参数
        _orderFieldInformationService.Validator(tempResult.Templates, input.OrderFields, true);

        // 新订单是没有订单确认信息的
        var orderSureTemp = tempResult.Templates.FirstOrDefault(x => x.TemplateType == TemplateType.Order);
        if (orderSureTemp != null)
        {
            input.OrderFields.Add(_mapper.Map<SaveOrderFieldInformationTypeDto>(orderSureTemp));
        }

        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var agency = await _tenantApiCaller.GetAgencyDetail(input.AgencyId);

        //获取产品sku 价格信息 手工单无需校验产品规格上下架等状态信息
        var lineProductResponse = await _travelLineOrderService.GetLineProductResponse(input.LineProductId, input.TravelDate, false, false);
        var lineProductSkuResponse = await _travelLineOrderService.GetLineProductSkuResponse(
            new GetLineProductSkuResponseInputDto
            {
                LineProductSkuId = input.LineProductSkuId,
                TravelDate = input.TravelDate,
                OrderPrices = input.OrderPrices.Select(x => new OrderPriceInput
                {
                    Type = x.Type,
                    Quantity = x.Quantity,
                    SkuTypeItemId = x.SkuTypeItemId
                }).ToList(),
                NumberOfRooms = input.NumberOfRooms,
                CheckProductSku = false,
            }
        );
        var priceAndQuantityInfo = lineProductSkuResponse.PriceAndQuantityInfos
            .FirstOrDefault(x => x.Date == input.TravelDate);

        //查询sku票种信息
        var skuTypeItemIds = input.OrderPrices.Where(x => x.SkuTypeItemId.HasValue)
            .Select(x => x.SkuTypeItemId!.Value)
            .ToList();
        var lineProductSkuTypeItems = new List<GetLineSkuTypeItemDto>();
        if (skuTypeItemIds.Any())
        {
            var skuTypeItemsResponse = await _productApiCaller.QueryLineSkuTypeItems(
                new QueryLineSkuTypeItemInput
                {
                    LineProductId = lineProductSkuResponse.LineProductId,
                    LineProductSkuId = input.LineProductSkuId,
                    SkuTypeItemIds = skuTypeItemIds
                });
            lineProductSkuTypeItems = skuTypeItemsResponse.Select(x => new GetLineSkuTypeItemDto
            {
                SkuTypeItemId = x.SkuTypeItemId,
                SkuTypeItemName = x.SkuTypeItemName,
                ActivityId = x.SupplierProductId,
                PackageId = x.SupplierOptionId,
                SkuId = x.SupplierSkuId,
                SkuPriceType = x.SkuPriceType,
                CostDiscountRate = x.CostDiscountRate
            }).ToList();
        }

        //多币种
        string paymentCurrencyCode = agency.CurrencyCode;
        var priceExchangeRate = await GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = lineProductResponse.CostCurrencyCode,
            SaleCurrencyCode = lineProductResponse.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });

        var travelLineOrderMultPrices = input.OrderPrices.Select(x =>
        {
            var priceResponse = priceAndQuantityInfo.SkuCalendarPriceInfos.FirstOrDefault(p => p.Type == x.Type && p.SkuTypeItemId == x.SkuTypeItemId);
            return new TravelLineOrderMultPriceDto
            {
                Type = x.Type,
                Quantity = x.Quantity,
                LineSkuTypeItemId = x.SkuTypeItemId,
                OrderMultPrice = new Contracts.Common.Order.DTOs.OrderMultPriceDto
                {
                    Quantity = x.Quantity,
                    OrgCostPrice = priceResponse.CostPrice!.Value,
                    CostPrice = x.CostPrice,
                    CostCurrencyCode = lineProductResponse.CostCurrencyCode,
                    OrgPrice = Math.Round(x.SalePrice / priceExchangeRate.ExchangeRate, 2),
                    Price = x.SalePrice,
                    SaleCurrencyCode = lineProductResponse.SaleCurrencyCode,
                    PaymentCurrencyCode = paymentCurrencyCode,
                    CostExchangeRate = priceExchangeRate.CostExchangeRate,
                    ExchangeRate = priceExchangeRate.ExchangeRate,
                }
            };
        }).ToList();
        OrderDiscountItemDto? orderDiscountItemDto = input.DiscountAmount > 0 ?
            new OrderDiscountItemDto { DiscountAmount = input.DiscountAmount, DiscountType = OrderDiscountType.None } : null;

        var sellingPlatform = SellingPlatform.System;
        //处理失败异常订单创建
        if (input.SyncFailOrderId.HasValue)
        {
            var syncFailOrder =
                await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(new GetOpenChannelSyncFailOrderInput
                {
                    SyncFailOrderId = input.SyncFailOrderId.Value
                });

            //异常单状态判断
            if (syncFailOrder.SyncStatus != OpenChannelFailOrderSyncStatus.SyncFail)
            {
                throw new BusinessException(ErrorTypes.Order.SyncFailOrderStatusChanged);
            }
            sellingPlatform = syncFailOrder.SellingPlatform;
            input.SellingChannel = syncFailOrder.SellingChannels;
        }

        CreateDto createDto = new()
        {
            SyncFailOrderId = input.SyncFailOrderId,
            TenantId = currentUser.Tenant,
            SellingChannel = input.SellingChannel,
            SellingPlatform = sellingPlatform,
            LineProduct = lineProductResponse,
            LineProductSku = lineProductSkuResponse,
            TravelDate = input.TravelDate,
            NumberOfRooms = input.NumberOfRooms,
            PaymentCurrencyCode = paymentCurrencyCode,
            TravelLineOrderMultPrices = travelLineOrderMultPrices,
            OrderDiscountItem = orderDiscountItemDto,
            OrderTravelers = input.OrderTravelers,
            LineProductRallyPointId = input.LineProductRallyPointId,
            OrderUserInfo = new OrderUserInfoDto
            {
                UserId = currentUser.UserId,
                UserNickName = currentUser.NickName,
                UserType = UserType.Agency,
                AgencyId = agency.Id,
                AgencyName = agency.FullName,
                SalespersonId = agency.SalespersonId,
                SalespersonName = agency.SalespersonName,
            },
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            Remark = input.Message,//租户侧备注信息
            SupplierOrderRemark = input.SupplierOrderRemark,//供应商采购备注
            ChannelOrderNo = string.Join(",", input.ChannelOrderNo),
            SupplierOrderId = input.SupplierOrderId,
            OperationUser = GetOperationUser(),
            OrderFields = input.OrderFields,
            LineProductSkuTypeItems = lineProductSkuTypeItems,
            OrderExtraInfos = input.OrderExtraInfos
        };

        var result = await _orderApiCaller.TravelLineOrderCreate(createDto);
        return Ok(result.BaseOrderId);
    }

    /// <summary>
    /// 修改线路订单联系人
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.UpdateUpdateTravelLineOrderContactNotNull)]
    public async Task<IActionResult> UpdateContact(UpdateContactBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var operationUser = GetOperationUser();

        var updateContactInput = _mapper.Map<UpdateContactInput>(input);
        updateContactInput.OperationUser = operationUser;
        var result = await _orderApiCaller.UpdateTravelLineOrderContact(updateContactInput);

        return Ok(result);
    }

    /// <summary>
    /// 修改出行信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Order.UpdateTravelLineOrderTravelerNotNull)]
    public async Task<IActionResult> UpdateTravelInfo(UpdateTravelInfoBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var operationUser = GetOperationUser();

        var updateTravelerInput = _mapper.Map<UpdateTravelInfoInput>(input);
        if (input.LineProductRallyPointId.HasValue)
        {
            var product = await _productApiCaller.GetLineProduct(input.LineProductId);
            var rallyPoint = product.RallyPoints.Where(x => x.Id == input.LineProductRallyPointId).FirstOrDefault();
            if (rallyPoint != null)
            {
                updateTravelerInput.RallyPointAddress = rallyPoint.Address;
                updateTravelerInput.RallyPointTime = rallyPoint.Time;
                updateTravelerInput.RallyPointLatitude = rallyPoint.Latitude;
                updateTravelerInput.RallyPointLongitude = rallyPoint.Longitude;
            }
        }
        updateTravelerInput.OperationUser = operationUser;

        var result = await _orderApiCaller.UpdateTravelLineOrderTravelInfo(updateTravelerInput);

        return Ok(result);
    }

    /// <summary>
    /// 替换订单产品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200)]
    [SwaggerResponseExt(default, ErrorTypes.Order.NoMatchingData, ErrorTypes.Common.NotSupportedOperation,
        ErrorTypes.Common.ProductInvalid, ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> ReplaceOrderProduct(ReplaceOrderProductBffInput input)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();

        //查询产品详情
        var lineProductResponse = await _productApiCaller.GetLineProductV2(input.LineProductId);
        if (lineProductResponse == null)
            throw new BusinessException(ErrorTypes.Common.ProductInvalid);

        //查询套餐详情
        var lineProductSkuResponse = await _productApiCaller.LineProductSkuDetail(input.LineProductSkuId);
        if (lineProductSkuResponse == null)
            throw new BusinessException(ErrorTypes.Common.ProductInvalid);

        //查询套餐票种数据
        var lineProductSkuTypeItems = new List<QueryLineSkuTypeItemOutput>();
        input.SkuTypeItems = input.SkuTypeItems.Where(x => x.Quantity > 0).ToList();
        var inputSkuItemIds = input.SkuTypeItems.Where(x => x.SkuTypeItemId.HasValue)
            .Select(x => x.SkuTypeItemId!.Value).ToList();
        if (inputSkuItemIds.Any() is false)
        {
            if (lineProductResponse.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)//接口对接产品 查无对应票种
                throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        }
        else
        {
            lineProductSkuTypeItems = await _productApiCaller.QueryLineSkuTypeItems(new QueryLineSkuTypeItemInput
            {
                LineProductId = input.LineProductId,
                LineProductSkuId = input.LineProductSkuId,
                SkuTypeItemIds = inputSkuItemIds,
            });
        }

        //查询供应商配置
        var supplierInfo = (await _tenantApiCaller.QuerySuppliers(new QuerySuppliersInput
        {
            SupplierIds = new List<long> { lineProductResponse.SupplierId }
        })).FirstOrDefault();
        if (supplierInfo == null) throw new BusinessException(ErrorTypes.Tenant.SupplierInvalid);

        //查询对应日历价
        var schedule = await _productApiCaller.GetLineProductSkuCalendarPriceBySkuId(
            new Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice.GetLineProductSkuCalendarPriceInput
            {
                LineProductSkuId = input.LineProductSkuId,
                LineSkuTypeItemIds = inputSkuItemIds,
                StartDate = input.TravelDate,
                EndDate = input.TravelDate
            });
        var skuCalendarPriceInfos = schedule.PriceAndQuantityInfos
            .FirstOrDefault(x => x.Date == input.TravelDate)
            .SkuCalendarPriceInfos;

        var priceExchangeRate = await GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = lineProductResponse.CostCurrencyCode,
            SaleCurrencyCode = lineProductResponse.SaleCurrencyCode,
            PaymentCurrencyCode = lineProductResponse.SaleCurrencyCode,
        });

        var replaceRequest = new ReplaceOrderProductInput
        {
            BaseOrderId = input.BaseOrderId,
            TravelDate = input.TravelDate,
            LineProductId = input.LineProductId,
            LineProductSkuId = input.LineProductSkuId,
            SupplierApiType = supplierInfo.SupplierApiType,
            SupplierId = lineProductResponse.SupplierId,
            LineProductDetail = lineProductResponse,
            LineProductSkuDetail = lineProductSkuResponse,
            OperationUserDto = new OperationUserDto
            {
                UserId = currentUser.UserId,
                Name = currentUser.NickName,
                UserType = UserType.Merchant
            },
            SkuTypeItems = input.SkuTypeItems.Select(x =>
                {
                    var scheduleItem = skuCalendarPriceInfos.FirstOrDefault(s =>
                        s.Type == x.SkuPriceType && s.SkuTypeItemId == x.SkuTypeItemId);

                    if (scheduleItem == null)
                        throw new BusinessException(ErrorTypes.Order.CalendarNotEnable);
                    if (scheduleItem.Price == null || scheduleItem.CostPrice == null)
                        throw new BusinessException(ErrorTypes.Order.CalendarNotEnable);

                    var skuTypeItemInfo =
                        lineProductSkuTypeItems.FirstOrDefault(s => s.SkuTypeItemId == x.SkuTypeItemId);

                    var typeItem = new ReplaceOrderProductSkuTypeItem
                    {
                        SkuPriceType = x.SkuPriceType,
                        SkuTypeItemId = x.SkuTypeItemId,
                        SkuTypeItemName = skuTypeItemInfo?.SkuTypeItemName,
                        CostPrice = x.CostPrice,
                        OrgCostPrice = scheduleItem.CostPrice!.Value,
                        CostCurrencyCode = supplierInfo.CurrencyCode,
                        CostExchangeRate = priceExchangeRate.CostExchangeRate,
                        Price = x.Price,
                        OrgPrice = scheduleItem.Price!.Value,
                        Quantity = x.Quantity,
                        ActivityId = skuTypeItemInfo?.SupplierProductId,
                        PackageId = skuTypeItemInfo?.SupplierOptionId,
                        SkuId = skuTypeItemInfo?.SupplierSkuId
                    };
                    return typeItem;
                })
            .ToList()
        };
        await _orderApiCaller.ReplaceLineOrderProduct(replaceRequest);
        return Ok();
    }

    #region private

    private OperationUserDto GetOperationUser()
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        return new Contracts.Common.Order.DTOs.OperationUserDto
        {
            UserId = currentUser.UserId,
            Name = currentUser.NickName,
            UserType = UserType.Merchant
        };
    }

    private static void CheckOrderCreateInput(OrderCreateInput input, List<ProductInformationTemplateDetail> templates)
    {
        if (input.NumberOfRooms > 0) //包含住宿
        {
            //校验房间数(成人&长者)
            var roomsPriceTypes = new[] { LineSkuPriceType.Adult, LineSkuPriceType.Elderly };
            var adultCount = input.OrderPrices.Where(x => roomsPriceTypes.Contains(x.Type)).Sum(x => x.Quantity); //成人数
            var minRooms = (int)Math.Ceiling((decimal)adultCount / 2); //最少间数
            var maxRooms = adultCount; //最多房间数
            if (input.NumberOfRooms < minRooms || input.NumberOfRooms > maxRooms)
                throw new BusinessException(ErrorTypes.Order.NumberOfRoomsOutOfRange);
        }
        else
        {
            if (input.OrderPrices.Any(x => x.Type == LineSkuPriceType.RoomPriceDifference))
                throw new BusinessException(ErrorTypes.Order.RoomDiffQuantityInvalid);
        }

        if (input.CheckTravelerInfo)
        {
            //检查出行人信息
            foreach (var orderPrice in input.OrderPrices)
            {
                if (orderPrice.Type == LineSkuPriceType.RoomPriceDifference) continue; //排除房差
                ProductTemplateType? productTemplateType = null;
                switch (orderPrice.Type)
                {
                    case LineSkuPriceType.Adult:
                        productTemplateType = ProductTemplateType.EachAdult;
                        break;
                    case LineSkuPriceType.Child:
                        productTemplateType = ProductTemplateType.EachChild;
                        break;
                    case LineSkuPriceType.Baby:
                        productTemplateType = ProductTemplateType.EachBaby;
                        break;
                    case LineSkuPriceType.Elderly:
                        productTemplateType = ProductTemplateType.EachElderly;
                        break;
                    case LineSkuPriceType.Other:
                        productTemplateType = ProductTemplateType.EachOther;
                        break;
                }

                var travelerCount = input.OrderFields.Count(x => x.ProductTemplateType == productTemplateType);
                var tempCount = templates.Count(x => x.ProductTemplateType == productTemplateType); // 无模板的判断
                var fieldCount = templates.FirstOrDefault(x => x.ProductTemplateType == productTemplateType)?.Fields
                    .Count() ?? 0; // 空模板判断
                if (tempCount > 0 && fieldCount > 0 && travelerCount != orderPrice.Quantity)
                {
                    throw new BusinessException(ErrorTypes.Order.TravelerNotCompleted); //出行人不完整
                }
            }
        }
    }

    private async Task<OrderPriceExchangeRateOutput> GetOrderPriceExchange(OrderPriceExchangeRateInput input)
    {
        var costCurrencyCode = input.CostCurrencyCode;
        var saleCurrencyCode = input.SaleCurrencyCode;
        var paymentCurrencyCode = input.PaymentCurrencyCode;
        List<GetExchangeRatesInput> exchangeRatesInputs = new();
        var costEqualsSale = costCurrencyCode.Equals(saleCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!costEqualsSale)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = costCurrencyCode,
                TargetCurrencyCode = saleCurrencyCode
            });
        }

        var saleEqualsPayment = saleCurrencyCode.Equals(paymentCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!saleEqualsPayment)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = saleCurrencyCode,
                TargetCurrencyCode = paymentCurrencyCode
            });
        }

        var exchangeRates = await _paymentApiCaller.GetExchangeRates(exchangeRatesInputs);
        return new OrderPriceExchangeRateOutput
        {
            CostExchangeRate = costEqualsSale
                ? 1
                : exchangeRates
                    .Where(x => x.BaseCurrencyCode == costCurrencyCode && x.TargetCurrencyCode == saleCurrencyCode)
                    .First().ExchangeRate,
            ExchangeRate = saleEqualsPayment
                ? 1
                : exchangeRates
                    .Where(x => x.BaseCurrencyCode == saleCurrencyCode && x.TargetCurrencyCode == paymentCurrencyCode)
                    .First().ExchangeRate,
        };
    }

    /// <summary>
    /// 获取渠道订单详情处理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<GetLineChannelOrderInfoBffOutput> GetChannelOrderInfoProcess(
        GetLineChannelOrderInfoBffInput input)
    {
        GetLineChannelOrderInfoBffOutput? result;
        var channelOrderNo = input.ChannelOrderNo;
        long? lineProductId = null;
        if (input.LineProductSkuId.HasValue)
        {
            var lineSkuInfo = await _productApiCaller.GetLineProductSkuDetail(input.LineProductSkuId!.Value);
            lineProductId = lineSkuInfo.LineProductId;
        }

        //优先查询渠道端的订单信息
        var queryChannelOrderRequest = new QueryChannelOrderDetailInput
        {
            OtaOrderId = channelOrderNo,
            SellingPlatform = SellingPlatform.Fliggy //目前只支持飞猪
        };
        var openChannelOrderDetail = await _orderApiCaller.QueryChannelOrderDetail(queryChannelOrderRequest);
        if (openChannelOrderDetail.Passengers.Any()) //查到信息
        {
            openChannelOrderDetail.OutSkuId = input.LineProductSkuId.ToString();
            var skuTypeItems = await _productApiCaller.QueryLineSkuTypeItems(new QueryLineSkuTypeItemInput
            {
                LineProductSkuId = input.LineProductSkuId
            });
            var lineOrderTotalFee = openChannelOrderDetail.OrderItems.Sum(x => x.CostPrice);
            var lineOrderDiscountFee = openChannelOrderDetail.OrderItems.Sum(x => x.DiscountFee);
            var otaInputData = ConvertChannelOrderToOTAInput(openChannelOrderDetail);
            var lineChannelOrderInfo = new GetLineChannelOrderInfoOutput
            {
                ChannelOrderNo = input.ChannelOrderNo,
                PaymentAmount = lineOrderTotalFee / 100,
                DiscountAmount = lineOrderDiscountFee / 100,
                ContactsEmail = otaInputData.ContactsEmail,
                ContactsName = otaInputData.ContactsName,
                ContactsPhoneNumber = otaInputData.ContactsPhoneNumber,
                OrderFields = await GetFailOrderFields(otaInputData)
            };
            lineChannelOrderInfo.OrderPrices = openChannelOrderDetail.OrderItems
                .GroupBy(x => new { x.SaleType, x.OutSkuId })
                .Select(x =>
                {
                    var orderPrice = new TravelLineOrderPriceOutput
                    {
                        Type = _openPlatformService.MapOtaAgeSaleTypeToLineSkuPriceType(x.Key.SaleType),
                        Quantity = x.Sum(s => s.Quantity),
                        Price = x.First().Price / 100,
                    };
                    if (long.TryParse(x.Key.OutSkuId, out var skuTypeItemId))
                    {
                        var skuTypeItem = skuTypeItems.FirstOrDefault(s => s.SkuTypeItemId == skuTypeItemId);
                        orderPrice.OrderSubItemId = skuTypeItem?.SkuTypeItemId ?? null;
                        orderPrice.OrderSubItemName = skuTypeItem?.SkuTypeItemName ?? null;
                    }

                    return orderPrice;
                })
                .ToList();
            result = _mapper.Map<GetLineChannelOrderInfoBffOutput>(lineChannelOrderInfo);
            return result;
        }

        var channelOrderInfoResponse = await _orderApiCaller.GetLineChannelOrderInfo(channelOrderNo);
        if (channelOrderInfoResponse == null)
        {
            channelOrderInfoResponse = new GetLineChannelOrderInfoOutput
            {
                ChannelOrderNo = channelOrderNo
            };
            //查询同步失败的订单数据
            var syncFailOrder = await SyncFailOrderDetailProcess(new GetOpenChannelSyncFailOrderInput
            {
                ChannelOrderNo = channelOrderNo,
                LineProductId = lineProductId,
                LineProductSkuId = input.LineProductSkuId
            });
            if (syncFailOrder != null)
            {
                channelOrderInfoResponse = new GetLineChannelOrderInfoOutput
                {
                    ChannelOrderNo = syncFailOrder.ChannelOrderNo.FirstOrDefault(),
                    PaymentAmount = syncFailOrder.PaymentAmount,
                    DiscountAmount = syncFailOrder.DiscountAmount,
                    ContactsName = syncFailOrder.ContactsName,
                    ContactsPhoneNumber = syncFailOrder.ContactsPhoneNumber,
                    ContactsEmail = syncFailOrder.ContactsEmail,
                    OrderTravelers = syncFailOrder.OrderTravelers,
                    OrderPrices = syncFailOrder.OrderPrices,
                    OrderFields = syncFailOrder.OrderFields,
                    OrderExtraInfos = syncFailOrder.OrderExtraInfos
                };
            }
        }

        result = _mapper.Map<GetLineChannelOrderInfoBffOutput>(channelOrderInfoResponse);
        result?.OrderFields.ForEach(x =>
        {
            x.Id = 0; // 因为是会累加在原来出行人后面，当新增的处理，所以id返回 0 
        });
        return result;
    }

    /// <summary>
    /// 同步失败订单详情处理
    /// </summary>
    /// <param name="syncFailOrderId"></param>
    /// <returns></returns>
    private async Task<GetSyncFailLineOrderDetailBffOutput> SyncFailOrderDetailProcess(GetOpenChannelSyncFailOrderInput input)
    {
        input.OrderType = OrderType.TravelLineOrder;
        var response = await _orderApiCaller.GetOpenChannelSyncFailOrderDetail(input);
        var result = _mapper.Map<GetSyncFailLineOrderDetailBffOutput>(response);
        if (result == null)
        {
            return null;
        }

        //替换产品id
        if (input.LineProductSkuId.HasValue)
        {
            result.ProductId = input.LineProductId;
            result.SkuId = input.LineProductSkuId;
        }

        //处理供应商信息
        if (result.SupplierId.HasValue)
        {
            var supplierInfo = (await _tenantApiCaller.GetSupplierShortInfo(new ShortInfoInput
            {
                SupplierIds = new List<long> { result.SupplierId.Value },
                IsEnabled = false
            }))
                .FirstOrDefault();
            result.SupplierName = supplierInfo?.SupplierFullName;
        }

        //处理分销商信息
        if (result.AgencyId.HasValue)
        {
            var agencyInfo = await _tenantApiCaller.GetAgencyDetail(result.AgencyId.Value);
            result.AgencyName = agencyInfo?.FullName;
            result.AgencyType = agencyInfo?.AgencyType;
            result.AgencyApiType = agencyInfo?.AgencyApiType;
        }

        //查询产品信息
        OpenSupplierType? openSupplierType = null;
        if (result.ProductId.HasValue)
        {
            var productResponse = (await _productApiCaller.GetLineProductDetail(result.ProductId.Value)).FirstOrDefault();
            if (productResponse != null)
            {
                result.Days = productResponse.Days;
                result.Nights = productResponse.Nights;
                if (productResponse.PurchaseSourceType == LineProductPurchaseSourceType.InterfaceDock)
                    openSupplierType =
                        _openPlatformService.MapPriceInventorySourceToOpenSupplierType(productResponse
                            .PriceInventorySource);
            }
        }

        //处理dataJson
        var lineSyncJsonData = DataContentJsonConvert(response.DataContentJson);
        lineSyncJsonData.LineProductSkuId ??= result.SkuId;
        var discountFee = lineSyncJsonData.DiscountFee / 100;
        result.DiscountAmount = discountFee;
        result.OrderTravelers = _mapper.Map<List<OrderTravelerOutput>>(lineSyncJsonData.TravelerItems);
        result.OrderFields = await GetFailOrderFields(lineSyncJsonData);

        var skuTypeItemsResponse = await _productApiCaller.QueryLineSkuTypeItems(
            new QueryLineSkuTypeItemInput
            {
                LineProductSkuId = result.SkuId
            });
        if (skuTypeItemsResponse.Any())
        {
            foreach (var orderPriceItem in lineSyncJsonData.OrderPriceItems)
            {
                if (orderPriceItem.SkuTypeItemId.HasValue) continue;

                // 无对应票种ID.[年龄段]类型进行票种匹配
                var relateSkuTypeItems = skuTypeItemsResponse
                    .Where(x => x.SkuPriceType == orderPriceItem.LineSkuPriceType).ToList();

                if (relateSkuTypeItems.Any() is false) continue;

                // 匹配赋值
                orderPriceItem.SkuTypeItemId = relateSkuTypeItems.First().SkuTypeItemId;
            }

            var skuTypeItemIds = lineSyncJsonData.OrderPriceItems.Where(x => x.SkuTypeItemId.HasValue)
                .Select(x => x.SkuTypeItemId!.Value).ToList();
            skuTypeItemsResponse = skuTypeItemsResponse.Where(x => skuTypeItemIds.Contains(x.SkuTypeItemId)).ToList();
        }
        if (skuTypeItemsResponse.Any() && lineSyncJsonData.ExtraInfoIds.Any())
        {
            var matchExtraInfoRequest = new CreateOrderMatchExtraInfosDto
            {
                MatchExtraInfoIds = lineSyncJsonData.ExtraInfoIds,
                OpenSupplierType = openSupplierType!.Value,
                ProductId = skuTypeItemsResponse.Select(x => x.SupplierProductId).First(),
                OptionId = skuTypeItemsResponse.FirstOrDefault()?.SupplierOptionId,
                SkuIds = skuTypeItemsResponse.Where(x => !string.IsNullOrEmpty(x.SupplierSkuId))
                    .Select(x => x.SupplierSkuId).Distinct().ToList()
            };
            result.OrderExtraInfos = await _openChannelOrderService.CreateOrderMatchExtraInfos(matchExtraInfoRequest);
        }
        result.OrderPrices = lineSyncJsonData.OrderPriceItems
            .GroupBy(x => new { x.LineSkuPriceType, x.SkuTypeItemId })
            .Select(x =>
            {
                var orderPrice = new TravelLineOrderPriceOutput
                {
                    Type = x.Key.LineSkuPriceType,
                    Quantity = x.Sum(s => s.Quantity),
                    Price = x.First().Price!.Value / 100,
                };

                if (x.Key.SkuTypeItemId.HasValue)
                {
                    var skuTypeItem = skuTypeItemsResponse.FirstOrDefault(s => s.SkuTypeItemId == x.Key.SkuTypeItemId);
                    orderPrice.OrderSubItemId = skuTypeItem?.SkuTypeItemId ?? null;
                    orderPrice.OrderSubItemName = skuTypeItem?.SkuTypeItemName ?? null;
                }

                return orderPrice;
            })
            .ToList();

        return result;
    }

    /// <summary>
    /// 异常订单详情转换为正常订单详情
    /// </summary>
    /// <param name="syncFailOrderDetail"></param>
    /// <returns></returns>
    private GetTravelLineOrderDetailBffOutput ConvertFailOrderToNormalOrderDetail(
        GetSyncFailLineOrderDetailBffOutput? syncFailOrderDetail)
    {
        var result = new GetTravelLineOrderDetailBffOutput();

        #region BaseOrderInfo

        result.BaseOrder = new TravelLineBaseOrderDetailBffOutput();
        result.BaseOrder.Id = syncFailOrderDetail.SyncFailOrderId;
        result.BaseOrder.AgencyName = syncFailOrderDetail.AgencyName;
        result.BaseOrder.ChannelOrderNo = syncFailOrderDetail.ChannelOrderNo;
        result.BaseOrder.ContactsName = syncFailOrderDetail.ContactsName;
        result.BaseOrder.ContactsPhoneNumber = syncFailOrderDetail.ContactsPhoneNumber;
        result.BaseOrder.ContactsEmail = syncFailOrderDetail.ContactsEmail;
        result.BaseOrder.CreateTime = syncFailOrderDetail.CreateTime;
        result.BaseOrder.SellingChannels = syncFailOrderDetail.SellingChannels;
        result.BaseOrder.SellingPlatform = syncFailOrderDetail.SellingPlatform;
        result.BaseOrder.Status = TravelLineOrderMixStatus.ChannelAbnormal;
        result.BaseOrder.PaymentAmount = syncFailOrderDetail.PaymentAmount;
        result.BaseOrder.TotalAmount = syncFailOrderDetail.PaymentAmount + syncFailOrderDetail.DiscountAmount;
        result.BaseOrder.PaymentCurrencyCode = syncFailOrderDetail.CurrencyCode;
        result.BaseOrder.ProductName = syncFailOrderDetail.ProductName;
        result.BaseOrder.ProductSkuName = syncFailOrderDetail.SkuName;

        #endregion

        #region TravelLineOrder

        result.TravelLineOrder = new TravelLineOrderDetailBffOutput();
        result.TravelLineOrder.SupplierId = syncFailOrderDetail.SupplierId ?? 0;
        result.TravelLineOrder.LineProductId = syncFailOrderDetail.ProductId ?? 0;
        result.TravelLineOrder.LineProductSkuId = syncFailOrderDetail.SkuId ?? 0;
        result.TravelLineOrder.CreateTime = null;
        result.TravelLineOrder.TravelBeginDate = syncFailOrderDetail.TravelDate;
        result.TravelLineOrder.TravelEndDate = syncFailOrderDetail.TravelDate;
        if (syncFailOrderDetail.Days is > 0)
        {
            result.TravelLineOrder.TravelEndDate =
                syncFailOrderDetail.TravelDate.AddDays(syncFailOrderDetail.Days!.Value - 1);
        }

        #endregion

        #region supplyChannelInfo

        result.SupplyChannelInfo = new SupplyChannelInfo();
        result.SupplyChannelInfo.SupplierId = syncFailOrderDetail.SupplierId ?? 0;
        result.SupplyChannelInfo.SupplierName = syncFailOrderDetail.SupplierName;

        #endregion

        #region distributionChannelInfo

        result.DistributionChannelInfo = new DistributionChannelInfo();
        result.DistributionChannelInfo.ChannelOrderNo = result.BaseOrder.ChannelOrderNo;
        result.DistributionChannelInfo.AgencyId = syncFailOrderDetail.AgencyId ?? 0;
        result.DistributionChannelInfo.AgencyName = syncFailOrderDetail.AgencyName;
        ;
        result.DistributionChannelInfo.SellingChannels = syncFailOrderDetail.SellingChannels;
        result.DistributionChannelInfo.SellingPlatform = syncFailOrderDetail.SellingPlatform;

        #endregion

        #region traveler

        result.OrderTravelers = _mapper.Map<List<TravelLineOrderTravelerBffOutput>>(syncFailOrderDetail.OrderTravelers);

        #endregion

        #region orderField

        result.OrderFields = syncFailOrderDetail.OrderFields;

        #endregion

        #region orderPrice

        result.OrderPrices = syncFailOrderDetail.OrderPrices;

        #endregion

        return result;
    }

    private CreateTravelLineOTAOrderBffInput ConvertChannelOrderToOTAInput(
        QueryChannelOrderDetailOutput channelOrderDetail)
    {
        var lineOrderTravelerItems = (from lineOtaOrderPassenger in channelOrderDetail.Passengers
                                      let lineSkuPriceType =
                                          _openPlatformService.MapOtaAgeSaleTypeToLineSkuPriceType(lineOtaOrderPassenger.SaleType)
                                      select new TravelLineOTAOrderTravelerBffItem
                                      {
                                          LineSkuPriceType = lineSkuPriceType,
                                          ExtendInfo = lineOtaOrderPassenger.ExtendInfo
                                      }).ToList();

        _ = long.TryParse(channelOrderDetail.OutSkuId, out long skuId);
        var contactItem = _mapper.Map<TravelLineOTAOrderContactBffItem>(channelOrderDetail.ContactItem);
        var inputData = new CreateTravelLineOTAOrderBffInput
        {
            TravelerItems = lineOrderTravelerItems,
            SellingPlatform = channelOrderDetail.SellingPlatform,
            LineProductSkuId = skuId,
            ContactItem = contactItem,
            ContactsName = contactItem.Name,
            ContactsEmail = contactItem.Email,
            ContactsPhoneNumber = contactItem.Mobile,
        };
        return inputData;
    }

    private async Task<List<OrderFieldInformationTypeOutput>> GetFailOrderFields(
        CreateTravelLineOTAOrderBffInput syncJsonData)
    {
        List<OrderFieldInformationTypeOutput> orderFields = new List<OrderFieldInformationTypeOutput>();

        long productId = 0, productSkuId = 0;
        if (syncJsonData.LineProductSkuId is > 0)
        {
            var product = await _productApiCaller.LineProductSkuDetail(syncJsonData.LineProductSkuId!.Value);
            productId = product?.LineProductId ?? 0;
            productSkuId = product?.Id ?? 0;
        }

        // 产品不存在时使用默认模板，把字段显示
        var tempResult = await _productApiCaller.ProductInformationTemplateGetProductTempFieldsDetail(
            new GetProductTempFieldsDetailInput()
            {
                ProductId = productId,
                ProductSkuId = productSkuId,
                ProductType = ProductType.Line
            });
        var templates = tempResult.Templates;

        var channelNewOrderContact = _mapper.Map<TravelLineOTAOrderContactBffItem>(syncJsonData.ContactItem);
        if (channelNewOrderContact == null)
        {
            channelNewOrderContact = new TravelLineOTAOrderContactBffItem
            {
                Name = syncJsonData.ContactsName,
                Email = syncJsonData.ContactsEmail,
                Mobile = syncJsonData.ContactsPhoneNumber,
            };
        }

        Contracts.Common.Order.DTOs.TravelLineOrder.CreateDto dto =
            new Contracts.Common.Order.DTOs.TravelLineOrder.CreateDto()
            {
                ContactsName = syncJsonData.ContactsName,
                ContactsEmail = syncJsonData.ContactsEmail,
                ContactsPhoneNumber = syncJsonData.ContactsPhoneNumber,
                SellingPlatform = syncJsonData.SellingPlatform,
                OrderTravelers = new List<Contracts.Common.Order.DTOs.TravelLineOrder.OrderTravelerInput>()
                // SellingChannel = input.SellingChannel
            };
        foreach (var item in syncJsonData.TravelerItems)
        {
            var info = new Contracts.Common.Order.DTOs.TravelLineOrder.OrderTravelerInput()
            {
                Birthday = item.Birthday,
                CardValidDate = item.CardValidDate,
                Type = item.LineSkuPriceType,
                Email = item.Email,
                ExtendInfo = item.ExtendInfo,
                FirstName = item.FirstName,
                Gender = item.Gender,
                Height = item.Height,
                IDCard = item.IDCard,
                IdCardType = item.IdCardType,
                LastName = item.LastName,
                Name = item.Name,
                NationalityCode = item.NationalityCode,
                NationalityName = item.NationalityName,
                Phone = item.Phone,
                ShoeSize = item.ShoeSize,
                Weight = item.Weight,
            };
            dto.OrderTravelers.Add(info);
        }

        var saveFields = await _orderFieldInformationService.ConvertTravelOrderData(dto, channelNewOrderContact,
            templates,
            OrderType.TravelLineOrder);
        orderFields = _mapper.Map<List<OrderFieldInformationTypeOutput>>(saveFields);
        orderFields.ForEach(temp =>
        {
            // 订单详情不需要信息提示
            temp.Fields = temp.Fields.Where(x => x.FieldType != FieldsType.Label).OrderBy(x => x.Sort).ToList();
            temp.Fields.ForEach(x =>
            {
                x.FieldValue ??= "";
            });
        });
        return orderFields;
    }

    /// <summary>
    /// 处理不同订单类型转换后的差异数据
    /// </summary>
    /// <param name="scenicTicketSyncJsonData"></param>
    private CreateTravelLineOTAOrderBffInput DataContentJsonConvert(string dataContentJson)
    {
        var lineSyncJsonData = JsonConvert.DeserializeObject<CreateTravelLineOTAOrderBffInput>(dataContentJson);
        if (lineSyncJsonData.TravelerItems.Any() is false)
        {
            //处理订单类型变动,导致的字段不一致
            var dynamicData = JsonConvert.DeserializeObject<JObject>(dataContentJson);
            if (dynamicData.TryGetValue("TravelerInfos", StringComparison.OrdinalIgnoreCase,
                    out var travelerInfosToken))
            {
                lineSyncJsonData.TravelerItems = travelerInfosToken.ToObject<List<TravelLineOTAOrderTravelerBffItem>>();
                lineSyncJsonData.TravelerItems.ForEach(x => x.LineSkuPriceType = LineSkuPriceType.Adult);//默认 adult
            }
        }

        if (lineSyncJsonData.OrderPriceItems.Any())
        {
            //处理订单类型变动.数量兼容
            lineSyncJsonData.Quantity = lineSyncJsonData.OrderPriceItems.Sum(x => x.Quantity);

            //LineSkuPriceType 未赋值.默认赋值[成人]枚举值
            foreach (var item in lineSyncJsonData.OrderPriceItems.Where(x => x.LineSkuPriceType == default(LineSkuPriceType)))
            {
                item.LineSkuPriceType = LineSkuPriceType.Adult;
            }
        }
        else
        {
            lineSyncJsonData.OrderPriceItems = new List<TravelLineOTAOrderBffItem>
            {
                new TravelLineOTAOrderBffItem
                {
                    LineSkuPriceType = LineSkuPriceType.Adult,
                    Price = lineSyncJsonData.Price,
                    Quantity = lineSyncJsonData.Quantity
                }
            };
        }

        return lineSyncJsonData;
    }

    #endregion
}
