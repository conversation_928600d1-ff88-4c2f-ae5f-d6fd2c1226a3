using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.OpenSupplierProduct;
using Bff.Vebk.Models.ScenicTicket;
using Bff.Vebk.Services.Interfaces;
using Common.Swagger;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Product.DTOs.OpenSupplier;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class OpenSupplierProductController : ControllerBase
{
    private readonly IProductApiCaller _productApiCaller;
    private readonly IScenicSpotApiCaller _scenicApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IOpenPlatformService _openPlatformService;
    
    private readonly IMapper _mapper;
    
    public OpenSupplierProductController(
        IProductApiCaller productApiCaller,
        IScenicSpotApiCaller scenicSpotApiCaller,
        ITenantApiCaller tenantApiCaller,
        IOrderApiCaller orderApiCaller,
        IOpenPlatformService openPlatformService,
        IMapper mapper)
    {
        _productApiCaller = productApiCaller;
        _scenicApiCaller = scenicSpotApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _orderApiCaller = orderApiCaller;
        _openPlatformService = openPlatformService;
        
        _mapper = mapper;
    }
    
    /// <summary>
    /// 查询关联SKU的附加信息
    /// <value>数据源->查询API接口</value>
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetOpenSupplierProductLocationBffOutput))]
    public async Task<IActionResult> GetExtraLocation(GetOpenSupplierProductLocationBffInput input)
    {
        var result = new GetOpenSupplierProductLocationBffOutput
        {
            ProductId = input.ProductId,
            SkuId = input.SkuId,
            SupplierActivityId = input.SupplierActivityId,
            SupplierPackageId = input.SupplierPackageId,
            SupplierSkuId = input.SupplierSkuId
        };

        var openSupplierProductRequest = new GetOpenSupplierProductDetailInput();
        var filterOptionIds = new List<string>(); // 用于过滤接送位置附加信息
        var filterSkuIds = new List<string>(); // 用于过滤接送位置附加信息
        
        //目前只支持线路产品
        if (input.BaseOrderId.HasValue)
        {
            //查询订单关联的套餐子项id
            var orderSkuItems = await _orderApiCaller.QueryTravelLineOrderSkuTypeItems(new QueryTravelLineOrderSkuTypeItemInput
            {
                BaseOrderIds = new List<long> { input.BaseOrderId.Value }
            });
            input.SupplierActivityId = orderSkuItems.SkuTypeItems.FirstOrDefault()?.ActivityId;
            filterSkuIds = orderSkuItems.SkuTypeItems.Select(x => x.SkuId!).Distinct().ToList();
            filterOptionIds = orderSkuItems.SkuTypeItems.Select(x => x.PackageId!).Distinct().ToList();
        }
        else if (input.ProductId.HasValue)
        {
            var productDetail = await _productApiCaller.GetLineProductV2(input.ProductId!.Value);
            if (productDetail == null)
                throw new BusinessException(ErrorTypes.Common.ProductInvalid);

            if(productDetail.PurchaseSourceType!= LineProductPurchaseSourceType.InterfaceDock)
                throw new BusinessException(ErrorTypes.Common.ProductInvalid);

            var skuTypeItems = await _productApiCaller.QueryLineSkuTypeItems(new QueryLineSkuTypeItemInput
            {
                LineProductId = input.ProductId,
                LineProductSkuId = input.SkuId,
                SkuTypeItemIds = input.SkuTypeItemIds
            });
            if (skuTypeItems.Any() is false)
                throw new BusinessException(ErrorTypes.Common.ProductInvalid);

            input.SupplierActivityId = productDetail.OpenSupplierSettingInfo.ActivityId;
            filterOptionIds = skuTypeItems.Where(x => !string.IsNullOrEmpty(x.SupplierOptionId))
                .Select(x => x.SupplierOptionId!).Distinct().ToList();
            filterSkuIds = skuTypeItems
                .Where(x => !string.IsNullOrEmpty(x.SupplierSkuId))
                .Select(x => x.SupplierSkuId!).Distinct().ToList();
            input.SupplierApiType =
                _openPlatformService.MapPriceInventorySourceToSupplierApiType(productDetail.PriceInventorySource);
        }

        openSupplierProductRequest.SupplierApiType = input.SupplierApiType!.Value;
        openSupplierProductRequest.OutProductId = input.SupplierActivityId;
        if (string.IsNullOrEmpty(openSupplierProductRequest.OutProductId))
            return Ok(result);
        
        var openSupplierProductDetail = await _productApiCaller.GetOpenSupplierProductDetail(openSupplierProductRequest);
        var filterSkuItems = openSupplierProductDetail.SkuList
            .Where(x => filterOptionIds.Contains(x.OutProductOptionId))
            .Where(x => filterSkuIds.Contains(x.OutSkuId))
            .ToList();
        
        //合并位置数据
        result.PickUpLocationData = filterSkuItems
            .Where(x => x.PickUpLocationData != null)
            .Select(s => s.PickUpLocationData)
            .Aggregate(result.PickUpLocationData, (target, source) => MergeLocationData(source, target));

        result.DeliveryLocationData = filterSkuItems
            .Where(x => x.DeliveryLocationData != null)
            .Select(s => s.DeliveryLocationData)
            .Aggregate(result.DeliveryLocationData, (target, source) => MergeLocationData(source, target));

        return Ok(result);
    }
    
    /// <summary>
    /// 查询关联SKU的附加信息
    /// <value>数据源->基于saas维护的数据</value>
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(QueryOpenSupplierSkuExtraInfoBffOutput))]
    public async Task<IActionResult> QuerySkuExtraInfo(QueryOpenSupplierSkuExtraInfoBffInput input)
    {
        var result = new QueryOpenSupplierSkuExtraInfoBffOutput();
        
        ProductSupplierSettingData productSupplierSettingData = null;
        if (input.ProductType.HasValue)
        {
            productSupplierSettingData = await GetProductSupplierSetting(
                productType: input.ProductType!.Value,
                productId: input.ProductId!.Value,
                skuId: input.SkuId);
        }
        else if (input.OrderType.HasValue)
        {
            productSupplierSettingData = await GetOrderRelatedSupplierSetting(
                orderType: input.OrderType!.Value,
                baseOrderId: input.BaseOrderId!.Value);
        }

        if (productSupplierSettingData == null) return Ok(result);

        var request = new QueryOpenSupplierSkuExtraInfoInput
        {
            OpenSupplierTypes = new List<OpenSupplierType>{productSupplierSettingData.OpenSupplierApiType},
            ProductIds = new List<string> {productSupplierSettingData.SupplierActivityId},
            OptionIds = productSupplierSettingData.SupplierPackageIds,
            SkuIds = productSupplierSettingData.SupplierSkuIds
        };
        var response = (await _productApiCaller.QueryOpenSupplierSkuExtraInfo(request)).FirstOrDefault();
        if (response == null) return Ok(result);
        
        /*
         * 处理数据
         * 返回多个sku附加信息的选项值重复默认取第一个
         */
        result.ExtraInfos = response.Sub.SelectMany(x => x.ExtraInfos)
            .GroupBy(g => new { g.DataType, g.ValueType })
            .Select(x =>
            {
                var outputItem = new GetSkuExtraInfoItem
                {
                    DataType = x.Key.DataType,
                    ValueType = x.Key.ValueType,
                    IsRequired = x.First().IsRequired
                };
                
                // 返回多个sku附加信息的选项值重复默认取第一个
                outputItem.ValueOptions = x.SelectMany(o => o.ValueOptions)
                    .GroupBy(og => og.Key)
                    .Select(os =>
                    {
                        var firstVp = os.OrderBy(ob => ob.Id).First();
                        var valueOptionsOutItem = new GetSkuExtraInfoValueOption
                        {
                            Id = firstVp.Id,
                            Key = firstVp.Key,
                            Value = firstVp.Value
                        };
                        return valueOptionsOutItem;
                    })
                    .ToList();
                
                return outputItem;
            })
            .ToList();
        
        return Ok(result);
    }

    /// <summary>
    /// 批量查询关联SKU的附加信息
    /// <value>数据源->基于saas维护的数据</value>
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200,typeof(List<QueryOpenSupplierSkuExtraInfoListBffOutput>))]
    public async Task<IActionResult> QuerySkuExtraInfoList(QueryOpenSupplierSkuExtraInfoListBffInput input)
    {
        // 目前为了支持门票组合产品.后续完善
        var result = new List<QueryOpenSupplierSkuExtraInfoListBffOutput>();
        if (input.ProductType != ProductType.Scenic) return Ok(result);
        var request = new QueryOpenSupplierSkuExtraInfoInput();
        var tickets = await _scenicApiCaller.GetTicketInfoByIds(input.ProductIds.ToArray());
        foreach (var item in tickets.Where(x=>x.CredentialSourceType == CredentialSourceType.InterfaceDock))
        {
            request.ProductIds.Add(item.ActivityId);
            if (!string.IsNullOrEmpty(item.PackageId))
                request.OptionIds.Add(item.PackageId);
            if (!string.IsNullOrEmpty(item.SkuId))
                request.SkuIds.Add(item.SkuId);
            var supplierApiType = _openPlatformService.MapPriceInventorySourceToSupplierApiType(item.PriceInventorySource!.Value);
            var openSupplierType = _openPlatformService.MapSupplierApiTypeToOpenSupplierType(supplierApiType);
            request.OpenSupplierTypes.Add(openSupplierType);
        }

        var response = await _productApiCaller.QueryOpenSupplierSkuExtraInfo(request);
        foreach (var item in tickets.Where(x=>x.CredentialSourceType == CredentialSourceType.InterfaceDock))
        {
            var responseSub = response.Where(x => x.ProductId == item.ActivityId)
                .SelectMany(x => x.Sub)
                .Where(x => x.OptionId == item.PackageId)
                .Where(x => x.SkuId == item.SkuId)
                .ToList();
            
            /*
             * 处理数据
             * 返回多个sku附加信息的选项值重复默认取第一个
             */
            var extraInfos = responseSub.SelectMany(x => x.ExtraInfos)
                .GroupBy(g => new { g.DataType, g.ValueType })
                .Select(x =>
                {
                    var outputItem = new GetSkuExtraInfoItem
                    {
                        DataType = x.Key.DataType,
                        ValueType = x.Key.ValueType,
                        IsRequired = x.First().IsRequired
                    };
                
                    // 返回多个sku附加信息的选项值重复默认取第一个
                    outputItem.ValueOptions = x.SelectMany(o => o.ValueOptions)
                        .GroupBy(og => og.Key)
                        .Select(os =>
                        {
                            var firstVp = os.OrderBy(ob => ob.Id).First();
                            var valueOptionsOutItem = new GetSkuExtraInfoValueOption
                            {
                                Id = firstVp.Id,
                                Key = firstVp.Key,
                                Value = firstVp.Value
                            };
                            return valueOptionsOutItem;
                        })
                        .ToList();
                
                    return outputItem;
                })
                .ToList();
            
            result.Add(new QueryOpenSupplierSkuExtraInfoListBffOutput
            {
                ProductId = item.Id,
                ExtraInfos = extraInfos
            });
        }

        return Ok(result);
    }
    
    /// <summary>
    /// 查询开放平台-产品详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetOpenSupplierProductDetailBffOutput))]
    public async Task<IActionResult> GetDetail(GetOpenSupplierProductDetailBffInput input)
    {
        var result = new GetOpenSupplierProductDetailBffOutput
        {
            ActivityId = input.ActivityId
        };
        var request = _mapper.Map<GetOpenSupplierProductDetailInput>(input);
        if (!_openPlatformService.OpenSupplierApiTypes.Contains(request.SupplierApiType))
        {
            return Ok(result);
        }

        var response = await _productApiCaller.GetOpenSupplierProductDetail(request);
        result.IsInstant = response.Instant;
        result.HasContent = response.HasContent;
        result.Items = response.SkuList.GroupBy(x => x.OutProductOptionId)
            .Select(group => new GetOpenSupplierProductDetailItem
            {
                PackageId = group.Key,
                PackageName = group.First().OutProductOptionName,
                SkuIds = group.Select(s => s.OutSkuId).Distinct().ToList(),
                SkuInfos = group.Select(s => new GetOpenSupplierProductSkuInfo
                {
                    SkuId = s.OutSkuId, 
                    SkuName = s.OutSkuName,
                    CostDiscountRate = s.CommissionRate
                }).ToList()
            })
            .ToList();
        return Ok(result);
    }
    
    /// <summary>
    /// 查询供应端 - 基础产品信息
    /// </summary>
    /// <param name="productId">saas线路产品id</param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(GetOpenSupplierBasicProductBffOutput))]
    public async Task<IActionResult> GetLineProductBasicInfo(long lineProductId)
    {
        //查询线路产品详情
        var lineProductDetail = await _productApiCaller.GetLineProductV2(lineProductId);
        if (lineProductDetail.PurchaseSourceType != LineProductPurchaseSourceType.InterfaceDock)
        {
            throw new BusinessException(ErrorTypes.Common.ThirdProductConfigurationError);
        }
        
        var supplierApiType = _openPlatformService.MapPriceInventorySourceToSupplierApiType(lineProductDetail.PriceInventorySource);
        var outProductId = lineProductDetail.OpenSupplierSettingInfo?.ActivityId;
        if (string.IsNullOrEmpty(outProductId))
        {
            throw new BusinessException(ErrorTypes.Common.ThirdProductConfigurationError);
        }
        
        //查询维护的基础产品信息
        var basicProductRequest = new GetOpenSupplierBasicProductInput
        {
            SupplierApiType = supplierApiType,
            OutProductId = outProductId
        };
        var basicProductResponse = await _productApiCaller.GetOpenSupplierBasicProducts(basicProductRequest);

        //查询已经创建的套餐信息
        var skuItemMatchInfo = await _productApiCaller.GetLineSkuItemMatchBasicProducts(
            new GetBasicProductInput
            {
                LineProductId = lineProductId
            });

        var result = new GetOpenSupplierBasicProductBffOutput
        {
            SupplierProductId = outProductId,
            SupplierProductName = basicProductResponse.FirstOrDefault()?.ProductName,
            OptionItems = basicProductResponse.GroupBy(x => x.OptionId)
                .Select(x => new OpenSupplierBasicProductOptionItem
                {
                    SupplierOptionId = x.Key,
                    SupplierOptionName = x.FirstOrDefault().OptionName,
                    TimeSlotItems = x.GroupBy(t => new {t.TimeSlotId, t.TimeSlotName})
                        .Select(t =>
                        {
                            var timeSlotResult = new OpenSupplierBasicProductTimeSlotItem
                            {
                                SupplierTimeSlotId = t.Key.TimeSlotId,
                                SupplierTimeSlotName = t.Key.TimeSlotName ?? "全天",
                                SkuItems = t.Select(s =>
                                    {
                                        var skuItemResult = new OpenSupplierBasicProductSkuItem
                                        {
                                            MatchId = s.MatchId,
                                            SupplierSkuId = s.SkuId,
                                            SupplierSkuName = s.SkuName,
                                            SkuPriceType = s.SkuPriceType,
                                            MatchStatus = s.MatchStatus switch
                                            {
                                                OpenSupplierBasicProductMatchStatus.WaitingMatch => BasicProductMatchStatus
                                                    .WaitingInit,
                                                OpenSupplierBasicProductMatchStatus.MatchSuccess =>
                                                    BasicProductMatchStatus.New,
                                                OpenSupplierBasicProductMatchStatus.MatchFailed => BasicProductMatchStatus
                                                    .MatchFailed,
                                                _ => throw new ArgumentOutOfRangeException()
                                            }
                                        };
                            
                                        //处理已生成套餐的匹配状态
                                        if (!skuItemMatchInfo.Any())
                                        {
                                            return skuItemResult;
                                        }

                                        if (skuItemResult.MatchStatus == BasicProductMatchStatus.New)
                                        {
                                            if (skuItemMatchInfo.Any(m => m.MatchId == skuItemResult.MatchId))
                                            {
                                                skuItemResult.MatchStatus = BasicProductMatchStatus.Added;
                                            }
                                        }

                                        return skuItemResult;
                                    })
                                    .ToList()
                            };
                            return timeSlotResult;
                        })
                        .ToList()
                }).ToList()
        };
        
        return Ok(result);
    }


    #region private

    private GetApiSkuLocationData MergeLocationData(GetApiSkuLocationData source, GetApiSkuLocationData? target)
    {
        target ??= new GetApiSkuLocationData
        {
            KeyType = source.KeyType,
            ValueType = source.ValueType
        };
        var existingKeys = new HashSet<string>(target.ValueOptions.Select(x => x.Key));

        foreach (var valueOption in source.ValueOptions.Where(x => !existingKeys.Contains(x.Key)))
        {
            target.ValueOptions.Add(
                new GetApiSkuLocationValueOptions {Key = valueOption.Key, Value = valueOption.Value});
        }

        return target;
    }

    record ProductSupplierSettingData(OpenSupplierType OpenSupplierApiType,
        string SupplierActivityId,
        List<string> SupplierPackageIds,
        List<string> SupplierSkuIds);
    private async Task<ProductSupplierSettingData> GetProductSupplierSetting(ProductType productType, long productId,long? skuId)
    {
        OpenSupplierType? openSupplierType = null;
        SupplierApiType? supplierApiType = null;
        var supplierActivityId = string.Empty;
        var supplierPackageIds = new List<string>();
        var supplierSkuIds = new List<string>();
        
        switch (productType)
        {
            case ProductType.Scenic:

                var scenicTicketDetail = await _scenicApiCaller.GetScenicTicketDetail(productId);
                if (scenicTicketDetail == null)
                    throw new BusinessException(ErrorTypes.Common.ProductInvalid);

                if(scenicTicketDetail.CredentialSourceType!= CredentialSourceType.InterfaceDock)
                    throw new BusinessException(ErrorTypes.Common.ProductInvalid);

                supplierActivityId = scenicTicketDetail.ActivityId;
                if (!string.IsNullOrEmpty(scenicTicketDetail.PackageId))
                    supplierPackageIds.Add(scenicTicketDetail.PackageId);

                if (!string.IsNullOrEmpty(scenicTicketDetail.SkuId))
                    supplierSkuIds.Add(scenicTicketDetail.SkuId);
                
                supplierApiType =
                    _openPlatformService.MapPriceInventorySourceToSupplierApiType(scenicTicketDetail.PriceInventorySource!.Value);
                
                break;
            case ProductType.Line:
                
                var productDetail = await _productApiCaller.GetLineProductV2(productId);
                if (productDetail == null)
                    throw new BusinessException(ErrorTypes.Common.ProductInvalid);

                if(productDetail.PurchaseSourceType!= LineProductPurchaseSourceType.InterfaceDock)
                    throw new BusinessException(ErrorTypes.Common.ProductInvalid);

                var skuTypeItems = await _productApiCaller.QueryLineSkuTypeItems(new QueryLineSkuTypeItemInput
                {
                    LineProductId = productId,
                    LineProductSkuId = skuId
                });
                if (skuTypeItems.Any() is false)
                    throw new BusinessException(ErrorTypes.Common.ProductInvalid);

                supplierActivityId = productDetail.OpenSupplierSettingInfo.ActivityId;
                supplierPackageIds = skuTypeItems.Where(x => !string.IsNullOrEmpty(x.SupplierOptionId))
                    .Select(x => x.SupplierOptionId!).Distinct().ToList();
                supplierSkuIds = skuTypeItems
                    .Where(x => !string.IsNullOrEmpty(x.SupplierSkuId))
                    .Select(x => x.SupplierSkuId!).Distinct().ToList();
                supplierApiType =
                    _openPlatformService.MapPriceInventorySourceToSupplierApiType(productDetail.PriceInventorySource);
                
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(productType), productType, null);
        }
        
        openSupplierType = _openPlatformService.MapSupplierApiTypeToOpenSupplierType(supplierApiType!.Value);
        
        return new ProductSupplierSettingData(openSupplierType!.Value,supplierActivityId, supplierPackageIds, supplierSkuIds);
    }

    private async Task<ProductSupplierSettingData> GetOrderRelatedSupplierSetting(OrderType orderType, long baseOrderId)
    {
        OpenSupplierType? openSupplierType = null;
        var supplierActivityId = string.Empty;
        var supplierPackageIds = new List<string>();
        var supplierSkuIds = new List<string>();

        switch (orderType)
        {
            case OrderType.ScenicTicket:

                var scenicTicketOrderDetail = (await _orderApiCaller.GetScenicTicketOrderSimpleInfo(baseOrderId)).FirstOrDefault();
                openSupplierType = scenicTicketOrderDetail.OpenSupplierType;
                supplierActivityId = scenicTicketOrderDetail.SupplierActivityId;
                if (!string.IsNullOrEmpty(scenicTicketOrderDetail.SupplierPackageId))
                    supplierPackageIds.Add(scenicTicketOrderDetail.SupplierPackageId); 
                if(!string.IsNullOrEmpty(scenicTicketOrderDetail.SupplierSkuId))
                    supplierSkuIds.Add(scenicTicketOrderDetail.SupplierSkuId);
                
                break;
            case OrderType.TravelLineOrder:
                
                //查询订单关联的套餐子项id
                var orderSkuItems = await _orderApiCaller.QueryTravelLineOrderSkuTypeItems(new QueryTravelLineOrderSkuTypeItemInput
                {
                    BaseOrderIds = new List<long> { baseOrderId }
                });

                openSupplierType = orderSkuItems.SkuTypeItems.FirstOrDefault().OpenSupplierType;
                supplierActivityId = orderSkuItems.SkuTypeItems.FirstOrDefault()?.ActivityId;
                supplierPackageIds = orderSkuItems.SkuTypeItems.Where(x=>!string.IsNullOrEmpty(x.PackageId)).Select(x => x.PackageId!).Distinct().ToList();
                supplierSkuIds = orderSkuItems.SkuTypeItems.Where(x=>!string.IsNullOrEmpty(x.SkuId)).Select(x => x.SkuId!).Distinct().ToList();
                
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(orderType), orderType, null);
        }
        
        return new ProductSupplierSettingData(openSupplierType!.Value,  supplierActivityId, supplierPackageIds, supplierSkuIds);
    } 
    #endregion
}