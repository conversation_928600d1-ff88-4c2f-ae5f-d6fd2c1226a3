using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.TenantReceiptFlow;
using Common.Swagger;
using Common.Utils;
using Contracts.Common.Payment.DTOs.TenantReceiptFlow;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 商户收款流水
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class TenantReceiptFlowController : ControllerBase
{
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IMapper _mapper;

    public TenantReceiptFlowController(IPaymentApiCaller paymentApiCaller,
        ITenantApiCaller tenantApiCaller,
        IMapper mapper)
    {
        _paymentApiCaller = paymentApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 查询收款流水
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<TenantReceiptFlowOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        var result = await _paymentApiCaller.SearchTenantReceiptFlows(input);
        var agencyIds=result.Data.Select(x => x.AgencyId).Distinct().ToList();
        var agencies = await _tenantApiCaller.GetAgencyByIds(new Contracts.Common.Tenant.DTOs.Agency.GetAgenciesByIdsInput
        {
            AgencyIds = agencyIds
        });
        PagingModel<TenantReceiptFlowOutput> output = new()
        {
            PageIndex = result.PageIndex,
            PageSize = result.PageSize,
            Total = result.Total,
            Data = result.Data.Select(item =>
            {
                item.TenantBankAccount.AccountNo = DataSecrecyUtil.Sensitive(item.TenantBankAccount.AccountNo, 4, 4);
                TenantReceiptFlowOutput flowOutput = _mapper.Map<TenantReceiptFlowOutput>(item);
                flowOutput.AgencyName = agencies.FirstOrDefault(x => x.Id == item.AgencyId)?.FullName;
                return flowOutput;
            })
        };

        return Ok(output);
    }

    /// <summary>
    /// 收款流水导出数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<TenantReceiptFlowOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportFlows(QueryTenantReceiptFlowDto input)
    {
        var result = await _paymentApiCaller.ExportTenantReceiptFlows(input);

        var agencyIds = result.Select(x => x.AgencyId).Distinct().ToList();
        var agencies = await _tenantApiCaller.GetAgencyByIds(new Contracts.Common.Tenant.DTOs.Agency.GetAgenciesByIdsInput
        {
            AgencyIds = agencyIds
        });

        var output = result.Select(item =>
        {
            item.TenantBankAccount.AccountNo = DataSecrecyUtil.Sensitive(item.TenantBankAccount.AccountNo, 4, 4);
            TenantReceiptFlowOutput flowOutput = _mapper.Map<TenantReceiptFlowOutput>(item);
            flowOutput.AgencyName = agencies.FirstOrDefault(x => x.Id == item.AgencyId)?.FullName;
            return flowOutput;
        });
        return Ok(output);
    }
}
