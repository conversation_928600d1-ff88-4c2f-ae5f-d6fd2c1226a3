using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.Insure;
using Bff.Vebk.Models.LineProductSku;
using Common.Swagger;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Product.DTOs.LineProductSku;
using Contracts.Common.Product.DTOs.LineProductSkuTypeItem;
using Contracts.Common.Product.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;


namespace Bff.Vebk.Controllers;

/// <summary>
/// 线路产品SKU信息
/// </summary>
[Authorize]
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class LineProductSkuController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IProductApiCaller _productApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;

    public LineProductSkuController(IMapper mapper,
        IProductApiCaller productApiCaller,
        ITenantApiCaller tenantApiCaller,
        IUserApiCaller userApiCaller,
        IOrderApiCaller orderApiCaller)
    {
        _mapper = mapper;
        _productApiCaller = productApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _userApiCaller = userApiCaller;
        _orderApiCaller = orderApiCaller;
    }


    /// <summary>
    /// 创建线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> Add(AddInput input)
    {
        var id = await _productApiCaller.AddLineProductSku(input);

        var insureProductSku = _mapper.Map<SaveInsureProductSkuRelationInput>(input.InsureProductSku);
        insureProductSku.ProductId = input.LineProductId;
        insureProductSku.ProductSkuId = id;
        await _orderApiCaller.SaveInsureProductSkuRelation(insureProductSku);
        return Ok();
    }

    /// <summary>
    /// 编辑线路产品套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> Edit(EditInput input)
    {
        await _productApiCaller.EditLineProductSku(input);

        var insureProductSku = _mapper.Map<SaveInsureProductSkuRelationInput>(input.InsureProductSku);
        insureProductSku.ProductId = input.LineProductId;
        insureProductSku.ProductSkuId = input.Id;
        await _orderApiCaller.SaveInsureProductSkuRelation(insureProductSku);

        return Ok();
    }

    /// <summary>
    /// 查询线路产品套餐详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [SwaggerResponseExt(200, typeof(DetailOutput))]
    public async Task<IActionResult> Detail(long id)
    {
        DetailOutput result = null;
        var lineProductSkuDetail = await _productApiCaller.LineProductSkuDetail(id);
        if (lineProductSkuDetail == null)
            return Ok(result);

        var insureProductSkuRelation = await _orderApiCaller.GetInsureProductSkuRelation(new GetInsureProductSkuRelationInput { 
            ProductSkuId = id,
            ProductId = lineProductSkuDetail.LineProductId,
        });

        result = _mapper.Map<DetailOutput>(lineProductSkuDetail);
        result.InsureProductSku = _mapper.Map<InsureProductSkuRelationBffOutput>(insureProductSkuRelation);
        if (result is { PurchaseSourceType: LineProductPurchaseSourceType.InterfaceDock, CostDiscountRate: 0 })
            result.CostDiscountRate = null;// 暂时不展示
        return Ok(result);
    }

    /// <summary>
    /// 删除线路产品套餐
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200)]
    public async Task<IActionResult> Delete(DeleteInput input)
    {
        await _productApiCaller.DeleteLineProductSku(new RemoveProductSkuInput { 
            Id = input.Id,
        });
        await _orderApiCaller.DeleteInsureProductSkuRelation(new DeleteInsureProductSkuRelationInput
        {
            ProductSkuId = input.Id
        });
        return Ok();
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200,typeof(List<GetSimpleDetailBffOutput>))]
    public async Task<IActionResult> GetSimpleDetail(GetSimpleDetailBffIInput input)
    {
        var skuList = await _productApiCaller.GetLineSkuByProductId(input.LineProductId);
        var result = skuList.Select(x => new GetSimpleDetailBffOutput
        {
            LineProductId = input.LineProductId,
            LineProductSkuId = x.Id,
            LineProductSkuName = x.Name
        })
        .ToList();
        return Ok(result);
    }
    
    /// <summary>
    /// 同步创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    public async Task<IActionResult> AddSkuTypeItem(AddSkuTypeItemBffInput input)
    {
        var request = new AddLineSkuTypeItemInput
        {
            LineProductId = input.LineProductId,
            MatchIds = input.MatchIds
        };

        await _productApiCaller.AddLineSkuItem(request);
        return Ok();
    }
    
    /// <summary>
    /// 查询套餐票种信息列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(List<QuerySkuTypeItemBffOutput>))]
    public async Task<IActionResult> QuerySkuTypeItem(QuerySkuTypeItemBffInput input)
    {
        var response = await _productApiCaller.QueryLineSkuTypeItems(new QueryLineSkuTypeItemInput
        {
            LineProductId = input.LineProductId,
            LineProductSkuId = input.LineProductSkuId
        });
        var output = _mapper.Map<List<QuerySkuTypeItemBffOutput>>(response);
        return Ok(output);
    }
}