using Contracts.Common.Tenant.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.TenantUser;

public class UpdateTenantUserVaildBffInput
{

    /// <summary>
    /// 验证方式
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public TenantUserVaildContactType ContactType { get; set; }

    /// <summary>
    /// 新手机号或者邮箱
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ContactTypeValue { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string VerifyCode { get; set; }

}
