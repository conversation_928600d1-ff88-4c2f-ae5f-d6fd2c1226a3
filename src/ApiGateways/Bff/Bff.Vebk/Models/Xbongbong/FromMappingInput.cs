using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.Xbongbong;

public class FromMappingInput
{
    /// <summary>
    /// 公司全称，长度1-20
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string FullName { get; set; } = null!;
    /// <summary>
    /// 联系人，长度1-20
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string Contact { get; set; } = null!;

    /// <summary>
    /// 联系人手机号，长度1-20
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ContactNumber { get; set; } = null!;
}
