using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.AgencyUser;

public class AgencyUserUpdateInput
{
    /// <summary>
    /// 分销商用户Id
    /// </summary>
    public long AgencyUserId { get; set; }

    /// <summary>
    /// 姓名 2-10位
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string RealName { get; set; }

    /// <summary>
    /// 手机号 如有填则必须为11位
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 邮箱 如有填则是正确邮箱格式
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string? Email { get; set; }

    /// <summary>
    /// AclKey
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public IEnumerable<string> AclKeys { get; set; }

    /// <summary>
    /// 国家区号
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string? CountryDialCode { get; set; }
}
