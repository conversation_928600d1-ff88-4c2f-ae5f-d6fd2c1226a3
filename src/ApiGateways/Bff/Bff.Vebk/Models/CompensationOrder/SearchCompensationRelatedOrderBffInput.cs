using Contracts.Common.Order.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.CompensationOrder;

public class SearchCompensationRelatedOrderBffInput : PagingInput
{
    /// <summary>
    /// 订单id
    /// <value>旧版本逻辑,看情况移除</value>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? BaseOrderId { get; set; }

    /// <summary>
    /// 查询的订单类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public OrderType OrderType { get; set; }
    
    /// <summary>
    /// 分销商id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? AgencyId { get; set; }
    
    /// <summary>
    /// 搜索 - 订单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<long> BaseOrderIdArr { get; set; } = new();
    
    /// <summary>
    /// 搜索 -售卖渠道单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<string> ChannelOrderNoArr { get; set; } = new();
}