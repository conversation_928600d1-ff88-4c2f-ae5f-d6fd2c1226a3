using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.AgencyACL;

public class AgencyAclDTO
{
    /// <summary>
    /// 权限
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string AclKey { get; set; } = null!;

    /// <summary>
    /// 前端路由 - 正则
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? DisplayPattern { get; set; }

    /// <summary>
    /// API路由 - 正则
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ApiPattern { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? Remark { get; set; }

    /// <summary>
    /// 排序 - 正序
    /// </summary>
    public int Order { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}
