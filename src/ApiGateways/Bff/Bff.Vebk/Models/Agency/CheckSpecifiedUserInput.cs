using Contracts.Common.Marketing.Enums;

namespace Bff.Vebk.Models.Agency;

public class CheckSpecifiedUserInput
{
    /// <summary>
    /// 优惠券活动用户类型
    /// </summary>
    public CouponActivitySpecifiedUserType SpecifiedUserType { get; set; }

    /// <summary>
    /// 指定用户ID列表
    /// </summary>
    public string[] SpecifiedUserIds { get; set; }
}

public record SpecifiedUserIndexDto(int Index, long Id);

public class CheckSpecifiedUserOutput
{
    public CheckSpecifiedUserOutput(bool isSuccessed, CheckSpecifiedUserError error = default)
    {
        Error = error;
        IsSuccessed = isSuccessed;
    }
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessed { get; set; }

    /// <summary>
    /// 错误项
    /// </summary>
    public CheckSpecifiedUserError Error { get; set; }
}

public class CheckSpecifiedUserError
{
    /// <summary>
    /// 序号
    /// </summary>
    public int Index { get; set; }
    /// <summary>
    /// id
    /// </summary>
    public string Id { get; set; }
    /// <summary>
    /// 错误类型
    /// </summary>
    public CheckSpecifiedUserErrorType ErrorType { get; set; }
}

public enum CheckSpecifiedUserErrorType
{
    /// <summary>
    /// ID错误 类型错误
    /// </summary>
    IdError = 1,

    /// <summary>
    /// ID错误 未找到指定用户
    /// </summary>
    IdNotExist = 2,

    //ID重复
    IdDuplicated = 3,

    /// <summary>
    /// ID禁用
    /// </summary>
    IdDisabled = 4,
}