using AutoMapper;
using Bff.Vebk.Models.GDSTag;
using Contracts.Common.Product.DTOs.Fields;
using Contracts.Common.Resource.DTOs.GDSTag;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.Fields;

public class GDSTagProfies : Profile
{
    public GDSTagProfies()
    {
        CreateMap<AddGDSTagBffInput, AddGDSTagInput>();
        CreateMap<EditGDSTagBffInput, EditGDSTagInput>();
        CreateMap<GetGDSTagsBffOutput, GetGDSTagsOutput>();
        CreateMap<SearchGDSTagBffInput, SearchGDSTagInput>();
        CreateMap<SearchGDSTagOutput, SearchGDSTagBffOutput>();
        CreateMap<PagingModel<SearchGDSTagBffOutput>, PagingModel<SearchGDSTagOutput>>();
    }
}
