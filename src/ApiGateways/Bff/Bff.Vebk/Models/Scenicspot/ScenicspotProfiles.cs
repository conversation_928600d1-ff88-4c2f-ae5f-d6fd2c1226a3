using AutoMapper;
using Contracts.Common.Scenic.DTOs.ScenicSpot;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.Scenicspot;

public class ScenicspotProfiles : Profile
{
    public ScenicspotProfiles()
    {
        CreateMap<SearchScenicSpotsOutput, SearchOutput>();
        CreateMap<PagingModel<SearchScenicSpotsOutput>, PagingModel<SearchOutput>>();

        CreateMap<GetScenicSpotCitiesOutput, BffGetScenicSpotCitiesOutput>()
            .ForMember(x => x.ZhCityName, r => r.MapFrom(x => x.CityName))
            .ForMember(x => x.ZhCountryName, r => r.MapFrom(x => x.CountryName));
    }
}
