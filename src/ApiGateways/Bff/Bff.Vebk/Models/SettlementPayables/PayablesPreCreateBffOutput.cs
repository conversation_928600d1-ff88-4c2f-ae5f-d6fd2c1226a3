using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;

namespace Bff.Vebk.Models.SettlementPayables;

public class PayablesPreCreateBffOutput
{
    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    public string SupplierName { get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactName { get; set; }
    /// <summary>
    /// 联系电话
    /// </summary>
    public string ContactPhone { get; set; }
    
    /// <summary>
    /// 结算周期
    /// </summary>
    public SupplierSettlementPeriod SettlementPeriod { get; set; }
    
    /// <summary>
    /// 预付款余额
    /// </summary>
    public decimal PrepaymentBalance { get; set; }
    
    /// <summary>
    /// 订单数量
    /// </summary>
    public int OrderCount { get; set; }
    
    /// <summary>
    /// 订单金额
    /// </summary>
    public decimal OrderAmount { get; set; }
    
    /// <summary>
    /// 应付款总额(应收金额=订单金额)
    /// </summary>
    public decimal TotalAmount { get; set; }
    
    /// <summary>
    /// 账单周期 - 起
    /// </summary>
    public DateTime BillingCycleBegin { get; set; }

    /// <summary>
    /// 账单周期 - 止
    /// </summary>
    public DateTime BillingCycleEnd { get; set; }

    /// <summary>
    /// 成本币种 =>供应商币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    #region 各业务订单日期类型

    /// <summary>
    /// 酒店订单日期类型
    /// </summary>
    public PayablesDateType HotelOrderDateType { get; set; }

    /// <summary>
    /// 邮寄订单日期类型
    /// </summary>
    public PayablesDateType MailOrderDateType { get; set; }

    /// <summary>
    /// 券类订单日期类型
    /// </summary>
    public PayablesDateType TicketOrderDateType { get; set; }

    /// <summary>
    /// 预约单日期类型
    /// </summary>
    public PayablesDateType ReservationOrderDateType { get; set; }

    /// <summary>
    /// 景点门票日期类型
    /// </summary>
    public PayablesDateType ScenicTicketOrderDateType { get; set; }

    /// <summary>
    /// 线路订单日期类型
    /// </summary>
    public PayablesDateType LineOrderDateType { get; set; }

    /// <summary>
    /// 用车订单日期类型
    /// </summary>
    public PayablesDateType CarProductOrderDateType { get; set; }

    #endregion

    #region 各订单结算数据汇总

    /// <summary>
    /// 酒店订单数据
    /// </summary>
    public PreCreateOrderBffDto HotelOrderData { get; set; }
    
    /// <summary>
    /// 券类订单数据
    /// </summary>
    public PreCreateOrderBffDto TicketOrderData { get; set; }
    
    /// <summary>
    /// 券类订单(需要预约)数据
    /// </summary>
    public PreCreateOrderBffDto ReservationOrderData { get; set; }
    
    /// <summary>
    /// 邮寄订单数据
    /// </summary>
    public PreCreateOrderBffDto MailOrderData { get; set; }
    
    /// <summary>
    /// 景点门票订单数据
    /// </summary>
    public PreCreateOrderBffDto ScenicTicketOrderData { get; set; }
    
    /// <summary>
    /// 线路门票订单数据
    /// </summary>
    public PreCreateOrderBffDto LineOrderOrderData { get; set; }

    /// <summary>
    /// 用车门票订单数据
    /// </summary>
    public PreCreateOrderBffDto CarProductOrderData { get; set; }

    /// <summary>
    /// 退款订单数据
    /// </summary>
    public PreCreateOrderBffDto RefundOrderData { get; set; }
    
    /// <summary>
    /// 抵冲单数据
    /// </summary>
    public PreCreateOrderBffDto OffsetOrderData { get; set; }

    #endregion

    /// <summary>
    /// 部门Id
    /// </summary>
    public long? TenantDepartmentId { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? TenantDepartmentName { get; set; }

    /// <summary>
    /// 充值货币
    /// </summary>
    public string PrepaymentBalanceCurrencyCode { get; set; }

    /// <summary>
    /// 应付金额 max(订单金额 - 充值剩余金额, 0)
    /// </summary>
    public decimal PayAmount { get; set; }
}

public class PreCreateOrderBffDto
{
    /// <summary>
    /// 结算日期方式
    /// </summary>
    public PayablesDateType PayablesDateType { get; set; }
    
    /// <summary>
    /// 订单账期 - 起
    /// </summary>
    public DateTime BillingCycleBegin { get; set; }

    /// <summary>
    /// 订单账期 - 止
    /// </summary>
    public DateTime BillingCycleEnd { get; set; }

    /// <summary>
    /// 订单数量
    /// </summary>
    public int OrderCount { get; set; }

    /// <summary>
    /// 账单总额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 成本币种 =>供应商币种
    /// </summary>
    public string CostCurrencyCode { get; set; }
}