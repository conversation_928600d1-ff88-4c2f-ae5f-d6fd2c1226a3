using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Models.OrderPrice;

public class OrderMultPriceBffOutput
{
    /// <summary>
    /// 各个子订单id
    /// </summary>
    public long SubOrderId { get; set; }

    /// <summary>
    /// 订单类型 
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 子类型（线路：成人、儿童、房差）
    /// </summary>
    public int OrderSubType { get; set; }
    
    /// <summary>
    /// 售价类型枚举 0-原价 1-会员价 2-抢购价
    /// </summary>
    public OrderPriceType? PriceType { get; set; }

    /// <summary>
    /// 支付币种 =>用户支付币种，B2B为分销商币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 价格 实际售卖价格
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 成本价类型枚举 0-原价 1-会员价 2-抢购价
    /// </summary>
    public OrderPriceType? CostPriceType { get; set; }

    /// <summary>
    /// 成本币种 =>供应商币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 成本价(采购价) 实际成本价格
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 原价币种 =>商户售卖币种
    /// </summary>
    public string OrgPriceCurrencyCode { get; set; }

    /// <summary>
    /// 原售价
    /// </summary>
    public decimal? OrgPrice { get; set; }

    /// <summary>
    /// 原成本价
    /// </summary>
    public decimal? OrgCostPrice { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 零售价，产品单价
    /// </summary>
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>如15表示为15%的折扣</remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
    
    /// <summary>
    /// 子类型关联项id
    /// <value>
    /// <para>线路-套餐票种id</para>
    /// </value>
    /// </summary>
    public long? OrderSubItemId { get; set; }

    /// <summary>
    /// 子类型关联项名称
    /// <value>
    /// <para>线路-套餐票种名称</para>
    /// </value>
    /// </summary>
    public string? OrderSubItemName { get; set; }
}