using FluentValidation;

namespace Bff.Vebk.Models.SelfSupportHotel;

public class GetHotelPriceStrategiesBffInputValidator : AbstractValidator<GetHotelPriceStrategiesBffInput>
{
    public GetHotelPriceStrategiesBffInputValidator()
    {
        RuleFor(x => x.ResourceHotelId).NotEmpty();
        RuleFor(x => x.BeginDate).NotEmpty().LessThanOrEqualTo(x => x.EndDate);
        RuleFor(x => x.EndDate).NotEmpty().GreaterThanOrEqualTo(x => x.BeginDate);
    }
}
