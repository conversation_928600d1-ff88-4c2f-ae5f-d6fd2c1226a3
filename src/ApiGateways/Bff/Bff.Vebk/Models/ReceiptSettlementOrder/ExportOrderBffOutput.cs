using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Bff.Vebk.Models.ReceiptSettlementOrder;

public class ExportOrderBffOutput
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商名称
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 订单总量
    /// </summary>
    public int OrderCount { get; set; }

    /// <summary>
    /// 订单金额
    /// </summary>
    public decimal OrderAmount { get; set; }

    /// <summary>
    /// 充值金额
    /// </summary>
    public decimal RechargeAmount { get; set; }

    /// <summary>
    /// 应收总额(应收金额=订单金额-充值金额)
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 账单周期 - 起
    /// </summary>
    public DateTime BillingCycleBegin { get; set; }

    /// <summary>
    /// 账单周期 - 止
    /// </summary>
    public DateTime BillingCycleEnd { get; set; }

    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 状态
    /// </summary>
    public ReceiptSettlementOrderStatus Status { get; set; }

    #region 收款操作人信息

    /// <summary>
    /// 收款操作人人Id
    /// </summary>
    public long? PayeeId { get; set; }

    /// <summary>
    /// 收款操作人
    /// </summary>
    public string? PayeeName { get; set; }

    /// <summary>
    /// 收款时间
    /// </summary>
    public DateTime? PayeeTime { get; set; }

    #endregion

    #region 收款账户信息

    /// <summary>
    /// 收款方式
    /// </summary>
    public ReceiptSettlementTransferType SettlementTransferType { get; set; }

    /// <summary>
    /// 开户名称
    /// </summary>
    public string? AccountName { get; set; }

    /// <summary>
    /// 银行编号
    /// </summary>
    public string? BankCode { get; set; }

    /// <summary>
    /// 银行名称
    /// </summary>
    public string? BankName { get; set; }

    /// <summary>
    /// 分行名称
    /// </summary>
    public string? BranchName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public string? BankAccount { get; set; }

    /// <summary>
    /// 转账凭证图片
    /// </summary>
    public string? ProofImg { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    #endregion

    #region 收款金额信息

    /// <summary>
    /// 应收总额币种
    /// </summary>
    public string TotalAmountCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 实收金额
    /// </summary>
    public decimal? ReceivedAmount { get; set; }

    /// <summary>
    /// 实收总额币种
    /// </summary>
    public string? ReceivedAmountCurrencyCode { get; set; }

    #endregion
    
    #region 开票信息

    /// <summary>
    /// 发票流水号
    /// </summary>
    public string? InvoiceSerialNo { get; set; }
    
    /// <summary>
    /// 开票状态
    /// </summary>
    public InvoiceStatus? InvoiceStatus { get; set; }

    #endregion

    /// <summary>
    /// 部门Id
    /// </summary>
    public long? TenantDepartmentId { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? TenantDepartmentName { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public string? SalespersonName { get; set; }
}