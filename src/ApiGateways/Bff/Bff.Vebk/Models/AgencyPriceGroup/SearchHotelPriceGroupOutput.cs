using Contracts.Common.Product.Enums;

namespace Bff.Vebk.Models.AgencyPriceGroup;

public class SearchHotelPriceGroupOutput
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    /// <summary>
    /// 房型id
    /// </summary>
    public long RoomId { get; set; }

    /// <summary>
    /// 房型名称
    /// </summary>
    public string RoomName { get; set; }

    /// <summary>
    /// 价格策略id
    /// </summary>
    public long PriceStrategyId { get; set; }

    /// <summary>
    /// 价格策略名称
    /// </summary>
    public string PriceStrategyName { get; set; }
    
    /// <summary>
    /// 采购价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 基础售价
    /// </summary>
    public decimal? SalePrice { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 渠道价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
    
    /// <summary>
    /// 基准价格类型
    /// </summary>
    public ChannelBasePriceType BasePriceType { get; set; }

    /// <summary>
    /// 价格设置类型
    /// </summary>
    public ChannelPriceSettingType PriceSettingType { get; set; }

    /// <summary>
    /// 价格设置对应值
    /// </summary>
    public decimal PriceSettingValue { get; set; }
    
        
    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }
}