using AutoMapper;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Scenic.DTOs;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.AgencyPriceGroup;

public class AgencyPriceGroupMapProfile : Profile
{
    public AgencyPriceGroupMapProfile()
    {
        CreateMap<SearchHotelPriceGroupInput,GetHotelAndPriceStrategyInput>();
        CreateMap<GetHotelAndPriceStrategyOutput,SearchHotelPriceGroupOutput>();
        CreateMap<PagingModel<GetHotelAndPriceStrategyOutput>,PagingModel<SearchHotelPriceGroupOutput>>();

        CreateMap<SearchScenicPriceGroupInput,GetScenicAndTicketsInput>();
        CreateMap<GetScenicAndTicketsOutput, SearchScenicPriceGroupOutput>();
        CreateMap<PagingModel<GetScenicAndTicketsOutput>, PagingModel<SearchScenicPriceGroupOutput>>();

        CreateMap<SetHuiZhiHotelChannelPriceBffItem, SetHuiZhiHotelChannelPriceItem>();
        CreateMap<SetHuiZhiHotelDefaultChannelPriceBffItem, SetHuiZhiHotelDefaultChannelPriceItem>();
        CreateMap<SetHuiZhiHotelChannelPriceBffInput, SetHuiZhiHotelChannelPriceInput>();

        CreateMap<HuiZhiHotelDefaultChannelPriceItem, HuiZhiHotelDefaultChannelPriceBffItem>();
        CreateMap<HuiZhiHotelChannelPriceItem, HuiZhiHotelChannelPriceBffItem>();
        CreateMap<GetHuiZhiHotelChannelPriceOutput, GetHuiZhiHotelChannelPriceBffOutput>();

        CreateMap<SearchCarProductPriceGroupInput, Contracts.Common.Product.DTOs.CarProduct.GetProductsAndSkuInput>();
        CreateMap<GetCarProductsAndSkuOutput, SearchCarProductPriceGroupOutput>();
        CreateMap<PagingModel<GetCarProductsAndSkuOutput>, PagingModel<SearchCarProductPriceGroupOutput>>();
    }
}