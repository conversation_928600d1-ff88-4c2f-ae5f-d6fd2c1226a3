using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.Contact;

public class CreateVisitorInput
{
    /// <summary>
    /// 手机号, 11位
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 邮箱地址，最大长度50
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string Email { get; set; }

    /// <summary>
    /// 联系人，最大长度50
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string LinkmanName { get; set; }

    /// <summary>
    /// 公司名称，最大长度100
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string CompanyName { get; set; }
}
