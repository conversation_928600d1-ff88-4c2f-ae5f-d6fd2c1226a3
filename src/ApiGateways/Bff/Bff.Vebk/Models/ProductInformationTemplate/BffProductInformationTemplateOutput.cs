using Bff.Vebk.Models.InformationTemplate;
using Contracts.Common.Product.DTOs.InformationTemplateFields;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.Enums;

namespace Bff.Vebk.Models.ProductInformationTemplate;

public class BffProductInformationTemplateOutput
{
    /// <summary>
    /// 产品Id/景区Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 门票/套餐/规格Id
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 模板
    /// </summary>
    public List<BffProductInformationTemplateDetail> Templates { get; set; }
}

public class BffProductInformationTemplateDetail
{
    /// <summary>
    /// 模板Id
    /// </summary>
    public long TemplateId { get; set; }

    /// <summary>
    /// 模板类型
    /// </summary>
    public TemplateType TemplateType { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public ProductTemplateType ProductTemplateType { get; set; }

    /// <summary>
    /// 字段
    /// </summary>
    public List<BffProductInformationTemplateFieldsOutput> Fields { get; set; }
}
