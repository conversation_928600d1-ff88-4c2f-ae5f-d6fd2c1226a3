using AutoMapper;
using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using Contracts.Common.Order.DTOs.ScenicTicketPurchaseOrder;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.ScenicTicketPurchaseOrder;

public class ScenicTicketPurchaseOrderMapProfile : Profile
{
    public ScenicTicketPurchaseOrderMapProfile()
    {
        CreateMap<TicketPurchaseVoucherBffDto, TicketPurchaseVoucherDto>();
        CreateMap<CreateTicketPurchaseOrderBffInput, CreateTicketPurchaseOrderInput>();
        CreateMap<EditTicketPurchaseOrderBffInput, EditTicketPurchaseOrderInput>();
        CreateMap<GetTicketPurchaseOrderDetailOutput, GetTicketPurchaseOrderDetailBffOutput>();
        CreateMap<SearchTicketPurchaseOrderBffInput, SearchTicketPurchaseOrderInput>();
        CreateMap<SearchTicketPurchaseOrderOutput,SearchTicketPurchaseOrderBffOutput>();
        CreateMap<PagingModel<SearchTicketPurchaseOrderOutput>,PagingModel<SearchTicketPurchaseOrderBffOutput>>();
        CreateMap<ExportTicketPurchaseOutput, ExportTicketPurchaseBffOutput>();
        CreateMap<GetScenicTicketPurchaseInventoryOutput, GetScenicTicketPurchaseInventoryBffOutput>();
        CreateMap<ScenicTicketOrderDeliveryOutput, ScenicTicketOrderDeliveryBffOutput>();
        CreateMap<ExportPurchaseOrderListBffInput, ExportPurchaseOrderListInput>();
        CreateMap<ExportPurchaseOrderListOutput, ExportPurchaseOrderListBffOutput>();

        CreateMap<SearchTicketPurchaseInvCheckRecordBffInput,SearchTicketPurchaseInvCheckRecordInput>();
        CreateMap<SearchTicketPurchaseInvCheckRecordOutput,SearchTicketPurchaseInvCheckRecordBffOutput>();
        CreateMap<PagingModel<SearchTicketPurchaseInvCheckRecordOutput>,PagingModel<SearchTicketPurchaseInvCheckRecordBffOutput>>();

        CreateMap<SearchPurchaseConsumptionBffInput, SearchPurchaseConsumptionInput>();
        CreateMap<SearchPurchaseConsumptionOutput, SearchPurchaseConsumptionBffOutput>();
        CreateMap<PagingModel<SearchPurchaseConsumptionOutput>, PagingModel<SearchPurchaseConsumptionBffOutput>>();
    }
}