using Contracts.Common.Product.Enums;

namespace Bff.Vebk.Models.ScenicTicket;

/// <summary>
/// 查询同步字段
/// </summary>
public class GetOpenSupplierProductSyncFieldBffOutput
{
    /// <summary>
    /// 模块类型
    /// </summary>
    public OpenSupplierProductModuleType ModuleType { get; set; }

    /// <summary>
    /// 模块名称
    /// </summary>
    public string ModuleName { get; set; }
    
    /// <summary>
    /// 字段名称
    /// </summary>
    public string FieldName { get; set; }

    /// <summary>
    /// 是否返回
    /// </summary>
    public bool IsReturned { get; set; }

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool IsRequired { get; set; }
}

public class OpenSupplierProductSyncFieldMapping
{
    public OpenSupplierProductModuleType ModuleType { get; set; }

    public OpenSupplierProductSystemFieldType FieldType { get; set; }

    public bool IsRequired { get; set; } = false;
}

public static class OpenSupplierProductSyncFieldMappingConfig
{
    public static readonly List<OpenSupplierProductSyncFieldMapping> Mappings = new List<OpenSupplierProductSyncFieldMapping>
    {
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.ProductIntroduction, FieldType = OpenSupplierProductSystemFieldType.ProductImage , IsRequired = true},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.ProductIntroduction, FieldType = OpenSupplierProductSystemFieldType.Name , IsRequired = true},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.ProductIntroduction, FieldType = OpenSupplierProductSystemFieldType.Highlights},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.ProductIntroduction, FieldType = OpenSupplierProductSystemFieldType.BusinessHours},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.ProductIntroduction, FieldType = OpenSupplierProductSystemFieldType.ScenicAnnouncement},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.ProductIntroduction, FieldType = OpenSupplierProductSystemFieldType.Address},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.ProductIntroduction, FieldType = OpenSupplierProductSystemFieldType.ProductDetails , IsRequired = true},
        
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.PriceExplanation, FieldType = OpenSupplierProductSystemFieldType.PriceIncluded , IsRequired = true},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.PriceExplanation, FieldType = OpenSupplierProductSystemFieldType.PriceNotIncluded , IsRequired = true},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.PriceExplanation, FieldType = OpenSupplierProductSystemFieldType.ValidityPeriod},
        
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.BookingKnowledge, FieldType = OpenSupplierProductSystemFieldType.Precautions},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.BookingKnowledge, FieldType = OpenSupplierProductSystemFieldType.HowToUse},
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.BookingKnowledge, FieldType = OpenSupplierProductSystemFieldType.Others},
        
        new OpenSupplierProductSyncFieldMapping { ModuleType = OpenSupplierProductModuleType.Policy, FieldType = OpenSupplierProductSystemFieldType.CancellationPolicy , IsRequired = true},
    };
}