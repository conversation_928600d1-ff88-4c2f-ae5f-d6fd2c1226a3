using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.ScenicTicket;

public class SearchTicketsBffInput : PagingInput
{
    /// <summary>
    /// 景区Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? ScenicSpotId { get; set; }

    /// <summary>
    /// 搜索关键字
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? KeyWord { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? Enabled { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SupplierId { get; set; }

    /// <summary>
    /// 凭证来源（期票时必填）-兼容,后续移除
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public CredentialSourceType? CredentialSourceType { get; set; }

    /// <summary>
    /// 查询类型 默认查询产品名称
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public ProductSearchType SearchType { get; set; } = ProductSearchType.ProductName;

    /// <summary>
    /// 凭证来源
    /// <value> -1 : 手工单创单界面门票选择会传该值限制查询结果 </value>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<CredentialSourceType> CredentialSourceTypes { get; set; } = new();

    /// <summary>
    /// 门票类型
    /// <value> -1 : 手工单创单界面门票选择会传该值限制查询结果 </value>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<ScenicTicketsType> TicketsTypes { get; set; } = new();

    /// <summary>
    /// 对接渠道
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<PriceInventorySyncChannelType> PriceInventorySyncChannelTypes { get; set; } = new();

    /// <summary>
    /// 模糊查询-产品名称关键字
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? TicketNameKeyWord { get; set; }

    /// <summary>
    ///  模糊查询-id关键字
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? TicketIdKeyWord { get; set; }
    
    /// <summary>
    /// 时段id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? TimeSlotId { get; set; }

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? B2bSellingStatus { get; set; }
    
    
    /// <summary>
    /// 是否支持订单补差调整
    /// <value>[手工发货] - 支持订单补差</value>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? IsCompensation { get; set; }
}