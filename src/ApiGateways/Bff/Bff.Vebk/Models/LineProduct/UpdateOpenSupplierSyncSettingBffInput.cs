using Contracts.Common.Scenic.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.LineProduct;

public class UpdateOpenSupplierSyncSettingBffInput
{
    /// <summary>
    /// 产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }
    
    /// <summary>
    /// 价库同步类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public PriceInventorySyncType PriceInventorySyncType { get; set; }
    
    /// <summary>
    /// 同步日期范围 单位：天
    /// <value>默认90天</value>
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int SyncDateRange { get; set; }

    /// <summary>
    /// 同步轮询间隔 单位：分钟
    /// <value>默认10分钟</value>
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public int SyncInterval { get; set; }
}