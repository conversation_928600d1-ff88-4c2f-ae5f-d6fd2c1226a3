using AutoMapper;
using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.LineProduct;

public class LineProductMapProfiles : Profile
{
    public LineProductMapProfiles()
    {
        CreateMap<SearchLineProductSkuItem,SearchLineProductSkuBffItem>();
        CreateMap<SearchLineProductBffInput, SearchLineProductInput>()
            .ForMember(dest => dest.SkuId, s => s.Ignore())
            .ForMember(dest => dest.SkuTypeItemId, s => s.Ignore());
        CreateMap<SearchLineProductSummary, SearchLineProductSummaryBffOutput>();
        CreateMap<SearchLineProductOutput, SearchLineProductBffOutput>()
            .ForMember(dest => dest.Path, opt => opt.MapFrom(x => x.Picture));
        CreateMap<PagingModel<SearchLineProductOutput, SearchLineProductSummary>,
            PagingModel<SearchLineProductBffOutput, SearchLineProductSummaryBffOutput>>();

        CreateMap<LineProduct_CityItem, BffLineProduct_CityItem>()
        .ForMember(x => x.ZhCityName, r => r.MapFrom(x => x.CityName))
        .ForMember(x => x.ZhCountryName, r => r.MapFrom(x => x.CountryName));

        CreateMap<GetLineProductCitiesOutput, BffGetLineProductCitiesOutput>();
        CreateMap<LineProductOpenChannelSettingInfo, GetOpenChannelSettingInfoBffOutput>();

        CreateMap<GetOpenChannelTimelinessSettingOutput, GetOpenChannelTimelinessSettingBffOutput>();
    }
}