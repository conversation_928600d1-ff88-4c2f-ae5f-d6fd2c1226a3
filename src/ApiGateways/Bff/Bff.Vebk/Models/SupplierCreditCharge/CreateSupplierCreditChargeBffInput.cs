using Contracts.Common.Payment.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.SupplierCreditCharge;

/// <summary>
/// 供应商-预付款充值
/// </summary>
public class CreateSupplierCreditChargeBffInput
{
    /// <summary>
    /// 供应商id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 充值金额
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public decimal Amount { get; set; }
    
    /// <summary>
    /// 附件路径
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? AttachmentPath { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    /// <returns></returns>
    [SwaggerSchema(Nullable = true)]
    public string? Remark { get; set; }
}