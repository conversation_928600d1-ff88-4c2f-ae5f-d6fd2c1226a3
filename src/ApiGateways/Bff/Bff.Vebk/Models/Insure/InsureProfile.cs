using AutoMapper;
using Bff.Vebk.Models.Insure;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.Invoice;

namespace Bff.Vebk.Models.Invoice;

public class InsureProfile : Profile
{
    public InsureProfile()
    {
        CreateMap<AddOrUpdateInsurePolicyHolderInput, SaveInsurePolicyHolderInput>();
        CreateMap<AddOrUpdateInsureProductInput, SaveInsureProductInput>();
        CreateMap<GetInsureProductSkuRelationOutput, InsureProductSkuRelationBffOutput>();
        CreateMap<GetInsureTotalAmountOutput, GetInsureTotalAmountBffOutput>();
    }
}
