using Contracts.Common.User.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.Insure;

public class AddOrUpdateInsurePolicyHolderInput
{
    /// <summary>
    /// 中文名
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string CNName { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string ENName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public UserGender Gender { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public UserIdCardType Type { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string IDCard { get; set; }

    /// <summary>
    /// 生日 yyyy-MM-dd
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime Birthday { get; set; }
}
