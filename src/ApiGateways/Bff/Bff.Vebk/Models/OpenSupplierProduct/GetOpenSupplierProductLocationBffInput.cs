using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.OpenSupplierProduct;

public class GetOpenSupplierProductLocationBffInput
{
    /// <summary>
    /// saas - 产品id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? ProductId { get; set; }
    
    /// <summary>
    /// saas - 套餐id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SkuId { get; set; }

    /// <summary>
    /// saas - 套餐子项id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<long>? SkuTypeItemIds { get; set; } = new();

    /// <summary>
    /// 订单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? BaseOrderId { get; set; }

    /// <summary>
    /// 对接产品参数 - 活动id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? SupplierActivityId { get; set; }
    
    /// <summary>
    /// 对接产品参数 - optionid
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? SupplierPackageId { get; set; }
    
    /// <summary>
    /// 对接产品参数 - skuid
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? SupplierSkuId { get; set; }

    /// <summary>
    /// 供应商Api类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public SupplierApiType? SupplierApiType { get; set; }

    /// <summary>
    /// 产品类型
    /// <remarks>目前只有线路先忽略传参</remarks>
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public ProductType ProductType { get; set; } = ProductType.Line;
    
}