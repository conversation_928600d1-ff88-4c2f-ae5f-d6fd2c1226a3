using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.OpenSupplierProduct;

/// <summary>
/// 根据产品|订单关联的编码查询关联的附加信息
/// </summary>
public class QueryOpenSupplierSkuExtraInfoBffInput
{
    #region 产品层面查询

    /// <summary>
    /// 产品类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public ProductType? ProductType { get; set; }
    
    /// <summary>
    /// 产品id
    /// <value>线路产品id</value>
    /// <value>门票id</value>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? ProductId { get; set; }

    /// <summary>
    /// 套餐id
    /// <value>线路套餐id</value>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SkuId { get; set; }

    #endregion

    #region 订单层面查询

    /// <summary>
    /// 订单类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public OrderType? OrderType { get; set; }
    
    /// <summary>
    /// 订单id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? BaseOrderId { get; set; }

    #endregion
}

public class QueryOpenSupplierSkuExtraInfoListBffInput
{
    /// <summary>
    /// 产品类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 产品id列表
    /// 门票id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<long> ProductIds { get; set; } = new();
}