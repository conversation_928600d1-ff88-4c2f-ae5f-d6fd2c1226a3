using Contracts.Common.Product.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.CarProduct;

public class SearchCarProductBffInput : PagingInput
{
    /// <summary>
    /// 产品id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? Id { get; set; }
    
    /// <summary>
    /// 产品标题
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? Title { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public int? CountryCode { get; set; }

    /// <summary>
    /// 城市 接送机为机场所在城市
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public int? CityCode { get; set; }

    /// <summary>
    /// 机场id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? AirportId { get; set; }

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? B2bSellingStatus { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? Enabled { get; set; }

    /// <summary>
    /// 产品id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? ProductId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public CarProductType? CarProductType { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SupplierId { get; set; }

    /// <summary>
    /// 套餐id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? SkuId { get; set; }
    
    /// <summary>
    /// 套餐名称
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? SkuName { get; set; }
}