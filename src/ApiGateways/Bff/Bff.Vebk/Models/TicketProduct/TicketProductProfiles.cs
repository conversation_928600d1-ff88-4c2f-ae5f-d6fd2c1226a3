using AutoMapper;
using Contracts.Common.Product.DTOs.TicketProduct;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.TicketProduct;

public class TicketProductProfiles : Profile
{
    public TicketProductProfiles()
    {
        CreateMap<SearchTicketInfo, SearchOutput>();
        CreateMap<PagingModel<SearchTicketInfo, SearchTicketSummary>, PagingModel<SearchOutput, SearchTicketSummary>>();
    }
}
