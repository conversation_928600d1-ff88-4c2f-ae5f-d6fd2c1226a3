using Contracts.Common.Marketing.Enums;
using Contracts.Common.Product.Enums;

namespace Bff.Vebk.Models.PromotionTraceRecord;

public class SearchCustomerCluesBffOutput
{
    /// <summary>
    /// c端用户id
    /// </summary>
    public long? CustomerId { get; set; }

    /// <summary>
    /// 其它身份识别码
    /// </summary>
    public string OtherIdentifier { get; set; }

    /// <summary>
    /// 用户昵称
    /// </summary>
    public string CustomerNickName { get; set; }

    /// <summary>
    /// 用户联系方式
    /// </summary>
    public string CustomerPhoneNumber { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? CustomerAvatar { get; set; }
    
    /// <summary>
    /// 意向度
    /// </summary>
    public PromotionCustomerIntentionType IntentionType { get; set; }

    /// <summary>
    /// 推广追踪码Id
    /// </summary>
    public long PromotionTraceId { get; set; }
    
    /// <summary>
    /// 追踪类型
    /// </summary>
    public PromotionTraceType TraceType { get; set; }
    
    /// <summary>
    /// 记录名称
    /// </summary>
    public string TargetTitle { get; set; }
    
    /// <summary>
    /// 商品类型
    /// </summary>
    public ProductType? ProductType { get; set; }
    
    /// <summary>
    /// 线索获取时间
    /// </summary>
    public DateTime SourceOfTraceTime { get; set; }

    /// <summary>
    /// 最近一次足迹时间
    /// </summary>
    public DateTime LastOfTraceTime { get; set; }
    
    /// <summary>
    /// 推广人Id
    /// </summary>
    public long PromoterId { get; set; }
    
    /// <summary>
    /// 推广人名称
    /// </summary>
    public string PromoterName { get; set; }
    
    /// <summary>
    /// 推广人手机号
    /// </summary>
    public string PromoterPhoneNumber { get; set; }
    
    /// <summary>
    /// 投放人角色类型
    /// </summary>
    public PromoterRoleType? PromoterRoleType { get; set; }
}