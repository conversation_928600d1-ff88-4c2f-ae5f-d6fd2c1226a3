using Contracts.Common.Marketing.Enums;
using Contracts.Common.Product.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.PromotionTraceRecord;

public class SearchCustomerCluesBffInput : PagingInput
{
    /// <summary>
    /// 追踪类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public PromotionTraceType[] TraceTypes { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public ProductType[] ProductTypes { get; set; }
    
    /// <summary>
    /// 线索来源名称(投放物名称)
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string TargetTitle { get; set; }

    /// <summary>
    /// 意向类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public PromotionCustomerIntentionType? IntentionType { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime BeginTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime EndTime { get; set; }
    
    /// <summary>
    /// 用户昵称
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string CustomerNickName { get; set; }
    
    /// <summary>
    /// 用户联系方式
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string CustomerPhoneNumber { get; set; }
    
    /// <summary>
    /// 推广人角色类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public PromoterRoleType? PromoterRoleType { get; set; }
    
    /// <summary>
    /// 推广人名称
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string PromoterName { get; set; }
    
    /// <summary>
    /// 推广人手机号
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string PromoterPhoneNumber { get; set; }
}