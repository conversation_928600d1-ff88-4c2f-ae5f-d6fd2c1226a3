using AutoMapper;
using Contracts.Common.Order.DTOs.Invoice;

namespace Bff.Vebk.Models.Invoice;

public class InvoiceProfile : Profile
{
    public InvoiceProfile()
    {
        CreateMap<ApplyBffInput, ApplyInput>();
        CreateMap<CheckStatusBffInput, CheckStatusInput>();
        CreateMap<SearchBffInput, SearchInvoiceRecordInput>();

        CreateMap<ApplyInvoicingBffInput, ApplyInvoicingInput>().ReverseMap();
    }
}
