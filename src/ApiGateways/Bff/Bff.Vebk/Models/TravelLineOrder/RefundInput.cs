using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.TravelLineOrder;

public class RefundInput
{
    [SwaggerSchema(Nullable = false)]
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 退款金额
    /// </summary>
    public decimal? RefundAmount { get; set; }

    /// <summary>
    /// 发起退款原因
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public string Reason { get; set; }

    /// <summary>
    /// 凭据图片url
    /// </summary>
    public string[] ProofImgs { get; set; } = Array.Empty<string>();
}
