using Contracts.Common.Order.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.TravelLineOrder;

public class SearchLineOrderBffInput : PagingInput
{
    [SwaggerSchema(Nullable = true)]
    public DateTime? CreateBeginDate { get; set; }
    
    [SwaggerSchema(Nullable = true)]
    public DateTime? CreateEndDate { get; set; }
    
    [SwaggerSchema(Nullable = true)]
    public DateTime? TravelBeginDate { get; set; }
    
    [SwaggerSchema(Nullable = true)]
    public DateTime? TravelEndDate { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SupplierId { get; set; }

    /// <summary>
    /// 售卖平台 1-公众号 2-小程序 3-手工单
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public SellingPlatform? SellingPlatform { get; set; }

    /// <summary>
    /// 认领人Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? ClaimantId { get; set; }

    /// <summary>
    /// 搜索状态
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public SearchLineOrderBffStatus Status { get; set; }

    /// <summary>
    /// 用户id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? AgencyId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ProductName { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? OrderId { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ContactsName { get; set; }

    /// <summary>
    /// 来源单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? ChannelOrderNo { get; set; }

    /// <summary>
    /// 产品ID
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? ProductId { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? SupplierOrderId { get; set; }

    /// <summary>
    /// 保险状态
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public InsureOrderStatus? InsureOrderStatus { get; set; }
}

public enum SearchLineOrderBffStatus
{
    /// <summary>
    /// 待认领
    /// </summary>
    WaitingForClaim = 1,

    /// <summary>
    /// 待确认
    /// </summary>
    WaitingForConfirm = 2,

    /// <summary>
    /// 待完成
    /// </summary>
    UnFinished = 3,

    /// <summary>
    /// 已完成
    /// </summary>
    Finished = 4,

    /// <summary>
    /// 已关闭
    /// </summary>
    Closed = 5,

    /// <summary>
    /// 待支付
    /// </summary>
    WaitingForPay = 6,
    
    /// <summary>
    /// 待处理 = 待认领 + 待确认
    /// </summary>
    WaitingForPending = 7,
    
    /// <summary>
    /// 渠道异常
    /// </summary>
    ChannelAbnormal  = 8,
}