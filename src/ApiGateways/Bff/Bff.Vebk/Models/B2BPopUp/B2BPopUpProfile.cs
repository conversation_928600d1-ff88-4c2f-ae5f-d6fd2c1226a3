using AutoMapper;
using Bff.Vebk.Models.B2BPopUp;
using Contracts.Common.Tenant.DTOs.B2BPage;
using Contracts.Common.Tenant.DTOs.B2BPopUp;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Bff.Vebk.Models.B2BPage;

public class B2BPopUpProfile : Profile
{
    public B2BPopUpProfile()
    {
        CreateMap<GetPopUpByAgencyUserBffInput, GetPopUpByAgencyUserInput>();
        CreateMap<GetPopUpByAgencyUserOutput, GetPopUpByAgencyUserBffOutput>();
        CreateMap<B2BPopUpDetailOutput, B2BPopUpDetailBffOutput>();
        CreateMap<SearchB2BPopUpBffInput, SearchB2BPopUpInput>();
        CreateMap<B2BPopUpListOutput, B2BPopUpListBffOutput>();
        CreateMap<B2BPopUpStatOutput, B2BPopUpStatBffOutput>();       
        CreateMap<PagingModel<B2BPopUpListOutput>, PagingModel<B2BPopUpListBffOutput>>();
        CreateMap<AddB2BPopUpBffInput, AddB2BPopUpInput>();
        CreateMap<UpdateB2BPopUpBffInput, UpdateB2BPopUpInput>();
    }
}