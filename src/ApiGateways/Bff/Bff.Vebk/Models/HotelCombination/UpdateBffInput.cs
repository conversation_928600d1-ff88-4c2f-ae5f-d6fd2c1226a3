using Contracts.Common.Hotel.DTOs.HotelCombination;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.HotelCombination;

public class UpdateBffInput : HotelCombinationDto
{
    [SwaggerSchema(Nullable = false)]
    public long Id { get; set; }

    /// <summary>
    /// 目的地
    /// </summary>
    public new List<UpdateHotelCombinationDestinationInfo> DestinationInfos { get; set; }

    /// <summary>
    /// SKU配置
    /// </summary>
    public new List<UpdateHotelCombinationSkuInfo> SkuInfos { get; set; }

    /// <summary>
    /// 要删除的SkuId
    /// </summary>
    public List<long> DeleteSkuIds { get; set; } = new List<long>();
}
