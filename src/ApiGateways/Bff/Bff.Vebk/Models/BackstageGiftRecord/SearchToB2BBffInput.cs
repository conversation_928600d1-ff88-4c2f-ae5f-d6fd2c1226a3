using Contracts.Common.Marketing.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.BackstageGiftRecord;

public class SearchToB2BBffInput : PagingInput
{
    [SwaggerSchema(Nullable = false)]
    public long Id { get; set; }

    /// <summary>
    /// 赠送项类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public BackstageGiftType GiftType { get; set; }

    /// <summary>
    /// 赠送备注
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string Describe { get; set; }

    /// <summary>
    /// 赠送操作人
    /// </summary>

    [SwaggerSchema(Nullable = true)]
    public string OperatorName { get; set; }

}
