using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Services.Interfaces;
using Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs.TenantBankAccount;

namespace Bff.Vebk.Services;

public class ReceiptSettlementOrderService : IReceiptSettlementOrderService
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IPaymentApiCaller _paymentApiCaller;

    private readonly IMapper _mapper;

    public ReceiptSettlementOrderService(
        ITenantApiCaller tenantApiCaller,
        IOrderApiCaller orderApiCaller,
        IPaymentApiCaller paymentApiCaller,
        IMapper mapper)
    {
        _tenantApiCaller = tenantApiCaller;
        _orderApiCaller = orderApiCaller;
        _paymentApiCaller = paymentApiCaller;
        _mapper = mapper;
    }

    public async Task<List<ExportDetailOutput>> ExportDetail(params long[] settlementOrderIds)
    {
        var result = new List<ExportDetailOutput>();

        //查询付款单抵充退款明细
        var exportDetails = await _orderApiCaller.SearchExportReceiptSettlementOrderDetail(new SearchDetailExportInput
        {
            OrderTypes = new List<OrderType> { 
                OrderType.Hotel, 
                OrderType.TravelLineOrder, 
                OrderType.ScenicTicket,
                OrderType.CarProduct 
            },
            ReceiptSettlementOrderId = settlementOrderIds.ToList(),
            ReceiptSettlementBusinessTypes = new List<ReceiptSettlementBusinessType>() {
                    ReceiptSettlementBusinessType.OffsetOrder,
                    ReceiptSettlementBusinessType.RefundOrder
                }
        });

        foreach (var id in settlementOrderIds)
        {
            var exportResponse = await _orderApiCaller.ExportReceiptSettlementOrderDetail(new ExportDetailInput
            {
                SettlementOrderId = id
            });
            if (exportResponse.Status != ReceiptSettlementOrderStatus.ReceiptComplete)
            {
                //默认展示配置的收款账户
                var receivableAccount = await GetReceivableAccount();
                exportResponse.BankName = receivableAccount.BankName;
                exportResponse.BankCode = receivableAccount.BankCode;
                exportResponse.BankAccount = receivableAccount.AccountNo;
                exportResponse.AccountName = receivableAccount.AccountName;
                exportResponse.OpeningBankCode = receivableAccount.OpeningBankCode;
                exportResponse.BranchName = receivableAccount.BranchName;
                exportResponse.TenantBankAccountType = receivableAccount.TenantBankAccountType;
                exportResponse.BankAccountType = receivableAccount.BankAccountType;
            }
            else
            {
                var receivableAccounts = await _paymentApiCaller.GetTenantBankAccount(new GetTenantBankAccountInput()
                {
                    AccountName = exportResponse.AccountName,
                    AccountNo = exportResponse.BankAccount,
                    BankCode = exportResponse.BankCode,
                });
                var receivableAccount = receivableAccounts?.FirstOrDefault();
                exportResponse.TenantBankAccountType = receivableAccount?.TenantBankAccountType ?? 0;
                exportResponse.BankAccountType = receivableAccount?.BankAccountType ?? 0;
            }

            //查询充值单数据
            var reChargeOrderIds = exportResponse.CreditRechargeOrder?.Order.Select(x => x.OrderId).ToArray();
            if (reChargeOrderIds != null && reChargeOrderIds.Any())
            {
                var reChargeOrders = (await _tenantApiCaller.AgencyCreditChargeDetails(reChargeOrderIds)).ToList();
                foreach (var item in exportResponse.CreditRechargeOrder.Order)
                {
                    var reChargeOrder = reChargeOrders.FirstOrDefault(x => x.Id == item.OrderId);
                    if (reChargeOrder == null) continue;
                    item.CreateOrderDate = reChargeOrder.CreateTime;
                    item.PayType = reChargeOrder.PayType;
                    item.PayChannel = reChargeOrder.PayChannel;
                    item.FinishTime = reChargeOrder.ConfirmTime;
                    item.PaymentCurrencyCode = reChargeOrder.CurrencyCode;
                    item.AgencyName = reChargeOrder.AgencyName;
                }
            }

            var exportDetail = exportDetails.Where(x => x.SettlementOrderId == exportResponse.SettlementOrderId).ToList();
            exportResponse.Hotel.Order = FillOrderByExport<ReceivablesHotelOrderOutput>(exportResponse.Hotel.Order, exportDetail);
            exportResponse.Line.Order = FillOrderByExport<ReceivablesLineOrderOutput>(exportResponse.Line.Order, exportDetail);
            exportResponse.ScenicTicket.Order = FillOrderByExport<ReceivablesScenicOrderOutput>(exportResponse.ScenicTicket.Order, exportDetail);
            exportResponse.CarProduct.Order = FillOrderByExport<ReceivablesCarProductOrderOutput>(exportResponse.CarProduct.Order, exportDetail);

            result.Add(exportResponse);
        }
        return result;
    }

    private IEnumerable<T> FillOrderByExport<T>(IEnumerable<T> orders,List<SearchDetailExportOutput> exportDetail)  where T : ReceivablesInfo
    {
        if (orders.Any() is false)
            return orders;

        foreach (var order in orders)
        {
            if (typeof(T) == typeof(ReceivablesHotelOrderOutput))
            {
                var hotelOrder = order as ReceivablesHotelOrderOutput;
                if (!hotelOrder.IsStatistics)
                    continue;
            }
            order.ReceivedAmount = order.TotalAmount;
            var detail = exportDetail.Where(x => x.BaseOrderId == order.BaseOrderId).ToList();
            if (detail.Any())
            {
                order.RefundTotalAmount = detail
                    .Where(x => x.BusinessType == ReceiptSettlementBusinessType.RefundOrder)
                    .Sum(x => x.TotalAmount);

                order.OffsetTotalAmount = detail
                    .Where(x => x.BusinessType == ReceiptSettlementBusinessType.OffsetOrder)
                    .Sum(x => x.TotalAmount);
            }
            order.ReceivedAmount = order.TotalAmount + order.RefundTotalAmount + order.OffsetTotalAmount;
        }

        return orders;
    }

    /// <summary>
    /// 获取配置的收款结算账户
    /// </summary>
    /// <returns></returns>
    public async Task<GetTenantBankAccountOutput> GetReceivableAccount()
    {
        var receivableAccount = new GetTenantBankAccountOutput();
        //查询B2B设置增加默认收款账号信息
        var financialSetting = await _tenantApiCaller.GetFinancialSetting();
        if (long.TryParse(financialSetting?.ReceivablesAccounts, out long receivablesAccountId))
        {
            //查询收款账号基础信息
            receivableAccount = await _paymentApiCaller.GetTenantBankAccount(receivablesAccountId);
        }
        return receivableAccount;
    }
}