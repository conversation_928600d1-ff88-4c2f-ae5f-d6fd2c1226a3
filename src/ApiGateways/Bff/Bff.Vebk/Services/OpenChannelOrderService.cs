using Bff.Vebk.Callers;
using Bff.Vebk.Models.OpenChannelOrder;
using Bff.Vebk.Models.OpenSupplierOrder;
using Bff.Vebk.Services.Interfaces;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.OpenSupplier;

namespace Bff.Vebk.Services;

public class OpenChannelOrderService : IOpenChannelOrderService
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IOpenPlatformService _openPlatformService;
    
    public OpenChannelOrderService(
        IOrderApiCaller orderApiCaller,
        IProductApiCaller productApiCaller,
        IOpenPlatformService openPlatformService)
    {
        _orderApiCaller = orderApiCaller;
        _productApiCaller = productApiCaller;
        _openPlatformService = openPlatformService;
    }

    public async Task CheckChannelOrderNo(CheckOpenChannelOrderNoDto input)
    {
        switch (input)
        {
            /*
             * 1.携程 - 同一批新订订单号重复异常标签
             * 2.其他渠道不处理
             */
            case {SellingPlatform: SellingPlatform.Ctrip, DuplicateChannelOrderNumberTag: true}:
                throw new BusinessException(ErrorTypes.Order.OtaOrderNoAlreadyExists);
            /*
             * 1. 携程 - 同一批次第一条订单才做校验.
             * 2. 其他渠道直接校验
             */
            case {SellingPlatform: SellingPlatform.Ctrip, InitialOrderTag: false}:
                return;
        }

        //拆解渠道单号
        var dismantleChannels = _openPlatformService.DismantleChannelOrderNos(new DismantleChannelOrderNosInput
        {
            SellingPlatform = input.SellingPlatform, 
            ChannelOrderNos = input.ChannelOrderNos
        });
        var checkResponse = await _orderApiCaller.SetTenantId(input.TenantId)
            .CheckChannelOrder(new CheckChannelOrderInput {ChannelOrderNo = dismantleChannels.ChannelOrderNos});
        if (checkResponse.IsExists)
        {
            throw new BusinessException(ErrorTypes.Order.OtaOrderNoAlreadyExists);
        }
    }

    public async Task<List<AddOpenSupplierOrderExtraInfoItem>> CreateOrderMatchExtraInfos(CreateOrderMatchExtraInfosDto dto)
    {
        var result = new List<AddOpenSupplierOrderExtraInfoItem>();

        var skuExtraInfos = await _productApiCaller.QueryOpenSupplierSkuExtraInfo(new QueryOpenSupplierSkuExtraInfoInput
        {
            ProductIds = new List<string> { dto.ProductId },
            OptionIds = new List<string> { dto.OptionId },
            SkuIds = dto.SkuIds,
            OpenSupplierTypes = new List<OpenSupplierType> { dto.OpenSupplierType }
        });
        if (skuExtraInfos.Any() is false) return result;

        // 与渠道传入附加信息编码做匹配
        var groupExtraInfos = skuExtraInfos.FirstOrDefault().Sub.SelectMany(x => x.ExtraInfos)
            .GroupBy(g => new { g.DataType, g.ValueType })
            .Select(x =>
            {
                var outputItem = new GetSkuExtraInfoItem { DataType = x.Key.DataType, ValueType = x.Key.ValueType };

                // 返回多个sku附加信息的选项值重复默认取第一个
                outputItem.ValueOptions = x.SelectMany(o => o.ValueOptions)
                    .GroupBy(og => og.Key)
                    .Select(os =>
                    {
                        var firstVp = os.OrderBy(ob => ob.Id).First();
                        var valueOptionsOutItem = new GetSkuExtraInfoValueOption
                        {
                            Id = firstVp.Id, Key = firstVp.Key, Value = firstVp.Value
                        };
                        return valueOptionsOutItem;
                    })
                    .ToList();

                return outputItem;
            })
            .ToList();

        foreach (var extraInfoItem in groupExtraInfos)
        {
            var matchExtraInfos = extraInfoItem.ValueOptions.Where(x => dto.MatchExtraInfoIds.Contains(x.Id))
                .Select(x => new AddOpenSupplierOrderExtraInfoItem
                {
                    DataType = extraInfoItem.DataType, OptionKey = x.Key, OptionValue = x.Value
                })
                .ToList();
            result.AddRange(matchExtraInfos);
        }

        // 一种类型只能匹配一个.存在多个直接清空列表不做保存
        if (result.GroupBy(x => x.DataType).Any(x => x.Count() > 1))
            return new List<AddOpenSupplierOrderExtraInfoItem>();

        return result;
    }
    
}