using Bff.Vebk.Callers;
using Bff.Vebk.Models.OpenSupplierOrder;
using Bff.Vebk.Services.Interfaces;
using Contracts.Common.Order.DTOs.CarProductSupplierOrder;
using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Services.OpenSupplierNotifyProcessing;

/// <summary>
/// 开放平台供应端 - 用车询价查询通知处理服务
/// </summary>
public class CarSearchNotifyProcessingService : IOpenSupplierNotifyProcessingService
{
    private readonly IOrderApiCaller _orderApiCaller;
    public CarSearchNotifyProcessingService(
        IOrderApiCaller orderApiCaller)
    {
        _orderApiCaller = orderApiCaller;
    }
    public OpenSupplierNotifyType NotifyType => OpenSupplierNotifyType.CarSearchNotify;
    public async Task<SupplierOrderBaseNotifyBffOutput> Process(SupplierNotifyProcessingBodyDto notifyBody)
    {
        if (notifyBody.TenantId is null || notifyBody.BaseOrderId is null)
        {
            throw new BusinessException(ErrorTypes.Order.OrderNotFind);
        }
        
        long tenantId = notifyBody.TenantId!.Value;
        long baseOrderId = notifyBody.BaseOrderId!.Value;
        var quoteNotifyBody = notifyBody.JObject.ToObject<OpenSupplierCarSearchNotifyBody>();
        
        var result = new SupplierOrderBaseNotifyBffOutput
        {
            Code = 200
        };

        //询价通知结果处理
        var processRequest = new CarProductSupplierOrderQuoteNotifyInput
        {
            BaseOrderId = baseOrderId,
            TenantId = tenantId,
            NotifyId = quoteNotifyBody.NotifyId,
            SearchId = quoteNotifyBody.SearchId,
            Results = new List<CarProductSupplierOrderQuoteItem>()
        };
        processRequest.Results = quoteNotifyBody.Results.Select(x => new
                CarProductSupplierOrderQuoteItem
                {
                    ResultId = x.ResultId,
                    Price = x.Price,
                    Currency = x.Currency,
                    Vehicle = new CarProductSupplierOrderQuoteVehicleItem()
                    {
                        MaxPassengerCount = x.Vehicle.MaxPassengerCount,
                        MaxLuggageCount = x.Vehicle.MaxLuggageCount
                    }
                })
            .ToList();
        await _orderApiCaller.CarProductSupplierOrderQuoteNotifyProcess(processRequest);
        
        return result;
    }
}