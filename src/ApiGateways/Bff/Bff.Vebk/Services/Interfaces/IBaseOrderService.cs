using Bff.Vebk.Models.Order;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Services.Interfaces;

public interface IBaseOrderService
{
    /// <summary>
    /// 获取相关人员信息
    /// </summary>
    /// <value>目前只支持查单个订单相关人员信息</value>
    /// <returns></returns>
    Task<QueryOrderRelatedPersonsInfoBffOutput> GetRelatedPersonsInfo(QueryOrderRelatedPersonsInfoBffInput input);
}