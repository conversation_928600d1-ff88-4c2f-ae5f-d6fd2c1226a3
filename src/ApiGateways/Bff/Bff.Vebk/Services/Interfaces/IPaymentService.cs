using Bff.Vebk.Models.OrderPayment;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Payment.DTOs.Currency;

namespace Bff.Vebk.Services.Interfaces;

public interface IPaymentService
{
    /// <summary>
    /// 汇率转换计算
    /// </summary>
    Task<List<GetExchangeRateOutput>> GetCurrencyExchangeRateList(List<GetExchangeRatesInput> input);

    /// <summary>
    /// 获取汇率信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<OrderPriceExchangeRateOutput> GetOrderPriceExchange(OrderPriceExchangeRateInput input);

    /// <summary>
    /// 分销商币种汇率信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetAgencyCurrencyExchangeRateOutput> GetAgencyCurrencyExchangeRate(GetAgencyCurrencyExchangeRateInput input);
}
