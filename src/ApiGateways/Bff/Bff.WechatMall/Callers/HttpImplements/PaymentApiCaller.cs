using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.DTOs.YeeMerConfig;
using Microsoft.Extensions.Options;

namespace Bff.WechatMall.Callers.HttpImplements;

public class PaymentApiCaller : HttpCallerBase, IPaymentApiCaller
{
    public PaymentApiCaller(IOptions<ServicesAddress> servicesAddress, IHttpClientFactory httpClientFactory)
        : base(servicesAddress.Value.Payment, httpClientFactory)
    {
    }

    #region CurrencyExchange

    public Task<List<GetExchangeRateOutput>> GetExchangeRates(IEnumerable<GetExchangeRatesInput> input)
    {
        var relativePath = "/CurrencyExchangeRate/Get";
        return PostAsync<IEnumerable<GetExchangeRatesInput>, List<GetExchangeRateOutput>>(relativePath, input);
    }

    #endregion

    #region OrderPayment

    public Task<GetPaymentInfoOutput> GetOrderPaymentInfo(GetPaymentInfoInput input)
    {
        var relativePath = "/Order/GetPaymentInfo";
        return PostAsync<GetPaymentInfoInput, GetPaymentInfoOutput>(relativePath, input);
    }

    public Task<YeeMerPaymentConfigOutput> GetYeeMerPaymentConfig(long tenantId)
    {
        var relativePath = $"/YeeMerConfig/GetPaymentConfig?tenantId={tenantId}";
        return GetAsync<YeeMerPaymentConfigOutput>(relativePath);
    }

    #endregion

    #region Pay

    public Task<OrderPayOutput> HuizhiWechatPay(HuizhiWechatPayInput input)
    {
        var relativePath = "/Order/HuizhiWechatPay";
        return PostAsync<HuizhiWechatPayInput, OrderPayOutput>(relativePath, input);
    }

    public Task<OrderPayOutput> CreditCardGuaranteePay(CreditCardOrderPayInput input)
    {
        var relativePath = "/Order/CreditCardGuaranteePay";
        return PostAsync<CreditCardOrderPayInput, OrderPayOutput>(relativePath, input);
    }

    #endregion
}
