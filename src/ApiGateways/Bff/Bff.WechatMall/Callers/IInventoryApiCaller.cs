using Common.Caller;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Product.DTOs.SkuCalendarPrice;

namespace Bff.WechatMall.Callers;

public interface IInventoryApiCaller : IHttpCallerBase
{
    /// <summary>
    /// 获取产品日历价库存信息
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<CapCalendarInventory>> GetCalendarInventories(
        Contracts.Common.Inventory.DTOs.GetCalendarInventoryInput input);

    /// <summary>
    /// 批量获取产品SKU库存
    /// </summary>
    Task<IEnumerable<GeneralInventoryOutput>> BatchGeneralInventories(List<GetGeneralInventoryInput> input);

    /// <summary>
    /// 获取产品库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GeneralInventoryOutput>> GeneralGetInventories(GetGeneralInventoryInput input);

}