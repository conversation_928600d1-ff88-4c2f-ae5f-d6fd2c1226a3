using Common.Caller;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.CustomerUser;
using Contracts.Common.User.DTOs.Daren;
using Contracts.Common.User.DTOs.DarenBonus;
using Contracts.Common.User.DTOs.DarenProductCommission;
using Contracts.Common.User.DTOs.DarenUser;
using Contracts.Common.User.DTOs.Vip;
using Contracts.Common.User.Enums;

namespace Bff.WechatMall.Callers;

public interface IUserApiCaller : IHttpCallerBase
{
    #region Security

    /// <exception cref="ErrorTypes.User.GetCodeTooFrequent"></exception>
    Task SendCaptcha(CaptchaDTO input);

    Task<bool> CheckCaptcha(CaptchaDTO input);

    #endregion

    #region CustomerUser

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <returns></returns>
    Task<UserInfoOutput> GetCustomerUserInfo();

    /// <summary>
    /// 根据OpenId获取UserId
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<long> GetUserIdByOpenId(GetUserIdByOpenIdInput input);

    /// <summary>
    /// 获取用户当前绑定的openId
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="userBindPlatformType"></param>
    /// <returns></returns>
    Task<string> GetBindingCode(long userId, UserBindPlatformType userBindPlatformType);

    /// <summary>
    /// 批量获取用户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<CustomerUserDTO>> GetCustomers(GetCustomersInput input);

    /// <summary>
    /// 注册或绑定
    /// </summary>
    /// <exception cref="ErrorTypes.User.PhoneNumberIsDisabled"></exception>
    /// <returns></returns>
    Task<CustomerUserDTO> CreateOrBind(CreateOrBindInput input);

    /// <summary>
    /// 邀请码邀请用户
    /// </summary>
    /// <returns></returns>
    Task<bool> InviteUser(InviteUserInput input);

    /// <summary>
    /// 检查用户是否被邀请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CheckInviteCodeUser(CheckInviteUserInput input);
    #endregion

    #region VIP

    /// <summary>
    /// 获取产品会员折扣
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<DiscountRightsByProductOutput>> GetVipDiscountRightsByProduct(List<DiscountRightsByProductInput> input);

    /// <summary>
    /// 获取酒店会员折扣
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<DiscountRightsByHotelOutput>> GetVipDiscountRightsByHotel(List<DiscountRightsByHotelInput> input);

    /// <summary>
    /// 获取VIP等级信息
    /// </summary>
    /// <param name="levelId"></param>
    /// <returns></returns>
    Task<VipLevelDto> GetVipLevelById(long levelId);

    #endregion

    #region DarenUser

    /// <summary>
    /// 升级成达人
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DarenUserInstall(AddDarenUserInput input);

    #endregion

    #region DarenSetting

    /// <summary>
    /// 获取达人配置
    /// </summary>
    /// <returns></returns>
    Task<DarenSettingDto> GetDarenSetting();

    #endregion

    #region DarenBonus

    /// <summary>
    /// 个人达人奖励汇总
    /// </summary>
    /// <returns></returns>
    Task<SummaryDarenBonus> GetSummaryDarenBonus();

    #endregion

    #region DarenProductCommission

    /// <summary>
    /// 获取产品达人佣金
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetDarenProductCommissionOutput>> GetDarenProductCommission(GetDarenProductCommissionInput input);

    #endregion
}
