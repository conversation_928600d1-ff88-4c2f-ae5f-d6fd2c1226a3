using Cit.Storage.Redis;
using Common;
using Common.Jwt;
using Common.ServicesHttpClient;
using Common.Swagger.Header;
using Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;

namespace Bff.WechatMall.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection ConfigureApplicationServices(this IServiceCollection services,
        IConfiguration configuration,
        IWebHostEnvironment env)
    {
        services.AddBasicServices(env, TenantHeaderRequirement.UnauthorizedAddAndRequired);

        services.AddControllers(options =>
        {
            options.Filters.Add<SetTenantHeaderFilter>();
        });
        services.AddCustomConfiguration(configuration);
        services.AddAutoMapper(System.Reflection.Assembly.GetExecutingAssembly());
        services.AddCallers();
        services.AddServices();
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ClockSkew = TimeSpan.FromSeconds(5),
                    ValidAudience = SysRole.Customer.ToString(),
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtConfig:SecurityKey"]!)),
                };
            });
        services.AddCitRedis(configuration.GetConnectionString("Redis"));

        return services;
    }

    /// <summary>
    /// 配置文件
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    private static IServiceCollection AddCustomConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ServicesAddress>(configuration.GetSection(nameof(ServicesAddress)));
        services.Configure<JwtConfig>(configuration.GetSection(nameof(JwtConfig)));

        return services;
    }

    private static IServiceCollection AddCallers(this IServiceCollection services)
    {
        var types = System.Reflection.Assembly
            .GetExecutingAssembly()
            .GetInterfaceAndImplement(s => s.Name.EndsWith("Caller"));
        foreach (var item in types)
            services.AddSingleton(item.Value, item.Key);

        return services;
    }

    /// <summary>
    /// 批量注册Service
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    private static IServiceCollection AddServices(this IServiceCollection services)
    {
        var types = System.Reflection.Assembly
            .GetExecutingAssembly()
            .GetInterfaceAndImplement(s => s.Name.EndsWith("Service"));
        foreach (var item in types)
            services.AddScoped(item.Value, item.Key);

        return services;
    }
}

