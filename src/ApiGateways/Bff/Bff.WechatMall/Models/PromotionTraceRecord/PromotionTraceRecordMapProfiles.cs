using AutoMapper;
using Contracts.Common.Marketing.DTOs.PromotionTrace;

namespace Bff.WechatMall.Models.PromotionTraceRecord;

public class PromotionTraceRecordMapProfiles : Profile
{
    public PromotionTraceRecordMapProfiles()
    {
        CreateMap<AddPromotionTraceRecordBffInput, AddPromotionTraceRecordInput>()
            .ForMember(f => f.CreateTimeStr, m => m.MapFrom(m => m.CreateTime.ToString("yyyy-MM-ddTHH:mm:ss.fff")));
    }
}