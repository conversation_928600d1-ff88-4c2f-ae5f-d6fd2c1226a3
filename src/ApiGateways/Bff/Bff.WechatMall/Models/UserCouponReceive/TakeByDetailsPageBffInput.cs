using Swashbuckle.AspNetCore.Annotations;

namespace Bff.WechatMall.Models.UserCouponReceive;

public class TakeByDetailsPageBffInput
{
    /// <summary>
    /// 优惠券活动id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long CouponActivityId { get; set; }

    /// <summary>
    /// 优惠券id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long CouponId { get; set; }
}
