using Contracts.Common.Product.Enums;

namespace Bff.WechatMall.Models.FlashSaleItems;

public class GetProductByIdsResponse
{
    public long Id { get; set; }
    public string Title { get; set; }
    public List<string> CityName { get; set; }
    public string Address { get; set; }
    public string Picture { get; set; }
    public decimal? Price { get; set; }
    public decimal? LinePrice { get; set; }
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 票券业务类型
    /// </summary>
    public TicketBusinessType TicketBusinessType { get; set; }
    
    /// <summary>
    /// 售卖类型 0-团购 1-预订
    /// </summary>
    public TicketSaleType TicketSaleType { get; set; }

    #region 线路

    /// <summary>
    /// 出发国家名称
    /// </summary>
    public string DepartureCountryName { get; set; }

    /// <summary>
    /// 出发地城市名称
    /// </summary>
    public string DepartureCityName { get; set; }

    /// <summary>
    /// 目的地国家名称
    /// </summary>
    public string DestinationCountryName { get; set; }

    /// <summary>
    /// 目的地城市名称
    /// </summary>
    public string DestinationCityName { get; set; }


    #endregion
}
