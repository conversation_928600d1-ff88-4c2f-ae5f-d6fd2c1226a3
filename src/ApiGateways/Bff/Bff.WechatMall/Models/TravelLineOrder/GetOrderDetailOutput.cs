using Bff.WechatMall.Models.OrderFieldInformation;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;

namespace Bff.WechatMall.Models.TravelLineOrder;

public class GetOrderDetailOutput
{
    public BaseOrderDetailOutput BaseOrder { get; set; }

    public TravelLineOrderDetailOutput TravelLineOrder { get; set; }

    public IList<OrderTravelerOutput> OrderTravelers { get; set; }

    public IList<TravelLineOrderPriceOutput> OrderPrices { get; set; }

    /// <summary>
    /// 订单字段信息
    /// </summary>
    public List<BffOrderFieldInformationTypeOutput> OrderFields { get; set; }
}

public class BaseOrderDetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 下单用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 下单用户昵称
    /// </summary>
    public string UserNickName { get; set; }

    /// <summary>
    /// 会员等级id
    /// </summary>
    public long VipLevelId { get; set; }

    /// <summary>
    /// 会员等级
    /// </summary>
    public string VipLevelName { get; set; }


    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactsEmail { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 规格名称
    /// </summary>
    public string ProductSkuName { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BaseOrderStatus Status { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 售卖渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public string ChannelOrderNo { get; set; }

    /// <summary>
    /// 订单总额 = DiscountAmount + Payment.Amount
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    public PayType PaymentType { get; set; }

    /// <summary>
    /// 留言
    /// </summary>
    public string Message { get; set; }

    public DateTime CreateTime { get; set; }
}

public class TravelLineOrderDetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 入住房间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    /// <summary>
    /// 预订日期
    /// </summary>
    public DateTime TravelBeginDate { get; set; }

    /// <summary>
    /// 预订结束日期
    /// </summary>
    public DateTime TravelEndDate { get; set; }

    /// <summary>
    /// 认领人Id
    /// </summary>
    public long? ClaimantId { get; set; }

    /// <summary>
    /// 认领人名称
    /// </summary>
    public string ClaimantName { get; set; }

    /// <summary>
    /// 集结点时间
    /// </summary>
    public TimeSpan? RallyPointTime { get; set; }

    /// <summary>
    /// 集结点地址
    /// </summary>
    public string? RallyPointAddress { get; set; }

    /// <summary>
    /// 车牌号
    /// </summary>
    public string TourGuideCarNumber { get; set; }

    /// <summary>
    /// 导游名称
    /// </summary>
    public string TourGuideName { get; set; }

    /// <summary>
    /// 导游电话
    /// </summary>
    public string TourGuidePhoneNumber { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public TravelLineOrderStatus Status { get; set; }

    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 线路产品SkuId
    /// </summary>
    public long LineProductSkuId { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int Days { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int Nights { get; set; }

    public DateTime CreateTime { get; set; }

    #region 产品信息

    /// <summary>
    /// 图片路径
    /// </summary>
    public string ImagePath { get; set; }

    /// <summary>
    /// 使用说明
    /// </summary>
    public string Instructions { get; set; }

    /// <summary>
    /// 温馨提示
    /// </summary>
    public string KindReminder { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }

    /// <summary>
    /// 预订须知
    /// </summary>
    public string? OtherNote { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public List<Contracts.Common.Order.DTOs.TravelLineOrder.FeeInclude> FeeIncludes { get; set; }

    /// <summary>
    /// 成人标准说明
    /// </summary>
    public string? AdultsStandard { get; set; }

    /// <summary>
    /// 儿童标准说明
    /// </summary>
    public string? ChildrenStandard { get; set; }
    
    /// <summary>
    /// 婴儿标准说明
    /// </summary>
    public string? BabyStandard { get; set; }
    
    /// <summary>
    /// 长者标准说明
    /// </summary>
    public string? ElderlyStandard { get; set; }

    #endregion

    #region 退款规则

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    #endregion

    /// <summary>
    /// 经度
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double? Latitude { get; set; }
    
    /// <summary>
    /// 坐标系默认BD09
    /// </summary>
    public CoordinateType CoordinateType { get; set; } = CoordinateType.BD09;
}

public class OrderTravelerOutput
{
    /// <summary>
    /// 1-成人 2-儿童
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IDCard { get; set; }

    public string Phone { get; set; }
}

public class TravelLineOrderPriceOutput
{
    /// <summary>
    /// 1-成人 2-儿童 3-房差价
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 售价类型枚举 0-原价 1-会员价 2-抢购价
    /// </summary>
    public OrderPriceType? PriceType { get; set; }

    /// <summary>
    /// 支付币种 =>用户支付币种，B2B为分销商币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 价格 实际售卖价格
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 原售价
    /// </summary>
    public decimal? OrgPrice { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }
        
    /// <summary>
    /// 子类型关联项id
    /// <value>
    /// <para>线路-套餐票种id</para>
    /// </value>
    /// </summary>
    public long? OrderSubItemId { get; set; }

    /// <summary>
    /// 子类型关联项名称
    /// <value>
    /// <para>线路-套餐票种名称</para>
    /// </value>
    /// </summary>
    public string? OrderSubItemName { get; set; }
}


