using AutoMapper;
using Contracts.Common.Product.DTOs.InformationTemplateFields;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;

namespace Bff.WechatMall.Models.ProductInformationTemplate;

public class ProductInformationTemplateMapProfiles : Profile
{
    public ProductInformationTemplateMapProfiles()
    {
        CreateMap<GetProductTempFieldsDetailOutput, BffProductInformationTemplateOutput>();

        CreateMap<ProductInformationTemplateDetail, BffProductInformationTemplateDetail>();

        CreateMap<InformationTemplateFieldsOutput, BffProductInformationTemplateFieldsOutput>();

        CreateMap<InformationTemplateFieldsOutput, BffProductInformationTemplateFieldsOutput>()
             .ForMember(x => x.FieldValue, x => x.MapFrom(m => m.DefaultValue));// 前端要求加 FieldValue ，和订单详情回显做兼容;
    }
}
