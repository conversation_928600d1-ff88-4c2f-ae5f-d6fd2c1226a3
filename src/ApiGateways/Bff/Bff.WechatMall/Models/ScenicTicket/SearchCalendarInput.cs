using Contracts.Common.Scenic.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.WechatMall.Models.ScenicTicket;

public class SearchCalendarInput
{
    /// <summary>
    /// 景区Id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long ScenicSpotId { get; set; }

    /// <summary>
    /// 指定门票id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketId { get; set; }

    [SwaggerSchema(Nullable = false)]
    public DateTime BeginDate { get; set; }

    [SwaggerSchema(Nullable = false)]
    public DateTime EndDate { get; set; }
    
    /// <summary>
    /// 门票时段id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? TimeSlotId { get; set; }
}
