using Common.Swagger;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Marketing.Enums;
using Contracts.Common.Order.DTOs.TicketOrder;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Microsoft.AspNetCore.Mvc;
using Contracts.Common.Product.DTOs.ProductSku;
using Contracts.Common.Product.DTOs.SkuCalendarPrice;
using Bff.WechatMall.Services.Interfaces;
using Bff.WechatMall.Callers;
using Bff.WechatMall.Models.FlashSale;
using Contracts.Common.Marketing.DTOs.FlashSale;
using Contracts.Common.User.DTOs.Vip;
using Common.Jwt;
using Bff.WechatMall.Models.TicketOrder;
using Microsoft.AspNetCore.Authorization;

namespace Bff.WechatMall.Controllers;
/// <summary>
/// 券类订单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class TicketOrderController : ControllerBase
{
    private readonly ICurrencyExchangeRateService _currencyExchangeRateService;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IInventoryApiCaller _inventoryApiCaller;
    private readonly IMarketingApiCaller _marketingApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;

    public TicketOrderController(ICurrencyExchangeRateService currencyExchangeRateService,
        IProductApiCaller productApiCaller,
        IInventoryApiCaller inventoryApiCaller,
        IMarketingApiCaller marketingApiCaller,
        IUserApiCaller userApiCaller,
        IOrderApiCaller orderApiCaller)
    {
        _currencyExchangeRateService = currencyExchangeRateService;
        _productApiCaller = productApiCaller;
        _inventoryApiCaller = inventoryApiCaller;
        _marketingApiCaller = marketingApiCaller;
        _userApiCaller = userApiCaller;
        _orderApiCaller = orderApiCaller;
    }

    /// <summary>
    /// 下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Marketing.UserCouponDisabled,
            ErrorTypes.Product.ProductDisabled,
            ErrorTypes.Inventory.ProductInventoryNotEnough,
            ErrorTypes.Order.InvalidTravelDate,
            ErrorTypes.Order.SomeDatesAreUnavailable)]
    public async Task<long> Create(CreateInput input)
    {
        CurrentUser user = HttpContext.User.ParseUserInfo<CurrentUser>();
        GetTicketProductInput getTicketProductInput = new()
        {
            ProductId = input.ProductId,
            ProductSkuId = input.ProductSkuId,
            Quantity = input.Quantity,
            TravelDate = input.TravelDate,
            ProductEnabled = true
        };
        var response = await GetProduct(getTicketProductInput);
        //多币种
        string paymentCurrencyCode = Currency.CNY.ToString();
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = response.Product.CostCurrencyCode,
            SaleCurrencyCode = response.Product.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });
        OrderMultPriceDto orderMultPrice = new()
        {
            SaleCurrencyCode = response.Product.SaleCurrencyCode,
            OrgPrice = response.Sku.SellingPrice,
            CostCurrencyCode = response.Product.CostCurrencyCode,
            OrgCostPrice = response.Sku.CostPrice,
            CostPrice = response.Sku.CostPrice,
            CostPriceType = OrderPriceType.Default,
            PaymentCurrencyCode = paymentCurrencyCode,
            Price = Math.Round(response.Sku.SellingPrice * priceExchangeRate.ExchangeRate, 2),
            PriceType = OrderPriceType.Default,
            Quantity = input.Quantity,
            ExchangeRate = priceExchangeRate.ExchangeRate,
            CostExchangeRate = priceExchangeRate.CostExchangeRate
        };

        //限时抢购
        if (input.FlashSaleId > 0)
        {
            var flashSaleOrderResponse = await GetFlashSalePreOrder(new FlashSaleOrderInput
            {
                ProductType = FlashSaleItemsType.Ticket_Group,
                FlashSaleId = input.FlashSaleId,
                FlashSaleOrderItems = new List<FlashSaleOrderItem> {
                   new FlashSaleOrderItem() {
                       FlashSaleItemId = input.FlashSaleItemId,
                       ProductId=input.ProductId,
                       SkuId=input.ProductSkuId,
                       Quantity=input.Quantity
                   }
                }
            });
            var flashSaleItem = flashSaleOrderResponse
                .FlashSaleItems
                .FirstOrDefault(x => x.SkuId == input.ProductSkuId);
            //设置为抢购价
            if (flashSaleItem.CostPriceValue.HasValue)
            {
                orderMultPrice.CostPrice = flashSaleItem.CostPriceType switch
                {
                    FlashSaleItemsPriceType.Appoint => flashSaleItem.CostPriceValue.Value,
                    FlashSaleItemsPriceType.Derate =>
                    orderMultPrice.CostPrice > flashSaleItem.CostPriceValue.Value ?
                    (orderMultPrice.CostPrice - flashSaleItem.CostPriceValue.Value) : 0,
                    _ => orderMultPrice.CostPrice
                };
                orderMultPrice.CostPriceType = OrderPriceType.FlashSalePrice;
            }
            orderMultPrice.Price = flashSaleItem.SellingPriceType switch
            {
                FlashSaleItemsPriceType.Appoint => flashSaleItem.SellingPriceValue * orderMultPrice.ExchangeRate,
                FlashSaleItemsPriceType.Derate =>
                orderMultPrice.Price > flashSaleItem.SellingPriceValue ?
                    (orderMultPrice.Price - flashSaleItem.SellingPriceValue * orderMultPrice.ExchangeRate) : 0,
                _ => orderMultPrice.Price
            };
            orderMultPrice.PriceType = OrderPriceType.FlashSalePrice;
        }

        if (orderMultPrice.PriceType == OrderPriceType.Default)//默认售价
        {
            //计算价格
            var priceInput = new GetSingleProductMultPriceInput
            {
                ProductType = ProductType.Ticket,
                Price = orderMultPrice.Price,
                ProductId = response.Product.Id,
                ProductSkuId = response.Sku.Id
            };
            var productMultPrices = await GetProductMultPrices(priceInput.ProductType, priceInput);
            var productMultPriceOutput = productMultPrices.Single(r => r.ProductId == input.ProductId && r.ProductSkuId == input.ProductSkuId);
            var multPrice = productMultPriceOutput.MultPrice;
            if (multPrice.VipPrice.HasValue)
            {
                //设置为会员价
                orderMultPrice.PriceType = OrderPriceType.VipPrice;
                orderMultPrice.Price = multPrice.VipPrice.Value;
            }
        }
        //验证优惠券
        var totalAmount = orderMultPrice.Price * orderMultPrice.Quantity;
        var discountAmount = 0m;//优惠金额
        var discountItems = new List<OrderDiscountItemDto>();
        if (input.UserCouponId > 0)
        {
            var request = new GetOrderUserCouponsInput
            {
                UserId = user.UserId,
                UserCouponId = input.UserCouponId,
                ProductType = LimitProductType.Ticket,//卡券
                OrderProductInfos = new List<OrderProductInfo>
                {
                    new OrderProductInfo()
                    {
                       ProductId = response.Product.Id,
                       ItemId = response.Sku.Id,
                       OrderAmount = totalAmount
                    }
                }
            };
            var orderUserCoupon = await GetOrderUserCoupon(request);
            if (orderUserCoupon?.IsEnable is not true)
            {
                throw new BusinessException(ErrorTypes.Marketing.UserCouponDisabled);//优惠券不可用
            }
            discountItems.Add(new OrderDiscountItemDto
            {
                DiscountType = OrderDiscountType.UserCoupon,
                DiscountId = orderUserCoupon.UserCouponId,
                DiscountAmount = orderUserCoupon.Discount,
                Title = orderUserCoupon.CouponName
            });
            discountAmount = orderUserCoupon.Discount;//优惠金额
        }

        //生成主订单
        var customerInfo = await _userApiCaller.GetCustomerUserInfo();

        var createInput = new CreateTicketOrderInput()
        {
            SellingChannels = SellingChannels.WechatMall,
            SellingPlatform = input.SellingPlatform,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            Quantity = input.Quantity,
            Message = input.Message,
            UserInfo = new OrderUserInfo()
            {
                TenantId = user.Tenant,
                UserId = user.UserId,
                UserNickName = customerInfo?.NickName ?? user.NickName,
                VipLevelId = customerInfo?.VipLevel?.VipLevelId ?? 0,
                VipLevelName = customerInfo?.VipLevel?.Name
            },
            Product = response,
            DiscountItems = discountItems,
            AmountInfo = new OrderAmountInfo()
            {
                OrderMultPrice = orderMultPrice,
                TotalAmount = totalAmount,
                DiscountAmount = discountAmount,
                PaymentAmount = totalAmount - discountAmount,
                PaymentCurrencyCode = paymentCurrencyCode,
            },
            TraceId = input.TraceId,
            Remark = "",
            TravelDate = input.TravelDate,
            FlashSaleInfo = new FlashSaleInfo() { FlashSaleId = input.FlashSaleId, FlashSaleItemId = input.FlashSaleItemId },
            OperationUser = new OperationUserDto()
            {
                UserType = Contracts.Common.Order.Enums.UserType.Customer,
                UserId = user.UserId,
                Name = user.NickName
            }
        };

        return await _orderApiCaller.TicketOrderCreate(createInput);
    }

    /// <summary>
    /// 获取产品信息
    /// </summary>
    private async Task<TicketOrderGetProductOutput> GetProduct(GetTicketProductInput input)
    {
        //获取产品和Sku信息
        var response = await _productApiCaller.TicketOrderGetProduct(input.ProductId, input.ProductSkuId);

        //判断产品是否可售
        if (input.ProductEnabled && (!response.Product.Enabled || !response.Sku.Enabled))
            throw new BusinessException(ErrorTypes.Product.ProductDisabled);

        //是否预订产品
        switch (response?.Product.TicketSaleType)
        {
            case TicketSaleType.GroupPurchase:
                {
                    //验证产品总库存
                    var generalInventoryResponses = await _inventoryApiCaller.BatchGeneralInventories(new List<Contracts.Common.Inventory.DTOs.GetGeneralInventoryInput> {
                        new (){ ProductId = input.ProductId,  ItemIds = new[] { input.ProductSkuId } }
                    });

                    var generalInventory = generalInventoryResponses.FirstOrDefault(x => x.ItemId == input.ProductSkuId);
                    if (generalInventory?.Enabled is not true
                        || generalInventory.AvailableQuantity < input.Quantity)
                    {
                        throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);
                    }
                }
                break;
            case TicketSaleType.Booking:
                {
                    if (input.TravelDate.HasValue is false)
                        throw new BusinessException(ErrorTypes.Order.InvalidTravelDate);
                    var request = new GetProductSkuCalendarPriceInput()
                    {
                        ProductSkuId = input.ProductSkuId,
                        StartDate = input.TravelDate.Value,
                        EndDate = input.TravelDate.Value
                    };
                    var checkReponse = await _productApiCaller.GetProductSkuCalendarPrice(request);
                    var skuCalendarPrice = checkReponse?.SkuCalendarPrices.FirstOrDefault();
                    if (skuCalendarPrice?.Price is null || skuCalendarPrice?.Enabled != true)
                        throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);//该日期不可下单
                    //提前预订天数
                    if (input.TravelDate.Value < DateTime.Today.AddDays(response.Product.ReservationDaysInAdvance))
                        throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);//该日期不可下单

                    //库存是否可用
                    if (skuCalendarPrice.AvailableQuantity < input.Quantity)
                        throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);
                    //出行日期价格
                    response.Sku.SellingPrice = skuCalendarPrice.Price.Value;
                    response.Sku.CostPrice = skuCalendarPrice.CostPrice.Value;
                }
                break;
        }
        return response;
    }

    private async Task<FlashSalePreOrderOutput> GetFlashSalePreOrder(FlashSaleOrderInput request)
    {
        var input = new FlashSalePreOrderInput
        {
            FlashSaleId = request.FlashSaleId,
            ProductType = request.ProductType,
            FlashSaleItems = request.FlashSaleOrderItems.Select(x => new FlashSaleOrderItemInput { FlashSaleItemId = x.FlashSaleItemId })
        };
        var result = await _marketingApiCaller.GetFlashSalePreOrder(input);
        var flashSaleItems = result.FlashSaleItems;

        foreach (var item in request.FlashSaleOrderItems)
        {
            var flashSaleItem = flashSaleItems.FirstOrDefault(
                x => x.Id == item.FlashSaleItemId
                && x.ProductId == item.ProductId
                && x.SkuId == item.SkuId
                && x.SkuSubClass == item.SkuSubClass);
            //预先检查
            if (item.Quantity > flashSaleItem.AvailableInventory)
                throw new BusinessException(ErrorTypes.Marketing.FlashSaleInventoryNotEnough);
            //校验sku限购数
            var quantity = request.FlashSaleOrderItems
                .Where(x => x.SkuId == item.SkuId)
                .Sum(s => s.Quantity);
            if (quantity > flashSaleItem.UserLimit)
                throw new BusinessException(ErrorTypes.Marketing.FlashSaleOverProductLimit);
        }
        return result;
    }

    private async Task<IList<ProductMultPriceOutput>> GetProductMultPrices(ProductType productType, params GetProductMultPriceInput[] inputs)
    {
        var productIds = inputs.Select(x => x.ProductId);
        //计算会员价
        var discountRights = await GetDiscountRightsByProduct(productType, productIds);
        List<ProductMultPriceOutput> outputs = new();
        foreach (var input in inputs)
        {
            var price = input.Price;
            ProductMultPriceOutput calculatePriceOutput = new()
            {
                ProductId = input.ProductId,
                ProductSkuId = input.ProductSkuId,
                MultPrice = new ProductMultPrice
                {
                    Price = price
                }
            };
            var discountRight = discountRights.FirstOrDefault(x => x.ProductId == input.ProductId);
            if (discountRight?.DiscountValue > 0)
            {
                calculatePriceOutput.MultPrice.VipPrice =
                    decimal.Round(price * (discountRight.DiscountValue / 10), 2);
            }
            outputs.Add(calculatePriceOutput);
        }
        return outputs;
    }
    private async Task<IEnumerable<DiscountRightsByProductOutput>> GetDiscountRightsByProduct(
        ProductType productType,
        IEnumerable<long> productIds)
    {
        Contracts.Common.User.Enums.VipRightsProductType vipRightsProductType = productType switch
        {
            ProductType.Ticket => Contracts.Common.User.Enums.VipRightsProductType.Ticket,
            ProductType.Mail => Contracts.Common.User.Enums.VipRightsProductType.Mail,
            ProductType.Line => Contracts.Common.User.Enums.VipRightsProductType.Line,
            _ => throw new NotImplementedException()
        };
        var inputs = productIds.Select(x => new Contracts.Common.User.DTOs.Vip.DiscountRightsByProductInput
        {
            ProductType = vipRightsProductType,
            ProductId = x,
        }).ToList();
        var response = await _userApiCaller.GetVipDiscountRightsByProduct(inputs);
        return response;
    }

    private async Task<OrderUserCouponOutput> GetOrderUserCoupon(GetOrderUserCouponsInput request)
    {
        var response = await _marketingApiCaller.GetOrderUserCoupons(request);
        var result = response.Where(x => x.UserCouponId == request.UserCouponId).FirstOrDefault();
        return result;
    }
}
