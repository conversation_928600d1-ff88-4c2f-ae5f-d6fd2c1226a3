using AutoMapper;
using Bff.WechatMall.Callers;
using Bff.WechatMall.Models.GDSHotel;
using Bff.WechatMall.Models.HotelOrder;
using Bff.WechatMall.Services.Interfaces;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Marketing.DTOs.UserCoupon;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.ExportPdf;
using Contracts.Common.Order.DTOs.GDSOrder;
using Contracts.Common.Order.DTOs.HotelOrder;
using Contracts.Common.Order.DTOs.HotelOrder.GDS;
using Contracts.Common.Order.DTOs.HotelSupplierOrderRecord;
using Contracts.Common.Order.DTOs.OrderCommission;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.DTOs.YouxiaHotel;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.User.DTOs.Vip;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Bff.WechatMall.Controllers;
/// <summary>
/// 酒店日历房订单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IMarketingApiCaller _marketingApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly ICurrencyExchangeRateService _currencyExchangeRateService;
    private readonly IGDSHotelService _gdsHotelService;
    private readonly IMapper _mapper;

    private readonly ITenantApiCaller _tenantApiCaller;

    public HotelOrderController(IOrderApiCaller orderApiCaller,
        IHotelApiCaller hotelApiCaller,
        IResourceApiCaller resourceApiCaller,
        IMarketingApiCaller marketingApiCaller,
        IUserApiCaller userApiCaller,
        ICurrencyExchangeRateService currencyExchangeRateService,
        ITenantApiCaller tenantApiCaller,
        IMapper mapper,
        IGDSHotelService gdsHotelService)
    {
        _orderApiCaller = orderApiCaller;
        _hotelApiCaller = hotelApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _marketingApiCaller = marketingApiCaller;
        _userApiCaller = userApiCaller;
        _currencyExchangeRateService = currencyExchangeRateService;
        _mapper = mapper;
        _tenantApiCaller = tenantApiCaller;
        _gdsHotelService = gdsHotelService;
    }

    /// <summary>
    /// 试单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(CheckSaleOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> PreOrder(CheckSaleInput input)
    {
        var result = await PreOrderCheckSale(input);
        return Ok(result);
    }

    /// <summary>
    /// 提交订单
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(CreateHotelOrderOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Marketing.UserCouponDisabled, ErrorTypes.Order.SomeDatesAreUnavailable)]
    public async Task<IActionResult> Submit(SubmitInput input)
    {
        CurrentUser user = HttpContext.User.ParseUserInfo<CurrentUser>();

        var priceRequest = new CheckSaleInput()
        {
            SupplierApiType = input.SupplierApiType,
            HotelId = input.HotelId,
            RoomId = input.RoomId,
            PriceStrategyId = input.StrategyId,
            BeginDate = input.DateBegin,
            EndDate = input.DateEnd,
            Quantity = input.Num,
            AdultNum = input.AdultNum > 0 ? input.AdultNum : 1,//成人数
            SalesChannel = SellingChannels.WechatMall,
            PreBookingCode = input.PreBookingCode,
        };
        var priceReponse = await PreOrderCheckSale(priceRequest);
        if (priceReponse?.Code != CheckPriceStrategySaleCode.Success)
            throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);

        var tenantId = user.Tenant;
        var room = priceReponse.Data.Room;
        var priceStrategy = priceReponse.Data.PriceStrategy;

        HotelApiOrderInfo? hotelApiOrderInfo = priceStrategy.SupplierApiType != SupplierApiType.None ?
         new HotelApiOrderInfo
         {
             ArrivalTaxFees = priceStrategy.ArrivalTaxFees,
             TaxDescription = priceStrategy.TaxDescription,
             HotelId = priceStrategy.HotelId,
             RoomId = priceStrategy.RoomId,
             PriceStrategyId = priceStrategy.Id,
             ResourceHotelId = priceReponse.Data.ResourceHotelId,
             SupplierApiType = priceStrategy.SupplierApiType,
         } :
         null;

        //多币种
        string paymentCurrencyCode = Currency.CNY.ToString();
        var priceExchangeRate = await _currencyExchangeRateService.GetOrderPriceExchange(new OrderPriceExchangeRateInput
        {
            CostCurrencyCode = priceStrategy.CostCurrencyCode,
            SaleCurrencyCode = priceStrategy.SaleCurrencyCode,
            PaymentCurrencyCode = paymentCurrencyCode,
        });
        //每日价格
        var calendarPrices = new List<HotelCalendarPriceDto>();
        IEnumerable<HotelDateMultPrice> dateMultPrices = await GetHotelDateMultPrices(new GetHotelMultPriceInput
        {
            SupplierApiType = input.SupplierApiType,
            HotelId = input.HotelId,
            PriceStragyId = priceStrategy.Id,
            DatePrices = priceStrategy.DatePrice
                .Select(d => new HotelDatePrice
                {
                    Date = d.Date,
                    Price = d.Price
                })
                .ToList()
        });
        var totalAmount = 0m;//总房价
        List<decimal> datePrices = new();
        foreach (var price in priceStrategy.DatePrice)
        {
            var multPrice = dateMultPrices.First(d => d.Date == price.Date);
            HotelCalendarPriceDto calendarPriceDto = new()
            {
                Date = price.Date,
                PriceType = multPrice.VipPrice.HasValue ? OrderPriceType.VipPrice : OrderPriceType.Default,
                CostCurrencyCode = priceStrategy.CostCurrencyCode,
                CostPrice = price.Cost ?? 0,
                OrgPrice = multPrice.RealPrice,
                OrgPriceCurrencyCode = priceStrategy.SaleCurrencyCode,
                PaymentCurrencyCode = paymentCurrencyCode,
                SalePrice = multPrice.RealPrice * priceExchangeRate.ExchangeRate,
                CostExchangeRate = priceExchangeRate.CostExchangeRate,
                ExchangeRate = priceExchangeRate.ExchangeRate
            };
            calendarPrices.Add(calendarPriceDto);
            var datePrice = calendarPriceDto.SalePrice * input.Num;
            totalAmount += datePrice;
            datePrices.Add(datePrice);
        }

        var discountItems = new List<OrderDiscountItemDto>();
        //验证优惠券
        if (input.UserCouponId > 0 && input.SupplierApiType == SupplierApiType.None)
        {
            var getOrderUserCouponRequest = new GetOrderUserCouponsInput()
            {
                UserId = user.UserId,
                UserCouponId = input.UserCouponId,
                ProductType = Contracts.Common.Marketing.Enums.LimitProductType.Hotel,//酒店
                OrderProductInfos = new List<OrderProductInfo>
                    {
                        new OrderProductInfo()
                        {
                           ProductId=input.HotelId,
                           ItemId=long.Parse( priceStrategy.Id),
                           OrderAmount=totalAmount,
                           OrderHotelInfo = new OrderHotelInfo { NumberOfRoom = input.Num, DatePrices = datePrices }
                        }
                    }
            };
            var orderUserCoupon = await GetOrderUserCoupon(getOrderUserCouponRequest);
            if (orderUserCoupon?.IsEnable is not true)
            {
                throw new BusinessException(ErrorTypes.Marketing.UserCouponDisabled);//优惠券不可用
            }

            discountItems.Add(new OrderDiscountItemDto
            {
                DiscountType = OrderDiscountType.UserCoupon,
                DiscountId = orderUserCoupon.UserCouponId,
                DiscountAmount = orderUserCoupon.Discount,
                Title = orderUserCoupon.CouponName
            });
        }
        var discountAmount = discountItems.Sum(x => x.DiscountAmount);//优惠金额
        var paymentAmount = totalAmount - discountAmount;
        if (paymentCurrencyCode == Currency.CNY.ToString())//CNY 抹零
        {
            var diff = paymentAmount - Math.Floor(paymentAmount);
            if (diff > 0)
            {
                discountItems.Add(new OrderDiscountItemDto
                {
                    DiscountType = OrderDiscountType.IgnoreDecimals,
                    DiscountAmount = diff,
                });
                discountAmount = discountItems.Sum(x => x.DiscountAmount);
                paymentAmount -= diff;
            }
        }

        var customerInfo = await _userApiCaller.GetCustomerUserInfo();
        var createInput = new CreateHotelOrderInput()
        {
            TenantId = user.Tenant,
            SellingPlatform = input.SellingPlatform,
            SellingChannel = SellingChannels.WechatMall,
            ChannelOrderNo = "",
            CheckIn = input.DateBegin,
            CheckOut = input.DateEnd,
            RoomCount = input.Num,
            Adults = input.HotelGuests.Count(),
            GuestInfos = input.HotelGuests,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            DiscountItems = discountItems,
            TotalAmount = totalAmount,
            DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            CalendarPrices = calendarPrices,
            SupplierApiType = input.SupplierApiType,
            HotelApiOrderInfo = hotelApiOrderInfo,
            HotelId = input.HotelId,
            HotelName = priceReponse.Data.HotelName,
            IsAutoConfirmRoomStatus = priceReponse.Data.IsAutoConfirmRoomStatus,
            Room = _mapper.Map<CheckSaleRoomDto>(room),
            PriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy),
            UserInfo = new OrderUserInfo()
            {
                UserId = user.UserId,
                NickName = user.NickName,
                VipLevelId = customerInfo?.VipLevel?.VipLevelId ?? 0,
                VipLevelName = customerInfo?.VipLevel?.Name ?? "",
                UserType = Contracts.Common.Order.Enums.UserType.Customer,
            },
            TraceId = input.TraceId
        };

        var result = await _orderApiCaller.HotelOrderCreate(createInput);

        //点击下单（不管是否支付），权重分 10
        if (input.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.None)
        {
            var pushWeightValueInputs = new List<PushWeightValueInput>() {
                new PushWeightValueInput
                {
                    HotelId = input.HotelId,
                    TenantId = tenantId,
                    Value = 10
                }
            };
            _ = _hotelApiCaller.PushWeightValue(pushWeightValueInputs);
        }

        return Ok(result);
    }

    /// <summary>
    /// C端酒店订单详情
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(DetailByCustomerOutput), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> Detail(long baseOrderId)
    {

        var user = HttpContext.GetCurrentUser();
        var result = await _orderApiCaller.HotelOrderDetail(new DetailInput
        {
            BaseOrderId = baseOrderId,
            UserId = user.userid
        });
        var output = _mapper.Map<DetailByCustomerOutput>(result);

        var gdsSupplierApiType = new List<SupplierApiType>
        {
            SupplierApiType.GDS,
            SupplierApiType.Youxia
        };
        // 因为高定的订单明细转换成分销商币种加总会与订单总价不一致，差几分钱的，所以显示的是供应商币种数据
        if (gdsSupplierApiType.Contains(output.OrderDetail.SupplierApiType))
        {
            output.CalendarPrices = result.CalendarPrices.Select(x => new CalendarPriceBase
            {
                Date = x.Date,
                PaymentCurrencyCode = x.PaymentCurrencyCode,
                SalePrice = x.SalePrice,
                OrgPriceCurrencyCode = x.CostCurrencyCode,
                OrgPrice = x.CostPrice,
            });
            // 房费
            output.RoomPrice = new PriceItem();
            output.RoomPrice.Price = result.CalendarPrices.Sum(x => x.SalePrice) ?? 0;
            output.RoomPrice.CurrencyCode = result.CalendarPrices.FirstOrDefault()?.PaymentCurrencyCode;
            output.RoomPrice.OrgPrice = result.CalendarPrices.Sum(x => x.CostPrice);
            output.RoomPrice.OrgCurrencyCode = result.CalendarPrices.FirstOrDefault()?.CostCurrencyCode;

            output.Tax = new PriceItem();
            // 税费与服务费
            output.Tax.Price = result.HotelOrderPrices.Sum(x => x.Price);
            output.Tax.CurrencyCode = result.HotelOrderPrices.FirstOrDefault()?.PaymentCurrencyCode;
            output.Tax.OrgPrice = result.HotelOrderPrices.Sum(x => x.CostPrice);
            output.Tax.OrgCurrencyCode = result.HotelOrderPrices.FirstOrDefault()?.CostCurrencyCode;
            output.OrgAmount = result.OrgAmount;
            output.OrgCurrencyCode = result.OrgCurrencyCode;
        }

        return Ok(output);
    }

    /// <summary>
    /// 导出酒店入住单pdf
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [ProducesResponseType(typeof(byte[]), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ExportHoterOrderPdf(long baseOrderId, string language = "zh")
    {
        var detailOutput = await _orderApiCaller.HotelOrderDetail(new DetailInput
        {
            BaseOrderId = baseOrderId,
        });
        var gdsSupplierApiTypes = new List<SupplierApiType> {
            SupplierApiType.GDS,
            SupplierApiType.Youxia
        };

        var fileName = string.Empty;
        if (!gdsSupplierApiTypes.Contains(detailOutput.OrderDetail.SupplierApiType))
        {
            var esHotelInfo = await _hotelApiCaller.GetEsHotelDetailV2(detailOutput.OrderDetail.HotelId);
            if (esHotelInfo.Any())
            {
                var esHotel = esHotelInfo.First();
                //优先展示本地酒店
                var localHotel = esHotel.HotelIdList.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.None);
                if (localHotel != null)
                {//查询本地酒店
                    var localHotelDetail = await _hotelApiCaller.GetHotelDetail(localHotel.HotelId);
                    detailOutput.OrderDetail.HotelTelePhone = localHotelDetail?.Telephone;
                    detailOutput.OrderDetail.HotelAddress = localHotelDetail?.Address;
                    detailOutput.OrderDetail.EnHotelAddress = localHotelDetail?.ENAddress;
                }
                else
                {//查询资源库酒店信息
                    var resourceHotelDetail = await _resourceApiCaller.GetHotelDetail(esHotel.ResourceHotelId);
                    detailOutput.OrderDetail.HotelTelePhone = resourceHotelDetail?.Telephone;
                    detailOutput.OrderDetail.HotelAddress = resourceHotelDetail?.Address;
                    detailOutput.OrderDetail.EnHotelAddress = resourceHotelDetail?.ENAddress;
                }
                fileName = language == "zh" ? esHotelInfo.FirstOrDefault().ZHName : esHotelInfo.FirstOrDefault().ENName;
            }
        }
        else
        {
            var gdsHotel = await _resourceApiCaller.GDSHotelDetail(detailOutput.OrderDetail.HotelId);
            detailOutput.OrderDetail.HotelTelePhone = gdsHotel?.Telephone;
            detailOutput.OrderDetail.HotelAddress = gdsHotel?.Address;
            detailOutput.OrderDetail.EnHotelAddress = gdsHotel?.ENAddress;

            fileName = language == "zh" ? gdsHotel.ZHName : gdsHotel.ENName;
        }

        var input = _mapper.Map<ExportHotelOrderPdfInput>(detailOutput);
        input.OrderDetail.BaseOrderId = baseOrderId;
        input.ExportHotelOrderType = ExportHotelOrderType.CheckIn;
        input.Language = language;

        var file = await _orderApiCaller.ExportHotelOrderPdf(input);

        return File(file, "application/pdf",
            $"{DateTime.Now.ToString("yyyyMMdd")}-{fileName}-{baseOrderId}.pdf");
    }

    /// <summary>
    /// 验证价格
    /// </summary>
    private async Task<CheckSaleOutput> PreOrderCheckSale(CheckSaleInput request)
    {
        var response = await _hotelApiCaller.PreOrderCheckSale(request);

        if (response.Data is not null && request.SupplierApiType != SupplierApiType.None)
        {
            var resourceHotelId = response.Data.ResourceHotelId;
            var resourceRoomId = response.Data.Room.ResourceRoomId;
            var hotel = (await _hotelApiCaller.GetHotelIdsByResourceHotelIds(new List<long> { resourceHotelId })).FirstOrDefault();
            if (hotel?.HotelId is > 0)
            {
                var config = request.HotelId is > 0 ? (await _hotelApiCaller.GetHotelOperationConfigs(
                                new Contracts.Common.Hotel.DTOs.HotelOperationConfig.GetHotelOperationConfigsInput
                                {
                                    HotelIds = new long[] { hotel.HotelId },
                                    OperationConfigType = HotelOperationConfigType.B2C,
                                }))?.FirstOrDefault()?.Configs?.FirstOrDefault() : null;
                if (config?.AllowSelfMaintainHotel is true)
                {
                    response.Data.HotelName = hotel.ZHName;
                    response.Data.HotelEnName = hotel.ENName;
                }
                if (config?.AllowSelfMaintainRoom is true)
                {
                    var room = (await _hotelApiCaller.GetHotelRooms(new Contracts.Common.Hotel.DTOs.Hotel.GetHotelRoomsInput
                    {
                        HotelId = hotel.HotelId,
                    }))?.HotelRooms?.FirstOrDefault(s => s.ResourceRoomId == resourceRoomId);
                    if (room is not null)
                    {
                        var checkSale_Room = response.Data.Room;
                        checkSale_Room.AreaMax = room.AreaMax;
                        checkSale_Room.AreaMin = room.AreaMin;
                        checkSale_Room.BedTypes = room.BedType;
                        checkSale_Room.BedType = string.Join(",", room.BedType?.Select(x => x.main) ?? new List<string>());
                        checkSale_Room.EnName = room.ENName;
                        checkSale_Room.Name = room.ZHName;
                        checkSale_Room.MaximumOccupancy = room.MaximumOccupancy;
                        checkSale_Room.WindowType = room.WindowType;
                    }
                }
            }
        }
        return response;
    }

    /// <summary>
    /// 第三方平台酒店试单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    private async Task<HotelApiOrderInfo> GetHotelApiOrderInfo(CheckAvailabilityInput input)
    {
        //第三方平台试单接口
        var checkAvailabilityOutput = await _resourceApiCaller.ThirdHotelCheckAvailability(input);
        switch (checkAvailabilityOutput.CheckCode)
        {
            case CheckPriceStrategySaleCode.Success:
                return new HotelApiOrderInfo
                {
                    HotelId = checkAvailabilityOutput.HotelId,
                    RoomId = checkAvailabilityOutput.RoomId,
                    PriceStrategyId = checkAvailabilityOutput.PricestrategyId,
                    ResourceHotelId = input.ResourceHotelId,
                    SupplierApiType = input.SupplierApiType
                };
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SomeDatesAreUnavailable);
                break;
        }
    }

    private async Task<IEnumerable<HotelDateMultPrice>> GetHotelDateMultPrices(GetHotelMultPriceInput input)
    {
        if (input.SupplierApiType != Contracts.Common.Tenant.Enums.SupplierApiType.None)
        {
            return input.DatePrices.Select(x => new HotelDateMultPrice
            {
                Date = x.Date,
                Price = x.Price,
            });
        }

        var discountRights = await _userApiCaller.GetVipDiscountRightsByHotel(new List<DiscountRightsByHotelInput>
        {
                 new (){ HotelId=input.HotelId, PriceStrategyId=long.Parse( input.PriceStragyId) }
        });
        //计算会员价
        var discountRight = discountRights.FirstOrDefault(x => x.HotelId == input.HotelId && x.PriceStrategyId == long.Parse(input.PriceStragyId));

        var result = input.DatePrices.Select(x => new HotelDateMultPrice
        {
            Date = x.Date,
            Price = x.Price,
            VipPrice = discountRight?.DiscountValue > 0
            ? decimal.Round(x.Price * (discountRight.DiscountValue / 10), 2) : null,
        });
        return result;
    }
    private async Task<OrderUserCouponOutput> GetOrderUserCoupon(GetOrderUserCouponsInput request)
    {
        var response = await _marketingApiCaller.GetOrderUserCoupons(request);
        var result = response.Where(x => x.UserCouponId == request.UserCouponId).FirstOrDefault();
        return result;
    }



    #region GDS

    /// <summary>
    /// 试单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(BffGDSHotelPriceCheckOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GDSPriceCheck(BffGDSHotelPriceCheckInput input)
    {
        var priceRequest = new GDSHotelPriceCheckInput()
        {
            RateKey = input.RateKey,
            IsOriginalJson = true,
            HotelId = input.HotelId,
            RoomId = input.RoomId,
        };
        var priceReponse = await _orderApiCaller.GDSPriceCheck(priceRequest);
        var res = _mapper.Map<BffGDSHotelPriceCheckOutput>(priceReponse);
        res.Room = _mapper.Map<GDSHotelRoomBffOutput>(priceReponse.Room);
        var priceStrategy = res?.Room?.PriceStrategies?.FirstOrDefault();
        priceStrategy.HotelId = input.HotelId;
        return Ok(res);
    }


    /// <summary>
    /// 下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Inventory.ProductInventoryNotEnough,
        ErrorTypes.Order.OrderTimeout,
        ErrorTypes.Order.SupplierApiTypeError)]
    [ProducesResponseType(typeof(CreateHotelOrderOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GDSCreate(GDSCreateInput input)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        var tenantId = user.Tenant;

        GDSHotelRoomInfo room = null;
        string rateKey = string.Empty;
        string bookingKey = input.BookingKey;
        DateTime? expireStartTime = null;

        switch (input.SupplierApiType)
        {
            case SupplierApiType.GDS:

                var priceCheckInfo = await _orderApiCaller.GetGDSPriceCheckInfo(input.BookingKey);
                room = priceCheckInfo.PriceCheckInfo.Room;
                rateKey = priceCheckInfo.RateKey;
                expireStartTime = priceCheckInfo.CreateTime;
                // bookingKey 5 分钟内有效
                if (priceCheckInfo.CreateTime.AddMinutes(5) < DateTime.Now)
                {
                    throw new BusinessException(ErrorTypes.Order.OrderTimeout);
                }
                break;
            case SupplierApiType.Youxia:
                long.TryParse(input.RateKey, out var priceStrategyId);
                var youxiaPriceStrategy = await _resourceApiCaller.PriceStrategyDetail(new SearchYouxiaHotelPriceStrategyDetailInput()
                {
                    Adults = input.Adults,
                    LiveDate = input.LiveDate,
                    LeaveDate = input.LeaveDate,
                    PriceStrategyId = priceStrategyId,
                    TenantId = tenantId,
                });
                room = _mapper.Map<GDSHotelRoomInfo>(youxiaPriceStrategy);
                rateKey = input.RateKey?.ToString() ?? string.Empty;
                bookingKey = youxiaPriceStrategy?.PriceStrategies?.FirstOrDefault()?.BookingKey;
                break;
            default:
                throw new BusinessException(ErrorTypes.Order.SupplierApiTypeError);
        }

        if (string.IsNullOrWhiteSpace(bookingKey) || room == null)
        {
            throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);
        }
        var supplierSettings = await _tenantApiCaller.GetSupplierApiSettingInfos(new GetApiSettingInfosInput()
        {
            SupplierApiType = input.SupplierApiType,
            TenantId = tenantId,
            SupplierEnabled = true
        });
        var supplierSetting = supplierSettings.FirstOrDefault();
        if (supplierSetting == null)
        {
            throw new BusinessException(ErrorTypes.Inventory.ProductInventoryNotEnough);
        }

        var localHotelDetail = await _resourceApiCaller.GDSHotelDetail(input.HotelId);
        var thirdHotelId = localHotelDetail.ThirdHotelId;

        ////每日价格
        var calendarPrices = new List<HotelCalendarPriceDto>();
        var priceStrategy = room.PriceStrategies.FirstOrDefault();
        var dateRates = priceStrategy.CalendarPrices;
        //多币种
        string paymentCurrencyCode = priceStrategy.CurrencyCode;

        HotelApiOrderInfo? hotelApiOrderInfo = new HotelApiOrderInfo
        {
            HotelId = input.HotelId.ToString(),
            RoomId = input.RoomId.ToString(),
            PriceStrategyId = priceStrategy.Id ?? "0",
            ResourceHotelId = 0,
            SupplierApiType = input.SupplierApiType,
        };

        foreach (var price in dateRates)
        {
            HotelCalendarPriceDto calendarPriceDto = new()
            {
                Date = price.Date,
                PriceType = OrderPriceType.Default,
                CostCurrencyCode = price.OrgCurrencyCode,
                CostPrice = price.OrgPrice,
                OrgPrice = price.Price,
                OrgPriceCurrencyCode = price.CurrencyCode,
                PaymentCurrencyCode = paymentCurrencyCode,
                SalePrice = price.Price,
                CostExchangeRate = 1,
                ExchangeRate = price.OrgPrice == 0 ? 1 : (price.Price / price.OrgPrice),
            };
            calendarPrices.Add(calendarPriceDto);
        }
        var paymentAmount = priceStrategy.AmountAfterTax;

        List<HotelOrderPriceDto> orderMultPrices = new();
        if (priceStrategy.Taxes != null)
        {
            orderMultPrices.Add(new HotelOrderPriceDto()
            {
                CostCurrencyCode = priceStrategy.Taxes.OrgCurrencyCode,
                CostPrice = priceStrategy.Taxes.OrgAmount,
                CostPriceType = OrderPriceType.Default,
                OrgPrice = priceStrategy.Taxes.Amount,
                OrgPriceCurrencyCode = priceStrategy.Taxes.CurrencyCode,
                OrgCostPrice = priceStrategy.Taxes.Amount,
                PaymentCurrencyCode = paymentCurrencyCode,
                PriceType = OrderPriceType.Default,
                Price = priceStrategy.Taxes.Amount,
                CostExchangeRate = 1,
                ExchangeRate = priceStrategy.Taxes.Amount == 0 ? 1 : (priceStrategy.Taxes.OrgAmount / priceStrategy.Taxes.Amount),
                Quantity = 1,
                OrderSubType = HotelOrderSubType.Tax,
            });
        }
        if (priceStrategy.Fees != null)
        {
            orderMultPrices.Add(new HotelOrderPriceDto()
            {
                CostCurrencyCode = priceStrategy.Fees.OrgCurrencyCode,
                CostPrice = priceStrategy.Fees.OrgAmount,
                CostPriceType = OrderPriceType.Default,
                OrgPrice = priceStrategy.Fees.Amount,
                OrgPriceCurrencyCode = priceStrategy.Fees.CurrencyCode,
                OrgCostPrice = priceStrategy.Fees.Amount,
                PaymentCurrencyCode = paymentCurrencyCode,
                PriceType = OrderPriceType.Default,
                Price = priceStrategy.Fees.Amount,
                CostExchangeRate = 1,
                ExchangeRate = priceStrategy.Fees.Amount == 0 ? 1 : (priceStrategy.Fees.OrgAmount / priceStrategy.Fees.Amount),
                Quantity = 1,
                OrderSubType = HotelOrderSubType.Service,
            });
        }
        decimal? supplierCommissionFee = priceStrategy.Commission?.Amount;
        decimal? suppliercommissionRate = priceStrategy.Commission?.Percent;
        if ((supplierCommissionFee == null || supplierCommissionFee <= 0) && suppliercommissionRate != null)
        {
            supplierCommissionFee = decimal.Round((decimal)(paymentAmount * suppliercommissionRate / 100), 2, MidpointRounding.AwayFromZero);
        }
        if (supplierCommissionFee > 0 && (suppliercommissionRate == null && suppliercommissionRate <= 0) && paymentAmount > 0)
        {
            suppliercommissionRate = decimal.Round((decimal)(supplierCommissionFee / paymentAmount) * 100, 2, MidpointRounding.AwayFromZero);
        }
        var commission = new OrderCommissionDto()
        {
            SupplierCommissionFee = supplierCommissionFee,
            SupplierCommissionRate = suppliercommissionRate,
            SupplierCommisionStatus = CommisionStatus.NotCredited,
        };
        var customerInfo = await _userApiCaller.GetCustomerUserInfo();
        var createPriceStrategy = _mapper.Map<CheckSalePriceStrategyDto>(priceStrategy);
        createPriceStrategy.SupplierId = supplierSetting.SupplierId;
        var createRoom = _mapper.Map<CheckSaleRoomDto>(room);
        createRoom.Name = string.IsNullOrEmpty(createRoom.Name) ? createRoom.EnName : createRoom.Name;
        createPriceStrategy.Name = string.IsNullOrEmpty(createPriceStrategy.Name) ? createPriceStrategy.EnName : createPriceStrategy.Name;
        var hotelName = localHotelDetail.ZHName ?? localHotelDetail.ENName;
        var createInput = new CreateHotelOrderInput()
        {
            TenantId = tenantId,
            SellingPlatform = input.SellingPlatform,
            SellingChannel = SellingChannels.WechatMall,
            ChannelOrderNo = "",
            CheckIn = input.LiveDate,
            CheckOut = input.LeaveDate,
            RoomCount = input.RoomNum,
            Adults = input.Adults,
            GuestInfos = input.HotelGuests,
            ContactsName = input.ContactsName,
            ContactsPhoneNumber = input.ContactsPhoneNumber,
            ContactsEmail = input.ContactsEmail,
            Message = input.Message,
            // DiscountItems = discountItems,
            TotalAmount = paymentAmount,
            //DiscountAmount = discountAmount,
            PaymentAmount = paymentAmount,
            PaymentCurrencyCode = paymentCurrencyCode,
            CalendarPrices = calendarPrices,
            SupplierApiType = input.SupplierApiType,
            HotelApiOrderInfo = hotelApiOrderInfo,
            HotelId = input.HotelId,
            HotelName = hotelName,
            HotelEnName = localHotelDetail.ENName,
            IsAutoConfirmRoomStatus = false,
            Room = createRoom,
            PriceStrategy = createPriceStrategy,
            UserInfo = new OrderUserInfo()
            {
                UserId = user.UserId,
                NickName = user.NickName,
                VipLevelId = customerInfo?.VipLevel?.VipLevelId ?? 0,
                VipLevelName = customerInfo?.VipLevel?.Name ?? "",
                UserType = Contracts.Common.Order.Enums.UserType.Customer,
            },
            TraceId = input.TraceId,
            GDSHotelRateInfo = new GDSHotelRateInfo()
            {
                RateKey = rateKey,
                BookingKey = bookingKey,
                AdditionalFeesInclusive = priceStrategy.AdditionalFeesInclusive,
                IncidentalsInclusive = priceStrategy.IncidentalsInclusive,
                LocalFeesInclusive = priceStrategy.LocalFeesInclusive,
                SabreHotelCode = thirdHotelId?.ToString() ?? "",
                TaxInclusive = priceStrategy.TaxInclusive,
                GuaranteeJson = priceStrategy.Guarantee == null ? null : JsonConvert.SerializeObject(priceStrategy.Guarantee),
                BedTypeJson = room.BedType == null ? null : JsonConvert.SerializeObject(room.BedType),
                OrgAmount = priceStrategy.OrgAmountAfterTax,
                OrgCurrencyCode = priceStrategy.OrgCurrencyCode,
                RoomDescribe = room.RoomDescribe,
                ExclusivePrivileges = priceStrategy.ExclusivePrivileges,
            },
            OrderMultPrices = orderMultPrices,
            // ServiceItems = serviceItems, //目前没服务
            ChildrenAges = input.ChildrenAges,
            OrderCommission = commission,
            ExpireStartTime = expireStartTime,
        };

        var result = await _orderApiCaller.HotelOrderCreate(createInput);
        return Ok(result);
    }


    /// <summary>
    /// 订单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<Contracts.Common.Order.DTOs.HotelOrder.SearchOutput, IList<HotelStatusCountOutput>>), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Permission.OrderViewallDataNotExist)]
    [Authorize]
    public async Task<IActionResult> Search(Contracts.Common.Order.DTOs.HotelOrder.SearchInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        input.UserId = currentUser.UserId;
        input.IsDisplayCost = false;
        if (input.CheckInDate.HasValue && !input.CheckInDateEnd.HasValue)
        {
            input.CheckInDateEnd = input.CheckInDate;
        }
        if (input.CheckOutDate.HasValue && !input.CheckOutDateEnd.HasValue)
        {
            input.CheckOutDateEnd = input.CheckOutDate;
        }
        if (input.CreateTime.HasValue && !input.CreateTimeEnd.HasValue)
        {
            input.CreateTimeEnd = input.CreateTime;
        }
        var result = await _orderApiCaller.HotelOrderSearch(input);
        return Ok(result);
    }
    #endregion

    #region 
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchGDSOrderListByUserOutput>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> SearchGDSOrderListByUser(SearchGDSOrderListByUserInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        input.UserId = currentUser.UserId;
        if (input.CheckInDate.HasValue && !input.CheckInDateEnd.HasValue)
        {
            input.CheckInDateEnd = input.CheckInDate;
        }
        if (input.CheckOutDate.HasValue && !input.CheckOutDateEnd.HasValue)
        {
            input.CheckOutDateEnd = input.CheckOutDate;
        }
        if (input.CreateTime.HasValue && !input.CreateTimeEnd.HasValue)
        {
            input.CreateTimeEnd = input.CreateTime;
        }
        var result = await _orderApiCaller.SearchGDSOrderListByUser(input);
        return Ok(result);
    }
    #endregion
}
