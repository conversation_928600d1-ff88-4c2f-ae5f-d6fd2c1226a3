using Bff.WechatMall.Models.PromotionTraceRecord;
using FluentValidation;

namespace Bff.WechatMall.Validators.PromotionTrace;

public class AddTraceRecordBffInputValidator : AbstractValidator<List<AddPromotionTraceRecordBffInput>>
{
    public AddTraceRecordBffInputValidator()
    {
        RuleForEach(r => r)
            .ChildRules(c =>
            {
                c.RuleFor(x => x.PromotionTraceId).NotEmpty();
                c.RuleFor(x => x.BehaviorType).IsInEnum();
                c.RuleFor(x => x.CreateTime).NotEmpty();
                c.RuleFor(x => x.ShareType).IsInEnum();
                c.RuleFor(x => x.VisitTargetType).IsInEnum();
                c.When(x => x.CustomerId is null or <= 0, () =>
                {
                    c.RuleFor(x => x.OtherIdentifier).NotEmpty();
                });
                c.When(x => string.IsNullOrEmpty(x.OtherIdentifier), () =>
                {
                    c.RuleFor(x => x.CustomerId).NotEmpty();
                });
            });
    }
}