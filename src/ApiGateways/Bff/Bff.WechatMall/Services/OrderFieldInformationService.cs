using Bff.WechatMall.Services.Interfaces;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Product.DTOs.Fields.Attributes;
using Contracts.Common.Product.DTOs.Fields.Group;
using Contracts.Common.Product.DTOs.InformationTemplateFields;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Product.Enums;
using Newtonsoft.Json;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Bff.WechatMall.Services;

public class OrderFieldInformationService: IOrderFieldInformationService
{

    public bool Validator(List<ProductInformationTemplateDetail> productFields, List<SaveOrderFieldInformationTypeDto> orderFields)
    {
        foreach (var orderField in orderFields)
        {
            var orderTempField = productFields.Where(x => x.TemplateType == orderField.TemplateType)
                    .Where(x => x.ProductTemplateType == orderField.ProductTemplateType)
                    .FirstOrDefault();

            var count = orderField.Fields.Count;
            var tempCount = orderTempField?.Fields?.Count ?? 0;
            var fieldCodes = orderField.Fields.Select(x => x.FieldCode);

            if ((count != tempCount || orderTempField?.Fields?.Any(x => !fieldCodes.Contains(x.FieldCode)) is true))
            {
                throw new BusinessException(ErrorTypes.Order.OrderFieldChange);
            }

            foreach (var tempField in orderTempField.Fields)
            {
                if (tempField.FieldType == FieldsType.Label)
                    continue;
                var field = orderField.Fields.FirstOrDefault(x => x.FieldCode == tempField.FieldCode);
                if (tempField.IsRequired && (field == null || string.IsNullOrWhiteSpace(field.FieldValue)))
                {
                    throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(), $"缺少[{tempField.FieldName}]字段信息",
                       new OrderFieldValidaterDto()
                       {
                           FieldName = tempField.FieldName,
                           FieldEnName = tempField.FieldEnName,
                           IsRequired = true,
                       });
                }
                if (!string.IsNullOrWhiteSpace(field?.FieldValue))
                {
                    switch (field.FieldType)
                    {
                        case FieldsType.Text:
                            var len = field.FieldValue.Length;
                            if ((tempField.MinLength != null && len < tempField.MinLength) || (tempField.Maxlength != null && len > tempField.Maxlength))
                            {
                                string msg = tempField.MinLength.HasValue ? $"大于等于{tempField.MinLength}，" : "";
                                msg += tempField.Maxlength.HasValue ? $"小于等于{tempField.Maxlength}，" : "";
                                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(), $"[{tempField.FieldName}]字段信息长度范围：{msg.TrimEnd(',')}",
                                         new OrderFieldValidaterDto()
                                         {
                                             FieldName = tempField.FieldName,
                                             FieldEnName = tempField.FieldEnName,
                                             Max = tempField.Maxlength,
                                             Min = tempField.MinLength,
                                         });
                            }
                            //if (field.FieldCode == "ContactEmail" || field.FieldCode == "TourGuideEmail" || field.FieldCode == "DriverEmail")
                            //{
                            //    var reg = new Regex("\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*");
                            //    if (!reg.IsMatch(field.FieldValue.ToString()))
                            //    {
                            //        throw new BusinessException($"[{tempField.FieldName}]字段请正确填写");
                            //    }
                            //}
                            ValidatorRuler(tempField, field.FieldValue, null);
                            break;
                        case FieldsType.Int:
                        case FieldsType.BigInt:
                            var value = (int)Convert.ToDecimal(field.FieldValue);
                            if ((tempField.MinLength != null && value < tempField.MinLength) || (tempField.Maxlength != null && value > tempField.Maxlength))
                            {
                                string msg = tempField.MinLength.HasValue ? $"大于等于{tempField.MinLength}，" : "";
                                msg += tempField.Maxlength.HasValue ? $"小于等于{tempField.Maxlength}，" : "";
                                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(), $"[{tempField.FieldName}]字段信息范围：{msg.TrimEnd(',')}",
                                      new OrderFieldValidaterDto()
                                      {
                                          FieldName = tempField.FieldName,
                                          FieldEnName = tempField.FieldEnName,
                                          Max = tempField.Maxlength,
                                          Min = tempField.MinLength,
                                      });
                            }
                            break;
                        case FieldsType.DateTime:
                            var dateTimeValue = Convert.ToDateTime(field.FieldValue);
                            if ((tempField.MinDate != null && dateTimeValue < tempField.MinDate) || (tempField.MaxDate != null && dateTimeValue > tempField.MaxDate))
                            {
                                string msg = tempField.MinDate.HasValue ? $"大于等于{tempField.MinDate.Value.ToString("yyyy-MM-dd HH:mm")}，" : "";
                                msg += tempField.MaxDate.HasValue ? $"小于等于{tempField.MaxDate.Value.ToString("yyyy-MM-dd HH:mm")}，" : "";
                                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(), $"[{tempField.FieldName}]字段信息范围：{msg.TrimEnd(',')}",
                                      new OrderFieldValidaterDto()
                                      {
                                          FieldName = tempField.FieldName,
                                          FieldEnName = tempField.FieldEnName,
                                          Max = tempField.MaxDate,
                                          Min = tempField.MinDate,
                                          ValueFormat = "yyyy-MM-dd HH:mm"
                                      });
                            }
                            break;
                        case FieldsType.Date:
                            var dateValue = Convert.ToDateTime(field.FieldValue);
                            if ((tempField.MinDate != null && dateValue < tempField.MinDate) || (tempField.MaxDate != null && dateValue > tempField.MaxDate))
                            {
                                string msg = tempField.MinDate.HasValue ? $"大于等于{tempField.MinDate.Value.ToString("yyyy-MM-dd")}，" : "";
                                msg += tempField.MaxDate.HasValue ? $"小于等于{tempField.MaxDate.Value.ToString("yyyy-MM-dd")}，" : "";
                                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(), $"[{tempField.FieldName}]字段信息范围：{msg.TrimEnd(',')}",
                                       new OrderFieldValidaterDto()
                                       {
                                           FieldName = tempField.FieldName,
                                           FieldEnName = tempField.FieldEnName,
                                           Max = tempField.MaxDate,
                                           Min = tempField.MinDate,
                                           ValueFormat = "yyyy-MM-dd"
                                       });
                            }
                            break;
                        case FieldsType.Float:
                            var floatValue = Convert.ToDecimal(field.FieldValue);
                            if ((tempField.MinLength != null && floatValue < tempField.MinLength) || (tempField.Maxlength != null && floatValue > tempField.Maxlength))
                            {
                                string msg = tempField.MinLength.HasValue ? $"大于等于{tempField.MinLength}，" : "";
                                msg += tempField.Maxlength.HasValue ? $"小于等于{tempField.Maxlength}，" : "";
                                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(), $"[{tempField.FieldName}]字段信息范围：{msg.TrimEnd(',')}",
                                      new OrderFieldValidaterDto()
                                      {
                                          FieldName = tempField.FieldName,
                                          FieldEnName = tempField.FieldEnName,
                                          Max = tempField.Maxlength,
                                          Min = tempField.MinLength,
                                      });
                            }
                            break;
                        case FieldsType.Json:
                            if (FieldJsonTypes.TryGetValue(field.FieldCode, out Type? jsonType))
                            {
                                var obj = JsonConvert.DeserializeObject(field.FieldValue, jsonType);
                                var properties = jsonType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                                // 详细地址要求 address 与 addressDetail 二选一 ，所以单独处理
                                if (jsonType == typeof(AddressDto))
                                {
                                    ValidatorAddress(tempField, field, jsonType);
                                }
                                else
                                {
                                    foreach (var property in properties)
                                    {
                                        var jsonValue = property.GetValue(obj);
                                        ValidatorVerifyAttr(property, jsonValue, tempField, properties, obj);
                                    }
                                }
                            }
                            break;
                    }
                }
            }
        }
        return true;
    }

    private void ValidatorRuler(InformationTemplateFieldsOutput tempField, string value, string code)
    {
        if (!string.IsNullOrEmpty(tempField.RulerFilter))
        {
            var rulers = JsonConvert.DeserializeObject<List<RulerFilterDto>>(tempField.RulerFilter);
            var ruler = rulers.FirstOrDefault(x => string.IsNullOrEmpty(x.Code) || x.Code == code);
            if (ruler != null && !string.IsNullOrEmpty(ruler.Pattern))
            {
                var reg = new Regex(ruler.Pattern!);
                if (!reg.IsMatch(value.ToString()))
                {
                    var errorMsg = (string.IsNullOrEmpty(ruler.Msg) ? $"[{tempField.FieldName}]字段请正确填写" : ruler.Msg);

                    throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(),
                                                             errorMsg,
                                                             new OrderFieldValidaterDto()
                                                             {
                                                                 FieldName = tempField.FieldName,
                                                                 FieldEnName = tempField.FieldEnName,
                                                             });
                }
            }
        }
    }


    /// <summary>
    /// 字段对象type
    /// </summary>
    private readonly Dictionary<string, Type> FieldJsonTypes = new Dictionary<string, Type>()
        {
            //{ "Name",typeof(NameDto) },
            { "Nationality",typeof(AddressDto) },
            { "Card",typeof(CardDto) },
            { "CardCountry",typeof(AddressDto) },
            { "ContactPhone",typeof(PhoneDto) },
            { "LocalPhone",typeof(PhoneDto) },
            { "ContactSocialMedia",typeof(SocialMediaDto) },
            { "TourGuidePhone",typeof(PhoneDto) },
            { "TourGuideSocialMedia",typeof(SocialMediaDto) },
            { "DriverPhone",typeof(PhoneDto) },
            { "DriverSocialMedia",typeof(SocialMediaDto) },
            { "DepartureJson",typeof(AddressDto) },
            { "DepartureHotelPhone",typeof(PhoneDto) },
            { "DestinationJson",typeof(AddressDto) },
            { "DestinationHotelPhone",typeof(PhoneDto) },
        };

    private void ValidatorVerifyAttr(PropertyInfo property,
    object jsonValue,
    InformationTemplateFieldsOutput tempField,
    PropertyInfo[] properties,
    object data)
    {
        var verifyAttr = property.GetCustomAttribute<VerifyAttribute>();
        if (verifyAttr != null)
        {
            var errorMsg = verifyAttr.ErrorMsg;
            if (jsonValue != null && !string.IsNullOrEmpty(jsonValue?.ToString()))
            {
                if (property.PropertyType == typeof(string))
                {
                    var jsonValueLength = jsonValue.ToString().Length;
                    if ((verifyAttr.MinLength != null && jsonValueLength < verifyAttr.MinLength) || (verifyAttr.Maxlength != null && jsonValueLength > verifyAttr.Maxlength))
                    {
                        if (string.IsNullOrEmpty(errorMsg))
                        {
                            string msg = verifyAttr.MinLength.HasValue ? $"大于等于{verifyAttr.MinLength}，" : "";
                            msg += verifyAttr.Maxlength.HasValue ? $"小于等于{verifyAttr.Maxlength}，" : "";
                            errorMsg = $"[{tempField.FieldName}]字段信息长度范围：{msg.TrimEnd(',')}";
                        }
                        throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(),
                              errorMsg,
                              new OrderFieldValidaterDto()
                              {
                                  FieldName = tempField.FieldName,
                                  FieldEnName = tempField.FieldEnName,
                                  Max = verifyAttr.Maxlength,
                                  Min = verifyAttr.MinLength,
                              });
                    }
                    // 下拉框的，有部分选择需要验证
                    var verifyValueAttr = property.GetCustomAttribute<VerifyValueAttribute>();
                    if (verifyValueAttr != null)
                    {
                        var verifyCodePro = properties.FirstOrDefault(x => x.GetCustomAttribute<VerifyCodeAttribute>() != null);
                        if (verifyCodePro != null)
                        {
                            var verifyCode = verifyCodePro.GetValue(data, null);
                            ValidatorRuler(tempField, jsonValue.ToString(),
                                               verifyCode == null ? null : verifyCode.ToString());
                        }
                        else
                        {
                            ValidatorRuler(tempField, jsonValue.ToString(), null);
                        }
                    }
                }
                else if ((property.PropertyType == typeof(double) || property.PropertyType == typeof(int)
                    || property.PropertyType == typeof(decimal) || property.PropertyType == typeof(long)
                    || property.PropertyType == typeof(float)
                    ))
                {
                    var jsonConverValue = Convert.ToDouble(jsonValue);
                    if ((verifyAttr.MinLength != null && jsonConverValue < verifyAttr.MinLength) || (verifyAttr.Maxlength != null && jsonConverValue > verifyAttr.Maxlength))
                    {
                        if (string.IsNullOrEmpty(errorMsg))
                        {
                            string msg = verifyAttr.MinLength.HasValue ? $"大于等于{verifyAttr.MinLength}，" : "";
                            msg += verifyAttr.Maxlength.HasValue ? $"小于等于{verifyAttr.Maxlength}，" : "";
                            errorMsg = $"[{tempField.FieldName}]字段信息范围：{msg.TrimEnd(',')}";
                        }
                        throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(),
                              errorMsg,
                              new OrderFieldValidaterDto()
                              {
                                  FieldName = tempField.FieldName,
                                  FieldEnName = tempField.FieldEnName,
                                  Max = verifyAttr.Maxlength,
                                  Min = verifyAttr.MinLength,
                              });
                    }
                }
                else if (property.PropertyType.IsEnum)
                {
                    var underlyingType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
                    if (string.IsNullOrEmpty(errorMsg))
                    {
                        errorMsg = $"请填写[{tempField.FieldName}]字段正确信息";
                    }
                    if (jsonValue != null)
                    {
                        var valuePro = properties.FirstOrDefault(x => x.Name == "Value");
                        var enumDtoValue = valuePro.GetValue(data, null);
                        if (enumDtoValue != null && !string.IsNullOrEmpty(enumDtoValue.ToString()))
                        {
                            var convertValue = Enum.Parse(property.PropertyType, jsonValue.ToString(), ignoreCase: true);
                            var enumValue = (int)convertValue;
                            if ((verifyAttr.MinLength != null && enumValue < verifyAttr.MinLength) || (verifyAttr.Maxlength != null && enumValue > verifyAttr.Maxlength))
                            {
                                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(),
                                    errorMsg,
                                    new OrderFieldValidaterDto()
                                    {
                                        FieldName = tempField.FieldName,
                                        FieldEnName = tempField.FieldEnName,
                                        Max = verifyAttr.Maxlength,
                                        Min = verifyAttr.MinLength,
                                    });
                            }
                        }
                    }

                }
            }
        }
    }

    private void ValidatorAddress(InformationTemplateFieldsOutput tempField, OrderFieldInformationDto field, Type? jsonType)
    {
        var obj = JsonConvert.DeserializeObject(field.FieldValue, jsonType);
        var properties = jsonType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        var addressPro = properties.FirstOrDefault(x => x.Name == "Address");
        var hasAddressPro = properties.FirstOrDefault(x => x.Name == "HasAddress");
        var addressDetailPro = properties.FirstOrDefault(x => x.Name == "Detail");
        var addressValue = addressPro.GetValue(obj);
        var hasAddressValue = hasAddressPro.GetValue(obj);
        var addressDetailValue = addressDetailPro.GetValue(obj) as AddressDetailDto;
        object resourceZhName = null;
        if (addressDetailValue != null)
        {
            var detailProperties = typeof(AddressDetailDto).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var resourceProperty = detailProperties.FirstOrDefault(x => x.Name == "ResourceZhName");// 目的地选择器，ResourceZhName 是必须有值
            resourceZhName = resourceProperty.GetValue(addressDetailValue);
        }
        string errorMsg = string.Empty;
        // 详细地址要求 address 与 addressDetail 二选一
        if (hasAddressValue?.Equals(true) is true)
        {
            if (tempField.IsRequired && string.IsNullOrWhiteSpace(addressValue?.ToString()) && string.IsNullOrWhiteSpace(resourceZhName?.ToString()))
            {
                if (string.IsNullOrEmpty(errorMsg))
                {
                    errorMsg = $"缺少[{tempField.FieldName}]字段信息";
                }
                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(),
                               errorMsg,
                       new OrderFieldValidaterDto()
                       {
                           FieldName = tempField.FieldName,
                           FieldEnName = tempField.FieldEnName,
                           IsRequired = true,
                       });
            }
            if (!string.IsNullOrWhiteSpace(addressValue?.ToString()))
            {
                var verifyAttr = addressPro.GetCustomAttribute<VerifyAttribute>();
                if (verifyAttr != null)
                {
                    ValidatorVerifyAttr(addressPro, addressValue, tempField, properties, obj);
                }
            }
        }
        else if (tempField.IsRequired)
        {
            if (string.IsNullOrWhiteSpace(resourceZhName?.ToString()))
            {
                if (string.IsNullOrEmpty(errorMsg))
                {
                    errorMsg = $"缺少[{tempField.FieldName}]字段信息";
                }
                throw new BusinessException(ErrorTypes.Order.OrderFieldValidater.ToString(),
                               errorMsg,
                       new OrderFieldValidaterDto()
                       {
                           FieldName = tempField.FieldName,
                           FieldEnName = tempField.FieldEnName,
                           IsRequired = true,
                       });
            }
        }

    }
}
