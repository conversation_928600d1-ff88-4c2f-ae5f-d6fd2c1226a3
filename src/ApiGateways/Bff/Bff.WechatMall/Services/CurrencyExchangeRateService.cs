using Bff.WechatMall.Callers;
using Bff.WechatMall.Services.Interfaces;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Payment.DTOs.Currency;

namespace Bff.WechatMall.Services;

public class CurrencyExchangeRateService : ICurrencyExchangeRateService
{
    private readonly IPaymentApiCaller _paymentApiCaller;

    public CurrencyExchangeRateService(IPaymentApiCaller paymentApiCaller)
    {
        _paymentApiCaller = paymentApiCaller;
    }

    public async Task<OrderPriceExchangeRateOutput> GetOrderPriceExchange(OrderPriceExchangeRateInput input)
    {

        var costCurrencyCode = input.CostCurrencyCode;
        var saleCurrencyCode = input.SaleCurrencyCode;
        var paymentCurrencyCode = input.PaymentCurrencyCode;
        List<GetExchangeRatesInput> exchangeRatesInputs = new();
        var costEqualsSale = costCurrencyCode.Equals(saleCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!costEqualsSale)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = costCurrencyCode,
                TargetCurrencyCode = saleCurrencyCode
            });
        }
        var saleEqualsPayment = saleCurrencyCode.Equals(paymentCurrencyCode, StringComparison.OrdinalIgnoreCase);
        if (!saleEqualsPayment)
        {
            exchangeRatesInputs.Add(new GetExchangeRatesInput
            {
                BaseCurrencyCode = saleCurrencyCode,
                TargetCurrencyCode = paymentCurrencyCode
            });
        }
        var exchangeRates = await _paymentApiCaller.GetExchangeRates(exchangeRatesInputs);
        return new OrderPriceExchangeRateOutput
        {
            CostExchangeRate = costEqualsSale ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == costCurrencyCode && x.TargetCurrencyCode == saleCurrencyCode)
                .First().ExchangeRate,
            ExchangeRate = saleEqualsPayment ? 1 : exchangeRates
                .Where(x => x.BaseCurrencyCode == saleCurrencyCode && x.TargetCurrencyCode == paymentCurrencyCode)
                .First().ExchangeRate,
        };
    }
}
