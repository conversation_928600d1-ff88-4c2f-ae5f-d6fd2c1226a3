using Bff.Manage.Models.Hubs;
using Common.Swagger;
using MessageHub;
using MessageHub.Hubs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;

namespace Bff.Manage.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class MessageHubController : ControllerBase
{
    private readonly IHubContext<CommonHub> _commonHubContext;

    public MessageHubController(IHubContext<CommonHub> commonHubContext)
    {
        _commonHubContext = commonHubContext;
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SendCompareVersions(SendCompareVersionsInput input)
    {
        var clientproxy = _commonHubContext.Clients.Group(input.Plamform.ToString());
        if (input.UserIds?.Any() == true)
            clientproxy = _commonHubContext.Clients.Users(input.UserIds);
        await clientproxy.SendAsync(CommonHubMethod.CompareVersions, JsonConvert.SerializeObject(input.Data));
        return Ok();
    }
}
