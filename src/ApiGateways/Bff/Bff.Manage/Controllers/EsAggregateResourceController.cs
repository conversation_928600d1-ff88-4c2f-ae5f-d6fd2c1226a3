using AutoMapper;
using Bff.Manage.Callers;
using Bff.Manage.Models.EsAggregateResource;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Resource.DTOs.AggregateResource;
using Contracts.Common.Resource.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Manage.Controllers;

/// <summary>
/// es聚合资源查询
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class EsAggregateResourceController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IResourceApiCaller _resourceApiCaller;
    public EsAggregateResourceController(
        IMapper mapper,
        IResourceApiCaller resourceApiCaller)
    {
        _mapper = mapper;
        _resourceApiCaller = resourceApiCaller;
    }

    /// <summary>
    /// 聚合资源-目的地搜索
    /// <remarks>
    /// 只支持系统资源查询 排序规则：国家 > 城市 > 机场
    /// </remarks>
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt(200,typeof(IEnumerable<DestinationSearchBffOutput>))]
    public async Task<IActionResult> DestinationSearch(DestinationSearchBffInput input)
    {
        var request = _mapper.Map<DestinationSearchInput>(input);
        var sysResourceTypes = new[]
        {
            EsAggregateResourceParentType.Country,
            EsAggregateResourceParentType.Province,
            EsAggregateResourceParentType.City,
            EsAggregateResourceParentType.Airport
        };
        //过滤掉非系统资源的类型
        input.ResourceTypes = input.ResourceTypes.Where(x => sysResourceTypes.Contains(x))
            .ToList();
        if (input.ResourceTypes.Any() is false)
            return Ok(Enumerable.Empty<DestinationSearchBffOutput>());
        
        var response = await _resourceApiCaller.AggregateResourceSearchDestinations(request);
        var concatResponse = response.CountryItems
            .Concat(response.ProvinceItems)
            .Concat(response.CityItems)
            .Concat(response.AirportItems);
        var result = _mapper.Map<IEnumerable<DestinationSearchBffOutput>>(concatResponse);
        return Ok(result);
    }
}