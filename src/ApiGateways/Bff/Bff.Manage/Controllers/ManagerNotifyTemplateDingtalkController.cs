using Bff.Manage.Callers;
using Common.Swagger;
using Contracts.Common.Notify.DTOs.ManagerNotify.Dingtalks;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Manage.Controllers;
/// <summary>
/// 钉钉模版与机器人配置
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class ManagerNotifyTemplateDingtalkController : ControllerBase
{
    public readonly INotifyApiCaller _notifyApiCaller;

    public ManagerNotifyTemplateDingtalkController(INotifyApiCaller  notifyApiCaller)
    {
        _notifyApiCaller = notifyApiCaller;
    }

    /// <summary>
    /// 获取钉钉模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<ManagerNotifyTemplateDingtalkListDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(GetManagerNotifyTemplateDingtalkInput input)
    {
        var result = await _notifyApiCaller.ManagerNotifyTemplateDingtalkSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取钉钉模版下拉选择列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [ProducesResponseType(typeof(List<ManagerNotifyTemplateDingtalkDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> List()
    {
        var result = await _notifyApiCaller.ManagerNotifyTemplateDingtalkList();
        return Ok(result);
    }

    /// <summary>
    /// 更新钉钉模版与机器人配置
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(ManagerNotifyTemplateDingtalkDetailDto input)
    {
        await _notifyApiCaller.ManagerNotifyTemplateDingtalkUpdate(input);
        return Ok();
    }

    /// <summary>
    /// 新增钉钉模版与机器人配置
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(ManagerNotifyTemplateDingtalkDetailDto input)
    {
        await _notifyApiCaller.ManagerNotifyTemplateDingtalkAdd(input);
        return Ok();
    }

    /// <summary>
    /// 删除钉钉模版与机器人配置
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Delete(ManagerNotifyTemplateDingtalkInput input)
    {
        await _notifyApiCaller.ManagerNotifyTemplateDingtalkDelete(input);
        return Ok();
    }

    /// <summary>
    /// 获取钉钉模版详情信息与机器人列表
    /// </summary>
    /// <param name="templateDingtalkId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [ProducesResponseType(typeof(ManagerNotifyTemplateDingtalkDetailDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> DetailAsync(long templateDingtalkId)
    {
        var result = await _notifyApiCaller.ManagerNotifyTemplateDingtalkDetail(templateDingtalkId);
        return Ok(result);
    }


}
