using Common;
using Common.Jwt;
using Common.ServicesHttpClient;
using Common.Swagger.Header;
using Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using MessageHub;

namespace Bff.Manage.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection ConfigureApplicationServices(this IServiceCollection services,
        IConfiguration configuration,
        IWebHostEnvironment env)
    {
        services.AddBasicServices(env, TenantHeaderRequirement.DontAdd);

        services.AddControllers(options =>
        {
        });
        services.AddCustomConfiguration(configuration);
        services.AddAutoMapper(System.Reflection.Assembly.GetExecutingAssembly());
        services.AddCallers();
        services.AddServices();
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ClockSkew = TimeSpan.FromSeconds(5),
                    ValidAudience = SysRole.Manager.ToString(),
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtConfig:SecurityKey"]!)),
                };
            });
        services.AddMessageService(configuration);

        return services;
    }

    /// <summary>
    /// 配置文件
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    private static IServiceCollection AddCustomConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ServicesAddress>(configuration.GetSection(nameof(ServicesAddress)));
        services.Configure<JwtConfig>(configuration.GetSection(nameof(JwtConfig)));

        return services;
    }

    private static IServiceCollection AddCallers(this IServiceCollection services)
    {
        var types = System.Reflection.Assembly
            .GetExecutingAssembly()
            .GetInterfaceAndImplement(s => s.Name.EndsWith("Caller"));
        foreach (var item in types)
            services.AddSingleton(item.Value, item.Key);

        return services;
    }

    private static IServiceCollection AddMessageService(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer("CommonHubScheme", options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    ValidateAudience = false, //不验证aud
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ClockSkew = TimeSpan.FromSeconds(5),
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtConfig:SecurityKey"]!)),
                };
                options.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var path = context.HttpContext.Request.Path;
                        if (!path.StartsWithSegments("/v1/commonhub"))
                            return Task.CompletedTask;

                        var accessToken = context.Request.Query["access_token"];
                        if (!string.IsNullOrEmpty(accessToken))
                        {
                            context.Token = accessToken;
                        }
                        return Task.CompletedTask;
                    }
                };
            });
        services.AddSignalrService(configuration.GetConnectionString("Redis")!);

        return services;
    }

    /// <summary>
    /// 批量注册Service
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    private static IServiceCollection AddServices(this IServiceCollection services)
    {
        var types = System.Reflection.Assembly
            .GetExecutingAssembly()
            .GetInterfaceAndImplement(s => s.Name.EndsWith("Service"));
        foreach (var item in types)
            services.AddScoped(item.Value, item.Key);

        return services;
    }
}
