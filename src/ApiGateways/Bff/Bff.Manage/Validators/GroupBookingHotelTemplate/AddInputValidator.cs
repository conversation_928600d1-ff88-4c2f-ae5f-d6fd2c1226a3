using Bff.Manage.Models.GroupBookingHotelTemplate;
using FluentValidation;

namespace Bff.Manage.Validators.GroupBookingHotelTemplate;

public class AddInputValidator : AbstractValidator<AddInput>
{
    public AddInputValidator()
    {
        RuleFor(s => s.Regions).NotEmpty();
        RuleFor(s => s.ValidityPeriod).NotEmpty();
        RuleFor(s => s.TemplateItems).NotEmpty();
        RuleFor(s => s.Prompt).NotEmpty();
        RuleFor(s => s.ReplyPrompt).NotEmpty().When(s => string.IsNullOrWhiteSpace(s.LinkPrompt));
        RuleFor(s => s.LinkPrompt).NotEmpty().When(s => string.IsNullOrWhiteSpace(s.ReplyPrompt)); ;
    }
}
