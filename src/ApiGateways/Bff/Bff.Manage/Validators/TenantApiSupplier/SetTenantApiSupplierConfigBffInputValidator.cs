using Bff.Manage.Models.TenantApiSupplierConfig;
using Contracts.Common.Tenant.Enums;
using FluentValidation;

namespace Bff.Manage.Validators.TenantApiSupplier;

public class SetTenantApiSupplierConfigBffInputValidator : AbstractValidator<SetTenantApiSupplierConfigBffInput>
{
    public SetTenantApiSupplierConfigBffInputValidator()
    {
        RuleFor(x => x.TenantId).NotEmpty();
        RuleFor(x => x.SupplierApiType).NotEmpty();
        When(x => x.SupplierApiType == SupplierApiType.Hop, () =>
        {
            // hotelId最多100个
            RuleFor(x => x.HotelIds).Must(x => x.Count <= 100);
        });
    }
}