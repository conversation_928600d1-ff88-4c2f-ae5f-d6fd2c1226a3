using Contracts.Common.Notify.DTOs.ManagerNotify;
using FluentValidation;

namespace Bff.Manage.Validators.Notify;

public class UpdateManagerNotifySettingInputValidator : AbstractValidator<UpdateManagerNotifySettingInput>
{
    public UpdateManagerNotifySettingInputValidator()
    {
        RuleFor(x => x.NotifyEventType)
          .NotEmpty()
          .IsInEnum()
          .When(x => x.NotifyEventSubType is null);

        RuleFor(x => x.NotifyEventSubType)
            .NotEmpty()
            .IsInEnum()
            .When(x => x.NotifyEventType is null);

        RuleFor(x => x.CountryCode)
            .NotEmpty();

        RuleFor(x => x.StaffIds)
            .NotEmpty();
    }
}
