using Bff.Manage.Models.HotelGroupBookingInquiry;
using FluentValidation;

namespace Bff.Manage.Validators.HotelGroupBooking;

public class BffSaveInquiryInformationInputValidator : AbstractValidator<BffSaveInquiryInformationInput>
{
    public BffSaveInquiryInformationInputValidator()
    {
        RuleFor(x => x.TenantId).GreaterThan(0);
        RuleFor(x => x.GroupBookingHotelInquiryId).GreaterThan(0);
        RuleFor(x => x.ApplicationDemandId).GreaterThan(0);

        RuleFor(x => x.ValidityPeriod).NotNull();
        RuleFor(x => x.TeamNatureTitle).Length(1, 50);
        RuleFor(x => x.OtherRequirement).Length(1, 1000);
    }
}



