using Common.Caller;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Payment.DTOs.NationalCurrency;
using Contracts.Common.Payment.DTOs.WithdrawOrder;
using Contracts.Common.Payment.DTOs.YeeMerConfig;
using EfCoreExtensions.Abstract;

namespace Bff.Manage.Callers;

public interface IPaymentApiCaller : IHttpCallerBase
{
    #region YeeMerConfig

    /// <summary>
    /// 配置代收代付易宝商户
    /// </summary>
    /// <returns></returns>
    Task SetYeeAgentMerConfig(long tenantId);

    /// <summary>
    /// 查询商户易宝配置信息
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<List<GetYeeMerConfigOutput>> GetYeeMerConfigs(params long[] tenantIds);

    /// <summary>
    /// 商户入网
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RegisterOutput> YeeRegister(RegisterInput input);

    /// <summary>
    /// 配置
    /// </summary>
    /// <returns></returns>
    Task<GetYeeAgentMerConfigOutput> GetYeePaySetting();

    #endregion

    #region WithdrawOrder

    /// <summary>
    /// 搜索商户账户线下提现处理列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchAuditsOutput>> SearchWithdrawOrderAudits(SearchAuditsInput input);

    /// <summary>
    /// 提现详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<WithdrawOrderDetailOutput> WithdrawOrderDetail(DetailInput input);

    /// <summary>
    /// 处理提现
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task WithdrawOrderAuditHandle(AuditHandleInput input);

    /// <summary>
    /// 撤销提现
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task WithdrawOrderAuditCancel(AuditCancelInput input);

    #endregion

    #region Currency

    /// <summary>
    /// 获取币种汇率
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetExchangeRateOutput>> GetExchangeRates(IEnumerable<GetExchangeRatesInput> input);

    /// <summary>
    /// 获取国家货币
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<NationalCurrencyOutput>> NationalCurrencySearch(NationalCurrencyInput input);
    
    /// <summary>
    /// 获取币种信息列表
    /// </summary>
    /// <returns></returns>
    Task<List<GetCurrencyOutput>> GetCurrencies();

    #endregion

}