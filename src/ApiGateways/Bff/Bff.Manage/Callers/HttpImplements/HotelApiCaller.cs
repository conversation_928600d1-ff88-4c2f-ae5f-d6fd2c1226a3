using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;

namespace Bff.Manage.Callers.HttpImplements;

public class HotelApiCaller: HttpCallerBase,IHotelApiCaller
{
    public HotelApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.Hotel, httpClientFactory)
    {

    }

    #region Hotel

    public Task<PagingModel<SearchHotelsOutput>> HotelSearch(SearchHotelsInput input)
    {
        var relativePath = "/Hotel/Search";
        return PostAsync<SearchHotelsInput, PagingModel<SearchHotelsOutput>>(relativePath, input);
    }

    public async Task<List<SearchEsHotelOutput>> GetEsHotelDetail(params long[] ids)
    {
        var relativePath = "/EsHotel/Get";
        return await PostAsync<long[], List<SearchEsHotelOutput>>(
            relativePath, ids);
    }
    
    public async Task<List<SearchEsHotelOutput>> GetEsHotelDetailV2(params long[] mixHotelIds)
    {
        var relativePath = "/EsHotel/GetV2";
        return await PostAsync<long[], List<SearchEsHotelOutput>>(relativePath, mixHotelIds);
    }

    public async Task<GetHotelDetailsOutput> GetHotelDetail(long hotelId)
    {
        var relativePath = $"/Hotel/Detail?hotelId={hotelId}";
        return await GetAsync<GetHotelDetailsOutput>(relativePath);
    }

    public async Task<List<CheckIsEnabledOutput>> CheckHotelIsEnabled(CheckIsEnabledInput input)
    {
        var relativePath = "/Hotel/CheckHotelsEnabled";
        return await PostAsync<CheckIsEnabledInput, List<CheckIsEnabledOutput>>(relativePath, input);
    }

    public async Task<GetHotelRoomsOutput> GetHotelRooms(GetHotelRoomsInput input)
    {
        var relativePath = "/Hotel/GetHotelRooms";
        return await PostAsync<GetHotelRoomsInput, GetHotelRoomsOutput>(relativePath, input);
    }

    public async Task<IEnumerable<GetHotelIdsByResourceHotelIdsOutput>> GetHotelIdsByResourceHotelIds(long[] ids)
    {
        var relativePath = "/Hotel/GetHotelIdsByResourceHotelIds";
        return await PostAsync<long[], IEnumerable<GetHotelIdsByResourceHotelIdsOutput>>(relativePath, ids);
    }

    public async Task<IEnumerable<GetHotelFirstPhotoOutput>> GetHotelFirstPhoto(long[] ids)
    {
        var relativePath = "/Hotel/GetHotelFirstPhoto";
        return await PostAsync<long[], IEnumerable<GetHotelFirstPhotoOutput>>(relativePath, ids);
    }
    #endregion

    #region PriceStrategy

    public async Task<PagingModel<GetHotelAndPriceStrategyOutput>> GetHotelAndPriceStrategy(
        GetHotelAndPriceStrategyInput input)
    {
        var relativePath = "/PriceStrategy/GetHotelAndPriceStrategy";
        return await PostAsync<GetHotelAndPriceStrategyInput, PagingModel<GetHotelAndPriceStrategyOutput>>(relativePath,
            input);
    }

    public async Task<List<GetSimpleInfoByHotelIdsOutput>> GetSimpleInfoByHotelIds(GetSimpleInfoByHotelIdsInput input)
    {
        var relativePath = "/PriceStrategy/GetSimpleInfoByHotelIds";
        return await PostAsync<GetSimpleInfoByHotelIdsInput, List<GetSimpleInfoByHotelIdsOutput>>(relativePath, input);
    }

    public async Task<List<GetPriceStrategyOutput>> GetPriceStrategyByIds(List<long> priceStrategyIds)
    {
        var relativePath = "/PriceStrategy/GetByPriceStrategyIds";
        return await PostAsync<List<long>, List<GetPriceStrategyOutput>>(relativePath, priceStrategyIds);
    }

    public async Task<List<AgencyGetOutput>> GetAgencyStrategies(AgencyGetInput input)
    {
        var relativePath = "/PriceStrategyCalendarPrice/AgencyGet";
        return await PostAsync<AgencyGetInput, List<AgencyGetOutput>>(relativePath, input);
    }

    public Task<CheckSaleOutput> PreOrderCheckSale(CheckSaleInput input)
    {
        var relativePath = "/PriceStrategyCalendarPrice/PreOrderCheckSale";
        return PostAsync<CheckSaleInput, CheckSaleOutput>(relativePath, input);
    }


    #endregion

    #region ApiHotel

    public Task<List<Contracts.Common.Hotel.DTOs.ApiHotel.CheckRefOutput>> CheckRefByResourceHotelIds(Contracts.Common.Hotel.DTOs.ApiHotel.CheckRefInput input)
    {
        var relativePath = "/ApiHotel/CheckRefByResourceHotelIds";
        return PostAsync<Contracts.Common.Hotel.DTOs.ApiHotel.CheckRefInput, List<Contracts.Common.Hotel.DTOs.ApiHotel.CheckRefOutput>>(relativePath, input);
    }

    #endregion
}
