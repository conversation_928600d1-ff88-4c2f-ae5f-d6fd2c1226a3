using Contracts.Common.Hotel.Enums;

namespace Bff.Manage.Models.TenantHotel;

public class GetOperateInfoBffOutput
{
    /// <summary>
    /// 运营模式
    /// </summary>
    public OperatingModel OperatingModel { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string ENName { get; set; }

    public string CountryName { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 每日营业时间 - 开始
    /// </summary>
    public TimeSpan ServiceTimeBegin { get; set; }

    /// <summary>
    /// 每日营业时间 - 结束
    /// </summary>
    public TimeSpan ServiceTimeEnd { get; set; }

    /// <summary>
    /// 营业结束时间是否次日
    /// </summary>
    public bool ServiceEndtimeInNextDay { get; set; }
}