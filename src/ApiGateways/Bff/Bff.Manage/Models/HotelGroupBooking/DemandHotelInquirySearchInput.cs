using Contracts.Common.Notify.Enums;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.Enums;

namespace Bff.Manage.Models.HotelGroupBooking;

public class DemandHotelInquirySearchInput
{
    /// <summary>
    /// 申请单id
    /// </summary>
    public long ApplicationFormId { get; set; }

    public long TenantId { get; set; }
}

public class DemandHotelInquiryoutput
{
    /// <summary>
    /// 需求目的地id
    /// </summary>
    public long ApplicationDemandId { get; set; }

    public int? CountryCode { get; set; }

    public string? CountryName { get; set; }

    public int? ProvinceCode { get; set; }

    public string? ProvinceName { get; set; }

    public int? CityCode { get; set; }

    public string? CityName { get; set; }

    /// <summary>
    /// 指定酒店
    /// </summary>
    public ApplicationDemandHotelDto[]? Hotels { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime? CheckOutDate { get; set; }

    /// <summary>
    /// 报价信息
    /// </summary>
    public DemandHotelInquiryDto[] DemandHotelInquiries { get; set; }

    /// <summary>
    /// 报价过期时间
    /// </summary>
    public DateTime? ExpiredTime { get; set; }
}

public class DemandHotelInquiryDto
{
    /// <summary>
    /// 酒店询价单id 查询对于报价使用
    /// </summary>
    public long HotelInquiryId { get; set; }

    /// <summary>
    /// 需求目的地id
    /// </summary>
    public long ApplicationDemandId { get; set; }

    /// <summary>
    /// 目的地酒店id
    /// </summary>
    public long DemandHoteId { get; set; }

    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long? ResourceHotelId { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string? HotelName { get; set; }

    /// <summary>
    /// 酒店英文名称
    /// </summary>
    public string HotelENName { get; set; }

    /// <summary>
    /// 是否推荐酒店 false-指定酒店 true-推荐酒店
    /// </summary>
    public bool IsRecommend { get; set; }

    public string? FirstName { get; set; }

    public string? Email { get; set; }

    /// <summary>
    /// 状态 0-报价中 1-已报价 2-无法报价 3-报价过期 4-已结束 5-发送失败
    /// </summary>
    public GroupBookingHotelInquiryStatus Status { get; set; }

    /// <summary>
    /// 邮件发送状态
    /// </summary>
    public EmailSmtpStatusCode? MailStatus { get; set; }

    /// <summary>
    /// 无法报价原因
    /// </summary>
    public string? UnableToQuoteReason { get; set; }

    /// <summary>
    /// 报价过期时间
    /// </summary>
    public DateTime? ExpiredTime { get; set; }

    /// <summary>
    /// 酒店报价信息
    /// </summary>
    public List<GroupBookingHotelQuoteOutput> HotelQuotes { get; set; }

    /// <summary>
    /// 酒店回复时间
    /// </summary>
    public DateTime? HotelRecoveryTime { get; set; }
}