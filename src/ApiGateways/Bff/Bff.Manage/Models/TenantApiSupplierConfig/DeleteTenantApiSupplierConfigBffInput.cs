using Contracts.Common.Tenant.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Manage.Models.TenantApiSupplierConfig;

public class DeleteTenantApiSupplierConfigBffInput
{
    /// <summary>
    /// 租户id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TenantId { get; set; }
    
    /// <summary>
    /// API供应商类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public SupplierApiType SupplierApiType { get; set; }
}