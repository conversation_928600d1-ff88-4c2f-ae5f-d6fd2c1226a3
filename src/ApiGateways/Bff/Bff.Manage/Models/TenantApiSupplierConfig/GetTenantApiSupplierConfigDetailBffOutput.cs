using Contracts.Common.Hotel.Enums;
using Contracts.Common.Tenant.Enums;

namespace Bff.Manage.Models.TenantApiSupplierConfig;

public class GetTenantApiSupplierConfigDetailBffOutput
{
    /// <summary>
    /// API供应商类型
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enabled { get; set; }


    #region hop

    /// <summary>
    /// 是否全部酒店
    /// </summary>
    public bool? IsAllHotel { get; set; }

    /// <summary>
    /// 酒店价格类型
    /// </summary>
    public List<HotelPriceChannelType> HotelPriceChannelTypes { get; set; } = new();

    /// <summary>
    /// 酒店id列表
    /// </summary>
    public List<long> HotelIds { get; set; } = new();

    // /// <summary>
    // /// 酒店资源列表
    // /// </summary>
    // public List<TenantApiSupplierConfigHotelItem> HotelItems { get; set; } = new();

    #endregion
}


public class TenantApiSupplierConfigHotelItem
{
    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string Name { get; set; }
}