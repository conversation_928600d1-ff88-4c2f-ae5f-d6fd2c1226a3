namespace Contracts.Common.Hotel.DTOs.Hotel;

public class AgencySearchInput : PagingInput
{
    public IEnumerable<long> HotelId { get; set; } = Enumerable.Empty<long>();
    public IEnumerable<long> PriceStrategyId { get; set; } = Enumerable.Empty<long>();

    /// <summary>
    /// 城市编码
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime LiveDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime LeaveDate { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    public string Name { get; set; }

    public int? MaximumOccupancy { get; set; }
}