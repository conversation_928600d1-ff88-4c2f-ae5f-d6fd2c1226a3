using Contracts.Common.Hotel.Enums;

namespace Contracts.Common.Hotel.DTOs.Hotel;

public class GetHotelRoomDetailsOutput
{
    public long Id { get; set; }

    public long HotelId { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string ENName { get; set; }

    /// <summary>
    /// 窗户
    /// </summary>
    public WindowType WindowType { get; set; }

    /// <summary>
    /// 面积 - 最小
    /// </summary>
    public decimal AreaMin { get; set; }

    /// <summary>
    /// 面积 - 最大
    /// </summary>
    public decimal AreaMax { get; set; }

    /// <summary>
    /// 楼层 - 最低
    /// </summary>
    public int FloorMin { get; set; }

    /// <summary>
    /// 楼层 - 最高
    /// </summary>
    public int FloorMax { get; set; }

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaximumOccupancy { get; set; }

    /// <summary>
    /// 房间数量
    /// </summary>
    public int RoomQuantity { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 床型json
    /// </summary>
    public List<BedType> BedType { get; set; }

    public long ResourceRoomId { get; set; }

    public IEnumerable<string> Photos { get; set; }

    /// <summary>
    /// 房型显示状态
    /// </summary>
    public bool Viewable { get; set; }

    /// <summary>
    /// 房型说明
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 床型json
/// </summary>
public class BedType
{
    /// <summary>
    /// 床型
    /// </summary>
    public string main { get; set; }

    /// <summary>
    /// 床型详情
    /// </summary>
    public List<SubModel> sub { get; set; }
}

public class SubModel
{
    /// <summary>
    /// 中文名床型（单人床）
    /// </summary>
    public string cn { get; set; }
    /// <summary>
    /// 英文名 Single
    /// </summary>
    public string en { get; set; }
    /// <summary>
    /// 数量 2
    /// </summary>
    public int num { get; set; }

    /// <summary>
    /// 宽度 米 0.8
    /// </summary>
    public string width { get; set; }
}