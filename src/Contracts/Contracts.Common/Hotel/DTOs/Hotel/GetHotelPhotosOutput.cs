namespace Contracts.Common.Hotel.DTOs.Hotel;

public class GetHotelPhotosOutput
{
    public long Id { get; set; }

    public string Name { get; set; }

    public List<Room> Rooms { get; set; } = new List<Room>();

}

public class Room
{
    /// <summary>
    /// 酒店房型Id，0表示该照片仅关联Hotel不关联HotelRoom
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 资源酒店房型Id
    /// </summary>
    public long ResourceRoomId { get; set; }

    /// <summary>
    /// 房型名称
    /// </summary>
    public string Name { get; set; }

    public List<Photo> Photos { get; set; }
}

public class Photo
{
    public long Id { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string Url { get; set; }
}