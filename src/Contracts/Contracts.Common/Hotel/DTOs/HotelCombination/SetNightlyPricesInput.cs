using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.HotelCombination;
public class SetNightlyPricesInput
{
    public string PriceStrategyId { get; set; }

    public IEnumerable<NightlyPriceDto> Nightlies { get; set; }

    /// <summary>
    /// 是否修改日历价格,true则会推送相关变更事件
    /// </summary>
    public bool IsModify { get; set; }
}

public class NightlyPriceDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 库存
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 房态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 是否允许超售
    /// </summary>
    public bool IsOversell { get; set; }
}