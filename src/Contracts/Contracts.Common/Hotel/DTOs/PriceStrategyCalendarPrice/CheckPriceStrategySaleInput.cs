using Contracts.Common.Order.Enums;

namespace Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice
{
    public class CheckPriceStrategySaleInput
    {
        public long HotelId { get; set; }
        public long PriceStrategyId { get; set; }
        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        public int Quantity { get; set; }
        public SellingChannels? SalesChannel { get; set; }
    }
}
