using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
public class QuerySaleChanelsPriceInput
{
    public long HotelId { get; set; }
    public List<long> PriceStartegyIds { get; set; }

    public List<SellingChannels> SellingChannels { get; set; }

    public DateTime BeginDate { get; set; }
    public DateTime EndDate { get; set; }
}

public class QuerySaleChanelsPriceOutput
{
    public long PriceStrategyId { get; set; }
    public IEnumerable<SaleChanelsPriceOutput> SaleChanelsPrices { get; set; }
}

public class SaleChanelsPriceOutput
{
    public DateTime Date { get; set; }
    public SellingChannels SellingChannels { get; set; }

    public decimal? Price { get; set; }

    public string CurrencyCode { get; set; }
}