using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;

namespace Contracts.Common.Hotel.DTOs.SaleChannel;

public class UpdateHotelChannelMarkupInput
{
    public long HotelId { get; set; }

    public long PriceStrategyId { get; set; }

    public List<HotelChannelMarkupModel> HotelChannelMarkup { get; set; }

}

public class HotelChannelMarkupModel
{
    /// <summary>
    /// 编号
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 渠道
    /// </summary>
    public SellingChannels ChannelType { get; set; }

    /// <summary>
    /// 加价基础类型
    /// </summary>
    public MarkupPriceType PriceType { get; set; }

    /// <summary>
    /// 加价方式
    /// </summary>
    public MarkupType MarkupType { get; set; }

    /// <summary>
    /// 值，0.01 表示 1%
    /// </summary>
    public decimal Value { get; set; }

    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }
}