using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.HotelMapping;
public class ProcessMappingHotelInput
{
    public SellingPlatform SellingPlatform { get; set; }
}

public class ProcessMappingHotelOutput
{
    public long TenantId { get; set; }

    public IEnumerable<long> HotelIds { get; set; }
}
