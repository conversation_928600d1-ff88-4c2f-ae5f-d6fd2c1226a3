namespace Contracts.Common.Hotel.DTOs.PriceStrategy;

public class GetHotelAndPriceStrategyOutput
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    /// <summary>
    /// 房型id
    /// </summary>
    public long RoomId { get; set; }

    /// <summary>
    /// 房型名称
    /// </summary>
    public string RoomName { get; set; }

    /// <summary>
    /// 价格策略id
    /// </summary>
    public long PriceStrategyId { get; set; }

    /// <summary>
    /// 价格策略名称
    /// </summary>
    public string PriceStrategyName { get; set; }

    /// <summary>
    /// 采购价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 基础售价
    /// </summary>
    public decimal? SalePrice { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }
    
    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }
}