namespace Contracts.Common.Hotel.DTOs.PriceStrategy
{
    public class GetSimpleInfoByHotelIdsOutput
    {
        public long HotelId { get; set; }
        public IEnumerable<SimpleInfo_Room> Rooms { get; set; }
    }

    public class SimpleInfo_Room
    {
        public long RoomId { get; set; }
        public string RoomName { get; set; }

        public IEnumerable<SimpleInfo_PriceStrategy> PriceStrategies { get; set; }
    }

    public class SimpleInfo_PriceStrategy
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public decimal? Price { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>
        public long SupplierId { get; set; }
    }
}
