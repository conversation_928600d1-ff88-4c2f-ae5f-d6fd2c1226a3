using Contracts.Common.Hotel.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.SpecializedHotel;
public class ImportHotelOutput
{
    public bool Success { get; set; } = false;

    /// <summary>
    /// 错误酒店Id
    /// </summary>
    public string? ErrorHotelId { get; set; }

    /// <summary>
    /// 错误类型
    /// </summary>
    public ImportErrorType? ErrorType { get; set; }

    public int? Row { get; set; }
}
