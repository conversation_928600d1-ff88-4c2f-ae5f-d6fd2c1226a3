using Contracts.Common.Tenant.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.SpecializedHotel;
public class ImportHotelInput
{

    public long SpecializedHotelId { get; set; }

    public string FileUrl { get; set; }

    public OperationUserDto OperationUser { get; set; }
}

public class ImportHotelDto
{
    public string? HotelIdStr { get; set; }

    /// <summary>
    /// hop id
    /// </summary>
    public long HotelId { get; set; }

    public string HotelName { get; set; }

    public string? EnHotelName { get; set; }

    public bool Check { get; set; }

    public string? CheckName { get; set; }

    public string? Msg { get; set; }

    public long ApiHotelId { get; set; }

}