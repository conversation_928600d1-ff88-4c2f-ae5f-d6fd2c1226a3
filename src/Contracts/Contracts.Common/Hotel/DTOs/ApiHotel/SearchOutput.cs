using Contracts.Common.Hotel.DTOs.Tag;
using Contracts.Common.Hotel.Enums;

namespace Contracts.Common.Hotel.DTOs.ApiHotel;
public class SearchOutput : ApiHotelDto
{
    public long Id { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 是否置顶
    /// </summary>
    public bool OnTop { get; set; }

    /// <summary>
    /// 敏感度标签 英文逗号分隔
    /// </summary>
    public ApiHotelTag? Tags { get; set; }

    public List<TagInfo> TagInfos { get; set; }

    public long TenantId { get; set; }
}
