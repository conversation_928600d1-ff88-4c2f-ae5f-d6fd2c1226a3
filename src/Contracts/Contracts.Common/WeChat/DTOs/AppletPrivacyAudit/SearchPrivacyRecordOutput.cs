using Contracts.Common.WeChat.Enums;

namespace Contracts.Common.WeChat.DTOs.AppletPrivacyAudit;

public class SearchPrivacyRecordOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 申请类型
    /// </summary>
    public WechatAppletPrivacyApplyType ApplyType { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public WechatAppletPrivacyAuditStatus AuditStatus { get; set; }

    /// <summary>
    /// 原因(审核不通过)
    /// </summary>
    public string Reason { get; set; }

    /// <summary>
    /// 申请时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}