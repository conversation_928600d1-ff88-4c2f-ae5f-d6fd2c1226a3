namespace Contracts.Common.WeChat.DTOs.WechatAppletTemplate;

public class TemplateOutput
{
    /// <summary>
    /// 微信模板id
    /// </summary>
    public int TemplateId { get; set; }

    /// <summary>
    /// 草稿id
    /// </summary>
    public int DraftId { get; set; }

    /// <summary>
    /// 版本号，开发者自定义字段
    /// </summary>
    public string UserVersion { get; set; }

    /// <summary>
    /// 版本描述 开发者自定义字段
    /// </summary>
    public string UserDesc { get; set; }

    /// <summary>
    /// 0对应普通模板，1对应标准模板
    /// </summary>
    public byte TemplateType { get; set; }

    public DateTime CreateTime { get; set; }
}
