using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Messages;
public class HotelApiOrderResultHandleMessage
{
    public long TenantId { get; set; }

    public long BaseOrderId { get; set; }

    /// <summary>
    /// 供应商api订单id
    /// </summary>
    public string? SupplierOrderId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SupplierApiOrderStatus Status { get; set; }

    public string Message { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    public string? ConfirmCode { get; set; }

    /// <summary>
    /// 入住人
    /// </summary>
    public IEnumerable<HotelOrderGuestDto>? Guests { get; set; }
}