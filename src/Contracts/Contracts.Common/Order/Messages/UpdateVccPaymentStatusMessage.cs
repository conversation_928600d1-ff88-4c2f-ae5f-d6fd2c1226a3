using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.Messages;

public class UpdateVccPaymentStatusMessage
{
    /// <summary>
    /// 订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public OrderType OrderType { get; set; }
    
    /// <summary>
    /// vcc支付状态
    /// </summary>
    public OrderVirtualCreditCardPaymentStatus VccPaymentStatus { get; set; }
}