using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Messages;
public class KingdeeAPPayablePushMessage
{
    public long KingdeePushId { get; set; }

    /// <summary>
    /// 执行状态 1-保存 2-提交 3-审核
    /// </summary>
    public KingdeeFormStatus KingdeeFormStatus { get; set; }

    /// <summary>
    /// 指定供应商id
    /// </summary>
    public long? SupplierId { get; set; }
}
