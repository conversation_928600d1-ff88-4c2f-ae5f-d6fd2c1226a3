namespace Contracts.Common.Order.Enums;

/// <summary>
/// 订单凭证发货状态
/// </summary>
public enum OrderVoucherDeliverStatus
{
    /// <summary>
    /// 成功
    /// </summary>
    Success = 0,
    
    /// <summary>
    /// 库存不足
    /// </summary>
    InventoryNotEnough = 1,
    
    /// <summary>
    /// 接口同步失败
    /// </summary>
    SyncFailed = 2,
    
    /// <summary>
    /// 组合中-待统一同步到开放平台
    /// </summary>
    InCombination = 3
}