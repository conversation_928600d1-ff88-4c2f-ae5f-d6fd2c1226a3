namespace Contracts.Common.Order.Enums;

/// <summary>
/// 门票组合订单状态
/// </summary>
public enum TicketsCombinationOrderStatus
{
    /// <summary>
    /// 成功
    /// </summary>
    Success = 0,
    
    /// <summary>
    /// 待发货
    /// </summary>
    WaitingForDeliver = 1,
    
    /// <summary>
    /// 发货失败
    /// </summary>
    DeliveryFail = 2,
    
    /// <summary>
    /// 发货同步失败
    /// </summary>
    DeliverySyncFailed = 3,
    
    /// <summary>
    /// 渠道异常
    /// </summary>
    ChannelAbnormal = 4,
    
    /// <summary>
    /// 已关闭
    /// <remarks>目前只支持异常单的关闭</remarks>
    /// </summary>
    Closed = 5
}