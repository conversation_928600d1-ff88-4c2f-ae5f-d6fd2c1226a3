using System.ComponentModel;

namespace Contracts.Common.Order.Enums;

/// <summary>
/// 订单业务错误码枚举类型
/// <remarks>供应端&saas&渠道端3端流程的订单业务错误码</remarks>
/// </summary>
public enum OrderBusinessErrorCodeType
{
    #region 供应端开放平台提供的错误码,需要用到才补充定义
    
    /// <summary>
    /// 参数错误
    /// </summary>
    [Description("参数错误")]
    ParameterError = 3004,

    #endregion

    #region 通用业务状态
    
    /// <summary>
    /// 操作失败
    /// <remarks>目前用于应对供应端返回[1000]的业务错误码</remarks>
    /// </summary>
    [Description("操作失败")]
    OperationFailure = 10000,
    
    /// <summary>
    /// 库存不足
    /// </summary>
    [Description("库存不足")]
    InventoryNotEnough = 10001,
    
    /// <summary>
    /// 同步失败
    /// </summary>
    [Description("系统已发凭证给游客，订单同步渠道报错，联系运营处理")]
    SyncFailed = 10002,
    
    /// <summary>
    /// 已付款,待发货
    /// </summary>
    [Description("已付款，待供货方发货")]
    WaitingForDeliver = 10003,
    
    /// <summary>
    /// 供应商取消订单：供货方取消订单，发货失败，请退款手工处理
    /// </summary>
    [Description("供应商取消订单：供货方取消订单，发货失败，请退款手工处理")]
    Refunded = 10004,

    /// <summary>
    /// SAAS发货失败，请前往供货方后台获取凭证手动发货
    /// </summary>
    [Description("SAAS发货失败，请前往供货方后台获取凭证手动发货")]
    SaasDeliveryFailed = 10005,
    
    /// <summary>
    /// 组合中,待发货
    /// </summary>
    [Description("组合中,待发货")]
    InCombination = 10006,
    
    /// <summary>
    /// 等待手工上传凭证,待发货
    /// </summary>
    [Description("等待手工上传凭证,待发货")]
    InManual = 10007,
    
    /// <summary>
    /// 供货端拒单
    /// </summary>
    [Description("供货端拒单")]
    Rejected = 10008,
    
    /// <summary>
    /// 供货方发货失败
    /// </summary>
    [Description("供货方发货失败")]
    DeliverFailed = 10009,
    
    /// <summary>
    /// 供应端采购中
    /// </summary>
    [Description("供货方采购中")]
    TicketPurchasing = 10010,
    
    /// <summary>
    /// 支付处理中
    /// </summary>
    [Description("支付处理中")]
    PayProcessing = 20001,
    
    #endregion
    
    #region 线路特殊业务

    /// <summary>
    /// 自动确认失败
    /// </summary>
    [Description("自动确认失败")]
    LineAutoConfirmFailed = 11001,
    
    /// <summary>
    /// 同步确认失败
    /// </summary>
    [Description("渠道同步确认失败")]
    LineSyncConfirmFailed = 11002,

    #endregion
}