namespace Contracts.Common.Order.Enums
{
    /// <summary>
    /// 应付款项 日期类型
    /// </summary>
    public enum PayablesDateType
    {
        /// <summary>
        /// 下单日期 / 预约日期
        /// </summary>
        Create = 1,

        /// <summary>
        /// 完结日期
        /// </summary>
        Finish = 2,

        /// <summary>
        /// 入住日期
        /// </summary>
        CheckIn = 3,

        /// <summary>
        /// 离店日期
        /// </summary>
        CheckOut = 4,

        /// <summary>
        /// 退款日期
        /// </summary>
        Refund = 5,
            
        /// <summary>
        /// 出现日期
        /// </summary>
        TravelBeginDate = 7,
    }
}
