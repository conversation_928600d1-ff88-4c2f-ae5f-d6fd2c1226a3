using System.ComponentModel;

namespace Contracts.Common.Order.Enums
{
    public enum RefundOrderStatus
    {
        None,

        /// <summary>
        /// 审核中
        /// </summary>
        [Description("审核中")]
        UnderReview = 1,

        /// <summary>
        /// 已拒绝
        /// </summary>
        [Description("已拒绝")]
        Rejected = 2,

        /// <summary>
        /// 退款中
        /// </summary>
        [Description("退款中")]
        Refunding = 3,

        /// <summary>
        /// 已退款
        /// </summary>
        [Description("已退款")]
        Refunded = 4,

        /// <summary>
        /// 退款失败
        /// </summary>
        [Description("退款失败")]
        RefundFailed = 5
    }
}
