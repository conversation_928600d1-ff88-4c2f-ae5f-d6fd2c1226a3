using System.ComponentModel;

namespace Contracts.Common.Order.Enums;

public enum ReceiptDateTye
{
    /// <summary>
    /// 下单日期 / 预约日期
    /// </summary>
    [Description("下单时间")]
    Create = 1,

    /// <summary>
    /// 完结日期
    /// </summary>
    [Description("完成时间")]
    Finish = 2,

    /// <summary>
    /// 入住日期
    /// </summary>
    [Description("入住时间")]
    CheckIn = 3,

    /// <summary>
    /// 离店日期
    /// </summary>
    [Description("离店时间")]
    CheckOut = 4,

    /// <summary>
    /// 退款日期
    /// </summary>
    [Description("退款时间")]
    Refund = 5,
    
    /// <summary>
    /// 充值单提交时间
    /// </summary>
    [Description("充值时间")]
    ChargeCreate = 6,
    
    /// <summary>
    /// 出行日期
    /// </summary>
    [Description("出行日期")]
    TravelBeginDate = 7,
}