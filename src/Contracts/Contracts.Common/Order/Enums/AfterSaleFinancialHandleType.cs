using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;
public enum AfterSaleFinancialHandleType
{
    /// <summary>
    /// 加收
    /// </summary>
    Additional = 1,

    /// <summary>
    /// 退款
    /// </summary>
    Refund = 2,

    /// <summary>
    /// 赔付
    /// </summary>
    Compensate = 3,
}

public enum HandleOrderStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 已完成
    /// </summary>
    Finished = 1,
}
