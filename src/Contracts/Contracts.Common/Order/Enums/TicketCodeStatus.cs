namespace Contracts.Common.Order.Enums
{
    public enum TicketCodeStatus
    {
        /// <summary>
        /// 待预约
        /// </summary>
        WaitingForReservation = 1,

        /// <summary>
        /// 预约支付中
        /// </summary>
        Reservation = 2,

        /// <summary>
        /// 待使用
        /// </summary>
        WaitingForUse = 3,

        /// <summary>
        /// 退款中
        /// </summary>
        Refunding = 4,

        /// <summary>
        /// 已退款
        /// </summary>
        Refunded = 5,

        /// <summary>
        /// 已完成
        /// </summary>
        Finished = 6,

        /// <summary>
        /// 取消中(有加价的预约单退款中状态)
        /// </summary>
        Canceling = 7
    }
}
