using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;

/// <summary>
/// 表单状态
/// </summary>
public enum KingdeeFormStatus
{
    Pending = 0,
    /// <summary>
    /// 已保存
    /// </summary>
    Saved = 1,
    /// <summary>
    /// 已提交
    /// </summary>
    Submitted = 2,
    /// <summary>
    /// 已审核
    /// </summary>
    Audited = 3,

    /// <summary>
    /// 反审核
    /// </summary>
    UnAudited = 4,

    /// <summary>
    /// 删除
    /// </summary>
    Delete = 5,
}
