using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;
public enum AggregateOrderStatus
{
    /// <summary>
    /// 待付款
    /// </summary>
    WaitingForPay = 0,
    /// <summary>
    /// 待确认
    /// </summary>
    WaitingForConfirm = 1,
    /// <summary>
    /// 已确认
    /// </summary>
    Confirmed = 2,
    /// <summary>
    /// 已完成
    /// </summary>
    Finished = 3,
    /// <summary>
    /// 已关闭
    /// </summary>
    Closed = 4,
    /// <summary>
    /// 渠道异常
    /// </summary>
    ChannelAbnormal = 5,
}

public enum AggregateOrderRefundStatus
{
    /// <summary>
    /// 未退款
    /// </summary>
    UnRefund = 0,
    /// <summary>
    /// 退款中
    /// </summary>
    Refunding = 1,
    /// <summary>
    /// 退款失败
    /// </summary>
    RefundFailed = 2,
    /// <summary>
    /// 已退款
    /// </summary>
    Refunded = 3,
}

public enum AggregateOrderConfirmStatus
{
    /// <summary>
    /// 未确认
    /// </summary>
    UnConfirm = 0,
    /// <summary>
    /// 确认中
    /// </summary>
    Confirming = 1,
    /// <summary>
    /// 确认失败
    /// </summary>
    ConfirmFailed = 2,
    /// <summary>
    /// 已确认
    /// </summary>
    Confirmed = 3
}

public enum AggregateOrderTravelStatus
{
    /// <summary>
    /// 未出行/入住
    /// </summary>
    NotTraveled = 0,
    /// <summary>
    /// 已入住
    /// </summary>
    Traveled = 1,
}