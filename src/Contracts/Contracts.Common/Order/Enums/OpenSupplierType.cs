namespace Contracts.Common.Order.Enums;

/// <summary>
/// 开放平台 - 供应商类型
/// </summary>
public enum OpenSupplierType
{
    /// <summary>
    /// saas 系统 默认值
    /// </summary>
    System = 0,
    
    /// <summary>
    /// globaltix
    /// </summary>
    GlobalTix = 1,
    
    /// <summary>
    /// 客路
    /// </summary>
    Klook = 2,
    
    /// <summary>
    /// treep v2 - treepflyingearth
    /// </summary>
    TreepFlyingEarth = 3,
    
    /// <summary>
    /// Exoz(澳新)
    /// </summary>
    Experienceoz = 4,
    
    /// <summary>
    /// CA
    /// </summary>
    CA = 5,
    
    /// <summary>
    /// Mozio
    /// </summary>
    Mozio = 6,
    
    /// <summary>
    /// 超越天空
    /// </summary>
    CaoYueTianKong = 7,
    
    /// <summary>
    /// 新尚维 sctt
    /// </summary>
    Sctt = 8,
    
    /// <summary>
    /// Eyounz
    /// </summary>
    Eyounz = 9,
    
    /// <summary>
    /// tours group
    /// </summary>
    ToursGroup = 10
}