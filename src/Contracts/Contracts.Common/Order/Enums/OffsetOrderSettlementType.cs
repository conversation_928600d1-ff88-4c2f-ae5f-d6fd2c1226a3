using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;

/// <summary>
/// 抵冲单结算方式类型
/// </summary>
public enum OffsetOrderSettlementType
{
    /// <summary>
    /// 结算单结算
    /// </summary>
    SettlementOrder = 0,

    /// <summary>
    /// 订单退款结算
    /// </summary>
    OrderRefund = 1,

    /// <summary>
    /// 售后财务处理单结算
    /// </summary>
    AfterSaleFinancialHandleOrder = 2,
}
