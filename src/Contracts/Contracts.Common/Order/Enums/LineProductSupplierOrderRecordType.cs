namespace Contracts.Common.Order.Enums;

public enum LineProductSupplierOrderRecordType
{
    /// <summary>
    /// 创建订单
    /// </summary>
    Create = 1,

    /// <summary>
    /// 付款
    /// </summary>
    Payment = 2,
    
    /// <summary>
    /// 退款
    /// </summary>
    Refund = 3,
    
    /// <summary>
    /// 发货
    /// </summary>
    Delivery = 4,
    
    /// <summary>
    /// 同步api分销商发货
    /// </summary>
    SyncDelivery = 5,
    
    /// <summary>
    /// 组合中-待统一同步到开放平台
    /// </summary>
    InCombination = 6,
    
    /// <summary>
    /// 拒单
    /// </summary>
    Reject = 7,
}