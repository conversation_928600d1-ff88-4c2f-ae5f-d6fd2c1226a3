using System.ComponentModel;

namespace Contracts.Common.Order.Enums;

/// <summary>
/// 开放平台-分销单同步失败订单记录类型
/// </summary>
public enum OpenChannelSyncFailOrderRecordSource
{
    /// <summary>
    /// 渠道同步
    /// </summary>
    [Description("渠道同步")]
    ChannelSync = 1,
    
    /// <summary>
    /// 手工创建订单
    /// </summary>
    [Description("手工单")]
    ManualCreate  = 2,
    
    /// <summary>
    /// 其他
    /// </summary>
    [Description("其他")]
    Other = 3,
}