using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;

public enum InsurePurchaseStatus
{
    /// <summary>
    /// 初始化
    /// </summary>
    Init = 0,

    /// <summary>
    /// 已支付未出保险
    /// </summary>
    Uninsured = 1,

    /// <summary>
    /// 已退款
    /// </summary>
    Refunded = 2,

    /// <summary>
    /// 已出保险
    /// </summary>
    Insured = 3,

    /// <summary>
    /// 暂存订单
    /// </summary>
    Temporary = 5,

    /// <summary>
    /// 出单中
    /// </summary>
    Issuing = 10,

    /// <summary>
    /// 已删除
    /// </summary>
    Deleted = 11,
}