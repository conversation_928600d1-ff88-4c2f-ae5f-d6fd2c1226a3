using System.ComponentModel;

namespace Contracts.Common.Order.Enums;

public enum OpenChannelSyncFailOrderRecordType
{
    /// <summary>
    /// 同步失败
    /// </summary>
    [Description("同步失败")] 
    SyncFail = 1,

    /// <summary>
    /// 创建成功
    /// </summary>
    [Description("创建成功")] 
    CreatedSuccessfully = 2,

    /// <summary>
    /// 作废
    /// </summary>
    [Description("作废")] 
    Invalid = 3,
    
    /// <summary>
    /// 恢复
    /// </summary>
    [Description("恢复")] 
    Restored = 4,
    
    /// <summary>
    /// 变更订单类型
    /// </summary>
    [Description("变更订单类型")] 
    ChangeOrderType = 5,
}