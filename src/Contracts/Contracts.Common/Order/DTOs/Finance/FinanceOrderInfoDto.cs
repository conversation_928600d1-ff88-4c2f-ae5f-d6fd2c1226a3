using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Finance;

/// <summary>
/// 
/// </summary>
/// <param name="BaseCurrencyCode">基础币种</param>
/// <param name="CurrencyCode">币种</param>
/// <param name="ExchangeRate">汇率</param>
public record OrderPriceInfoDto(string BaseCurrencyCode, string CurrencyCode, decimal ExchangeRate);

public class OrderInfoDto
{
    public long BaseOrderId { get; set; }
    public DateTime? TravelDate { get; set; }
    public DateTime? FinishDate { get; set; }
    public OrderPriceInfoDto OrderPriceInfo { get; set; }
}

public class OffsetOrderInfoDto
{
    public long OffsetOrderId { get; set; }
    public OrderPriceInfoDto OrderPriceInfo { get; set; }
}

public class RefundOrderInfoDto
{
    public long RefundOrderId { get; set; }
    public OrderPriceInfoDto OrderPriceInfo { get; set; }
}

public class ReservationOrderInfoDto
{
    public long ReservationOrderId { get; set; }

    public DateTime? TravelDateBegin { get; set; }

    public DateTime? TravelDateEnd { get; set; }

    public OrderPriceInfoDto OrderPriceInfo { get; set; }
}

public class ReceiptSettlementOrderInfoDto
{
    public long ReceiptSettlementOrderId { get; set; }

    /// <summary>
    /// 实收金额
    /// </summary>
    public decimal? ReceivedAmount { get; set; }

    /// <summary>
    /// 实收总额币种
    /// </summary>
    public string? ReceivedAmountCurrencyCode { get; set; }
}
