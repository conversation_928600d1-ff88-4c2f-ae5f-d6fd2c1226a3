using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.ScenicTicketPurchaseOrder;

public class SearchTicketPurchaseInvCheckRecordInput : PagingInput
{
    /// <summary>
    /// 操作用户id
    /// </summary>
    public long? OperationUserId { get; set; }
    
    /// <summary>
    /// 盘点结果
    /// </summary>
    public InventoryCheckResultType? InventoryCheckResult { get; set; }
    
    /// <summary>
    /// 盘点处理结果错误类型
    /// </summary>
    public TaskResultType? ResultType { get; set; }
}