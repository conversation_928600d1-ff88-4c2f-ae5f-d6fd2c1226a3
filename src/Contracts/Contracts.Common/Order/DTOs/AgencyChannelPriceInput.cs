using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs;
public class AgencyChannelPriceInput
{
    /// <summary>
    /// 分销商价格分组id
    /// </summary>
    public long PriceGroupId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public ChannelProductType ChannelProductType { get; set; }

    /// <summary>
    /// 产品id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 规格id
    /// </summary>
    public long SkuId { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 采购价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 售价
    /// </summary>
    public decimal SalePrice { get; set; }

    /// <summary>
    /// 支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    public int Quantity { get; set; }

}
