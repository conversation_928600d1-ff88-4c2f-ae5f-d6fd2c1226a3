using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.GroupBookingFinancialHandleOrder;
public class FinancialHandleOrderSearchInput : PagingInput
{
    /// <summary>
    /// 审核单id
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// 团房单id
    /// </summary>
    public long? GroupBookingOrderId { get; set; }

    /// <summary>
    /// 团房支付单id
    /// </summary>
    public long? GroupBookingOrderPaymentId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public GroupBookingFinancialHandleOrderStatus? Status { get; set; }
}
