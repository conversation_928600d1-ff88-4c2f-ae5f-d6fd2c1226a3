using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.BaseOrder;
public class SetOrderOpUserIdInput
{
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public long? UserId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public OrderOperationType OrderOperationType { get; set; }

    public OperationUserDto OperationUser { get; set; }
}
