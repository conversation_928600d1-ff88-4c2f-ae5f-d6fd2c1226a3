using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.GroupBooking;
public class PreOrderInput
{
    /// <summary>
    /// 申请单id
    /// </summary>
    public long ApplicationFormId { get; set; }
    public OperationUserDto OperationUser { get; set; }

    /// <summary>
    /// 预订单信息
    /// </summary>
    public IEnumerable<PreOrderDto> PreOrders { get; set; }
}

public class PreOrderDto
{
    /// <summary>
    /// 可选项，编辑有值必传
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// 报价单id
    /// </summary>
    public long QuotationId { get; set; }

    /// <summary>
    /// 取消政策
    /// </summary>
    public string CancellationPolicy { get; set; }

    /// <summary>
    /// 特别备注
    /// </summary>
    public string SpecialRemarks { get; set; } = string.Empty;
    
    /// <summary>
    /// 首笔款金额
    /// </summary>
    public decimal InitialPaymentAmount { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime ValidityPeriod { get; set; }
    
    /// <summary>
    /// 尾款支付时间
    /// </summary>
    public DateTime FinalPaymentTime { get; set; }

    /// <summary>
    /// 添加入住人提醒 入住日前几天
    /// </summary>
    public int? RemindAdvanceDays { get; set; }

    /// <summary>
    /// 预订单报价信息
    /// </summary>
    public IEnumerable<PreOrderItemDto> PreOrderItems { get; set; }

}