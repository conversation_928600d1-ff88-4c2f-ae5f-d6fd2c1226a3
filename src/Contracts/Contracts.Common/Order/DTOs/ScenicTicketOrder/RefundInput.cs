namespace Contracts.Common.Order.DTOs.ScenicTicketOrder
{
    public class RefundInput
    {
        /// <summary>
        /// 主单id
        /// </summary>
        public long BaseOrderId { get; set; }

        /// <summary>
        /// 发起退款原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 凭据图片url数组
        /// </summary>
        public string[] ProofImgs { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal? RefundAmount { get; set; }

        /// <summary>
        /// 是否补差单退款
        /// </summary>
        public bool IsCompensationOrder { get; set; }
    }
}
