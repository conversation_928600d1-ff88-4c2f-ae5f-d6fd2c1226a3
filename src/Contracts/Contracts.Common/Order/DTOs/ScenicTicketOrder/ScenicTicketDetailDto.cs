using Contracts.Common.Order.Enums;
using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Order.DTOs.ScenicTicketOrder;
public class ScenicTicketDetailDto
{
    /// <summary>
    /// 租户Id
    /// </summary>
    public long TenantId { get; set; }

    /// <summary>
    /// 景区Id
    /// </summary>
    public long ScenicSpotId { get; set; }

    /// <summary>
    /// 门票Id
    /// </summary>
    public long TicketId { get; set; }

    /// <summary>
    /// 门票名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 英文门票名称
    /// </summary>
    public string? EnName { get; set; }

    /// <summary>
    /// 门票类型
    /// </summary>
    public ScenicTicketsType TicketsType { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public string FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string FeeNotNote { get; set; }

    /// <summary>
    /// 预定须知
    /// </summary>
    public string OtherNote { get; set; }

    /// <summary>
    /// 是否需要换票
    /// </summary>
    public bool NeedToExchange { get; set; }

    /// <summary>
    /// 换票地点
    /// </summary>
    public string ExchangeLocation { get; set; }

    /// <summary>
    /// 换票凭证
    /// </summary>
    public List<ExchangeProofType> ExchangeProofs { get; set; }

    /// <summary>
    /// 换票说明
    /// </summary>
    public string ExchangeNote { get; set; }

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 需提前几天退款
    /// </summary>
    public int RefundDaysInAdvance { get; set; }

    /// <summary>
    /// 提前退款时分
    /// </summary>
    public TimeSpan RefundTimeInAdvance { get; set; }

    /// <summary>
    /// 退款比例1-100
    /// </summary>
    public int RefundRate { get; set; }

    /// <summary>
    /// 是否过期自动退款
    /// </summary>
    public bool AutoRefundAfterExpiration { get; set; }

    /// <summary>
    /// 过期自动退款比例1-100
    /// </summary>
    public int AutoRefundRate { get; set; }

    /// <summary>
    /// 供货商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? SupplierOrderId { get; set; }

    #region 期票

    /// <summary>
    /// 有效期类型
    /// </summary>
    public ProductValidityType ValidityType { get; set; }

    /// <summary>
    /// 有效期 - 起
    /// </summary>
    public DateTime? ValidityBegin { get; set; }

    /// <summary>
    /// 有效期 - 止
    /// </summary>
    public DateTime? ValidityEnd { get; set; }

    /// <summary>
    /// 购买之后x天有效
    /// </summary>
    public int AfterPurchaseDays { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 成本价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 售价
    /// </summary>
    public decimal SellingPrice { get; set; }

    /// <summary>
    /// 划线价
    /// </summary>
    public decimal LinePrice { get; set; }

    /// <summary>
    /// 飞猪渠道价
    /// </summary>
    public decimal FeiZhuChannelPrice { get; set; }

    /// <summary>
    /// 凭证来源（期票时必填）
    /// </summary>
    public CredentialSourceType? CredentialSourceType { get; set; }

    #endregion

    #region 提前购买

    /// <summary>
    /// 需提前购买
    /// </summary>
    public bool NeedToBuyInAdvance { get; set; }

    /// <summary>
    /// 提前购买天数
    /// </summary>
    public int BuyDaysInAdvance { get; set; }

    /// <summary>
    /// 提前购买时分
    /// </summary>
    public TimeSpan BuyTimeInAdvance { get; set; }

    #endregion

    /// <summary>
    /// 上下架状态
    /// </summary>
    public bool Enabled { get; set; }

    #region API对接配置参数
    /// <summary>
    /// 活动ID
    /// </summary>
    public string? ActivityId { get; set; }

    /// <summary>
    /// 套餐ID
    /// </summary>
    public string? PackageId { get; set; }

    /// <summary>
    /// SKUID
    /// </summary>
    public string? SkuId { get; set; }
    #endregion

    /// <summary>
    /// 价格库存来源
    /// </summary>
    public PriceInventorySource? PriceInventorySource { get; set; }

    /// <summary>
    /// 门票价库类型
    /// </summary>
    public PriceInventoryType? PriceInventoryType { get; set; }

    /// <summary>
    /// 时段名称
    /// <value>场景 : 通过门票id和时段id查询</value>
    /// </summary>
    public TimeSpan? TimeSlot { get; set; }

    /// <summary>
    /// 时段id
    /// <value>场景 : 通过门票id和时段id查询</value>
    /// </summary>
    public long? TimeSlotId { get; set; }

    /// <summary>
    /// 产品人id，产品开发者
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 平台运营人
    /// </summary>
    public List<ProductOperatorUserDto> ProductOperatorUser { get; set; } = new();

    /// <summary>
    /// 游客信息类型
    /// </summary>
    public TouristInfoType TouristInfoType { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>如15表示为15%的折扣</remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
    
    /// <summary>
    /// 是否支持订单补差调整
    /// <value>[手工发货] - 支持订单补差,不需要做价格库存处理</value>
    /// </summary>
    public bool IsCompensation { get; set; }
    
    /// <summary>
    /// 是否渠道时效产品
    /// </summary>
    public bool IsChannelTimeliness { get; set; }

    /// <summary>
    /// 时效渠道配置列表
    /// </summary>
    public List<TimelinessChannelSettingInfo> TimelinessChannelSettingInfos { get; set; } = new();
}
