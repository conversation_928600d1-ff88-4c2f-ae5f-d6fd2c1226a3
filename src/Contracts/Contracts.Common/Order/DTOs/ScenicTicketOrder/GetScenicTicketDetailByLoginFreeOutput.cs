namespace Contracts.Common.Order.DTOs.ScenicTicketOrder;

public class GetScenicTicketDetailByLoginFreeOutput
{
    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 子订单id
    /// </summary>
    public long SubOrderId { get; set; }

    /// <summary>
    /// 景区资源名称
    /// </summary>
    public string ResourceName { get; set; }

    /// <summary>
    /// 门票产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 购买数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 有效期 - 起 预订票: 预订日期；期票：使用开始日期
    /// </summary>
    public DateTime ValidityBegin { get; set; }

    /// <summary>
    /// 有效期 - 止 预订票: 预订日期；期票：使用截止日期
    /// </summary>
    public DateTime ValidityEnd { get; set; }

    /// <summary>
    /// 券码
    /// </summary>
    public List<ScenicTicketOrderCodeInfo> TicketCodeInfos { get; set; }
}