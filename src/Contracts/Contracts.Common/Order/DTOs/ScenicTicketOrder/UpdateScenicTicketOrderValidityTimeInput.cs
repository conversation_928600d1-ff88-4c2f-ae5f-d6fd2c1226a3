namespace Contracts.Common.Order.DTOs.ScenicTicketOrder;

public class UpdateScenicTicketOrderValidityTimeInput
{
    /// <summary>
    /// 主订单Id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 子订单Id
    /// </summary>
    public long ScenicTicketOrderId { get; set; }

    /// <summary>
    /// 有效期 - 止 预订票: 预订日期；期票：使用截止日期
    /// </summary>
    public DateTime ValidityEnd { get; set; }
}