namespace Contracts.Common.Order.DTOs.MailOrder
{
    public class IsMailProductValidity
    {
        public long ProductId { get; set; }
        public long ProductSkuId { get; set; }
        /// <summary>
        /// 标题
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 是否支持退款
        /// </summary>
        public bool IsSupportRefund { get; set; }
        /// <summary>
        /// 邮费模板Id
        /// </summary>
        public long PostageTemplateId { get; set; }
        /// <summary>
        /// 邮费模板名称
        /// </summary>
        public string PostageTemplateName { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public long SupplierId { get; set; }
        /// <summary>
        /// Sku名称
        /// </summary>
        public string ProductSkuName { get; set; }
        /// <summary>
        /// 图片路径
        /// </summary>
        public string ImagePath { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
        public bool IsValidity { get; set; } = true;
        public string Message { get; set; }
    }
}
