using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.AggregateOrder;

public class SearchAggregateOrderBaseInput
{
    /// <summary>
    /// 排序字段 ordermark - 标记，sellingplatform - 平台，createtime - 下单时间，confirmtime - 确认时间，traveldate - 入住时间，finishdate-完成时间，pricesourcetype - 价格源
    /// </summary>
    public string? OrderByFiled { get; set; }

    /// <summary>
    /// 排序，asc - 正序, desc - 反序
    /// </summary>
    public string? OrderBy { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    public List<SearchAggregateOrderType> AggregateOrderTypeList { get; set; } = new();

    /// <summary>
    /// 订单状态
    /// </summary>
    public List<SearchAggregateOrderStatus> AggregateOrderStatusList { get; set; } = new();

    /// <summary>
    /// 退款状态
    /// </summary>
    public List<AggregateOrderRefundStatus> AggregateOrderRefundStatusList { get; set; } = new();

    /// <summary>
    /// 订单分类
    /// </summary>
    public List<OrderCategory> OrderCategoryList { get; set; } = new();

    /// <summary>
    /// 售卖平台
    /// </summary>
    public List<SellingPlatform> SellingPlatformList { get; set; } = new();

    /// <summary>
    /// 订单号
    /// </summary>
    public List<long> BaseOrderIdArr { get; set; } = new();

    /// <summary>
    /// 商品ID
    /// </summary>
    public long? ProductId { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string? EnProductName { get; set; }

    /// <summary>
    /// 价格源
    /// </summary>
    public List<SearchPriceSourceType> PriceSourceTypeList { get; set; } = new();

    /// <summary>
    /// 下单时间-start
    /// </summary>
    public DateTime? CreateTimeStart { get; set; }

    /// <summary>
    /// 下单时间
    /// </summary>
    public DateTime? CreateTimeEnd { get; set; }

    /// <summary>
    /// 确认时间-start
    /// </summary>
    public DateTime? ConfirmTimeStart { get; set; }

    /// <summary>
    /// 确认时间-end
    /// </summary>
    public DateTime? ConfirmTimeEnd { get; set; }

    /// <summary>
    /// 入住/出行日期-start
    /// </summary>
    public DateTime? TravelDateStart { get; set; }

    /// <summary>
    /// 入住/出行日期-end
    /// </summary>
    public DateTime? TravelDateEnd { get; set; }

    /// <summary>
    /// 离店/完成日期-start
    /// </summary>
    public DateTime? FinishDateStart { get; set; }

    /// <summary>
    /// 离店/完成日期-end
    /// </summary>
    public DateTime? FinishDateEnd { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public List<long> AgencyIds { get; set; } = new();

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public List<string> ChannelOrderNoArr { get; set; } = new();

    /// <summary>
    /// 创建人-下单用户昵称/手机
    /// </summary>
    public string? UserKeywork { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public List<long> SupplierIds { get; set; } = new();

    /// <summary>
    /// 采购单号
    /// </summary>
    public List<string> SupplierOrderIdArr { get; set; } = new();

    /// <summary>
    /// 确认号
    /// </summary>
    public string? ConfirmCode { get; set; }

    /// <summary>
    /// 团号 可选项
    /// </summary>
    public string GroupNo { get; set; }

    /// <summary>
    /// 产品人id
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 跟单人id
    /// </summary>
    public long? TrackingUserId { get; set; }

    /// <summary>
    /// 出行人
    /// </summary>
    public string? TravelerName { get; set; }

    /// <summary>
    /// 券码
    /// </summary>
    public string? TicketCode { get; set; }

    /// <summary>
    /// 线路 - 保险状态
    /// </summary>
    public InsureOrderStatus? Status { get; set; }

    /// <summary>
    /// 订单已过期
    /// </summary>
    public bool? OrderExpired { get; set; }

    /// <summary>
    /// 连续单号
    /// </summary>
    public string? SeriesNumber { get; set; }

    /// <summary>
    /// 标记
    /// </summary>
    public string? OrderMark { get; set; }

    /// <summary>
    /// vcc支付状态
    /// </summary>
    public OrderVirtualCreditCardPaymentStatus? VccPaymentStatus { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int? CountryCode { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public int? CityCode { get; set; }

    /// <summary>
    /// 订单处理等级标签
    /// </summary>
    public AggregateOrderProcessingLevelTag? ProcessingLevelTag { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    public PayType? PaymentType { get; set; }

    /// <summary>
    /// 是否延时支付 true-查询延时支付订单 null/false-查询非延时支付订单
    /// </summary>
    public bool? IsDelayedOrder { get; set; }

    /// <summary>
    /// 运营助理
    /// </summary>
    public long? OperatorAssistantUserId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? OrderUserName { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? OrderUserPhone { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string? OrderUserEmial { get; set; }
}
