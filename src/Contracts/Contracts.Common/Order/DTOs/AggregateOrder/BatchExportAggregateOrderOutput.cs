using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.Enums;
using MessagePack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.AggregateOrder;

[MessagePackObject]
public class BatchExportAggregateOrderOutput
{
    /// <summary>
    /// 主单id
    /// </summary>
    [Key(0)]
    public string BaseOrderId { get; set; }

    /// <summary>
    /// 标记
    /// </summary>
    [Key(1)]
    public string OrderMark { get; set; }

    [Key(2)]
    /// <summary>
    /// 部门名称
    /// </summary>
    public string? TenantDepartmentName { get; set; }

    [Key(3)]
    /// <summary>
    /// 售卖平台
    /// </summary>
    public string SellingPlatform { get; set; }

    [Key(4)]
    /// <summary>
    /// 价格源
    /// </summary>
    public string? PriceSourceType { get; set; }

    /// <summary>
    /// 产品id/房型id
    /// </summary>
    [Key(5)]
    public string ProductId { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    [Key(6)]
    public string? CountryName { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    [Key(7)]
    public string? CityCodeName { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    [Key(8)]
    public string OrderType { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    [Key(9)]
    public string? AggregateOrderType { get; set; }

    /// <summary>
    /// 规格名称/报价策略名称
    /// </summary>
    [Key(10)]
    public string ProductSkuName { get; set; }


    /// <summary>
    /// 订单支付币种
    /// </summary>
    [Key(11)]
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    [Key(12)]
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 订单总额 = DiscountAmount + Payment.Amount
    /// </summary>
    [Key(13)]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    [Key(14)]
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 支付金额-实收人民币 PaymentAmount/ExchangeRate
    /// </summary>
    [Key(15)]
    public decimal PaymentAmountTenantCurrency { get; set; }

    /// <summary>
    /// 取消总额
    /// </summary>
    [Key(16)]
    public decimal? CurrencyCodeRefundTotalAmount { get; set; }

    /// <summary>
    /// 取消总额 - 人民币
    /// </summary>
    [Key(17)]
    public decimal? RefundTotalAmount { get; set; }

    /// <summary>
    /// 采购币种
    /// </summary>
    [Key(18)]
    public string CostCurrencyCode { get; set; }
    
    /// <summary>
    /// 采购总折扣金额
    /// </summary>
    [Key(19)]
    public decimal CostDiscountAmount { get; set; }

    /// <summary>
    /// 采购总额
    /// </summary>
    [Key(20)]
    public decimal TotalCost { get; set; }

    /// <summary>
    /// 采购总额-人民币 TotalCost*CostExchangeRate
    /// </summary>
    [Key(21)]
    public decimal TotalCostTenantCurrency { get; set; }

    /// <summary>
    /// 下单时间
    /// </summary>
    [Key(22)]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    [Key(23)]
    public DateTime? ConfirmTime { get; set; }

    /// <summary>
    /// 离店/完成日期
    /// </summary>
    [Key(24)]
    public DateTime? FinishDate { get; set; }

    /// <summary>
    /// 入住/出行日期
    /// </summary>
    [Key(25)]
    public string? TravelDate { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    [Key(26)]
    public string OrderStatus { get; set; }

    /// <summary>
    /// 退款状态
    /// </summary>
    [Key(27)]
    public string RefundStatus { get; set; }

    /// <summary>
    /// 下单用户昵称-创建人
    /// </summary>
    [Key(28)]
    public string UserNickName { get; set; }

    /// <summary>
    /// 产品人
    /// </summary>
    [Key(29)]
    public string? DevelopUserName { get; set; }

    /// <summary>
    /// 运营人
    /// </summary>
    [Key(30)]
    public string? OperatorUserName { get; set; }

    /// <summary>
    /// 跟单人
    /// </summary>
    [Key(31)]
    public string? TrackingUserName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    [Key(32)]
    public string ContactsName { get; set; } = string.Empty;

    /// <summary>
    /// 出行人数
    /// </summary>
    [Key(33)]
    public string? TravelerCount { get; set; }

    /// <summary>
    /// 出行人数名称
    /// </summary>
    [Key(34)]
    public string? TravelerNames { get; set; }

    /// <summary>
    /// 最新备注
    /// </summary>
    [Key(35)]
    public string Remark { get; set; }

    /// <summary>
    /// 酒店取消政策      
    /// </summary>
    [Key(36)]
    public string HotelOrderCancelRule { get; set; }

    /// <summary>
    /// 分销商
    /// </summary>
    [Key(37)]
    public string AgencyName { get; set; }

    /// <summary>
    /// 分销商-简称
    /// </summary>
    [Key(38)]
    public string AgencyShortName { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    [Key(39)]
    public string ChannelOrderNo { get; set; } = string.Empty;

    /// <summary>
    /// 销售BD
    /// </summary>
    [Key(40)]
    public string SalespersonName { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    [Key(41)]
    public string SupplierName { get; set; }

    /// <summary>
    /// 结算周期
    /// </summary>
    [Key(42)]
    public string? SettlementPeriod { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    [Key(43)]
    public string SupplierOrderId { get; set; }

    /// <summary>
    /// 份数 间数
    /// </summary>
    [Key(44)]
    public int Quantity { get; set; }

    /// <summary>
    /// 票券
    /// </summary>
    [Key(45)]
    public string? SkuValidityDate { get; set; }

    /// <summary>
    /// 接送车 - 上车点
    /// </summary>
    [Key(46)]
    public string DeparturePointName { get; set; } = string.Empty;

    /// <summary>
    /// 接送车 - 下车点
    /// </summary>
    [Key(47)]
    public string DestinationPointName { get; set; } = string.Empty;

    /// <summary>
    /// 团号 可选项
    /// </summary>
    [Key(48)]
    public string GroupNo { get; set; }

    /// <summary>
    /// 票券 - 券码
    /// </summary>
    [Key(49)]
    public string TicketCodes { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    [Key(50)]
    public string ConfirmCode { get; set; }

    /// <summary>
    /// 线路 - 保险状态
    /// </summary>
    [Key(51)]
    public string? InsureOrderStatus { get; set; }

    /// <summary>
    /// 订单已过期
    /// </summary>
    [Key(52)]
    public string OrderExpired { get; set; }

    /// <summary>
    /// 入账抵充金额
    /// </summary>
    [Key(53)]
    public decimal ReceiptOffsetReceivedAmount { get; set; }

    /// <summary>
    /// 入账抵充金额货币
    /// </summary>
    [Key(54)]
    public string ReceiptOffsetReceivedAmountCurrencyCode { get; set; }

    /// <summary>
    /// 入账抵充金额（人民币）
    /// </summary>
    [Key(55)]
    public decimal ReceiptOffsetReceivedAmountTarget { get; set; }

    /// <summary>
    /// 出账抵充金额
    /// </summary>
    [Key(56)]
    public decimal SettlementOffsetTotalAmount { get; set; }

    /// <summary>
    /// 出账抵充金额（原币）
    /// </summary>
    [Key(57)]
    public decimal SettlementOffsetPaymentAmount { get; set; }

    /// <summary>
    /// 出账抵充金额货币（原币）
    /// </summary>
    [Key(58)]
    public string SettlementOffsetPaymentAmountCurrencyCode { get; set; }

    /// <summary>
    /// 供应端采购折扣比例[0%-100%]
    /// </summary>
    [Key(59)]
    public string CostDiscountRate { get; set; }

    /// <summary>
    /// 保险金额
    /// </summary>
    [Key(60)]
    public decimal? InsurePurchaseAmount { get; set; }

    /// <summary>
    /// 保险金额币种
    /// </summary>
    [Key(61)]
    public string InsurePurchaseAmountCurrencyCode { get; set; }

    /// <summary>
    /// 订单关闭时间
    /// </summary>
    [Key(62)]
    public DateTime? ClosedDate { get; set; }

    /// <summary>
    /// 运营助理
    /// </summary>
    [Key(63)]
    public string? OperatorAssistantUserName { get; set; }

    /// <summary>
    /// 产品名称/房型名称
    /// </summary>
    [Key(64)]
    public string ProductName { get; set; }
    
    /// <summary>
    /// 补差关联订单id数据
    /// <value>逗号分隔</value>
    /// </summary>
    [Key(65)]
    public string CompensationRelatedOrderIds { get; set; }

    /// <summary>
    /// 是否补差订单
    /// </summary>
    [Key(66)]
    public string IsCompensationOrder { get; set; }

    /// <summary>
    /// 是否会员
    /// </summary>
    [Key(67)]
    public string IsVip { get; set; }

    /// <summary>
    /// 等级
    /// </summary>
    [Key(68)]
    public string Level { get; set; }

    /// <summary>
    /// 所属行业
    /// </summary>
    [Key(69)]
    public string? IndustryType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Key(70)]
    public string AgencyRecencyStatus { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    [Key(71)]
    public string PaymentType { get; set; }
}
