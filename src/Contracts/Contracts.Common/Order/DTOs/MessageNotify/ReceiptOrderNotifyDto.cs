using Contracts.Common.Notify.Messages;

namespace Contracts.Common.Order.DTOs.MessageNotify;

public class ReceiptOrderNotifyDto
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销名称
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 分销商额度绑定邮箱
    /// </summary>
    public string Addressee { get; set; }

    /// <summary>
    /// 抄送人邮箱
    /// </summary>
    public IEnumerable<string> CcAddress { get; set; }

    /// <summary>
    /// 账单周期 - 起
    /// </summary>
    public DateTime BillingCycleBegin { get; set; }

    /// <summary>
    /// 账单周期 - 止
    /// </summary>
    public DateTime BillingCycleEnd { get; set; }

    /// <summary>
    /// 附件数据
    /// </summary>
    public List<AttachmentFilePath> AttachmentFilePaths { get; set; } = new();

    /// <summary>
    /// 合计金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 应收总额币种
    /// </summary>
    public string TotalAmountCurrencyCode { get; set; }

    /// <summary>
    /// 付款日期
    /// </summary>
    public string PaymentDate { get; set; }
}