using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.TicketCodeUsed;
public class TicketCodeDto
{
    public long Id { get; set; }

    public OrderType OrderType { get; set; }

    /// <summary>
    /// 主单Id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 票券订单Id
    /// </summary>
    public long SubOrderId { get; set; }

    public long Code { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public TicketCodeStatus Status { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
