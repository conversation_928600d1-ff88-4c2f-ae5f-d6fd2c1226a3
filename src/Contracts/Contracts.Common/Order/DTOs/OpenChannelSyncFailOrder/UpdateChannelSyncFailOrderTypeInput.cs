using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.OpenChannelSyncFailOrder;

public class UpdateChannelSyncFailOrderTypeInput
{
    /// <summary>
    /// 失败订单id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 失败订单类型
    /// </summary>
    public OrderType OrderType { get; set; }
    
    /// <summary>
    /// 操作人id
    /// </summary>
    public long? OperatorId { get; set; }

    /// <summary>
    /// 操作人名称
    /// </summary>
    public string? Operator { get; set; }
}