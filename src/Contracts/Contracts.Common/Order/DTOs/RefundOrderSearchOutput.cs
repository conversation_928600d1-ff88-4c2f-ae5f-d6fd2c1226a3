using Contracts.Common.Order.Enums;
using Newtonsoft.Json;

namespace Contracts.Common.Order.DTOs
{
    public class RefundOrderSearchOutput
    {
        /// <summary>
        /// 退款单id
        /// </summary>
        public long RefundOrderId { get; set; }

        public string ResourceName { get; set; }

        public string ProductName { get; set; }

        public string ProductSkuName { get; set; }

        /// <summary>
        /// 主单号
        /// </summary>
        public long BaseOrderId { get; set; }

        /// <summary>
        /// 子单号
        /// </summary>
        public long SubOrdeId { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public RefundOrderType OrderType { get; set; }

        /// <summary>
        /// 退款(产品)数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 总退款金额(含运费)
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; }

        /// <summary>
        /// 谁发起退款
        /// </summary>
        public UserType UserType { get; set; }

        /// <summary>
        /// 退款方用户id
        /// </summary>
        public long? UserId { get; set; }

        /// <summary>
        /// 退款方
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public RefundOrderStatus Status { get; set; }

        /// <summary>
        /// 运费金额
        /// </summary>
        public decimal PostageAmount { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? ReviewTime { get; set; }

        /// <summary>
        /// 发起退款原因
        /// </summary>
        public string Reason { get; set; }

        [JsonIgnore]
        public string ProofImgsStr { get; set; }

        /// <summary>
        /// 退款凭证
        /// </summary>
        public string[] ProofImgs => !string.IsNullOrWhiteSpace(ProofImgsStr)
            ? ProofImgsStr.Split(',') : Array.Empty<string>();

        /// <summary>
        /// 审核拒绝原因
        /// </summary>
        public string RejectedReason { get; set; }

        /// <summary>
        /// 退款失败原因
        /// </summary>
        public string FailedReason { get; set; }

        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    public class RefundOrderStatusCount
    {
        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 退款状态
        /// </summary>
        public RefundOrderStatus Status { get; set; }
    }
}
