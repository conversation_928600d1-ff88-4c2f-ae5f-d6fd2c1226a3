using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.WorkOrder;
public class ReplyInput
{
    public long WorkOrderId { get; set; }
    /// <summary>
    /// 沟通记录类型 1-沟通记录 2-内部备注
    /// </summary>
    public WorkOrderReplyType WorkOrderReplyType { get; set; }
    /// <summary>
    /// 回复用户类型 1-分销商用户 2-HOP用户
    /// </summary>
    public ReplyUserType ReplyUserType { get; set; }
    public long UserId { get; set; }
    public string UserName { get; set; }
    public string Content { get; set; }
    /// <summary>
    /// Hop回复记录
    /// </summary>
    public string? HopReplyNumber { get; set; }
    /// <summary>
    /// 是否未解决 默认不传，true-未解决
    /// </summary>
    public bool? UnSolved { get; set; }

    /// <summary>
    /// 图片链接
    /// </summary>
    public string[] ImagePaths { get; set; } = Array.Empty<string>();
}
