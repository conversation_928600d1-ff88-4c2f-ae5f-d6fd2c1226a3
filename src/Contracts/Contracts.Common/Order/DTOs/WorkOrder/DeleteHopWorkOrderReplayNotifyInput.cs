using Contracts.Common.Order.DTOs.HotelApiOrder;

namespace Contracts.Common.Order.DTOs.WorkOrder;
public class DeleteHopWorkOrderReplayNotifyInput
{
    public Head Head { get; set; }

    public DeleteHopWorkOrderReplayNotifyData Data { get; set; }
}

public class DeleteHopWorkOrderReplayNotifyData
{
    public string OrderId { get; set; }

    public string OrderWorkId { get; set; }

    public string OrderWorkFlowId { get; set; }
}
