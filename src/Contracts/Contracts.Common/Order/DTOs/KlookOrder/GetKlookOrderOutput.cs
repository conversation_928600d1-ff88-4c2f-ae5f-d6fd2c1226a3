using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.KlookOrder;

public class GetKlookOrderOutput
{
    /// <summary>
    /// 主订单Id
    /// </summary>
    public long BaseOrderId { get; set; }
    
    /// <summary>
    /// 子订单Id
    /// </summary>
    public long SubOrderId { get; set; }

    /// <summary>
    /// 第三方客路订单Id
    /// </summary>
    public string? KlookOrderId { get; set; }
    
    /// <summary>
    /// 客路活动Id
    /// </summary>
    public long KlookActiveId { get; set; }

    /// <summary>
    /// 客路套餐Id
    /// </summary>
    public long KlookPackageId { get; set; }

    /// <summary>
    /// 客路SkuId
    /// </summary>
    public long KlookSkuId { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Count { get; set; }
    
    /// <summary>
    /// 供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 客路订单状态
    /// </summary>
    public ScenicTicketSupplierOrderStatus OrderStatus { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }
}