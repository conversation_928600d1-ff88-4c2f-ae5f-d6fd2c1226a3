namespace Contracts.Common.Order.DTOs.KlookOrder;

public class KlookOrderDeliveryInput
{
    /// <summary>
    /// 客路订单号
    /// </summary>
    public string KlookOrderId { get; set; }

    /// <summary>
    /// saas订单号
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string Msg { get; set; }
    
    /// <summary>
    /// 错误码
    /// </summary>
    public int? ErrorCode { get; set; }

    /// <summary>
    /// pdf凭证
    /// </summary>
    public List<KlookOrderPdfVocherItem> Vouchers { get; set; } = new();
}

public class KlookOrderPdfVocherItem
{
    /// <summary>
    /// oss文件路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 缩略图路径
    /// </summary>
    public string? ThumbnailPath { get; set; }
    
    /// <summary>
    /// 凭证来源路径
    /// </summary>
    public string? SourcePath { get; set; }
    
    /// <summary>
    /// 凭证来源PDF路径
    /// </summary>
    public string PdfSourcePath { get; set; }
    
    /// <summary>
    /// 凭证来源图片路径
    /// </summary>
    public string ImageSourcePath { get; set; }
}

