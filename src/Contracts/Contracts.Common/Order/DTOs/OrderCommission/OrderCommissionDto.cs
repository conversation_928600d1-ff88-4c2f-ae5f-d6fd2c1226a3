using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.OrderCommission;
public class OrderCommissionDto
{
    /// <summary>
    /// 分销的佣金（入账）
    /// </summary>
    public decimal? AgencyCommissionFee { get; set; }

    /// <summary>
    /// 分销的佣金 百分比
    /// </summary>
    public decimal? AgencyCommissionRate { get; set; }

    /// <summary>
    /// 佣金状态
    /// </summary>
    public CommisionStatus AgencyCommisionStatus { get; set; }

    /// <summary>
    /// 供应商给的佣金（入账）
    /// </summary>
    public decimal? SupplierCommissionFee { get; set; }

    /// <summary>
    /// 供应商给的佣金 百分比
    /// </summary>
    public decimal? SupplierCommissionRate { get; set; }

    /// <summary>
    /// 佣金状态
    /// </summary>
    public CommisionStatus SupplierCommisionStatus { get; set; }
}
