using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Order.DTOs.SettlementOrder
{

    public class PayablesHotelOrderInfo : PayablesOrderInfo
    {
        /// <summary>
        /// 入住日期
        /// </summary>
        public DateTime CheckInDate { get; set; }

        /// <summary>
        /// 离店日期
        /// </summary>
        public DateTime CheckOutDate { get; set; }

        /// <summary>
        /// 酒店名称
        /// </summary>
        public string HotelName { get; set; }

        /// <summary>
        /// 确认号
        /// </summary>
        public string ConfirmCode { get; set; }

        /// <summary>
        /// 晚数
        /// </summary>
        public int NightsCount { get; set; }

        public HotelOrderStatus Status { get; set; }
        
                
        /// <summary>
        /// 售卖渠道单号
        /// </summary>
        public string? ChannelOrderNo { get; set; }

        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate
            => Status == HotelOrderStatus.Finished || Status == HotelOrderStatus.Refunded ? UpdateTime : null;
    }

    public class PayablesMailOrderInfo : PayablesOrderInfo
    {
        /// <summary>
        /// 快递单号
        /// </summary>
        public string TrackingNumber { get; set; }

        /// <summary>
        /// 物流公司
        /// </summary>
        public string LogisticsCompanyName { get; set; }

        public MailOrderStatus Status { get; set; }

        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate
            => Status == MailOrderStatus.Finished || Status == MailOrderStatus.Refunded ? UpdateTime : null;
    }

    public class PayablesTicketOrderInfo : PayablesOrderInfo
    {
        /// <summary>
        /// 券类产品二级分类
        /// </summary>
        public TicketBusinessType TicketBusinessType { get; set; }

        public BaseOrderStatus Status { get; set; }

        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate
            => Status == BaseOrderStatus.Finished || Status == BaseOrderStatus.Closed ? UpdateTime : null;
    }

    public class PayablesReservationOrderInfo : PayablesOrderInfo
    {
        /// <summary>
        /// 确认号
        /// </summary>
        public string ConfirmCode { get; set; }

        public long TicketOrderId { get; set; }

        /// <summary>
        /// 券类产品二级分类
        /// </summary>
        public TicketBusinessType TicketBusinessType { get; set; }

        public ReservationStatus Status { get; set; }

        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate
            => Status == ReservationStatus.Finished ? UpdateTime : null;
    }

    public class PayablesRefundOrderInfo : PayablesOrderInfo
    {
        /// <summary>
        /// 退款单关联的业务类型
        /// </summary>
        public RefundOrderType OrderType { get; set; }

        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate => UpdateTime;

    }

    public class PayablesScenicTicketOrderInfo : PayablesOrderInfo
    {

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactsName { get; set; }

        /// <summary>
        /// 资源名称
        /// </summary>
        public string ResourceName { get; set; }

        public BaseOrderStatus Status { get; set; }
        
        /// <summary>
        /// 售卖渠道单号
        /// </summary>
        public string? ChannelOrderNo { get; set; }
        
        /// <summary>
        /// 留言
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate
            => Status == BaseOrderStatus.Finished || Status == BaseOrderStatus.Closed ? UpdateTime : null;
    }

    public class PayablesLineOrderInfo : PayablesOrderInfo
    {

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactsName { get; set; }

        public BaseOrderStatus Status { get; set; }
        
                
        /// <summary>
        /// 售卖渠道单号
        /// </summary>
        public string? ChannelOrderNo { get; set; }
        
        
        /// <summary>
        /// 留言
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 预订日期
        /// </summary>
        public DateTime TravelBeginDate { get; set; }
        
        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate
            => Status == BaseOrderStatus.Finished || Status == BaseOrderStatus.Closed ? UpdateTime : null;
    }

    public class PayablesCarProductOrderInfo : PayablesOrderInfo
    {

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactsName { get; set; }

        public BaseOrderStatus Status { get; set; }


        /// <summary>
        /// 售卖渠道单号
        /// </summary>
        public string? ChannelOrderNo { get; set; }


        /// <summary>
        /// 留言
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 预订日期
        /// </summary>
        public DateTime TravelDate { get; set; }

        /// <summary>
        /// 完结日期
        /// </summary>
        public DateTime? FinishDate
            => Status == BaseOrderStatus.Finished || Status == BaseOrderStatus.Closed ? UpdateTime : null;
    }

    public class PayableOffsetOrderInfo : PayablesOrderInfo
    {
        /// <summary>
        /// 业务类型
        /// </summary>
        public OffsetOrderBusinessType BusinessType { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatorName { get; set; }

        /// <summary>
        /// 更新人名称
        /// </summary>
        public string? UpdaterName { get; set; }
    }
    
    public class PayablesOrderInfo
    {
        public long OrderId { get; set; }

        public long BaseOrderId { get; set; }

        public long SupplierId { get; set; }

        public string BuyerName { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品Sku名称
        /// </summary>
        public string ProductSkuName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 采购价
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// 结算金额  采购价*数量
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 下单日期
        /// </summary>
        public DateTime CreateOrderDate { get; set; }

        public DateTime? UpdateTime { get; set; }
        
        /// <summary>
        /// 成本币种 =>供应商币种
        /// </summary>
        public string CostCurrencyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }
        
        /// <summary>
        /// 采购单号
        /// </summary>
        public string? SupplierOrderId { get; set; }

        /// <summary>
        /// 部门Id
        /// </summary>
        public long? TenantDepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string? TenantDepartmentName { get; set; }
    }
}
