using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Order.DTOs.SettlementOrder
{
    public class PaymentInput
    {
        public long SettlementOrderId { get; set; }

        /// <summary>
        /// 转账类型
        /// </summary>
        public SettlementTransferType TransferType { get; set; }

        /// <summary>
        /// 银行编号
        /// </summary>
        public string BankCode { get; set; }

        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankName { get; set; }

        /// <summary>
        /// 分行名称
        /// </summary>
        public string? BranchName { get; set; }

        /// <summary>
        /// 开户账号
        /// </summary>
        public string BankAccount { get; set; }

        /// <summary>
        /// 开户名称
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// 转账凭证图片
        /// </summary>
        public string ProofImg { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 实付金额
        /// </summary>
        public decimal? PaymentAmount { get; set; }

        /// <summary>
        /// 实付金额币种
        /// </summary>
        public string PaymentAmountCurrencyCode { get; set; }

        #region 境外账户配置
        /// <summary>
        /// 供应商账户类型 1-国内 2-国外 3-第三方银行账户
        /// </summary>
        public TenantBankAccountType BankAccountType { get; set; }

        /// <summary>
        /// 境外账户货币代码
        /// </summary>
        public string? AbroadAccountCurrencyCode { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string? AbroadAccountAddress { get; set; }

        public string? SwiftCode { get; set; }
        #endregion
    }
}
