using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.SettlementOrder;

public class PayablesOrderExportInput
{
    /// <summary>
    /// 供应商
    /// </summary>
    public IEnumerable<long> SupplierIds { get; set; } = Enumerable.Empty<long>();

    #region 合并账期 - 过滤

    /// <summary>
    /// 账单周期 - 起
    /// </summary>
    public DateTime? BillingCycleBegin { get; set; }

    /// <summary>
    /// 账单周期 - 止
    /// </summary>
    public DateTime? BillingCycleEnd{ get; set; }

    /// <summary>
    /// 酒店订单日期类型
    /// </summary>
    public PayablesDateType HotelOrderDateType { get; set; }

    /// <summary>
    /// 邮寄订单日期类型
    /// </summary>
    public PayablesDateType MailOrderDateType { get; set; }

    /// <summary>
    /// 券类订单日期类型
    /// </summary>
    public PayablesDateType TicketOrderDateType { get; set; }

    /// <summary>
    /// 预约单日期类型
    /// </summary>
    public PayablesDateType ReservationOrderDateType { get; set; }

    /// <summary>
    /// 景点门票订单日期类型
    /// </summary>
    public PayablesDateType ScenicTicketOrderDateType { get; set; }

    /// <summary>
    /// 线路订单日期类型
    /// </summary>
    public PayablesDateType LineOrderDateType { get; set; }

    /// <summary>
    /// 用车订单日期类型
    /// </summary>
    public PayablesDateType CarProductOrderDateType { get; set; }

    #endregion

    #region 合并业务搜索生成账单

    /// <summary>
    /// 合并业务搜索生成账单
    /// </summary>
    public List<PreCreateBusinessSearch> BusinessSearches { get; set; } = new();

    #endregion
}