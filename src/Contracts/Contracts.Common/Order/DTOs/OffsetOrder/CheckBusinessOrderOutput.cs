using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Order.DTOs.OffsetOrder;

public class CheckBusinessOrderOutput
{
    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    public string AgencyName { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long TenantId { get; set; }
    
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 产品SkuId
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// 产品Sku名称
    /// </summary>
    public string ProductSkuName { get; set; }
    
    /// <summary>
    /// 抵冲金额币种
    /// </summary>
    public string CurrencyCode { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    public PayType PayType { get; set; }
}