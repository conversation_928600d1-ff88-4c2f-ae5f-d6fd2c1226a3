using Contracts.Common.Order.Enums;
using Newtonsoft.Json;

namespace Contracts.Common.Order.DTOs;

public class OrderMultPriceOutput : MultPriceOutput
{
    /// <summary>
    /// 主单id
    /// </summary>
    [JsonIgnore]
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 各个子订单id
    /// </summary>
    public long SubOrderId { get; set; }

    /// <summary>
    /// 订单类型 
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 子类型（线路：成人、儿童、房差）
    /// </summary>
    public int OrderSubType { get; set; }
    
    /// <summary>
    /// 子类型关联项id
    /// <value>
    /// <para>线路-套餐票种id</para>
    /// </value>
    /// </summary>
    public long? OrderSubItemId { get; set; }

    /// <summary>
    /// 子类型关联项名称
    /// <value>
    /// <para>线路-套餐票种名称</para>
    /// </value>
    /// </summary>
    public string? OrderSubItemName { get; set; }

    /// <summary>
    /// 原价币种转支付币种汇率 =>商户币种:支付币种=1:rate
    /// </summary>
    public decimal CurrentExchangeRate { get; set; }
}
