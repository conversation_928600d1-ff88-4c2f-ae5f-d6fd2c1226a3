using Contracts.Common.User.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Insure;

/// <summary>
/// 投保人信息
/// </summary>
public class InsurePolicyHolderInfo
{
    /// <summary>
    /// 中文名
    /// </summary>
    public string CNName { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    public string ENName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public UserGender Gender { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public UserIdCardType Type { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IDCard { get; set; }

    /// <summary>
    /// 生日 yyyy-MM-dd
    /// </summary>
    public DateTime Birthday { get; set; }
}
