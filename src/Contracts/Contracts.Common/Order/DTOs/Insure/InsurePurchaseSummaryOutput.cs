using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Insure;
public class InsurePurchaseSummaryOutput
{
    /// <summary>
    /// 有一个购买失败 都当成购买失败
    /// </summary>
    public bool IsBuySuccess { get; set; } = true;

    public int DataIdsCount { get; set; }

    /// <summary>
    /// 每次购买结果
    /// </summary>
    public List<InsurePurchaseSummaryDetailOutput> Details { get; set; } = new();
}

public class InsurePurchaseSummaryDetailOutput
{ 
    public long? DataId { get; set; }

    public bool IsBuySuccess { get; set; }

}
