using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Insure;
public class InsurePurchaseRecordInfo
{
    /// <summary>
    /// 保险产品订单id
    /// </summary>
    public long InsureProductId { get; set; }

    /// <summary>
    /// 成本归属订单
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 保单号
    /// </summary>
    public string PolicyNo { get; set; }

    /// <summary>
    /// 投保日期
    /// </summary>
    public DateTime InsureTime { get; set; }

    /// <summary>
    /// 产品引用Id
    /// </summary>
    public string ProductId { get; set; }

    /// <summary>
    /// 保险产品
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string SupplierName { get; set; }

    /// <summary>
    /// 保障计划名称
    /// </summary>
    public string PlanName { get; set; }

    /// <summary>
    /// 保险计划
    /// </summary>
    public string PlanFullName { get; set; }

    /// <summary>
    /// 保障计划代码
    /// </summary>
    public string PlanCode { get; set; }

    /// <summary>
    /// 起保日
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 终保日
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 采购单价(CNY)
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 采购总价(CNY)
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 被保人数
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 货币
    /// </summary>
    public string CurrencyCode { get; set; } = Currency.CNY.ToString();
}
