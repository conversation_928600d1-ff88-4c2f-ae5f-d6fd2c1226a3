using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Insure;

public class SupplierInsureProductOutput
{
    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 产品引用Id
    /// </summary>
    public string ProductId { get; set; }

    /// <summary>
    /// 产品唯一id
    /// </summary>
    public string ProductUnionId { get; set; }

    /// <summary>
    /// 产品名字
    /// </summary>
    public string ProductName { get; set; }

    public List<SupplierInsureProductPlanOutput> Plans { get; set; }
}

public class SupplierInsureProductPlanOutput
{
    /// <summary>
    /// 保障计划名称
    /// </summary>
    public string PlanName { get; set; }

    /// <summary>
    /// 保障计划代码
    /// </summary>
    public string PlanCode { get; set; }

    /// <summary>
    /// 保障天数对象
    /// </summary>
    public List<SupplierInsureProductDetailOutput> Details { get; set; }
}

/// <summary>
/// 保存
/// </summary>
public class SupplierInsureProductDetailOutput
{
    /// <summary>
    /// 货币
    /// </summary>
    public string CurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }
    /// <summary>
    /// 保障开始天数
    /// </summary>
    public int StartDay { get; set; }

    /// <summary>
    /// 保障结束天数
    /// </summary>
    public int EndDay { get; set; }

    /// <summary>
    /// 年龄限制（暂时只有一个 ，设计多个）
    /// </summary>
    public List<SupplierInsureProductAgeRange> AgeRanges { get; set; }
}

/// <summary>
/// 年龄限制
/// </summary>
public class SupplierInsureProductAgeRange
{
    /// <summary>
    /// 保障开始年龄
    /// </summary>
    public int StartAge { get; set; }

    /// <summary>
    /// 保障结束年龄
    /// </summary>
    public int EndAge { get; set; }
}
