using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Insure;
public class InsurePurchaseRecordDetailOutput
{
    /// <summary>
    /// 保险采购单id
    /// </summary>
    public long InsurePurchaseRecordId { get; set; }
    /// <summary>
    /// 成本归属订单
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 成本归属订单类型
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 被保人名字
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 采购成本分担金额(CNY)
    /// </summary>
    public decimal Price { get; set; }
}
