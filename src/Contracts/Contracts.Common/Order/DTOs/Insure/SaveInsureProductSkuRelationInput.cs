using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Insure;
public class SaveInsureProductSkuRelationInput
{
    /// <summary>
    /// 产品id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 产品sku id
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// 保险产品id
    /// </summary>
    public long? InsureProductId { get; set; }

    /// <summary>
    /// 类型 1-普通 2-不购买 3-跟随产品保险
    /// </summary>
    public InsureProductSkuRelationType Type { get; set; }
}
