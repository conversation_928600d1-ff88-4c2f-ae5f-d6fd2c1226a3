using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs;

/// <summary>
/// 订单优惠项
/// </summary>
public class OrderDiscountItemDto
{
    /// <summary>
    /// 优惠类型 1-优惠券 2-抹零优惠
    /// </summary>
    public OrderDiscountType DiscountType { get; set; }

    /// <summary>
    /// 本项优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 优惠关联id 比如：用户优惠券id
    /// </summary>
    public long DiscountId { get; set; }

    /// <summary>
    /// 标题 比如：优惠券名称
    /// </summary>
    public string Title { get; set; }
}
