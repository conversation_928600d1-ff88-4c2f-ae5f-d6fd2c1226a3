using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Order.DTOs.TravelLineOrder;

public class QueryTravelLineOrderSkuTypeItemOutput
{
    public List<QueryTravelLineOrderSkuTypeItem> SkuTypeItems { get; set; } = new();
}

public class QueryTravelLineOrderSkuTypeItem
{
    /// <summary>
    /// 主单Id
    /// </summary>
    public long BaseOrderId { get; set; }
    
    /// <summary>
    /// 开放平台供货方类型
    /// </summary>
    public OpenSupplierType? OpenSupplierType { get; set; }

    /// <summary>
    /// 线路订单Id
    /// </summary>
    public long TravelLineOrderId { get; set; }

    /// <summary>
    /// 线路套餐票种项id
    /// </summary>
    public long SkuTypeItemId { get; set; }

    /// <summary>
    /// 线路套餐票种项名称
    /// </summary>
    public string SkuTypeItemName { get; set; }

    /// <summary>
    /// 票种年龄段
    /// </summary>
    public LineSkuPriceType SkuPriceType { get; set; }

    /// <summary>
    /// 活动id
    /// <remarks>供应端 - productid</remarks>
    /// </summary>
    public string? ActivityId { get; set; }

    /// <summary>
    /// 套餐包id
    /// <remarks>供应端 - optionId</remarks>
    /// </summary>
    public string? PackageId { get; set; }

    /// <summary>
    /// SkuId
    /// <remarks>供应端 - SkuId</remarks>
    /// </summary>
    public string? SkuId { get; set; }
}