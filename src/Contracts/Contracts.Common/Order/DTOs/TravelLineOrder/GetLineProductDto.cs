using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Product.DTOs.LineProductTimeSlot;
using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Order.DTOs.TravelLineOrder;

public class GetLineProductDto : LineProductInfo
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int Days { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int Nights { get; set; }
    
    /// <summary>
    /// 售前电话
    /// </summary>
    public string PreSalePhone { get; set; }

    /// <summary>
    /// 售后电话
    /// </summary>
    public string AfterSalePhone { get; set; }

    /// <summary>
    /// 是否在售卖时间内
    /// </summary>
    public bool InSellingDate { get; set; }

    /// <summary>
    /// 销量
    /// </summary>
    public int? Sales { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }
}

public class LineProductInfo
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 英文标题
    /// </summary>
    public string EnTitle { get; set; }

    /// <summary>
    /// 卖点描述
    /// </summary>
    public string SellPointDescribe { get; set; }

    /// <summary>
    /// 出发地城市Id
    /// </summary>
    public int DepartureCityId { get; set; }

    /// <summary>
    /// 出发地城市名称
    /// </summary>
    public string DepartureCityName { get; set; }

    /// <summary>
    /// 目的地城市Id
    /// </summary>
    public int DestinationCityId { get; set; }

    /// <summary>
    /// 目的地城市名称
    /// </summary>
    public string DestinationCityName { get; set; }

    /// <summary>
    /// 成人标准说明
    /// </summary>
    public string AdultsStandard { get; set; }

    /// <summary>
    /// 儿童标准说明
    /// </summary>
    public string ChildrenStandard { get; set; }

    /// <summary>
    /// 婴儿标准说明
    /// </summary>
    public string? BabyStandard { get; set; }

    
    /// <summary>
    /// 长者标准说明
    /// </summary>
    public string? ElderlyStandard { get; set; }

    /// <summary>
    /// 预订须知
    /// </summary>
    public string? OtherNote { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public string? FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string? FeeNotNote { get; set; }

    /// <summary>
    /// 图文内容
    /// </summary>
    public string Content { get; set; }

    #region 预订规则

    /// <summary>
    /// 提前几天预订
    /// </summary>

    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 提前预订时间点
    /// </summary>

    public TimeSpan ReservationTimeInAdvance { get; set; }

    #endregion

    #region 退款规则

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    #endregion

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ProductPictures { get; set; } = new();

    /// <summary>
    /// 视频
    /// </summary>
    public List<string> ProductVideos { get; set; }

    /// <summary>
    /// 海报
    /// </summary>
    public List<string> ProductPosters { get; set; }

    /// <summary>
    /// 上车点
    /// </summary>
    public List<RallyPointItem> RallyPoints { get; set; }

    /// <summary>
    /// 产品分组
    /// </summary>
    public List<long> GroupIds { get; set; }

    /// <summary>
    /// 售前客服
    /// </summary>
    public long PreSaleStaff { get; set; }

    /// <summary>
    /// 售后客服
    /// </summary>
    public long AfterSaleStaff { get; set; }

    /// <summary>
    /// 产品人id
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 平台运营人
    /// </summary>
    public List<ProductOperatorUserDto> ProductOperatorUser { get; set; } = new();

    /// <summary>
    /// 是否系统自动确认  true 是系统自动确认
    /// </summary>
    public bool? AutoConfirm { get; set; }
    
    /// <summary>
    /// 采购来源类型
    /// </summary>
    public LineProductPurchaseSourceType PurchaseSourceType { get; set; }
    
    /// <summary>
    /// 价库类型
    /// </summary>
    public PriceInventoryType PriceInventoryType { get; set; }

    /// <summary>
    /// 价格库存来源
    /// </summary>
    public PriceInventorySource PriceInventorySource { get; set; }
    
    /// <summary>
    /// 分时段,场次库存展示
    /// </summary>
    public bool IsOpenTimeSlotInventory { get; set; }
    
    /// <summary>
    /// 供应端采购折扣比例[0-100] 2位小数
    /// <remarks>如15表示为15%的折扣</remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }

    /// <summary>
    /// 供应商配置信息
    /// </summary>
    public LineProductOpenSupplierSettingInfo OpenSupplierSettingInfo { get; set; }
    
    /// <summary>
    /// 渠道配置信息
    /// </summary>
    public List<LineProductOpenChannelSettingInfo> OpenChannelSettingInfos { get; set; } = new();

    /// <summary>
    /// 时段场次信息
    /// </summary>
    public List<LineProductTimeSlotInfo> TimeSlotInfos { get; set; } = new();
    
    /// <summary>
    /// 是否支持订单补差调整
    /// <value>[手工发货] - 支持订单补差</value>
    /// </summary>
    public bool IsCompensation { get; set; }
    
    /// <summary>
    /// 是否渠道时效产品
    /// </summary>
    public bool IsChannelTimeliness { get; set; }

    /// <summary>
    /// 时效渠道配置列表
    /// </summary>
    public List<TimelinessChannelSettingInfo> TimelinessChannelSettingInfos { get; set; } = new();
}

public class RallyPointItem
{
    public long Id { get; set; }

    public TimeSpan Time { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double? Latitude { get; set; }
    
    /// <summary>
    /// 坐标系默认BD09
    /// </summary>
    public CoordinateType CoordinateType { get; set; } = CoordinateType.BD09;
}
