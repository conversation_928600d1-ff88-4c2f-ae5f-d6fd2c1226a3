namespace Contracts.Common.Order.DTOs.TravelLineOrder;

public class TravelLineOrderSendConfirmedMessageInput
{
    /// <summary>
    /// 主单Id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 凭证信息
    /// </summary>
    public List<TravelLineOrderSendConfirmedMessageVouchers> Vouchers { get; set; } = new();
}

public class TravelLineOrderSendConfirmedMessageVouchers
{
    /// <summary>
    /// oss文件路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 缩略图路径
    /// </summary>
    public string? ThumbnailPath { get; set; }
}