using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;

namespace Contracts.Common.Order.DTOs.TravelLineOrder;

public class GetLineChannelOrderInfoOutput
{
    /// <summary>
    /// 渠道单号
    /// </summary>
    public string ChannelOrderNo { get; set; }
    
    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }
    
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactsEmail { get; set; }
    
    public List<OrderTravelerOutput> OrderTravelers { get; set; } = new();

    public List<TravelLineOrderPriceOutput> OrderPrices { get; set; } = new();

    /// <summary>
    /// 订单字段信息
    /// </summary>
    public List<OrderFieldInformationTypeOutput> OrderFields { get; set; } = new();
    /// <summary>
    /// 订单附加信息
    /// <value>渠道数据匹配</value>
    /// </summary>
    public List<AddOpenSupplierOrderExtraInfoItem> OrderExtraInfos { get; set; } = new();
}