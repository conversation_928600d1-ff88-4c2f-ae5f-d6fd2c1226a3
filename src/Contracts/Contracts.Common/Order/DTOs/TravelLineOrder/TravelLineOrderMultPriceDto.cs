using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Order.DTOs.TravelLineOrder;

public class TravelLineOrderMultPriceDto
{
    /// <summary>
    /// 线路价格类型
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 线路套餐票种id
    /// </summary>
    public long? LineSkuTypeItemId { get; set; }

    public int Quantity { get; set; }

    public OrderMultPriceDto OrderMultPrice { get; set; }
}