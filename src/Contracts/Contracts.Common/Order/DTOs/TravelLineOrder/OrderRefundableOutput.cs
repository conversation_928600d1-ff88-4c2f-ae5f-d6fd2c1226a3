using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;
using Newtonsoft.Json;

namespace Contracts.Common.Order.DTOs.TravelLineOrder;

public class OrderRefundableOutput
{
    /// <summary>
    /// 预订日期
    /// </summary>
    public DateTime TravelBeginDate { get; set; }

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 订单支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 可退最大金额
    /// </summary>
    public decimal RefundableAmount { get; set; }

    public IList<OrderPriceOutput> OrderPrices { get; set; }

    [JsonIgnore]
    public UserType OperatorUserType { get; set; }
    /// <summary>
    /// 是否可以退款
    /// </summary>
    public bool Refundable
    {
        get
        {
            if (OperatorUserType == UserType.Merchant)
                return true;

            var refundable = IsSupportRefund;
            if (RefundBeforeTravelDateDay.HasValue)
            {
                //最后可退时间
                var lastRefundTime = TravelBeginDate.AddDays(-RefundBeforeTravelDateDay.Value);
                if (RefundTravelDateTime.HasValue)
                {
                    lastRefundTime = lastRefundTime.Add(RefundTravelDateTime.Value);
                }
                if (lastRefundTime < DateTime.Now)
                    refundable = false;//已过可退款时间 不支持退款
            }
            return refundable;
        }
    }
    
    /// <summary>
    /// 订单类目
    /// </summary>
    public OrderCategory OrderCategory { get; set; }
}

public class OrderPriceOutput
{
    /// <summary>
    /// 1-成人 2-儿童 3-房差价
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }
}