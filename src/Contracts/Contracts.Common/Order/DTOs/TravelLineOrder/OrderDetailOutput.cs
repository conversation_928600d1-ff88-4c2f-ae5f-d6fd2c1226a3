using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.User.Enums;

namespace Contracts.Common.Order.DTOs.TravelLineOrder;

public class OrderDetailOutput
{
    public BaseOrderDetailOutput BaseOrder { get; set; }

    public TravelLineOrderDetailOutput TravelLineOrder { get; set; }

    public IList<OrderTravelerOutput> OrderTravelers { get; set; }

    public IList<TravelLineOrderPriceOutput> OrderPrices { get; set; }

    /// <summary>
    /// 订单发货结果
    /// </summary>
    public DeliveryResult DeliveryResult { get; set; }

    /// <summary>
    /// 分销渠道信息
    /// </summary>
    public DistributionChannelInfo DistributionChannelInfo { get; set; }

    /// <summary>
    /// 供货渠道信息
    /// </summary>
    public SupplyChannelInfo SupplyChannelInfo { get; set; }

    /// <summary>
    /// 订单字段信息
    /// </summary>
    public List<OrderFieldInformationTypeOutput> OrderFields { get; set; } = new();

    /// <summary>
    /// PDF信息
    /// </summary>
    public List<TravelLineOrderVoucherDeliveryInfo> PurchaseVoucherDeliveryInfos { get; set; } = new();

    /// <summary>
    ///  渠道单号可编辑状态
    /// </summary>
    public List<EditableStatusOfChannelOrderNo> EditableStatusOfChannelOrderNos { get; set; } = new();
}

public class BaseOrderDetailOutput
{

    public long Id { get; set; }

    /// <summary>
    /// 下单用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 下单用户昵称
    /// </summary>
    public string UserNickName { get; set; }

    /// <summary>
    /// 会员等级id
    /// </summary>
    public long VipLevelId { get; set; }

    /// <summary>
    /// 会员等级
    /// </summary>
    public string VipLevelName { get; set; }


    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactsEmail { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 规格名称
    /// </summary>
    public string ProductSkuName { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BaseOrderStatus Status { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 售卖渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public string[] ChannelOrderNo { get; set; }

    /// <summary>
    /// 订单总额 = DiscountAmount + Payment.Amount
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    public PayType PaymentType { get; set; }

    /// <summary>
    /// 留言
    /// </summary>
    public string Message { get; set; }

    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 产品人id
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 跟单人id
    /// </summary>
    public long? TrackingUserId { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public string? SalespersonName { get; set; }

    /// <summary>
    /// 销售BD手机号
    /// </summary>
    public string? SalespersonPhoneNumber { get; set; }

    /// <summary>
    /// 产品人姓名
    /// </summary>
    public string DevelopUserName { get; set; }

    /// <summary>
    /// 产品人手机号码
    /// </summary>
    public string DevelopUserPhoneNumber { get; set; }

    /// <summary>
    /// 运营人姓名
    /// </summary>
    public string OperatorUserName { get; set; }

    /// <summary>
    /// 运营人手机号码
    /// </summary>
    public string OperatorUserPhoneNumber { get; set; }

    /// <summary>
    /// 跟单人姓名
    /// </summary>
    public string TrackingUserName { get; set; }

    /// <summary>
    /// 跟单人手机号码
    /// </summary>
    public string TrackingUserPhoneNumber { get; set; }

    /// <summary>
    /// 创建人手机号码
    /// </summary>
    public string UserPhoneNumber { get; set; }

    /// <summary>
    /// 是否已存在付款结算单
    /// </summary>
    public bool ExistSettlementOrder { get; set; }

    /// <summary>
    /// vcc支付状态
    /// </summary>
    public OrderVirtualCreditCardPaymentStatus VccPaymentStatus { get; set; }

    /// <summary>
    /// 核销 - 付款结算单是否已付款
    /// </summary>
    public bool SettlementPayed { get; set; } = false;

    /// <summary>
    /// 佣金 门票 线路 用车 指定渠道 存在佣金
    /// </summary>
    public decimal? CommissionFee { get; set; }

    /// <summary>
    /// 运营助理
    /// </summary>
    public long? OperatorAssistantUserId { get; set; }

    /// <summary>
    /// 资源名称-英文
    /// </summary>
    public string? EnResourceName { get; set; }

    /// <summary>
    /// 产品名称-英文
    /// </summary>
    public string? EnProductName { get; set; }

    /// <summary>
    /// 规格名称-英文
    /// </summary>
    public string? EnProductSkuName { get; set; }
    
    /// <summary>
    /// 订单分类
    /// </summary>
    public List<OrderCategory> OrderCategoryList { get; set; }
}

public class TravelLineOrderDetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 入住房间数
    /// </summary>
    public int NumberOfRooms { get; set; }

    /// <summary>
    /// 预订日期
    /// </summary>
    public DateTime TravelBeginDate { get; set; }

    /// <summary>
    /// 预订结束日期
    /// </summary>
    public DateTime TravelEndDate { get; set; }

    /// <summary>
    /// 认领人Id
    /// </summary>
    public long? ClaimantId { get; set; }

    /// <summary>
    /// 认领人名称
    /// </summary>
    public string ClaimantName { get; set; }

    /// <summary>
    /// 集结点时间
    /// </summary>
    public TimeSpan? RallyPointTime { get; set; }

    /// <summary>
    /// 集结点地址
    /// </summary>
    public string? RallyPointAddress { get; set; }

    /// <summary>
    /// 车牌号
    /// </summary>
    public string TourGuideCarNumber { get; set; }

    /// <summary>
    /// 导游名称
    /// </summary>
    public string TourGuideName { get; set; }

    /// <summary>
    /// 导游电话
    /// </summary>
    public string TourGuidePhoneNumber { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public TravelLineOrderStatus Status { get; set; }

    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 线路产品SkuId
    /// </summary>
    public long LineProductSkuId { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int Days { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int Nights { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? SupplierOrderId { get; set; }

    public DateTime CreateTime { get; set; }

    #region 产品信息

    /// <summary>
    /// 图片路径
    /// </summary>
    public string ImagePath { get; set; }

    /// <summary>
    /// 使用说明
    /// </summary>
    public string Instructions { get; set; }

    /// <summary>
    /// 温馨提示
    /// </summary>
    public string KindReminder { get; set; }

    /// <summary>
    /// 费用包含备注
    /// </summary>
    public string FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string FeeNotNote { get; set; }

    /// <summary>
    /// 预订须知
    /// </summary>
    public string? OtherNote { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public List<FeeInclude> FeeIncludes { get; set; }

    /// <summary>
    /// 成人标准说明
    /// </summary>
    public string? AdultsStandard { get; set; }

    /// <summary>
    /// 儿童标准说明
    /// </summary>
    public string? ChildrenStandard { get; set; }

    /// <summary>
    /// 婴儿标准说明
    /// </summary>
    public string? BabyStandard { get; set; }


    /// <summary>
    /// 长者标准说明
    /// </summary>
    public string? ElderlyStandard { get; set; }

    #endregion

    #region 退款规则

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    #endregion

    /// <summary>
    /// 经度
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// 坐标系默认BD09
    /// </summary>
    public CoordinateType CoordinateType { get; set; } = CoordinateType.BD09;

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? FinishTime { get; set; }
    
    /// <summary>
    /// 是否系统自动确认
    /// </summary>
    public bool? AutoConfirm { get; set; }
    
    /// <summary>
    /// 采购来源类型
    /// </summary>
    public LineProductPurchaseSourceType PurchaseSourceType { get; set; }

    #region API配置参数

    /// <summary>
    /// 活动id
    /// <remarks>供应端 - productid</remarks>
    /// </summary>
    public string? SupplierActivityId { get; set; }

    /// <summary>
    /// 套餐包id
    /// <remarks>供应端 - optionId</remarks>
    /// </summary>
    public string? SupplierPackageId { get; set; }

    /// <summary>
    /// skuid
    /// <remarks>供应端 - skuid</remarks>
    /// </summary>
    public string? SupplierSkuId { get; set; }
    
    /// <summary>
    /// 开放平台供货方类型
    /// </summary>
    public OpenSupplierType? OpenSupplierType { get; set; }

    /// <summary>
    /// 时段场次Id
    /// </summary>
    public long? TimeSlotId { get; set; }
    
    /// <summary>
    /// 时段场次名称
    /// </summary>
    public string? TimeSlotName { get; set; }

    #endregion
}

public class OrderTravelerOutput
{
    /// <summary>
    /// 
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 1-成人 2-儿童
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    public string IDCard { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public UserIdCardType IdCardType { get; set; }

    /// <summary>
    /// 国际拨号代码
    /// </summary>
    public string? InternationalDialingCode { get; set; }

    public string Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 拼音或英文名
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// 拼音或英文姓
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 身高，单位cm
    /// </summary>
    public decimal? Height { get; set; }

    /// <summary>
    /// 体重，单位kg
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// 国籍名称
    /// </summary>
    public string? NationalityName { get; set; }

    /// <summary>
    /// 国际代码
    /// </summary>
    public string? NationalityCode { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public UserGender Gender { get; set; }

    /// <summary>
    /// 证件有效期，格式：“yyyy-MM-dd”
    /// </summary>
    public DateTime? CardValidDate { get; set; }

    /// <summary>
    /// 鞋码，单位欧码
    /// </summary>
    public decimal? ShoeSize { get; set; }

    /// <summary>
    /// 出行人扩展信息数据(json)
    /// </summary>
    public string? ExtendInfo { get; set; }
}

/// <summary>
/// 线路-发货凭证信息
/// </summary>
public class TravelLineOrderVoucherDeliveryInfo
{
    /// <summary>
    /// 凭证(PDF)文件路径
    /// </summary>
    public string FilePath { get; set; }

    /// <summary>
    /// 凭证(PDF)缩略图(第一页)
    /// </summary>
    public string Thumbnail { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

public class TravelLineOrderPriceOutput : MultPriceOutput
{
    /// <summary>
    /// 1-成人 2-儿童 3-房差价
    /// </summary>
    public LineSkuPriceType Type { get; set; }
    
    /// <summary>
    /// 子类型关联项id
    /// <value>
    /// <para>线路-套餐票种id</para>
    /// </value>
    /// </summary>
    public long? OrderSubItemId { get; set; }

    /// <summary>
    /// 子类型关联项名称
    /// <value>
    /// <para>线路-套餐票种名称</para>
    /// </value>
    /// </summary>
    public string? OrderSubItemName { get; set; }
}

/// <summary>
/// 分销渠道信息
/// </summary>
public class DistributionChannelInfo
{
    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商名称
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 售卖渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public string[] ChannelOrderNo { get; set; }

    /// <summary>
    /// 分销渠道订单状态
    /// </summary>
    public TravelLineOtaOrderStatus? ChannelOrderStatus { get; set; }
    
    /// <summary>
    /// 分销商类型
    /// </summary>
    public AgencyType? AgencyType { get; set; }

    /// <summary>
    /// API类型
    /// </summary>
    public AgencyApiType? AgencyApiType { get; set; }
}

/// <summary>
/// 供货渠道信息
/// </summary>
public class SupplyChannelInfo
{
    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    public string SupplierName { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? SupplierOrderId { get; set; }

    /// <summary>
    /// 供应商类型
    /// </summary>
    public SupplierType? SupplierType { get; set; }
    
    /// <summary>
    /// 供货平台
    /// </summary>
    public SupplierApiType? SupplierApiType { get; set; }

    /// <summary>
    /// 供货端订单状态
    /// </summary>
    public LineProductSupplierOrderStatus? SupplierOrderStatus { get; set; }
}

/// <summary>
/// 订单发货结果
/// </summary>
public class DeliveryResult
{
    /// <summary>
    /// 发货状态
    /// </summary>
    public TravelLineOrderDeliveryStatus DeliverStatus { get; set; }

    /// <summary>
    /// 发货结果错误消息描述
    /// </summary>
    public string DeliveryErrorMsg { get; set; }

    /// <summary>
    /// 发货结果错误码
    /// </summary>
    public int? DeliveryErrorCode { get; set; }
}

/// <summary>
/// 渠道单号可编辑状态
/// </summary>
public class EditableStatusOfChannelOrderNo
{
    public string ChannelOrderNo { get; set; }

    public bool CanEdit { get; set; }
}


