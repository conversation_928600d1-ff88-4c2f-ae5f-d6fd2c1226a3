namespace Contracts.Common.Order.DTOs.CarProductSupplierOrder;

/// <summary>
/// 开放平台 - 用车询价通知
/// </summary>
public class CarProductSupplierOrderQuoteNotifyInput
{
    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }
    
    /// <summary>
    /// 异步通知号
    /// </summary>
    public string NotifyId { get; set; }

    /// <summary>
    /// 搜索id
    /// <para>预定时必传</para>
    /// </summary>
    public string SearchId { get; set; }

    /// <summary>
    /// 结果列表
    /// </summary>
    public List<CarProductSupplierOrderQuoteItem> Results { get; set; }
}

public class CarProductSupplierOrderQuoteItem
{
    /// <summary>
    /// 搜索结果id
    ///<para>用车预定时必传</para>
    /// </summary>
    public string ResultId { get; set; }
    
    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }
    
    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }

    /// <summary>
    /// 车辆信息
    /// </summary>
    public CarProductSupplierOrderQuoteVehicleItem Vehicle { get; set; }
}

public class CarProductSupplierOrderQuoteVehicleItem
{
    /// <summary>
    /// 最大乘客数
    /// </summary>
    public int MaxPassengerCount { get; set; }
    
    /// <summary>
    /// 最大行李数
    /// </summary>
    public int MaxLuggageCount { get; set; }
}