using Contracts.Common.Order.DTOs.OrderLog;
using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs
{
    public class OrderLogsOutput
    {
        /// <summary>
        /// 角色
        /// </summary>
        public UserType OperationRole { get; set; }

        public long UserId { get; set; }
        public string UserName { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public OrderOperationType OperationType { get; set; }

        /// <summary>
        /// 订单日志类型
        /// </summary>
        public OrderLogType OrderLogType { get; set; }

        public string OperationTypeName { get; set; }

        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 延时实付信息 当类型OperationType=37 OrderDelayPaied订单延时已支付时有值
        /// <para>订单日志中记录“延时支付订单以{实付的支付方式}实付{实收金额} {付款币种}”</para>
        /// </summary>
        public OrderDelayPaiedLogOutput? OrderDelayPaiedLog { get; set; }
        
        /// <summary>
        /// 扩展数据
        /// <value>json 数据格式</value>
        /// </summary>
        public OrderLogExtensionDataDto? ExtensionData { get; set; }
    }

    public class OrderDelayPaiedLogOutput
    {
        public decimal PaymentAmount { get; set; }

        public string PaymentCurrencyCode { get; set; }

        public Contracts.Common.Payment.Enums.PayType PaymentType { get; set; }
    }
}
