using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.HotelOrder
{
    public class CreateByAgencyInput
    {
        /// <summary>
        /// 分销商Id
        /// </summary>
        public long AgencyId { get; set; }
        /// <summary>
        /// 分销商名称
        /// </summary>
        public string AgencyName { get; set; }
        /// <summary>
        /// 售卖平台
        /// </summary>
        public SellingPlatform SellingPlatform { get; set; }
        /// <summary>
        /// 渠道
        /// </summary>
        public SellingChannels SellingChannel { get; set; }
        /// <summary>
        /// 渠道单号
        /// </summary>
        public string ChannelOrderNo { get; set; }
        /// <summary>
        /// 酒店Id
        /// </summary>
        public long HotelId { get; set; }
        /// <summary>
        /// 房型Id
        /// </summary>
        public long RoomId { get; set; }
        /// <summary>
        /// 价格策略Id
        /// </summary>
        public long PriceStrategyId { get; set; }
        /// <summary>
        /// 入住时间
        /// </summary>
        public DateTime CheckIn { get; set; }
        /// <summary>
        /// 离店时间
        /// </summary>
        public DateTime CheckOut { get; set; }
        /// <summary>
        /// 房间数
        /// </summary>
        public int RoomCount { get; set; }
        /// <summary>
        /// 入住人数
        /// </summary>
        public int Adults { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactsName { get; set; }
        /// <summary>
        /// 联系人手机号
        /// </summary>
        public string ContactsPhoneNumber { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Message { get; set; }
    }
}
