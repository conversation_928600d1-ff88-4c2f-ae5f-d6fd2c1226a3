
using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.DTOs.GroupBookingOrder;
using Contracts.Common.Order.DTOs.OrderCommission;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Order.DTOs.HotelOrder
{
    public class DetailBase
    {
        /// <summary>
        /// 主单id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactsName { get; set; }

        /// <summary>
        /// 联系人手机号
        /// </summary>
        public string ContactsPhoneNumber { get; set; }

        /// <summary>
        /// 联系人邮箱
        /// </summary>
        public string ContactsEmail { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public BaseOrderStatus Status { get; set; }

        /// <summary>
        /// 订单总额 = DiscountAmount + Payment.Amount
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 优惠金额
        /// </summary>
        public decimal? DiscountAmount { get; set; }

        /// <summary>
        /// 支付金额
        /// </summary>
        public decimal? PaymentAmount { get; set; }

        /// <summary>
        /// 支付币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        public PayType PaymentType { get; set; }

        /// <summary>
        /// 留言
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 落单时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 剩余时间 单位：s
        /// </summary>
        public int LeftTime { get; set; }

    }

    public class DetailOutput : DetailBase
    {
        /// <summary>
        /// 下单用户Id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 下单用户昵称
        /// </summary>
        public string UserNickName { get; set; }

        /// <summary>
        /// 会员等级id
        /// </summary>
        public long VipLevelId { get; set; }

        /// <summary>
        /// 会员等级
        /// </summary>
        public string VipLevelName { get; set; }

        /// <summary>
        /// 分销商id
        /// </summary>
        public long AgencyId { get; set; }

        /// <summary>
        /// 分销商
        /// </summary>
        public string AgencyName { get; set; }

        /// <summary>
        /// 分销商简称
        /// </summary>
        public string? AgencySortName { get; set; }

        /// <summary>
        /// 销售BD
        /// </summary>
        public long? SalespersonId { get; set; }

        /// <summary>
        /// 销售BD
        /// </summary>
        public string? SalespersonName { get; set; }

        /// <summary>
        /// 售卖平台
        /// </summary>
        public SellingPlatform SellingPlatform { get; set; }

        /// <summary>
        /// 售卖渠道
        /// </summary>
        public SellingChannels SellingChannels { get; set; }

        /// <summary>
        /// 售卖渠道单号
        /// </summary>
        public string[] ChannelOrderNo { get; set; }

        /// <summary>
        /// 团号
        /// </summary>
        public string? GroupNo { get; set; }

        /// <summary>
        /// 支付渠道
        /// </summary>
        public string PaymentChannel { get; set; }

        /// <summary>
        /// 支付方式  微信h5、微信小程序、微信扫码等
        /// </summary>
        public string PaymentMode { get; set; }

        /// <summary>
        /// 外部支付单号
        /// </summary>
        public string PaymentExternalNo { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PayTime { get; set; }

        /// <summary>
        /// 延时支付状态 null-非延迟支付订单 0-延时订单未实付 1-延时订单已实付
        /// </summary>
        public bool? DelayedPayStatus { get; set; }

        /// <summary>
        /// 延时支付订单时有值 实付截至时间
        /// </summary>
        public DateTime? DelayedPayDeadline { get; set; }

        /// <summary>
        /// 酒店订单详情
        /// </summary>
        public HotelOrderDetail OrderDetail { get; set; }

        /// <summary>
        /// 日历价格
        /// </summary>
        public IEnumerable<CalendarPrice> CalendarPrices { get; set; }

        /// <summary>
        /// 取消价格
        /// </summary>
        public OrderCancelRule CancelRule { get; set; }

        /// <summary>
        /// 酒店入住人列表
        /// </summary>
        public IEnumerable<HotelRoomGuestsDto> HotelGuests { get; set; }

        /// <summary>
        /// 原价币种转支付币种汇率(同一货币为null)
        /// </summary>
        public decimal? AgencyCurrencyExchangeRate { get; set; }

        /// <summary>
        /// 优惠明细项
        /// </summary>
        public IEnumerable<OrderDiscountItemDto>? DiscountItems { get; set; }

        /// <summary>
        /// 团房-其他费用
        /// </summary>
        public List<GroupBookingOrderAdditionOutput>? OrderAdditions { get; set; }

        /// <summary>
        /// 团房-支付信息
        /// </summary>
        public List<GroupBookingOrderPaymentOutput>? GroupBookingOrderPayments { get; set; }

        /// <summary>
        /// 团房-支付金额调整信息
        /// </summary>
        public List<HotelOrderPaymentAdjustOutput> OrderPaymentAdjusts { get; set; }

        /// <summary>
        /// 团房-关联团房单id
        /// </summary>
        public long? GroupBookingOrderId { get; set; }

        /// <summary>
        /// 产品人id
        /// </summary>
        public long? DevelopUserId { get; set; }

        /// <summary>
        /// 运营人id
        /// </summary>
        public long? OperatorUserId { get; set; }

        /// <summary>
        /// 跟单人id
        /// </summary>
        public long? TrackingUserId { get; set; }

        /// <summary>
        /// 佣金
        /// </summary>
        public decimal? CommissionFee { get; set; }

        /// <summary>
        /// 佣金 百分比
        /// </summary>
        public decimal? CommissionRate { get; set; }

        #region GDS
        /// <summary>
        /// 信用卡
        /// </summary>
        public GDSGuaranteeInfo Guarantee { get; set; }

        /// <summary>
        /// 床型
        /// </summary>
        public List<Contracts.Common.Resource.DTOs.Hotel.BedType> BedType { get; set; }

        /// <summary>
        /// 返佣明细
        /// </summary>
        public OrderCommissionDto OrderCommission { get; set; }

        /// <summary>
        /// 税费、服务费明细
        /// </summary>
        public List<HotelOrderPriceDto> HotelOrderPrices { get; set; }

        /// <summary>
        /// 原币种支付总价
        /// </summary>
        public decimal? OrgAmount { get; set; }

        /// <summary>
        /// 原币种
        /// </summary>
        public string? OrgCurrencyCode { get; set; }

        /// <summary>
        /// 房型描述
        /// </summary>
        public string RoomDescribe { get; set; }

        /// <summary>
        /// 尊享权益
        /// </summary>
        public List<GDSHotelExclusivePrivilegeOutput> ExclusivePrivileges { get; set; } = new List<GDSHotelExclusivePrivilegeOutput>();
        #endregion
    }
    public class PriceItem
    {

        /// <summary>
        /// 支付币种
        /// </summary>
        public string CurrencyCode { get; set; }

        /// <summary>
        /// 价格 实际售卖价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        ///  原币种
        /// </summary>
        public string? OrgCurrencyCode { get; set; }

        /// <summary>
        /// 原价币种转支付币种汇率(同一货币为null)      
        /// </summary>
        public decimal? OrgPrice { get; set; }

    }
    public class HotelOrderBase
    {
        public long Id { get; set; }

        public long BaseOrderId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public HotelOrderStatus Status { get; set; }

        #region 酒店信息

        /// <summary>
        /// 酒店Id
        /// </summary>
        public long HotelId { get; set; }

        /// <summary>
        /// 酒店名称
        /// </summary>
        public string HotelName { get; set; }

        public string? HotelEnName { get; set; }

        public long HotelRoomId { get; set; }

        /// <summary>
        /// 房型名称
        /// </summary>
        public string HotelRoomName { get; set; }

        public string? HotelRoomEnName { get; set; }

        /// <summary>
        /// 房型图片
        /// </summary>
        public string HotelRoomImgPath { get; set; }

        #endregion

        #region 价格策略

        /// <summary>
        /// 价格策略Id
        /// </summary>
        public string PriceStrategyId { get; set; }

        public string PriceStrategyName { get; set; }

        public string? PriceStrategyEnName { get; set; }

        /// <summary>
        /// 价格策略类型
        /// </summary>
        public PriceStrategyType PriceStrategyType { get; set; }

        /// <summary>
        /// 间数
        /// </summary>
        public int PriceStrategyRoomsCount { get; set; }

        /// <summary>
        /// 夜数
        /// </summary>
        public int PriceStrategyNightsCount { get; set; }

        /// <summary>
        /// 含早数
        /// </summary>
        public int PriceStrategyNumberOfBreakfast { get; set; }

        /// <summary>
        /// 价格策略最大入住人数
        /// </summary>
        public int PriceStrategyMaximumOccupancy { get; set; }

        /// <summary>
        /// 餐食类型
        /// </summary>
        public BoardCodeType? BoardCodeType { get; set; }

        /// <summary>
        /// 餐食数量
        /// </summary>
        public int BoardCount { get; set; }

        #endregion

        /// <summary>
        /// 入住日期
        /// </summary>
        public DateTime CheckInDate { get; set; }

        /// <summary>
        /// 离店日期
        /// </summary>
        public DateTime CheckOutDate { get; set; }

        /// <summary>
        /// 确认号
        /// </summary>
        public string ConfirmCode { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? ConfirmTime { get; set; }

        /// <summary>
        /// 酒店地址
        /// </summary>
        public string? HotelAddress { get; set; }
        public string? EnHotelAddress { get; set; }
        /// <summary>
        /// 酒店联系电话
        /// </summary>
        public string? HotelTelePhone { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public int CountryCode { get; set; }

        /// <summary>
        /// 酒店国家
        /// </summary>
        public string? HotelCountryName { get; set; }
        public string? EnHotelCountryName { get; set; }
        /// <summary>
        /// 酒店城市名
        /// </summary>
        public string? HotelCityName { get; set; }
        public string? EnHotelCityName { get; set; }

        /// <summary>
        /// 资源酒店id
        /// </summary>
        public long? ResourceHotelId { get; set; }

        /// <summary>
        /// 价格策略是否直采/主推（汇智酒店）
        /// </summary>
        public bool? IsDirect { get; set; }

        /// <summary>
        /// 敏感度类型 
        /// </summary>
        public SellHotelTag? Tag { get; set; }

        /// <summary>
        /// 是否团房订单
        /// </summary>
        public bool IsGroupBooking { get; set; }

        /// <summary>
        /// 订单总额
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 支付币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; }

        /// <summary>
        /// 入住人国籍信息
        /// </summary>
        public NationalityInfo? NationalityInfo { get; set; }


        /// <summary>
        /// 第三方供应商api平台类型 0-无 1-汇智
        /// </summary>
        public SupplierApiType SupplierApiType { get; set; }

    }
    public class HotelOrderDetail : HotelOrderBase
    {
        /// <summary>
        /// 供应商Id
        /// </summary>
        public long PriceStrategySupplierId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? SupplierName { get; set; }

        /// <summary>
        /// 是否自动确认
        /// </summary>
        public bool PriceStrategyIsAutoConfirm { get; set; }

        /// <summary>
        /// 非立即确认报价预计确认时长，单位：分钟
        /// </summary>
        public int? ConfirmByMins { get; set; }

        /// <summary>
        /// 是否自动确认房态(入住/退房)
        /// </summary>
        public bool HotelIsAutoConfirmRoomStatus { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string SupplierOrderId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public List<string> SupplierOrderIds
        {
            get
            {
                return string.IsNullOrEmpty(SupplierOrderId)
                    ? new List<string>()
                    : SupplierOrderId.Split(',').ToList();
            }
        }
        /// <summary>
        /// 采购状态
        /// </summary>
        public SupplierApiOrderStatus? SupplierApiOrderStatus { get; set; }

        /// <summary>
        /// 是否超时赔付酒店
        /// </summary>
        public bool? IsOvertimeCompensation { get; set; }

        /// <summary>
        /// 是否超卖
        /// </summary>
        public bool? IsOverSaleable { get; set; }
        /// <summary>
        /// 床型 null-表示需要动态取资源床型信息
        /// </summary>
        public List<Contracts.Common.Hotel.DTOs.Hotel.BedType>? BedTypes { get; set; }

        /// <summary>
        /// 是否主推
        /// </summary>
        public bool? StaffTag { get; set; }

        /// <summary>
        /// 税费提醒
        /// </summary>
        public string? TaxDescription { get; set; }

        /// <summary>
        /// 预订礼遇
        /// </summary>
        public string? BookingBenefits { get; set; }

        /// <summary>
        /// 税费说明
        /// </summary>
        public Resource.DTOs.ThirdHotel.CheckAvailabilityArrivalTaxFeeOutput[]? ArrivalTaxFees { get; set; }

        /// <summary>
        /// 酒店名称-前端用
        /// </summary>
        public string ENHotelName
        {
            get
            {
                return this.HotelEnName;
            }
        }

        /// <summary>
        /// 房间英文名 - 前端用多语言切换使用
        /// </summary>
        public string EnHotelRoomName
        {
            get
            {
                return this.HotelRoomEnName;
            }
        }

        /// <summary>
        /// 价格策略英文名-前端用多语言切换使用
        /// </summary>
        public string EnPriceStrategyName
        {
            get
            {
                return this.PriceStrategyEnName;
            }
        }
    }

    public class CalendarPriceBase
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 支付币种 =>用户支付币种，B2B为分销商币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; }

        /// <summary>
        /// 基础售价（渠道加价之后的值）
        /// </summary>
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// 会员价
        /// </summary>
        public decimal? VipPrice { get; set; }

        /// <summary>
        /// 原价币种 =>商户售卖币种
        /// </summary>
        public string OrgPriceCurrencyCode { get; set; }

        /// <summary>
        /// 原价币种价格
        /// </summary>
        public decimal? OrgPrice { get; set; }
    }

    public class CalendarPrice : CalendarPriceBase
    {
        /// <summary>
        /// 成本价/采购价
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// 成本币种 =>供应商币种
        /// </summary>
        public string CostCurrencyCode { get; set; }

        /// <summary>
        /// 原价币种转支付币种汇率
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// 商户原币售价（渠道加价之后的值）
        /// </summary>
        public decimal OrgPrice { get; set; }
    }

    public class OrderCancelRule
    {
        /// <summary>
        /// 取消规则id
        /// </summary>
        public long CancelRuleId { get; set; }

        /// <summary>
        /// 取消策略类型
        /// </summary>
        public CancelRulesType CancelRulesType { get; set; }

        /// <summary>
        /// 入住日前x天
        /// </summary>
        public int BeforeCheckInDays { get; set; }

        /// <summary>
        /// 入住日前x天时间 如14:00
        /// </summary>
        public TimeSpan? BeforeCheckInTime { get; set; }

        /// <summary>
        /// 入住当日 - 时间 如14:00
        /// </summary>
        public TimeSpan CheckInDateTime { get; set; }

        /// <summary>
        /// 取消收费类型
        /// </summary>
        public CancelChargeType CancelChargeType { get; set; }

        /// <summary>
        /// 收费值
        /// </summary>
        public int ChargeValue { get; set; }

        /// <summary>
        /// 等级会员 可退改 退改说明
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 收费价格
        /// </summary>
        public decimal? Amount { get; set; }
    }

    /// <summary>
    /// 客户订单信息
    /// </summary>
    public class DetailByCustomerOutput : DetailBase
    {
        /// <summary>
        /// 下单用户Id
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 下单用户昵称
        /// </summary>
        public string UserNickName { get; set; }

        /// <summary>
        /// 酒店订单详情
        /// </summary>
        public HotelOrderBase OrderDetail { get; set; }

        /// <summary>
        /// 日历价格
        /// </summary>
        public IEnumerable<CalendarPriceBase> CalendarPrices { get; set; }

        /// <summary>
        /// 优惠明细项
        /// </summary>
        public IEnumerable<OrderDiscountItemDto> DiscountItems { get; set; } = Enumerable.Empty<OrderDiscountItemDto>();

        /// <summary>
        /// 取消价格
        /// </summary>
        public OrderCancelRule CancelRule { get; set; }

        /// <summary>
        /// 酒店入住人列表
        /// </summary>
        public IEnumerable<HotelRoomGuestsDto> HotelGuests { get; set; }

        /// <summary>
        /// 税
        /// </summary>
        public PriceItem Tax { get; set; }

        /// <summary>
        /// 信用卡
        /// </summary>
        public GDSGuaranteeInfo Guarantee { get; set; }

        /// <summary>
        /// 床型
        /// </summary>
        public List<Contracts.Common.Resource.DTOs.Hotel.BedType> BedType { get; set; }

        /// <summary>
        /// 房型描述
        /// </summary>
        public string RoomDescribe { get; set; }

        /// <summary>
        /// 尊享权益
        /// </summary>
        public List<GDSHotelExclusivePrivilegeOutput> ExclusivePrivileges { get; set; }

        /// <summary>
        /// 原币种支付总价- 高定酒店使用
        /// </summary>
        public decimal? OrgAmount { get; set; }

        /// <summary>
        /// 原币种
        /// </summary>
        public string? OrgCurrencyCode { get; set; }

        /// <summary>
        /// 房费
        /// </summary>
        public PriceItem RoomPrice { get; set; }
    }


    public class GroupBookingOrderAdditionOutput
    {
        /// <summary>
        /// 团房单id
        /// </summary>
        public long GroupBookingOrderId { get; set; }

        /// <summary>
        /// 关联主单id
        /// </summary>
        public long BaseOrderId { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public long? SupplierId { get; set; }

        /// <summary>
        /// 费用名称
        /// </summary>
        public string AdditionName { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        public string CurrencyCode { get; set; }

        /// <summary>
        /// 采购价
        /// </summary>
        public decimal Cost { get; set; }

        /// <summary>
        /// 采购币种
        /// </summary>
        public string CostCurrencyCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }

    public class GroupBookingOrderPaymentOutput
    {
        /// <summary>
        /// 最晚付款时间
        /// </summary>
        public DateTime LatestPaymentTime { get; set; }

        /// <summary>
        /// 支付比例类型 1-首款 2-尾款
        /// </summary>
        public PaymentRatioType PaymentRatioType { get; set; }

        /// <summary>
        /// 支付比例
        /// </summary>
        public decimal PaymentRatio { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal PaymentAmount { get; set; }

        /// <summary>
        /// 付款币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
        public PayType? PayType { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PaymentTime { get; set; }

        /// <summary>
        /// 支付状态
        /// </summary>
        public PayStatus PayStatus { get; set; }
    }

    public class GroupBaseOrderDownPaymentDto
    {
        public long BaseOrderId { get; set; }
        public decimal PaymentAmount { get; set; }
        public decimal DownPaymentRadio { get; set; }
        public int DownPaymentAmount { get; set; }
    }

    public enum ExportHotelOrderType
    {
        CheckIn = 1,
        Confirm = 2,
    }
}
