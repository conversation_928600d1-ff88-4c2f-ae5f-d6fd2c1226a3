using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.HotelOrder;

public class GetHotelOrderCountInput
{
    public List<long> HotelIds { get; set; } = new();

    /// <summary>
    /// 默认30天前
    /// </summary>
    public DateTime? BeginDate { get; set; }
    
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long? AgencyId { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform[] SellingPlatforms { get; set; } = Array.Empty<SellingPlatform>();
}