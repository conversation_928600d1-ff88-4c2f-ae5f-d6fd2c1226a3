namespace Contracts.Common.Order.DTOs.HotelOrder;

public class HotelOrderPaymentAdjustOutput
{
    public long BaseOrderId { get; set; }

    public long OrderId { get; set; }
    
    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    public string? HotelEnName { get; set; }
    
    /// <summary>
    /// 房型名称
    /// </summary>
    public string HotelRoomName { get; set; }

    public string? HotelRoomEnName { get; set; }
    
    /// <summary>
    /// 价格策略名称
    /// </summary>
    public string PriceStrategyName { get; set; }

    public string? PriceStrategyEnName { get; set; }

    /// <summary>
    /// 关联工单id
    /// </summary>
    public long? WorkOrderId { get; set; }

    /// <summary>
    /// 调整金额 正数表示增加金额，负数表示减少金额
    /// </summary>
    public decimal AdjustAmount { get; set; }

    /// <summary>
    /// 付款币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 操作人
    /// </summary>
    public string? Operator { get; set; }

    /// <summary>
    /// 操作人Id
    /// </summary>
    public long? OperatorId { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}