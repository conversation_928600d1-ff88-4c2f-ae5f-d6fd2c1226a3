using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.SettlementPayables;
public class AutoSettlementPayablesInput
{
    /// <summary>
    /// 供应商货币
    /// </summary>
    public string SupplierCurrency { get; set; }
    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 自动对账口径 1-采购单号 2-订单号
    /// </summary>
    public AutoSettlementPayableType Type { get; set; } = AutoSettlementPayableType.SupplierOrderId;

    /// <summary>
    /// 文件名
    /// </summary>
    public byte[] File { get; set; }
}
