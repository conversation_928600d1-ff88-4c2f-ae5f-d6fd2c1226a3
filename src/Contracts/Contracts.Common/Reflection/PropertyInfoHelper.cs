using System.Collections;
using System.ComponentModel;
using System.Reflection;
using System.Text;

namespace Contracts.Common.Reflection;
public class PropertyInfoHelper
{
    public static string PropertyInfoMsg<T1>(T1 newData, int? opType = null)
    {
        Type newType = typeof(T1);

        var newPropertyInfos = newType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        StringBuilder stringBuilder = new StringBuilder();
        foreach (var propertyInfo in newPropertyInfos)
        {
            var name = propertyInfo.Name;
            var value = propertyInfo.GetValue(newData, null);
            var attr = propertyInfo.GetCustomAttribute(typeof(PropertyMsgAttribute), true) as PropertyMsgAttribute;
            if (attr != null && (opType == null || (attr.OpType != null && attr.OpType.Contains(opType.Value))))
            {
                if (attr.IsSensitive && value != null)
                {
                    value = SensitiveValue(value.ToString(), attr.SensitiveType);
                }
                if (value != null)
                {
                    var genericTypeArgument = propertyInfo.PropertyType.GenericTypeArguments.FirstOrDefault();
                    if (propertyInfo.PropertyType.IsEnum)
                    {
                        var field = propertyInfo.PropertyType.GetField(value.ToString());
                        var descAttr = field?.GetCustomAttribute<DescriptionAttribute>();
                        if (descAttr != null)
                        {
                            value = descAttr.Description;
                        }
                    }
                    else if (genericTypeArgument is not null
                        && genericTypeArgument.IsEnum
                        && !propertyInfo.PropertyType.GetGenericTypeDefinition().Equals(typeof(List<>)))
                    {
                        var field = propertyInfo.PropertyType.GenericTypeArguments.First().GetField(value.ToString());
                        var descAttr = field?.GetCustomAttribute<DescriptionAttribute>();
                        if (descAttr != null)
                        {
                            value = descAttr.Description;
                        }
                    }
                    else if ((genericTypeArgument is not null && genericTypeArgument.Equals(typeof(bool)))
                        || propertyInfo.PropertyType.Equals(typeof(bool)))
                    {
                        value = value.Equals(true) ? "是" : "否";
                    }
                    else if (genericTypeArgument is not null
                        && (genericTypeArgument.Equals(typeof(List<>))
                        || propertyInfo.PropertyType.GetGenericTypeDefinition() == typeof(List<>)))
                    {
                        if (value is IEnumerable enumerable)
                        {
                            stringBuilder.Append($"{attr.Name}：");
                            IEnumerator enumerator = enumerable.GetEnumerator();
                            while (enumerator.MoveNext())
                            {
                                object current = enumerator.Current;
                                if (current.GetType().IsEnum)
                                {
                                    var field = current.GetType().GetField(current.ToString()!);
                                    var descAttr = field?.GetCustomAttribute<DescriptionAttribute>();
                                    if (descAttr != null)
                                    {
                                        stringBuilder.Append($"{descAttr.Description}，");
                                    }
                                }
                                else
                                {
                                    stringBuilder.Append($"{attr.Name}：{current.ToString()},");
                                }
                            }
                        }
                        continue;
                    }

                    stringBuilder.Append($"{attr.Name}：{value}，");
                }
            }
        }
        return stringBuilder.ToString();
    }

    private static string SensitiveValue(string str, SensitiveDataType sensitiveType)
    {
        var res = "*****";
        switch (sensitiveType)
        {
            case SensitiveDataType.Phone:
                res = DataSecrecyUtil.PhoneSensitive(str);
                break;
            case SensitiveDataType.Email:
                res = DataSecrecyUtil.EmailSensitive(str);
                break;
            case SensitiveDataType.Password:
                break;
            default:
                res = DataSecrecyUtil.Sensitive(str);
                break;
        }
        return res;
    }
}
