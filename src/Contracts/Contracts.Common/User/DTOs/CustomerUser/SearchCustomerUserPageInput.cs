using Contracts.Common.User.Enums;

namespace Contracts.Common.User.DTOs.CustomerUser;

public class SearchCustomerUserPageInput : PagingInput
{
    #region 会员筛选

    /// <summary>
    /// 标签Id
    /// </summary>
    public long? TagId { get; set; }

    /// <summary>
    /// 会员等级Id
    /// </summary>
    public long? VipLevelId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; }

    /// <summary>
    /// 注册来源
    /// </summary>
    public RegisterSourceType RegisterSource { get; set; }

    /// <summary>
    /// 分销商来源
    /// </summary>
    public long? AgencySource { get; set; }

    /// <summary>
    /// 开始注册日期 - 结束注册日期
    /// </summary>
    public DateTime? BeginRegisterTime { get; set; }
    public DateTime? EndRegisterTime { get; set; }

    #endregion
}