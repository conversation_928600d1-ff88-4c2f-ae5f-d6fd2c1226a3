namespace Contracts.Common.User.DTOs.AgencyUser
{
    public class AgencyUserDto
    {
        public long AgencyUserId { get; set; }

        public long TenantId { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public long AgencyId { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string RealName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// 邮箱验证状态
        /// </summary>

        public bool EmailStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 国家区号
        /// </summary>
        public string? CountryDialCode { get; set; }
    }
}
