using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.Enums;

/// <summary>
/// Hop定义的酒店费用类型
/// </summary>
public enum HotelFeeType
{
    /// <summary>
    /// 强制性费用，必须支付的与住宿相关的服务或设施费用
    /// 清洁费、停车费、早餐费、Wi-Fi 费用等
    /// </summary>
    MandatoryFee,
    /// <summary>
    /// 度假村费，酒店或度假村在入住期间收取的一种额外费用，通常用于支付酒店提供的各种设施和服务
    /// 游泳池、健身房、娱乐活动、交通服务等
    /// </summary>
    ResortFee,
    /// <summary>
    /// 强制性税费，由当地政府或税务机关规定的，酒店必须在客人入住时收取的税费。这些税费通常是法律要求的，所有入住的客人都必须支付
    /// 城市税、住宿税、环保税、州税或地方税等
    /// </summary>
    MandatoryTax
}