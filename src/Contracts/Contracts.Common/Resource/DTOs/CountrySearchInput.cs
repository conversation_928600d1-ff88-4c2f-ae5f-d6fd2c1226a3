using System.Text.Json.Serialization;

namespace Contracts.Common.Resource.DTOs;
public class CountrySearchInput : PagingInput
{
    /// <summary>
    /// 国家名称关键字
    /// </summary>
    public string Keyword { get; set; }

    /// <summary>
    /// 不需要查询的国家编码 境外国家管理 需要排除中国编码
    /// </summary>
    public int[]? ExcludeCountryCodes { get; set; }

    /// <summary>
    /// 要查询的国家编码
    /// </summary>
    public IEnumerable<long> CountryCodes { get; set; } = Enumerable.Empty<long>();
}

public class AddCountryInput
{
    /// <summary>
    /// 国家名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 国家英文名称
    /// </summary>
    public string ENName { get; set; } = string.Empty;

    /// <summary>
    /// ISO 3166 二字码 https://en.wikipedia.org/wiki/ISO_3166-1
    /// </summary>
    public string IsoCode  { get; set; }
}

public class RemoveCountryInput
{
    /// <summary>
    /// 国家编码
    /// </summary>
    public int CountryCode { get; set; }
}