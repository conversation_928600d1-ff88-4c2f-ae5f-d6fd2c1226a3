using Contracts.Common.Resource.Enums;
using NetTopologySuite.Geometries;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.Airport;
public class AirportModel
{
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    public int CityCode { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 三字码
    /// </summary>
    public string IATA { get; set; } = null!;

    /// <summary>
    /// 四字码
    /// </summary>
    public string ICAO { get; set; } = string.Empty;

    /// <summary>
    /// 机场名称 - 中文
    /// </summary>
    public string AirportZHName { get; set; } = null!;

    /// <summary>
    /// 机场名称 - 英文
    /// </summary>
    public string AirportENName { get; set; } = string.Empty;

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType CoordinateType { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double Latitude { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// 简介
    /// </summary>
    public string Intro { get; set; } = string.Empty;
}
