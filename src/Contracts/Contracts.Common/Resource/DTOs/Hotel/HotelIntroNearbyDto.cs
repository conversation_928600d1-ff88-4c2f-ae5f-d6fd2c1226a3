using Contracts.Common.Resource.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.Hotel;

public class HotelIntroNearbyDto
{
    public string? Name { get; set; }

    public string? EnName { get; set; }

    /// <summary>
    /// 距离
    /// </summary>
    public int? Distance { get; set; }

    /// <summary>
    /// 距离单位，m / km
    /// </summary>
    public DistanceUnitType? DistanceUnit { get; set; }

    /// <summary>
    /// 耗时
    /// </summary>
    public int? UseTime { get; set; }

    /// <summary>
    /// 耗时单位，步数 / 分钟
    /// </summary>
    public UseTimeUnitType? UseTimeUnit { get; set; }

}
