using Contracts.Common.Resource.DTOs.HotelPhotoExtend;

namespace Contracts.Common.Resource.DTOs.Hotel;

public class EditHotelInput : HotelBaseInfo
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 特色
    /// </summary>
    public List<HotelIntroFeatureDto> HotelIntroFeatures { get; set; } = new();

    /// <summary>
    /// 附近兴趣点
    /// </summary>
    public List<HotelIntroNearbyDto> HotelIntroNearbys { get; set; } = new();

    /// <summary>
    /// 客房数、会场数
    /// </summary>
    public List<HotelIntroRoomDto> HotelIntroRooms { get; set; } = new();

    /// <summary>
    /// 附件
    /// </summary>
    public List<HotelPhotoExtendDto> Attachments { get; set; } = new();

    /// <summary>
    /// 询价邮箱
    /// </summary>
    public List<HotelInquiryEmailDto> HotelInquiryEmails { get; set; } = new();
}