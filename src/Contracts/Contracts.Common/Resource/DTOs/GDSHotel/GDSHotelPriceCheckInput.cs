using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.GDSHotel;
public class GDSHotelPriceCheckInput
{
    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long HotelId { get; set; }

    public long? RoomId { get; set; }
    public string RateKey { get; set; }

    /// <summary>
    /// 入住时间
    /// </summary>
    public DateTime LiveDate { get; set; }

    /// <summary>
    /// 离店时间
    /// </summary>
    public DateTime LeaveDate { get; set; }

    /// <summary>
    /// 价格策略可住人数(最小/默认=1)
    /// </summary>
    public int Adults { get; set; } = 1;

    /// <summary>
    /// 每个儿童年龄集合
    /// </summary>
    public IEnumerable<int>? ChildrenAges { get; set; }

    /// <summary>
    /// 房间数(默认1)
    /// </summary>
    public int RoomNum { get; set; } = 1;

    /// <summary>
    /// 是否需要原数据
    /// </summary>
    public bool IsOriginalJson { get; set; } = false;

    public long TenantId { get; set; }

    /// <summary>
    /// 国家和地区的ISO二字代码 客人国籍，不传默认中国
    /// </summary>
    public string Nationality { get; set; }
}