using Contracts.Common.Hotel.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Resource.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.GDSHotel;
public class SearchGDSHotelPriceStrategyInput
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// GDSHotelid 短Code
    /// </summary>
    public string GDSHotelId { get; set; }

    /// <summary>
    /// 价格策略可住人数(最小/默认=1)
    /// </summary>
    public int Adults { get; set; } = 1;

    /// <summary>
    /// 每个儿童年龄集合
    /// </summary>
    public IEnumerable<int>? ChildrenAges { get; set; }

    /// <summary>
    /// 房间数(默认1)
    /// </summary>
    public int RoomNum { get; set; } = 1;

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime LiveDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime LeaveDate { get; set; }

    /// <summary>
    /// 床型名称
    /// </summary>
    public string? BedType { get; set; }

    /// <summary>
    /// 是否含早
    /// 不含早（=0时），含早（>0时）
    /// </summary>
    public bool? IsBreakfast { get; set; }

    /// <summary>
    /// 房型Id
    /// </summary>
    public long? RoomId { get; set; }

    /// <summary>
    /// 房间名字
    /// </summary>
    public string RoomName { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string CurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 价格范围-最低
    /// </summary>
    public decimal? RateRangeMin { get; set; }

    /// <summary>
    /// 价格范围-最高
    /// </summary>
    public decimal? RateRangeMax { get; set; }
    /// <summary>
    /// 取消政策
    /// </summary>
    public CancelRulesType? CancelRulesType { get; set; }

    public long TenantId { get; set; }

    /// <summary>
    /// 国家和地区的ISO二字代码 客人国籍，不传默认中国
    /// </summary>
    public string Nationality { get; set; } = "CN";

    public GDSSabreHotelCodeType GDSSabreHotelCodeType { get; set; }
}
