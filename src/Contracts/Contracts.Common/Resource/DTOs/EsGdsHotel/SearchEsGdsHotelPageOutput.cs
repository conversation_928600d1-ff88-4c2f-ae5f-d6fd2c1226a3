namespace Contracts.Common.Resource.DTOs.EsGdsHotel;

public class SearchEsGdsHotelPageOutput
{
    /// <summary>
    /// 高定酒店id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? ENName { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }
    public string CountryName { get; set; }
    
    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }
    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }
    public string CityName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public int DistrictCode { get; set; }
    public string? DistrictName { get; set; }
    
    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? ENAddress { get; set; }
    
    /// <summary>
    /// 星级
    /// </summary>
    public decimal? StarLevel { get; set; }
    
    /// <summary>
    /// 是否置顶
    /// </summary>
    public bool OnTop { get; set; }

    /// <summary>
    /// 距离(m)
    /// </summary>
    public decimal? Distance { get; set; }

    /// <summary>
    /// 设施id列表
    /// </summary>
    public List<long> FacilityIds { get; set; }
}