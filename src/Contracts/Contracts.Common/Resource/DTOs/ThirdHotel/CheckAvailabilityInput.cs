using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Resource.DTOs.ThirdHotel;

public class CheckAvailabilityInput
{
    public long TenantId { get; set; }

    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long ResourceHotelId { get; set; }

    /// <summary>
    /// 资源房型id
    /// </summary>
    public long ResourceRoomId { get; set; }

    /// <summary>
    /// 第三方平台
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 第三方报价计划id
    /// </summary>
    public string PricestrategyId { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime CheckIn { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime CheckOut { get; set; }

    /// <summary>
    /// 房间数
    /// </summary>
    public int RoomNum { get; set; }

    /// <summary>
    /// 成人数
    /// </summary>
    public int AdultNum { get; set; }

    /// <summary>
    /// 每个儿童年龄集合
    /// </summary>
    public IEnumerable<int>? ChildrenAges { get; set; }

    /// <summary>
    /// 入住客人国籍，入住客人国籍，国家或地区两位字母代码，例如：CN、TH 汇智酒店V3接口需要传入
    /// </summary>
    public string? Nationality { get; set; }

    /// <summary>
    /// 每日采购价信息 不传不校验总价
    /// </summary>
    public IEnumerable<HotelOrderCalendarPriceDto>? CalendarPrices { get; set; }

    /// <summary>
    /// 【汇智酒店】预订代码 加密存储报价信息
    /// </summary>
    public string? PreBookingCode { get; set; }
}

