using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.ThirdHotel;

public class GetThirdHotelPriceInput
{
    public long TenantId { get; set; }

    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long ResourceHotelId { get; set; }

    /// <summary>
    /// 指定第三方平台 PricestrategyId不为空时需指定具体平台
    /// </summary>
    public SupplierApiType[] SupplierApiTypes { get; set; } = Array.Empty<SupplierApiType>();

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime CheckIn { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime CheckOut { get; set; }

    /// <summary>
    /// 房间数 可选
    /// </summary>
    public int? RoomNum { get; set; } = 1;

    /// <summary>
    /// 成人数
    /// </summary>
    public int? AdultNum { get; set; }

    /// <summary>
    /// 每个儿童年龄集合
    /// </summary>
    public IEnumerable<int>? ChildrenAges { get; set; }

    /// <summary>
    /// 国家和地区的ISO二字代码 客人国籍，不传默认中国
    /// </summary>
    public string? Nationality { get; set; }

    /// <summary>
    /// 是否仅团房 团房标识 true仅返回团房，false返回非团房 不传返回全部
    /// </summary>
    public bool? IsGroupBooking { get; set; }

    /// <summary>
    /// 是否仅返回缓存价格
    /// </summary>
    public bool OnlyCachePrice { get; set; }
}