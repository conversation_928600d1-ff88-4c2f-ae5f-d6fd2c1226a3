namespace Contracts.Common.Resource.DTOs.SelfsupportHotel;
public class HotelPriceStrategyOutput
{
    /// <summary>
    /// 资源房型id
    /// </summary>
    public long ResourceRoomId { get; set; }
    public string RoomZHName { get; set; }
    public string RoomENName { get; set; }

    /// <summary>
    /// 策略id
    /// </summary>
    public string PriceStrategyId { get; set; }
    public string Name { get; set; }
    public string Enname { get; set; }

    /// <summary>
    /// 早餐数
    /// </summary>
    public int BreakfastCount { get; set; }

    /// <summary>
    /// 最小提前预订小时数
    /// </summary>
    public int MinAdvHours { get; set; }

    /// <summary>
    /// 最小入住天数（1-365）
    /// </summary>
    public int MinDays { get; set; }

    /// <summary>
    /// 最大入住天数（1-365）
    /// </summary>
    public int MaxDays { get; set; }

    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int MaxOccupancy { get; set; }
}