using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.HotelMeeting;
public class HotelMeetingDto
{
    public long HotelId { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string ENName { get; set; }

    /// <summary>
    /// 楼层
    /// </summary>
    public string Floor { get; set; }

    /// <summary>
    /// 面积（m²）
    /// </summary>
    public decimal Area { get; set; }

    /// <summary>
    /// 层高（m）
    /// </summary>
    public decimal? Height { get; set; }

    #region 会场布局
    /// <summary>
    /// 剧院（人）
    /// </summary>
    public int? Theatre { get; set; }

    /// <summary>
    /// 课桌型（人）
    /// </summary>
    public int? Classroom { get; set; }

    /// <summary>
    /// U字型（人）
    /// </summary>
    public int? UShape { get; set; }

    /// <summary>
    /// 宴会型（人）
    /// </summary>
    public int? Banquet { get; set; }

    /// <summary>
    /// 酒会型（人）
    /// </summary>
    public int? Cocktail { get; set; }

    /// <summary>
    /// 董事会（人）
    /// </summary>
    public int? Boardroom { get; set; }
    #endregion

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> Pictures { get; set; } = new();

    public bool Enabled { get; set; }
}
