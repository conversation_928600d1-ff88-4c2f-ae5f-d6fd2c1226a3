namespace Contracts.Common.Marketing.Enums
{
    public enum ProductBusinessType : int
    {
        /// <summary>
        /// 酒店套餐
        /// </summary>
        Ticket_HotelPackages = 1,

        /// <summary>
        /// 房券
        /// </summary>
        Ticket_RoomVoucher = 2,

        /// <summary>
        /// 餐饮
        /// </summary>
        Ticket_Catering = 4,

        /// <summary>
        /// 邮寄
        /// </summary>
        Mail = 8,

        /// <summary>
        /// 酒店日历房
        /// </summary>
        Hotel = 16,

        /// <summary>
        /// 预定门票
        /// </summary>
        Scenic_BookingTicket = 32,

        /// <summary>
        /// 期票
        /// </summary>
        Scenic_PeakDayTicket = 64,

        /// <summary>
        /// 线路
        /// </summary>
        TravelLine = 128,

        /// <summary>
        /// 接送车
        /// </summary>
        CarHailing = 256,
        
        /// <summary>
        /// 用车产品
        /// </summary>
        CarProduct = 512,
    }
}
