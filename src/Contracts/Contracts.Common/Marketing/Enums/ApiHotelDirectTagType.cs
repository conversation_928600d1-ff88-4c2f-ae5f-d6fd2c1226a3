using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Marketing.Enums;

/// <summary>
/// 汇智酒店支持价格策略类型
/// </summary>
public enum ApiHotelDirectTagType
{
    /// <summary>
    /// 主推散房
    /// </summary>
    DirectRoom = 1,
    /// <summary>
    /// 主推团房
    /// </summary>
    DirectGroupRoom = 2,
    /// <summary>
    /// O类
    /// </summary>
    O = 4,
    /// <summary>
    /// A类
    /// </summary>
    A = 8,
    /// <summary>
    /// B类
    /// </summary>
    B = 16,
    /// <summary>
    /// C类
    /// </summary>
    C = 32,
    /// <summary>
    /// D类
    /// </summary>
    D = 64,
    /// <summary>
    /// E类
    /// </summary>
    E = 128,
}