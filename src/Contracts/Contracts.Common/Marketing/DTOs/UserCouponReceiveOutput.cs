namespace Contracts.Common.Marketing.DTOs
{
    public class UserCouponReceiveOutput
    {

        public IList<UserReceivedCoupon> UserReceivedCoupons { get; set; }

        public UserCouponReceiveStatus Status { get; set; }
    }

    public enum UserCouponReceiveStatus
    {
        /// <summary>
        /// 待领取
        /// </summary>
        Unreceived = 0,

        /// <summary>
        /// 领取成功
        /// </summary>
        Received = 1,

        /// <summary>
        /// 活动结束（过期、发放取消、优惠券已领完）
        /// </summary>
        Ended = 2,

        /// <summary>
        /// 已领完
        /// </summary>
        Finishreceived = 3,

        /// <summary>
        /// 未开始
        /// </summary>
        NotStarted = 4,
    }
}
