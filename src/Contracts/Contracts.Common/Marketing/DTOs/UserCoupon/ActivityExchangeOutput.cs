using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Marketing.DTOs.UserCoupon;

public class ActivityExchangeOutput
{
    /// <summary>
    /// 兑换结果
    /// </summary>
    public UserCouponExchangeResult Result { get; set; }
    /// <summary>
    /// 领取优惠券信息
    /// </summary>
    public IList<UserReceivedCoupon> UserReceivedCoupons { get; set; }
}

public enum UserCouponExchangeResult
{
    /// <summary>
    /// 当前分销商不满足兑换条件
    /// </summary>
    NotSupport = -1,

    /// <summary>
    /// 待兑换
    /// </summary>
    UnExchange = 0,

    /// <summary>
    /// 兑换成功
    /// </summary>
    Exchanged = 1,

    /// <summary>
    /// 兑换码无效
    /// </summary>
    CodeInvalid = 2,

    /// <summary>
    /// 兑换码已使用
    /// </summary>
    CodeHasUsed = 3,

    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted = 3,

    /// <summary>
    /// 已过期
    /// </summary>
    Ended = 4
}