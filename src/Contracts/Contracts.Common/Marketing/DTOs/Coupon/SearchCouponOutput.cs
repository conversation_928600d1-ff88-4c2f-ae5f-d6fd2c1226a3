using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.Coupon
{
    public class SearchCouponOutput
    {
        public long Id { get; set; }
        /// <summary>
        /// 优惠券名称
        /// </summary>
        public string CouponName { get; set; }

        /// <summary>
        /// 优惠券类型
        /// </summary>
        public CouponType CouponType { get; set; }

        /// <summary>
        /// 面额 折扣券:[0,1)  满减券:[0,999999]；
        /// 例子：9折：0.9 
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 满多少元可用
        /// </summary>
        public decimal LimitMinAmt { get; set; }

        /// <summary>
        /// 发放数量 -1为不限制
        /// </summary>
        public int CouponQuantity { get; set; }

        public int ReceivedQuantity { get; set; }

        public int UsedQuantity { get; set; }

        /// <summary>
        /// 有效期类型 1-固定时间段 2-有效天数
        /// </summary>
        public CouponValidDateType ValidDateType { get; set; }

        /// <summary>
        /// 固定日期: 开始
        /// </summary>
        public DateTime? ValidStartDate { get; set; }

        /// <summary>
        /// 固定日期：结束
        /// </summary>
        public DateTime? ValidEndDate { get; set; }

        /// <summary>
        /// 自领取之日起多少天有效
        /// </summary>
        public int ValidDays { get; set; }

        /// <summary>
        /// 使用渠道
        /// </summary>
        public CouponUseChannel UseChannel { get; set; }

        /// <summary>
        /// 适用范围
        /// </summary>
        public List<LimitProductType>? LimitProductType { get; set; }

        /// <summary>
        /// 关联的有效活动数量
        /// </summary>
        public int CouponActivityCount { get; set; }
    }
}
