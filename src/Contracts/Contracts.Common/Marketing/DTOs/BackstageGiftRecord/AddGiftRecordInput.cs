using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.BackstageGiftRecord;

public class AddGiftRecordInput
{
    public long TenantId { get; set; }

    /// <summary>
    /// 赠送备注
    /// </summary>
    public string Describe { get; set; }

    /// <summary>
    /// 赠送项类型
    /// </summary>
    public BackstageGiftType GiftType { get; set; }

    /// <summary>
    /// 赠送项数据
    /// </summary>
    public List<GiftItemInfo> GiftItemInfos { get; set; }

    #region 指定会员发放

    /// <summary>
    /// 指定会员发放
    /// </summary>
    public List<GiftCustomerUserInfo> CustomerUserInfos { get; set; } = new List<GiftCustomerUserInfo>();

    #endregion

    #region 按照查询条件查询出来的所有会员发放

    /// <summary>
    /// 查询类型
    /// 0-默认会员管理筛选查询
    /// 1-RFM筛选查询
    /// </summary>
    public int SearchType { get; set; }

    #region 会员筛选

    /// <summary>
    /// 标签Id
    /// </summary>
    public long? TagId { get; set; }

    /// <summary>
    /// 会员等级Id
    /// </summary>
    public int VipLevelId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; }

    /// <summary>
    /// 注册来源
    /// </summary>
    public User.Enums.RegisterSourceType RegisterSource { get; set; }

    /// <summary>
    /// 开始注册日期 - 结束注册日期
    /// </summary>
    public DateTime? BeginRegisterTime { get; set; }
    public DateTime? EndRegisterTime { get; set; }

    #endregion

    #region RFM

    /// <summary>
    /// 最近消费日期-开始
    /// </summary>
    public DateTime? BeginConsumptionTime { get; set; }

    /// <summary>
    /// 最近消费日期-结束
    /// </summary>
    public DateTime? EndConsumptionTime { get; set; }

    /// <summary>
    /// 累计最小消费金额
    /// </summary>
    public decimal? MinConsumptionAmount { get; set; }

    /// <summary>
    /// 累计最大消费金额
    /// </summary>
    public decimal? MaxConsumptionAmount { get; set; }

    /// <summary>
    /// 累计最小消费次数
    /// </summary>
    public int? MinConsumptionCount { get; set; }

    /// <summary>
    /// 累计最大消费次数
    /// </summary>
    public int? MaxConsumptionCount { get; set; }

    #endregion

    #endregion
}

public class GiftCustomerUserInfo
{
    /// <summary>
    /// 被赠送用户的Id
    /// </summary>
    public long CustomerUserId { get; set; }

    /// <summary>
    /// 被赠送用户的昵称
    /// </summary>
    public string NickName { get; set; }

    /// <summary>
    /// 被赠送用户的手机号码
    /// </summary>
    public string PhoneNumber { get; set; }
}