using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.PromotionCustomerRemark;

public class SearchPromotionCustomerRemarkOutput
{
    /// <summary>
    /// 记录Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 客户id
    /// </summary>
    public long CustomerId { get; set; }

    /// <summary>
    /// 跟进人类型
    /// </summary>
    public PromoterType CreatorType { get; set; }

    /// <summary>
    /// 跟进人Id
    /// </summary>
    public long CreatorId { get; set; }

    /// <summary>
    /// 跟进人名称
    /// </summary>
    public string CreatorName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }

    public DateTime CreateTime { get; set; }
}