using Contracts.Common.Marketing.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Marketing.DTOs.PromotionTraceRecord;

public class SearchTraceStatisticsOutput
{
    /// <summary>
    /// 追踪Id
    /// </summary>
    public long PromotionTraceId { get; set; }
    
    /// <summary>
    /// 投放时间
    /// </summary>
    public DateTime PromotionCreateTime { get; set; }
    
    /// <summary>
    /// 推广人id
    /// </summary>
    public long PromoterId { get; set; }
    
    /// <summary>
    /// 推广人名称
    /// </summary>
    public string PromoterName { get; set; }
    
    /// <summary>
    /// 推广人角色类型
    /// </summary>
    public PromoterRoleType? PromoterRoleType { get; set; }
    
    /// <summary>
    /// 推广人类型
    /// </summary>
    public PromoterType PromoterType { get; set; }
    
    /// <summary>
    /// 追踪类型
    /// </summary>
    public PromotionTraceType TraceType { get; set; }
    
    /// <summary>
    /// 活码记录名称
    /// </summary>
    public string TargetTitle { get; set; }
    
    /// <summary>
    /// 商品类型
    /// </summary>
    public ProductType? ProductType { get; set; }
    
    /// <summary>
    /// 推广投放位置Name
    /// </summary>
    public string PromotionPositionName { get; set; }
    
    /// <summary>
    /// 浏览量(PV)
    /// </summary>
    public int PageViews { get; set; }

    /// <summary>
    /// 访客量(UV)
    /// </summary>
    public int UserSessions { get; set; }

    /// <summary>
    /// 创建订单数
    /// </summary>
    public int CreateOrderCount { get; set; }

    /// <summary>
    /// 取消订单数
    /// </summary>
    public int CancelOrderCount { get; set; }

    /// <summary>
    /// 支付订单数
    /// </summary>
    public int PaymentOrderCount { get; set; }

    /// <summary>
    /// 退款订单数
    /// </summary>
    public int RefundOrderCount { get; set; }

    /// <summary>
    /// 完结订单数
    /// </summary>
    public int CompleteOrderCount { get; set; }

    /// <summary>
    /// 成交金额
    /// </summary>
    public decimal? OrderAmount { get; set; }
}