using Contracts.Common.Marketing.Enums;
using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Marketing.DTOs.PromotionTraceRecord;

/// <summary>
/// 用户足迹
/// </summary>
public class GetCustomerTraceListOutput
{
    /// <summary>
    /// 足迹时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    
    /// <summary>
    /// 行为
    /// </summary>
    public TraceBehaviorType BehaviorType { get; set; }
    
    /// <summary>
    /// 追踪类型
    /// </summary>
    public PromotionTraceType TraceType { get; set; }
    
    /// <summary>
    /// 商品类型
    /// </summary>
    public ProductType? ProductType { get; set; }
    
    /// <summary>
    /// 推广码分享形式
    /// </summary>
    public TraceShareType ShareType { get; set; }

    /// <summary>
    /// 推广码访问目标
    /// </summary>
    public TraceVisitTargetType VisitTargetType { get; set; }
    
    /// <summary>
    /// 推广码访问目标名称
    /// </summary>
    public string VisitTargetName { get; set; }

    /// <summary>
    /// 标的物Id, 产品Id、活动Id等等
    /// </summary>
    public long? TargetId { get; set; }

    /// <summary>
    /// 标的物标题, XX大酒店、产品名称等等
    /// </summary>
    public string TargetTitle { get; set; } = string.Empty;

    /// <summary>
    /// 订单类型
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 订单Id
    /// </summary>
    public long? OrderId { get; set; }

    /// <summary>
    /// 订单金额
    /// 记录支付,退款金额
    /// </summary>
    public decimal? OrderAmount { get; set; }
    
    /// <summary>
    /// 推广人类型
    /// </summary>
    public PromoterType PromoterType { get; set; }

    /// <summary>
    /// 推广人Id
    /// </summary>
    public long PromoterId { get; set; }

    /// <summary>
    /// 推广人名称
    /// </summary>
    public string PromoterName { get; set; }

    /// <summary>
    /// 推广人手机号
    /// </summary>
    public string PromoterPhoneNumber { get; set; }

    /// <summary>
    /// 是否直接访问
    /// </summary>
    public bool IsDirectVisit { get; set; }
}