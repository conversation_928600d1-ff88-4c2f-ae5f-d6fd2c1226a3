using Contracts.Common.Notify.Enums;

namespace Contracts.Common.Notify.Messages;

public class NotifyMessageProcessMessage
{
    public long TenantId { get; set; }

    /// <summary>
    /// 通知给谁
    /// </summary>
    public SendToTheRole SendToTheRole { get; set; }

    /// <summary>
    /// 通知方式
    /// </summary>
    public NotifyMode NotifyMode { get; set; }

    /// <summary>
    /// 通知业务子类型
    /// </summary>
    public NotifyEventSubType NotifyEventSubType { get; set; }

    /// <summary>
    /// 指令来源渠道, SendToTheRole为Customer时必须指定
    /// </summary>
    public NotifyChannel NotifyChannel { get; set; }

    public object Variables { get; set; }

    /// <summary>
    /// 可选 消息关联id callback回传
    /// </summary>
    public long? MessageId { get; set; }
}

public class NotifyMessageProcessCallbackMessage 
{
    /// <summary>
    /// 消息关联id
    /// </summary>
    public long MessageId { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessed { get; set; }

    public string Message { get; set; }

    /// <summary>
    /// 邮件-发送状态码
    /// </summary>
    public EmailSmtpStatusCode? EmailSmtpStatusCode { get; set; }
}