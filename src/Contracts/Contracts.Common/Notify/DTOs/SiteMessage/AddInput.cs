using Contracts.Common.Notify.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Notify.DTOs.SiteMessage;
public class AddInput
{
    public long TenantId { get; set; }
    /// <summary>
    /// 通知事件类型
    /// </summary>
    public NotifyEventType NotifyEventType { get; set; }

    /// <summary>
    /// 通知事件子类型
    /// </summary>
    public NotifyEventSubType NotifyEventSubType { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 接收通知的用户id集合
    /// </summary>
    public List<long> UserIds { get; set; } = new();

    /// <summary>
    /// 通知对象类型 0-全部 1-指定/单个账号
    /// </summary>
    public SiteMessageObjectType ObjectType { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    public string EnTitle { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }
    public string EnContent { get; set; }

    /// <summary>
    /// 跳转链接类型
    /// </summary>
    public SiteMessageLinkType LinkType { get; set; }

    /// <summary>
    /// 链接参数id
    /// </summary>
    public long? LinkId { get; set; }

}