using Contracts.Common.Notify.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Notify.DTOs.SiteMessage;
public class DetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    public string EnTitle { get; set; }
    
    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    public string EnContent { get; set; }

    /// <summary>
    /// 跳转链接类型
    /// </summary>
    public SiteMessageLinkType LinkType { get; set; }

    /// <summary>
    /// 链接参数id
    /// </summary>
    public long? LinkId { get; set; }

    public DateTime CreateTime { get; set; }
}