namespace Contracts.Common.Product.Enums;

/// <summary>
/// 渠道价格产品类型
/// </summary>
public enum ChannelProductType
{
    /// <summary>
    /// 票券产品
    /// </summary>
    Ticket = 1,

    /// <summary>
    /// 线路产品
    /// </summary>
    Line = 2,

    /// <summary>
    /// 酒店日历房
    /// </summary>
    Hotel = 3,

    /// <summary>
    /// 景区门票
    /// </summary>
    ScenicTicket = 4,
    
    /// <summary>
    /// 汇智酒店
    /// </summary>
    HuiZhiHotel = 5,

    /// <summary>
    /// 用车
    /// </summary>
    CarUsing = 6,

    /// <summary>
    /// 用车服务项目
    /// </summary>
    CarUsingServiceItem = 7,
}