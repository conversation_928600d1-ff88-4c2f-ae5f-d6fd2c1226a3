namespace Contracts.Common.Product.DTOs.CarHailingCalendarPrice;

public class GetCarHailingCalendarPriceOutput
{
    /// <summary>
    /// 产品Id
    /// </summary>
    public long CarHailingProductId { get; set; }

    /// <summary>
    /// 产品规格Id
    /// </summary>
    public long CarHailingProductSkuId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 座位数
    /// </summary>
    public int Seats { get; set; }

    /// <summary>
    /// 乘客数
    /// </summary>
    public int Passengers { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> Images { get; set; } = new();

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 库存和日历价
    /// </summary>
    public List<CarHailingCalendarPriceAndQuantityInfo> PriceAndQuantityInfos { get; set; } = new();
}

public class CarHailingCalendarPriceAndQuantityInfo
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 总库存
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 售卖状态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 基础售价
    /// </summary>
    public decimal? SalePrice { get; set; }
}