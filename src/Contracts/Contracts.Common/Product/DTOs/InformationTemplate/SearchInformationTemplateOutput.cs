using Contracts.Common.Product.DTOs.InformationTemplateFields;
using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.InformationTemplate;
public class SearchInformationTemplateOutput
{
    public long Id { get; set; }

    public string TemplateName { get; set; }

    public TemplateType TemplateType { get; set; }

    public DateTime CreateTime { get; set; }

    public List<InformationTemplateFieldsOutput> Fields { get; set; }
}
