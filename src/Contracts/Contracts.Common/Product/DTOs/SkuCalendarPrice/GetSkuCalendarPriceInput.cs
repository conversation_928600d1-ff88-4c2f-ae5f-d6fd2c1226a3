using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.SkuCalendarPrice
{
    public class GetSkuCalendarPriceInput
    {
        public long ProductId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public IEnumerable<long> SkuIds { get; set; } = Enumerable.Empty<long>();

        /// <summary>
        /// sku上下架状态
        /// </summary>
        public bool? Enabled { get; set; }
    }
}
