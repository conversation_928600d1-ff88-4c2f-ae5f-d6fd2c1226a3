using Contracts.Common.Product.DTOs.LineProduct;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;

public class GetLineProductSkuCalendarPriceOutput
{
    public long LineProductId { get; set; }
    public long LineProductSkuId { get; set; }
    public string LineProductSkuName { get; set; }
    /// <summary>
    /// 含住宿
    /// </summary>
    public bool IncludedAccommodation { get; set; }
    
    /// <summary>
    /// 提前几天预订
    /// </summary>
    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 提前预订时间点
    /// </summary>
    public TimeSpan ReservationTimeInAdvance { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public List<FeeIncludeDto> FeeIncludes { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// B2B售卖状态
    /// </summary>
    public bool B2bSellingStatus { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    public List<LineProductSkuCalendarPriceAndQuantityInfo> PriceAndQuantityInfos { get; set; } = new();
    
    /// <summary>
    /// 关联时段场次名称
    /// </summary>
    public string? TimeSlotName { get; set; }

    /// <summary>
    /// 关联时段场次id
    /// </summary>
    public long? TimeSlotId { get; set; }

    /// <summary>
    /// 套餐票种信息
    /// </summary>
    public List<CalendarPriceSkuTypeItemInfo> SkuTypeItemInfos { get; set; } = new();
    
    /// <summary>
    /// 供应商是否销售
    /// </summary>
    public bool? SupplierIsSale { get; set; }

    /// <summary>
    /// 是否需要附加信息
    /// </summary>
    public bool IsNeedExtraInfo { get; set; }
    
        
    /// <summary>
    /// (套餐)采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }

}

/// <summary>
/// 日历价格信息
/// </summary>
public class LineProductSkuCalendarPriceAndQuantityInfo
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 总库存
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 售卖状态
    /// </summary>
    public bool Enabled { get; set; }
    
    /// <summary>
    /// 当天是否可售
    /// <remarks>开售，库存>0，有价格，满足提前预定</remarks>
    /// </summary>
    public bool IsForSale { get; set; }

    public List<LineProductSkuCalendarPriceInfo> SkuCalendarPriceInfos { get; set; }
}

public class CalendarPriceSkuTypeItemInfo
{
    /// <summary>
    /// sku票种id
    /// </summary>
    public long SkuTypeItemId { get; set; }
    
    /// <summary>
    /// sku票种名称
    /// </summary>
    public string SkuTypeItemName { get; set; }
    
    /// <summary>
    /// 票种类型
    /// </summary>
    public LineSkuPriceType SkuPriceType { get; set; }
    
    /// <summary>
    /// 供应商是否销售
    /// </summary>
    public bool? SupplierIsSale { get; set; }
}