using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;

public class LineProductSkuCalendarPriceInfo
{
    /// <summary>
    /// 线路价格类型
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 类型名称
    /// <value>票种类型名称</value>
    /// </summary>
    public string SkuTypeItemName { get; set; }

    /// <summary>
    /// 票种类型Id
    /// </summary>
    public long? SkuTypeItemId { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 成本价(采购价)
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 渠道价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
    
    /// <summary>
    /// 产品价格配置- 价格基准类型
    /// </summary>
    public PriceBasisType PriceBasisType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整类型
    /// </summary>
    public PriceAdjustmentType PriceAdjustmentType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整值
    /// </summary>
    public decimal? PriceAdjustmentValue { get; set; }
    
    /// <summary>
    /// 票种独立可用库存
    /// </summary>
    public int? AvailableQuantity { get; set; }
    
    /// <summary>
    /// 票种独立库存是否可售
    /// </summary>
    public bool? Enabled { get; set; }
}