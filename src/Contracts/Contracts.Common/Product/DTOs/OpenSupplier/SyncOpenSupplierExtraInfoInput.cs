using Contracts.Common.Order.Enums;

namespace Contracts.Common.Product.DTOs.OpenSupplier;

public class SyncOpenSupplierExtraInfoInput
{
    /// <summary>
    /// 供应商类型
    /// </summary>
    public OpenSupplierType OpenSupplierType { get; set; }

    /// <summary>
    /// product id
    /// </summary>
    public string ProductId { get; set; }
    
    /// <summary>
    /// option id
    /// </summary>
    public string? OptionId { get; set; }
    
    /// <summary>
    /// sku id
    /// </summary>
    public string? SkuId { get; set; }

    /// <summary>
    /// 租户id
    /// </summary>
    public List<long> TenantIds { get; set; } = new();
}