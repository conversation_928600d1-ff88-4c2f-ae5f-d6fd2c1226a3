using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.CarServiceItemCalendarPrices;
public class GetCarServiceItemCalendarPriceInput
{
    /// <summary>
    /// 用车产品id
    /// </summary>
    public long? CarProductId { get; set; }

    /// <summary>
    /// 服务项目id
    /// </summary>
    public long? CarServiceItemId { get; set; }

    /// <summary>
    /// 日期区间
    /// </summary>
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    /// <summary>
    /// sku上下架状态
    /// 默认查全部
    /// </summary>
    public bool? Enabled { get; set; }

    /// <summary>
    /// 分销商id
    /// 用于汇率换算
    /// </summary>
    public long? AgencyId { get; set; }
}
