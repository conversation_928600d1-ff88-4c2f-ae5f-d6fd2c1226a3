using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.CarHailingProduct;

public class GetCarHailingProductOutput
{
    /// <summary>
    /// 产品id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 国家Code
    /// </summary>
    public int? CountryCode { get; set; }

    /// <summary>
    /// 国家名称
    /// </summary>
    public string CountryName { get; set; }

    /// <summary>
    /// 城市Code
    /// </summary>
    public int? CityCode { get; set; }

    /// <summary>
    /// 城市名称
    /// </summary>
    public string CityName { get; set; }

    /// <summary>
    /// 出发地名称
    /// </summary>
    public string DeparturePointName { get; set; }

    /// <summary>
    /// 目的地名称
    /// </summary>
    public string DestinationPointName { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 服务语言
    /// </summary>
    public LanguageType LanguageType { get; set; }

    /// <summary>
    /// 用车类型
    /// </summary>
    public CarHailingType CarHailingType { get; set; }

    /// <summary>
    /// 出发地Id
    /// </summary>
    public long? DeparturePointId { get; set; }

    /// <summary>
    /// 目的地Id
    /// </summary>
    public long? DestinationPointId { get; set; }

    /// <summary>
    /// 提前几天预约
    /// </summary>

    public int ReservationDaysInAdvance { get; set; }

    /// <summary>
    /// 使用说明
    /// </summary>
    public string Instructions { get; set; }

    /// <summary>
    /// 温馨提示
    /// </summary>
    public string KindReminder { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 退款比例
    /// </summary>
    public decimal? RefundRate { get; set; }

    /// <summary>
    /// 退款-预订日期截止前多少天
    /// </summary>
    public int? RefundBeforeTravelDateDay { get; set; }

    /// <summary>
    /// 退款-预订当日 - 时间 如14:00
    /// </summary>
    public TimeSpan? RefundTravelDateTime { get; set; }

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ProductPictures { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 产品人id
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 平台运营人
    /// </summary>
    public List<ProductOperatorUserDto> ProductOperatorUser { get; set; }
}