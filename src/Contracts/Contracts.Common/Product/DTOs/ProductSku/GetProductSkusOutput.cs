using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.ProductSku
{
    public class GetProductSkusOutput
    {
        public long Id { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        /// Sku名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 成本价
        /// </summary>
        public decimal? CostPrice { get; set; }

        /// <summary>
        /// 售价
        /// </summary>
        public decimal? SellingPrice { get; set; }

        /// <summary>
        /// 划线价
        /// </summary>
        public decimal? LinePrice { get; set; }

        /// <summary>
        /// 包含晚数
        /// </summary>
        public int NumberOfNights { get; set; }

        /// <summary>
        /// 费用描述
        /// </summary>
        public string CostDescription { get; set; }

        /// <summary>
        /// 图片路径
        /// </summary>
        public string ImagePath { get; set; }

        /// <summary>
        /// 有效期类型
        /// </summary>
        public ProductValidityType ValidityType { get; set; }

        /// <summary>
        /// 有效期 - 起
        /// </summary>
        public DateTime? ValidityBegin { get; set; }

        /// <summary>
        /// 有效期 - 止
        /// </summary>
        public DateTime? ValidityEnd { get; set; }

        /// <summary>
        /// 购买之后x天有效
        /// </summary>
        public int AfterPurchaseDays { get; set; }
        /// <summary>
        /// 可用库存
        /// </summary>
        public int AvailableQuantity { get; set; }
        /// <summary>
        /// true：上架  false：下架
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// B2B售卖状态
        /// </summary>
        public bool B2bSellingStatus { get; set; }
    }
}
