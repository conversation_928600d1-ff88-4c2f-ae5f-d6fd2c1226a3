using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.ProductSku;

public class AgencyGetTicketSkuOutput
{
    /// <summary>
    /// 规格id
    /// </summary>
    public long SkuId { get; set; }

    /// <summary>
    /// 规格名称
    /// </summary>
    public string SkuName { get; set; }

    /// <summary>
    /// 费用描述
    /// </summary>
    public string CostDescription { get; set; }

    /// <summary>
    /// 有效期类型
    /// </summary>
    public ProductValidityType ValidityType { get; set; }

    /// <summary>
    /// 有效期 - 起
    /// </summary>
    public DateTime? ValidityBegin { get; set; }

    /// <summary>
    /// 有效期 - 止
    /// </summary>
    public DateTime? ValidityEnd { get; set; }

    /// <summary>
    /// 购买之后x天有效
    /// </summary>
    public int AfterPurchaseDays { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    public bool Enabled { get; set; }

    /// <summary>
    /// 渠道价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
}