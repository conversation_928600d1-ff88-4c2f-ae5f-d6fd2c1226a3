using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
public class GetAgencyChannelPriceSettingInput
{
    /// <summary>
    /// 分销商价格分组id
    /// </summary>
    public long PriceGroupId { get; set; }

    /// <summary>
    /// 规格id
    /// </summary>
    public IEnumerable<long> SkuIds { get; set; } = Enumerable.Empty<long>();
}
