using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.OpenPlatformPricingSyncLog;

public class SyncLogInfo
{
    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 平台类型
    /// </summary>
    public OpenPlatformType PlatformType { get; set; }

    /// <summary>
    /// 渠道类型
    /// </summary>
    public OtaChannelType? ChannelType { get; set; }
    
    /// <summary>
    /// 同步类型
    /// </summary>
    public OpenPlatformPricingSyncType SyncType { get; set; }
    
    /// <summary>
    /// 同步结果
    /// </summary>
    public OpenPlatformPricingSyncResult SyncResult { get; set; }

    /// <summary>
    /// 同步失败信息
    /// </summary>
    public string FailedMessage { get; set; }
    
    /// <summary>
    /// 产品id
    /// </summary>
    public long ProductId { get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 套餐Id
    /// </summary>
    public long SkuId { get; set; }
    
    /// <summary>
    /// 套餐名称
    /// </summary>
    public string SkuName { get; set; }

    /// <summary>
    /// 套餐子项名称
    /// </summary>
    public string? SkuTypeItemName { get; set; }
    
    /// <summary>
    /// 同步开始日期
    /// </summary>
    public DateTime SyncStartDate { get; set; }
    
    /// <summary>
    /// 同步结束日期
    /// </summary>
    public DateTime SyncEndDate { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 供应商是否可售
    /// </summary>
    public bool? SupplierIsSale { get; set; }
}