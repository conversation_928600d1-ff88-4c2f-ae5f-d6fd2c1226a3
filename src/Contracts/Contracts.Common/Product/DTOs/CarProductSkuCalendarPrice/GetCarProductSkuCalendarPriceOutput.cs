using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.CarProductSkuCalendarPrice;
public class GetCarProductSkuCalendarPriceOutput
{

    /// <summary>
    /// 用车产品id
    /// </summary>
    public long CarProductId { get; set; }

    /// <summary>
    /// 套餐id
    /// </summary>
    public long CarProductSkuId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public CarProductType CarProductType { get; set; }

    /// <summary>
    /// 接送机细类
    /// </summary>
    public AirportTransferType? AirportTransferType { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 说明
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 最大乘客数
    /// </summary>
    public int MaxPassengers { get; set; }

    /// <summary>
    /// 最大行李数
    /// </summary>
    public int MaxBaggages { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ProductPictures { get; set; } = new();

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    public bool B2bSellingStatus { get; set; }

    /// <summary>
    /// 采购价币种 (供应商币种)
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种 (商户币种)
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 服务项目
    /// </summary>
    public List<CarProductSkuServiceItemOutput> ServiceItems { get; set; } = new();

    /// <summary>
    /// 服务语言
    /// </summary>
    public List<LanguageType> ServiceLanguages { get; set; } = new();

    /// <summary>
    /// 库存和日历价
    /// </summary>
    public List<CarProductCalendarPriceAndQuantityInfo> PriceAndQuantityInfos { get; set; } = new();

    /// <summary>
    /// 车型等级名称
    /// </summary>
    public string GradeName { get; set; }

    /// <summary>
    /// 座位数
    /// </summary>
    public int Seating { get; set; }

    /// <summary>
    /// 预警提示
    /// </summary>
    public InventoryWarningType? InventoryWarningType { get; set; }
}

public class CarProductCalendarPriceAndQuantityInfo
{
    public long Id { get; set; }
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 总库存
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 售卖状态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 基础售价
    /// </summary>
    public decimal? SalePrice { get; set; }

    /// <summary>
    /// B2b售价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
    
        
    #region 产品价格配置信息

    /// <summary>
    /// 产品价格配置- 价格基准类型
    /// </summary>
    public PriceBasisType PriceBasisType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整类型
    /// </summary>
    public PriceAdjustmentType PriceAdjustmentType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整值
    /// </summary>
    public decimal? PriceAdjustmentValue { get; set; }

    #endregion

    /// <summary>
    /// 最小利润阈值
    /// </summary>
    public decimal? MinProfit { get; set; }

    /// <summary>
    /// 最大利润阈值
    /// </summary>
    public decimal? MaxProfit { get; set; }
}
