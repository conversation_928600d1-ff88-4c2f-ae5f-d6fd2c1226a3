using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.LineProductSkuItinerary;

public class LineProductSkuItineraryInfo
{
    /// <summary>
    /// 产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 产品套餐Id
    /// </summary>
    public long LineProductSkuId { get; set; }

    /// <summary>
    /// 关联资源Id
    /// </summary>
    public long ResourceId { get; set; }

    /// <summary>
    /// 第几天
    /// </summary>
    public int Day { get; set; }

    /// <summary>
    /// 时间点
    /// </summary>
    public TimeSpan Time { get; set; }

    /// <summary>
    /// 行程类型
    /// </summary>
    public LineProductActivityType ActivityType { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> ImgPath { get; set; } = new();

    /// <summary>
    /// 主题
    /// </summary>
    public string Subject { get; set; }

    /// <summary>
    /// 副标题
    /// </summary>
    public string Subtitle { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }
}