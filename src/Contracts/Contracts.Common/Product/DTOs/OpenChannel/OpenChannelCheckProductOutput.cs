using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.OpenChannel;

public class OpenChannelCheckProductOutput
{
    /// <summary>
    /// 渠道类别类型
    /// </summary>
    public string? ChannelCategoryTypeStr { get; set; }

    /// <summary>
    /// 渠道类别类型
    /// </summary>
    public OpenChannelCategoryType? ChannelCategoryType { get; set; }
    
    public long? ChannelProductSkuId { get; set; }

    /// <summary>
    /// saas产品skuId
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// saas产品skuItemId
    /// </summary>
    public long? ProductSkuItemId { get; set; }

    /// <summary>
    /// 线路sku价格类型
    /// </summary>
    public LineSkuPriceType? LineSkuPriceType { get; set; }
    
    /// <summary>
    /// 是否支持补差
    /// <value>[手工发货] - 支持订单补差</value>
    /// </summary>
    public bool IsCompensation { get; set; }
}