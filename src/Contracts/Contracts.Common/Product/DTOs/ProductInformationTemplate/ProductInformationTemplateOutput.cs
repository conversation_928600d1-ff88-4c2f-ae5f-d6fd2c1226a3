using Contracts.Common.Product.DTOs.InformationTemplateFields;
using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.ProductInformationTemplate;
public class ProductInformationTemplateOutput
{
    /// <summary>
    /// 产品Id/景区Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 门票/套餐/规格Id
    /// </summary>
    public long? ProductSkuId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }

    public List<ProductInformationTemplateDetail> Templates { get; set; }
}

public class ProductInformationTemplateDetail
{
    /// <summary>
    /// 模板Id
    /// </summary>
    public long TemplateId { get; set; }

    /// <summary>
    /// 模板类型
    /// </summary>
    public TemplateType TemplateType { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public ProductTemplateType ProductTemplateType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<InformationTemplateFieldsOutput> Fields { get; set; }
}
