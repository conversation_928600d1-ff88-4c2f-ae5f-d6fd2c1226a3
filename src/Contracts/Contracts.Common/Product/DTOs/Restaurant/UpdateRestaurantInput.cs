using Contracts.Common.Resource.Enums;

namespace Contracts.Common.Product.DTOs.Restaurant
{
    public class UpdateRestaurantInput
    {
        public long Id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 国家
        /// </summary>
        public int CountryCode { get; set; }

        public string CountryName { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        public int ProvinceCode { get; set; }

        public string ProvinceName { get; set; }

        /// <summary>
        /// 市
        /// </summary>
        public int CityCode { get; set; }

        public string CityName { get; set; }

        /// <summary>
        /// 区
        /// </summary>
        public int DistrictCode { get; set; }

        public string DistrictName { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        public string HouseNumber { get; set; }

        /// <summary>
        /// 经度
        /// </summary>
        public double Longitude { get; set; }

        /// <summary>
        /// 纬度
        /// </summary>
        public double Latitude { get; set; }

        /// <summary>
        /// 坐标系默认BD09
        /// </summary>
        public CoordinateType CoordinateType { get; set; }  = CoordinateType.BD09;
        
        /// <summary>
        /// 营业时间
        /// </summary>
        public string ServiceTime { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Telephone { get; set; }

        public bool Enabled { get; set; }
        /// <summary>
        /// 照片地址
        /// </summary>
        public List<string> Paths { get; set; }
    }
}
