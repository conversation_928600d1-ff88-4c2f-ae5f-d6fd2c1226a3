namespace Contracts.Common.Product.DTOs.MailProduct
{
    public class MallGetMailOutput
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 卖点描述
        /// </summary>
        public string SellPointDescribe { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 邮费模板
        /// </summary>
        public long PostageTemplateId { get; set; }

        /// <summary>
        /// 是否支持退款
        /// </summary>
        public bool IsSupportRefund { get; set; }

        /// <summary>
        /// 视频
        /// </summary>
        public List<string> Videos { get; set; }

        /// <summary>
        /// 图片
        /// </summary>
        public List<string> Pictures { get; set; }

        /// <summary>
        /// 海报
        /// </summary>
        public List<string> Posters { get; set; }

        /// <summary>
        /// 已售
        /// </summary>
        public int Sales { get; set; }

        /// <summary>
        /// 是否可售
        /// </summary>
        public bool Enabled { get; set; }

        public bool InSellingDate { get; set; }
    }
}
