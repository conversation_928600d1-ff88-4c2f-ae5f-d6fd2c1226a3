using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.MailProduct
{
    public class MailInfo
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 卖点描述
        /// </summary>
        public string SellPointDescribe { get; set; }

        /// <summary>
        /// 图片
        /// </summary>
        public List<string> ProductPictures { get; set; }

        /// <summary>
        /// 视频
        /// </summary>
        public List<string> ProductVideos { get; set; }

        /// <summary>
        /// 海报
        /// </summary>
        public List<string> ProductPosters { get; set; }

        /// <summary>
        /// 分组
        /// </summary>
        public List<long> GroupIds { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否支持退款
        /// </summary>
        public bool IsSupportRefund { get; set; }

        /// <summary>
        /// 售卖日期 - 起  Null表示不限
        /// </summary>
        public DateTime? SellingDateBegin { get; set; }

        /// <summary>
        /// 售卖日期 - 止  Null表示不限
        /// </summary>
        public DateTime? SellingDateEnd { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 上架/下架
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 邮费模板Id
        /// </summary>
        public long PostageTemplateId { get; set; }
    }
}
