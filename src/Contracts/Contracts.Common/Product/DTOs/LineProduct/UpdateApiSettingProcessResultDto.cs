using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductTimeSlot;

namespace Contracts.Common.Product.DTOs.LineProduct;

/// <summary>
/// 更新API设置处理结果
/// </summary>
public class UpdateApiSettingProcessResultDto
{
    /// <summary>
    /// 需要推空的时段场次
    /// </summary>
    public List<LineProductTimeSlotInfo> PushEmptyTimeSlots { get; set; } = new();

    /// <summary>
    /// 需要推空的渠道
    /// </summary>
    public List<LineProductOpenChannelSettingInfo> PushEmptyOpenChannelSettings { get; set; } = new();
}