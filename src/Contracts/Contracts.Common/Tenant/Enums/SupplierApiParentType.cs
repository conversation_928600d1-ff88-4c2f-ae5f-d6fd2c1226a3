using System.ComponentModel;

namespace Contracts.Common.Tenant.Enums;

[Flags]
public enum SupplierApiParentType
{
    /// <summary>
    /// 酒店预付
    /// </summary>
    [Description("酒店预付")]
    Hotel = 1, // 0b_0000_0001

    /// <summary>
    /// 门票
    /// </summary>
    [Description("门票")]
    Ticket = 2, // 0b_0000_0010

    /// <summary>
    /// 用车
    /// </summary>
    [Description("用车")]
    CarProduct = 4, // 0b_0000_0100

    /// <summary>
    /// 保险
    /// </summary>
    [Description("保险")]
    Insurance = 8,// 0b_0000_1000

    /// <summary>
    /// 酒店现付
    /// </summary>
    [Description("酒店现付")]
    HotelCashPayment = 16,// 0b_0001_0000

    /// <summary>
    /// 线路
    /// </summary>
    [Description("线路")]
    TravelLine = 32,// 0b_0010_0000
}
