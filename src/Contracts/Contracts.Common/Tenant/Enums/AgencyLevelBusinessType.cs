using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Tenant.Enums;
public enum AgencyLevelBusinessType
{
    /// <summary>
    /// 认证成功
    /// </summary>
    Certified = 0,

    /// <summary>
    /// 日历酒店
    /// </summary>
    Hotel = 1,

    /// <summary>
    /// 汇智酒店
    /// </summary>
    HuiZhiHotel = 2,

    /// <summary>
    /// 酒店套餐
    /// </summary>
    Ticket_HotelPackages = 3,

    /// <summary>
    /// 房券
    /// </summary>
    Ticket_RoomVoucher = 4,

    /// <summary>
    /// 餐饮
    /// </summary>
    Ticket_Catering = 5,

    /// <summary>
    /// 线路产品
    /// </summary>
    Line = 6,

    /// <summary>
    /// 门票
    /// </summary>
    Ticket = 7,

    /// <summary>
    /// 用车
    /// </summary>
    CarProduct = 8,

    /// <summary>
    /// 手动调整
    /// </summary>
    Manual = 88,

    /// <summary>
    /// 周期调整
    /// </summary>
    CycleManual = 89,

}
