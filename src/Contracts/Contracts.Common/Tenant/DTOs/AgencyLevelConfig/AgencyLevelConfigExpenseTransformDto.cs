using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Tenant.DTOs.AgencyLevelConfig;
public class AgencyLevelConfigExpenseTransformDto
{
    /// <summary>
    /// 等级配置id
    /// </summary>
    public long? AgencyLevelConfigId { get; set; }

    /// <summary>
    /// 业务
    /// </summary>
    public AgencyLevelBusinessType BusinessType { get; set; }

    /// <summary>
    /// 获得成长值需要消费的值，默认 100 CNY 
    /// </summary>
    public int TransformFromValue { get; set; } = 100;

    /// <summary>
    /// 转换值 每消费100 CNY 获得的成长值
    /// </summary>
    public int TransformToValue { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enable { get; set; }
}
