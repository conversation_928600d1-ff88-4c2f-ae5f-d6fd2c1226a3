using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.AgencyCredit;

public class AgencyCreditRecordInput
{
    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 收支金额
    /// </summary>
    public decimal ChangeAmount { get; set; }

    /// <summary>
    /// 外部关联唯一id
    /// </summary>
    public long UniqueOrderId { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public CreditBusinessType CreditBusinessType { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 业务编号
    /// </summary>
    public long OrderId { get; set; }
}