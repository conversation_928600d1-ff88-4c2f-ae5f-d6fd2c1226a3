using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.AgencyCredit;

public class OffsetOrderCreateRecordInput
{
    /// <summary>
    /// 唯一关联id
    /// </summary>
    public long UniqueOrderId { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public CreditBusinessType CreditBusinessType { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 业务编号
    /// </summary>
    public long OrderId { get; set; }

    /// <summary>
    /// 订单金额 正负数, 加收 =负数 扣减； 退款 赔付=正数 增加
    /// </summary>
    public decimal OrderAmount { get; set; }
}