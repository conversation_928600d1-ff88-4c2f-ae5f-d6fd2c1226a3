using Contracts.Common.Tenant.DTOs.AgencyTag;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.Agency;

public class SearchAgenciesOutput : AgencyDto
{
    /// <summary>
    /// 分销商Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 最后更改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 签约状态
    /// </summary>
    public TenantContractStatus ContractStatus { get; set; }

    /// <summary>
    /// 渠道类型
    /// </summary>
    public ChannelType ChannelType { get; set; }

    /// <summary>
    /// 分销商类型
    /// </summary>
    public AgencyType AgencyType { get; set; }

    /// <summary>
    /// 分销商标签
    /// </summary>
    public List<SearchAgencyTagDto> AgencyTagDtos { get; set; } = new List<SearchAgencyTagDto>();

    /// <summary>
    /// 分销商授信状态
    /// </summary>
    public bool AgencyCreditStatus { get; set; }

    /// <summary>
    /// 认证状态 0-未认证，1-认证成功，2-认证中，3-认证失败
    /// </summary>
    public AgencyCertificationStatus? CertificationStatus { get; set; }

    /// <summary>
    /// 最近购买时间
    /// </summary>
    public DateTime? RecencyTime { get; set; }

    /// <summary>
    /// 最近状态
    /// </summary>
    public AgencyRecencyStatus? AgencyRecencyStatus { get; set; }

    /// <summary>
    /// 等级
    /// </summary>
    public int? Level { get; set; }

    /// <summary>
    /// 等级名称
    /// </summary>
    public string LevelName { get; set; }

    /// <summary>
    /// 等级开启状态
    /// </summary>
    public bool LevelEnbale { get; set; } = false;

    /// <summary>
    /// 周期成长值
    /// </summary>
    public int? CycleGrowUpValue { get; set; }

    /// <summary>
    /// 注册来源
    /// </summary>
    public AgencyRegisterSourceType RegisterSource { get; set; }

    /// <summary>
    /// 是否签售转售协议
    /// </summary>
    public string CheckOtaContent { get; set; }

    /// <summary>
    /// 结算维度类型 默认预订时间
    /// </summary>
    public SettlementDimensionType DimensionType { get; set; }

    /// <summary>
    /// 认证时间
    /// </summary>
    public DateTime? FinishTime { get; set; }

    /// <summary>
    /// 推广活码id
    /// </summary>
    public long? PromotionTraceId { get; set; }

    /// <summary>
    /// 推广活码id
    /// </summary>
    public string? PromotionTraceName { get; set; }

    /// <summary>
    /// 注册来源-子来源
    /// </summary>
    public AgencyRegisterSubSourceType? RegisterSubSource { get; set; }

}

public class SearchAgencyTabStatusStat
{

    public int WaitAuditCount { get; set; }

    public int WaitSignCount { get; set; }

    public int SignedCount { get; set; }
}