using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.Agency;

public class SearchAgenciesInput : PagingInput
{
    public long TenantId { get; set; }
    public IEnumerable<long> AgencyIds { get; set; } = Enumerable.Empty<long>();

    /// <summary>
    /// 销售BD ID
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 是否包含汇登分销商
    /// </summary>
    public bool IncludeHuiDeng { get; set; }

    public string? KeyWord { get; set; }

    /// <summary>
    /// 分销商类型
    /// </summary>
    public AgencyType? AgencyType { get; set; }

    /// <summary>
    /// 价格分组id
    /// </summary>
    public long? PriceGroupId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public TenantContractStatus? ContractStatus { get; set; }

    /// <summary>
    /// 货币代码
    /// </summary>
    public string? CurrencyCode { get; set; }

    public bool? Enable { get; set; }

    /// <summary>
    /// 分销商标签id
    /// </summary>
    public List<long> AgencyTagIds { get; set; } = new List<long>();

    /// <summary>
    /// 认证状态
    /// </summary>
    public AgencyCertificationStatus? CertificationStatus { get; set; }

    /// <summary>
    /// 额度状态
    /// </summary>
    public bool? AgencyCreditStatus { get; set; }

    /// <summary>
    /// 是否有营业执照
    /// </summary>
    public bool? IsHasBusinessLicense { get; set; }

    /// <summary>
    ///最近购买开始时间
    /// </summary>
    public DateTime? RecencyBeginTime { get; set; }

    /// <summary>
    ///最近购买结束时间
    /// </summary>
    public DateTime? RecencyEndTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public AgencyRecencyStatus? AgencyRecencyStatus { get; set; }

    /// <summary>
    /// 区域id
    /// </summary>
    public long? RegionConfigId { get; set; }

    /// <summary>
    /// 是否显示最近购买时间
    /// </summary>
    public bool IsRecencyTime { get; set; }

    /// <summary>
    /// 查询无销售BD
    /// </summary>
    public bool IsNoSalePerson { get; set; }

    /// <summary>
    /// 分销商等级
    /// </summary>
    public int? Level { get; set; }

    /// <summary>
    /// 所属行业
    /// </summary>
    public AgencyIndustryType[] IndustryInvolved { get; set; } = Array.Empty<AgencyIndustryType>();

    /// <summary>
    /// 客户运营Id
    /// </summary>
    public long? CustomerOperationId { get; set; }

    /// <summary>
    /// 业务体量
    /// </summary>
    public int? BusinessVolume { get; set; }

    /// <summary>
    /// 租户员工id
    /// </summary>
    public long? TenantUserId { get; set; }

    /// <summary>
    /// 延时支付状态，true 表示已开启延时支付，false 表示未开启延时支付，null 表示不区分
    /// </summary>
    public bool? DelayedStatus { get; set; }

    /// <summary>
    /// 注册来源/来源渠道
    /// </summary>
    public AgencyRegisterSourceType? RegisterSource { get; set; }

    /// <summary>
    /// 是否审核
    /// </summary>
    public bool? IsAduit { get; set; }

    /// <summary>
    /// 来源页面、注册来源子分类
    /// </summary>
    public AgencyRegisterSubSourceType? RegisterSubSources { get; set; }

    /// <summary>
    /// 投放渠道、推广活码id
    /// </summary>
    public long? PromotionTraceId { get; set; }
}