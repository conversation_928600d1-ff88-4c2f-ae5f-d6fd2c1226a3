using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.Agency.AgencyRegister;
public class AgencyRegisterInput
{
    /// <summary>
    /// 公司全称 
    /// </summary>
    public string FullName { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string Contact { get; set; }

    /// <summary>
    /// 公司地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 联系人电话
    /// </summary>
    public string ContactNumber { get; set; }

    /// <summary>
    /// 联系人国家区号
    /// </summary>
    public string ContactCountryDialCode { get; set; }

    /// <summary>
    /// 注册来源
    /// </summary>
    public AgencyRegisterSourceType RegisterSource { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public long? CountryCode { get; set; }

    /// <summary>
    /// 国家名字
    /// </summary>
    public string? CountryName { get; set; }

    /// <summary>
    /// 省编码
    /// </summary>
    public long? ProvinceCode { get; set; }

    /// <summary>
    /// 省名字
    /// </summary>
    public string? ProvinceName { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public long? CityCode { get; set; }

    /// <summary>
    /// 城市名字
    /// </summary>
    public string? CityName { get; set; }

    /// <summary>
    /// 所属行业
    /// </summary>
    public AgencyIndustryType? IndustryInvolved { get; set; }

    /// <summary>
    /// 联系邮箱
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// 卫瓴企业id
    /// </summary>
    public string? WeilingCompanyId { get; set; }

    /// <summary>
    /// 推广活码id
    /// </summary>
    public long? PromotionTraceId { get; set; }

    /// <summary>
    /// 注册父级来源
    /// </summary>
    public string? RegisterParentSource { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 注册来源-子来源
    /// </summary>
    public AgencyRegisterSubSourceType? RegisterSubSource { get; set; }
}
