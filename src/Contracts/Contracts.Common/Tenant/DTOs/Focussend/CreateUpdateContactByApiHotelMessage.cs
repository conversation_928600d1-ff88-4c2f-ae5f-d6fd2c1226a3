using Contracts.Common.Hotel.Enums;

namespace Contracts.Common.Tenant.DTOs.Focussend;
public class CreateUpdateContactByApiHotelMessage
{
    /// <summary>
    /// 英文名称
    /// </summary>
    public string ENName { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    /// <summary>
    /// 资源酒店类型
    /// </summary>
    public HotelType HotelType { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Telephone { get; set; }

    public string Address { get; set; }

    public string CountryName { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 发单邮箱
    /// </summary>
    public string ReceiptEmail { get; set; }

    public long Id { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }
}
