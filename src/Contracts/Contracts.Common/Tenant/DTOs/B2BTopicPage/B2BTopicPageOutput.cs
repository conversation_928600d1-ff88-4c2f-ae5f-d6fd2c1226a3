using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.B2BTopicPage;
public class B2BTopicPageOutput : B2BTopicPageInfoOutput
{
    /// <summary>
    /// 标题
    /// </summary>
    public string? AppletDarenDescription { get; set; }

    /// <summary>
    /// 专题描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 小程序分享图片
    /// </summary>
    public string? AppletDarenImage { get; set; }

    /// <summary>
    /// 标签ids
    /// </summary>
    public List<long> B2BTopicPageTagIds { get; set; }

    public List<B2BTopicPageExtendOutput> Extends { get; set; }
}