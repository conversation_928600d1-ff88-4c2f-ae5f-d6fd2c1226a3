using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.B2BTopicPage;
public class UpdateComponentInput
{
    /// <summary>
    /// 组件id
    /// </summary>
    public long Id { get; set; }

    #region 专题页信息
    /// <summary>
    /// 专题页id
    /// </summary>
    public long B2BTopicPageId { get; set; }
    #endregion

    /// <summary>
    /// 更新组件 多语言项
    /// </summary>
    public List<B2BTopicPageComponentItemDto> ComponentItems { get; set; }

    public B2BTopicPageType B2BTopicPageType { get; set; } = B2BTopicPageType.B2B;
}
