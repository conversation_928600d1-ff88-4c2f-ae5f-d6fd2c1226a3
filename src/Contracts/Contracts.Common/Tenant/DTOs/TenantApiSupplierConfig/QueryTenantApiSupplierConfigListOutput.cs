using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;

public class QueryTenantApiSupplierConfigListOutput
{
    public long Id { get; set; }
    
    /// <summary>
    /// 中文
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 英文
    /// </summary>
    public string? EnName { get; set; }
    
    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// API类型
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }
    
    /// <summary>
    /// 业务类型
    /// </summary>
    public List<SupplierApiParentType> SupplierApiParentTypes { get; set; }
}