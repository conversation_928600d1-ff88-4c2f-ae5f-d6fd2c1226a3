using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.Enums.OpenPlatform;

namespace Contracts.Common.Tenant.DTOs.CitOpenTenantConfig;

/// <summary>
/// 查询CitOpenApi账户配置
/// </summary>
public class GetCitOpenApiAccountOutput
{
    public CitOpenApiSideType ApiSideType { get; set; }
    public CitOpenApiNameType ApiNameType { get; set; }

    public GetChannelApiConfigMixedModel? ChannelApiConfigMixedModel { get; set; }

    public List<ApiConfigFiledModel> SupplierApiConfigFileds { get; set; } = new();
}

public class ApiConfigFiledModel
{
    public string FieldCode { get; set; }

    public FieldsType FieldType { get; set; } = FieldsType.Text;

    public string? FieldValue { get; set; }
}

public class GetSupplierApiConfigMixedModel
{
    public string? AppKey { get; set; }

    public string? AppSecret { get; set; }

    public string? UserName { get; set; }

    public string? Password { get; set; }

    /// <summary>
    /// gt - 回调通知密钥
    /// </summary>
    public string? NotificationSecret { get; set; }

    /// <summary>
    /// 澳新- 产品目录API地址
    /// </summary>
    public string? CatalogueApiHost { get; set; }

    /// <summary>
    /// 澳新- 伙伴代码
    /// </summary>
    public string? PartnerCode { get; set; }

    /// <summary>
    /// 新尚维SCTT - 商户id
    /// </summary>
    public string? MerchantId { get; set; }

    /// <summary>
    /// ApiKey
    /// </summary>
    public string? ApiKey { get; set; }
}

public class GetChannelApiConfigMixedModel
{
    /// <summary>
    /// 飞猪 - SessionKey(UserSession)
    /// 携程 - 无
    /// 美团 - 无
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 飞猪 - AppKey
    /// 携程 - AppKey(AccountId)
    /// 美团 - AppKey(OtaId)
    /// 抖音 - client_key(client_key)
    /// </summary>
    public string Account { get; set; }

    /// <summary>
    /// 飞猪 - AppSecret
    /// 携程 - AppSecret(AccountSecretKey)
    /// 美团 - AppSecret(SecretCode)
    /// 抖音 - client_secret(client_secret)
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    /// 抖音 - account_id
    /// </summary>
    public string AccountId { get; set; }

    /// <summary>
    /// 携程 - AES加密密钥
    /// </summary>
    public string? AesKey { get; set; }

    /// <summary>
    /// 携程 - AES加密初始向量
    /// </summary>
    public string? AesIv { get; set; }
}