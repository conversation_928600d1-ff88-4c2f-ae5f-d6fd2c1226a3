using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Reflection;
using Contracts.Common.Tenant.DTOs.SupplierApiSetting;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.Supplier
{
    public class SupplierInfo
    {

        /// <summary>
        /// 全称
        /// </summary>
        [PropertyMsg(Name = "名称")]
        public string FullName { get; set; }
        /// <summary>
        /// 简称
        /// </summary>
        [PropertyMsg(Name = "简称")]
        public string ShortName { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        [PropertyMsg(Name = "联系人")]
        public string ContactName { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        [PropertyMsg(Name = "联系电话", IsSensitive = true, SensitiveType = SensitiveDataType.Phone)]
        public string ContactPhone { get; set; }
        /// <summary>
        /// 联系邮箱
        /// </summary>
        public string ContactEmail { get; set; }
        /// <summary>
        /// 结算类型
        /// </summary>
        [PropertyMsg(Name = "结算类型")]
        public SupplierSettlementType SettlementType { get; set; }
        /// <summary>
        /// 结算方式
        /// </summary>
        [PropertyMsg(Name = "结算方式")]
        public SupplierSettlementMode SettlementMode { get; set; }
        /// <summary>
        /// 结算周期
        /// </summary>
        [PropertyMsg(Name = "结算周期")]
        public SupplierSettlementPeriod SettlementPeriod { get; set; }

        /// <summary>
        /// 银行编号
        /// </summary>
        [PropertyMsg(Name = "银行编号")]
        public string BankCode { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        [PropertyMsg(Name = "银行名称")]
        public string BankName { get; set; }
        /// <summary>
        /// 开户账号
        /// </summary>
        [PropertyMsg(Name = "银行账号", IsSensitive = true, SensitiveType = SensitiveDataType.BankAccount)]
        public string BankAccount { get; set; }
        /// <summary>
        /// 开户名称
        /// </summary>
        [PropertyMsg(Name = "开户名称")]
        public string AccountName { get; set; }

        /// <summary>
        /// 运营权
        /// </summary>
        public bool HasOperationRight { get; set; }

        /// <summary>
        /// 货币代码 默认人民币
        /// </summary>
        [PropertyMsg(Name = "结算币种")]
        public string CurrencyCode { get; set; } = Currency.CNY.ToString();

        /// <summary>
        /// 合同附件地址
        /// </summary>
        [PropertyMsg(Name = "合同附件")]
        public string? AppendicesOfContractPath { get; set; }

        /// <summary>
        /// 分行名称
        /// </summary>
        [PropertyMsg(Name = "分行名称")]
        public string? BranchName { get; set; }

        [PropertyMsg(Name = "类型")]
        public SupplierType? SupplierType { get; set; }

        public SupplierApiSettingDto? SupplierApiSetting { get; set; }

        #region 境外账户配置

        /// <summary>
        /// 供应商账户类型 1-国内 2-国外  3-第三方银行账户
        /// </summary>
        [PropertyMsg(Name = "账号类型")]
        public TenantBankAccountType BankAccountType { get; set; }

        /// <summary>
        /// 境外账户货币代码
        /// </summary>
        [PropertyMsg(Name = "境外账户货币代码")]
        public string? AbroadAccountCurrencyCode { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [PropertyMsg(Name = "地址")]
        public string? AbroadAccountAddress { get; set; }

        [PropertyMsg(Name = "swift code")]
        public string? SwiftCode { get; set; }
        #endregion

        /// <summary>
        /// 签约主体Id
        /// </summary>
        public long? SignSubjectId { get; set; }

        /// <summary>
        /// 部门Id
        /// </summary>
        public long? TenantDepartmentId { get; set; }

        /// <summary>
        /// 银行账号Id
        /// </summary>
        public long? TenantBankAccountId { get; set; }


        #region 产品价格配置信息

        /// <summary>
        /// 产品价格配置- 价格基准类型
        /// </summary>
        [PropertyMsg(Name = "价格基准类型")]
        public PriceBasisType? PriceBasisType { get; set; }

        /// <summary>
        /// 产品价格配置- 价格调整类型
        /// </summary>
        [PropertyMsg(Name = "价格调整类型")]
        public PriceAdjustmentType? PriceAdjustmentType { get; set; }

        /// <summary>
        /// 产品价格配置- 价格调整值
        /// </summary>
        [PropertyMsg(Name = "价格调整值")]
        public decimal? PriceAdjustmentValue { get; set; }

        #endregion
    }
}
