using Contracts.Common.Order.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.Enums;
using Newtonsoft.Json;

namespace Contracts.Common.Scenic.DTOs.TicketTimeSlot;

/// <summary>
/// 第三方时段拉取检查
/// </summary>
public class CheckThirdTimeSlotInput
{
    /// <summary>
    /// 门票id
    /// 新增的时候不需要传值
    /// </summary>
    public long? TicketId { get; set; }
    
    /// <summary>
    /// 时段
    /// </summary>
    public TimeSpan? TimeSlot { get; set; }
    
    public string? TimeSlotName
    {
        get
        {
            return TimeSlot.HasValue
                ? $"{TimeSlot.Value.Hours:00}:{TimeSlot.Value.Minutes:00}"
                : null;
        }
    }

    /// <summary>
    /// 供应商类类型
    /// </summary>
    public OpenSupplierType OpenSupplierType { get; set; }
    
    #region API对接的配置参数
        
    /// <summary>
    /// klook-活动ID,
    /// GlobalTix-ProductID
    /// </summary>
    public string ActivityId { get; set; }

    /// <summary>
    /// klook-套餐ID,
    /// GlobalTix-OptionID
    /// </summary>
    public string PackageId { get; set; }

    /// <summary>
    /// klook-SkuID,
    /// GlobalTix-TicketTypeID
    /// </summary>
    public string SkuId { get; set; }
        
    #endregion
}