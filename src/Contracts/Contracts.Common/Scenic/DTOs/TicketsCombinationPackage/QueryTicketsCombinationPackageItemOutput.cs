using Contracts.Common.Payment.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Scenic.DTOs.TicketsCombinationPackage;

public class QueryTicketsCombinationPackageItemOutput
{
    /// <summary>
    /// 组合产品id
    /// </summary>
    public long TicketsCombinationId { get; set; }
    
    /// <summary>
    /// 组合套餐id
    /// </summary>
    public long TicketsCombinationPackageId { get; set; }
    
    /// <summary>
    /// 组合套餐名称
    /// </summary>
    public string TicketsCombinationPackageName { get; set; }

    /// <summary>
    /// 组合产品名称
    /// </summary>
    public string TicketsCombinationName { get; set; }

    /// <summary>
    /// 组合产品类型
    /// </summary>
    public TicketsCombinationSourceType CombinationType { get; set; }
    
    /// <summary>
    /// 套餐项集合
    /// </summary>
    public List<QueryTicketsCombinationPackageItem> PackageItems { get; set; } = new();
}

public class QueryTicketsCombinationPackageItem
{

    /// <summary>
    /// 套餐配置项id
    /// </summary>
    public long TicketsCombinationPackageItemId { get; set; }
    
    /// <summary>
    /// 景区Id
    /// </summary>
    public long? ScenicSpotId { get; set; }
    
    /// <summary>
    /// 门票id
    /// </summary>
    public long TicketsId { get; set; }

    /// <summary>
    /// 门票名称
    /// </summary>
    public string TicketsName { get; set; }

    /// <summary>
    /// 时段id
    /// </summary>
    public long? TimeSlotId { get; set; }

    /// <summary>
    /// 时段名称
    /// </summary>
    public string TimeSlotName { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }
    
    /// <summary>
    /// 门票价库类型
    /// </summary>
    public PriceInventoryType? PriceInventoryType { get; set; }

    /// <summary>
    /// 关联门票|时段是否有效
    /// </summary>
    public bool IsEffective { get; set; }
    
    /// <summary>
    /// 游客信息类型
    /// </summary>
    public TouristInfoType? TouristInfoType { get; set; }
    
    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; } = Currency.CNY.ToString();
    
    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; } = Currency.CNY.ToString();
    
    #region API对接的配置参数
        
    /// <summary>
    /// klook-活动ID,
    /// GlobalTix-ProductID
    /// </summary>
    public string? ActivityId { get; set; }

    /// <summary>
    /// klook-套餐ID,
    /// GlobalTix-OptionID
    /// </summary>
    public string? PackageId { get; set; }

    /// <summary>
    /// klook-SkuID,
    /// GlobalTix-TicketTypeID
    /// </summary>
    public string? SkuId { get; set; }
        
    #endregion
}