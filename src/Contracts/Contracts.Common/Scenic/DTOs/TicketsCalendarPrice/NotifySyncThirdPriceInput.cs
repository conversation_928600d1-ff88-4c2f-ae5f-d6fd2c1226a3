using Contracts.Common.Order.Enums;

namespace Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;

public class NotifySyncThirdPriceInput
{
    /// <summary>
    /// klook-活动ID,
    /// GlobalTix-ProductID
    /// </summary>
    public string? ActivityId { get; set; }

    /// <summary>
    /// klook-套餐ID,
    /// GlobalTix-OptionID
    /// </summary>
    public string? PackageId { get; set; }

    /// <summary>
    /// klook-SkuID,
    /// GlobalTix-TicketTypeID
    /// </summary>
    public string? SkuId { get; set; }

    /// <summary>
    /// 最新底价
    /// </summary>
    public decimal? NewPrice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// 供货方类型
    /// </summary>
    public OpenSupplierType OpenSupplierType { get; set; }
}

public class NotifySyncStockDto
{
    /// <summary>
    /// 产品维度Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// sku纬度Id
    /// </summary>
    public long SkuId { get; set; }
    
    /// <summary>
    /// 时段维度Id
    /// </summary>
    public long? TimeSlotId { get; set; }

    public IEnumerable<NotifySyncStockSchedule> Schedules { get; set; }
}

public class NotifySyncStockSchedule
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 时段
    /// </summary>
    public TimeSpan Time { get; set; }
    
    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }
}