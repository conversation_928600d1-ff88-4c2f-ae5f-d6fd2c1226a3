using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Scenic.DTOs.Ticket;

public class SearchTicketsInput : PagingInput
{
    /// <summary>
    /// 景区Id
    /// </summary>
    public long? ScenicSpotId { get; set; }

    /// <summary>
    /// 搜索关键字
    /// </summary>
    public string KeyWord { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool? Enabled { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 凭证来源（期票时必填）-兼容,后续移除
    /// </summary>
    public CredentialSourceType? CredentialSourceType { get; set; }

    /// <summary>
    /// 查询类型 默认查询产品名称
    /// </summary>
    public ProductSearchType SearchType { get; set; } = ProductSearchType.ProductName;

    /// <summary>
    /// 凭证来源
    /// </summary>
    public List<CredentialSourceType> CredentialSourceTypes { get; set; } = new();

    /// <summary>
    /// 门票类型
    /// </summary>
    public List<ScenicTicketsType> TicketsTypes { get; set; } = new();

    /// <summary>
    /// 对接渠道
    /// </summary>
    public List<PriceInventorySyncChannelType> PriceInventorySyncChannelTypes { get; set; } = new();

    /// <summary>
    /// 时效渠道
    /// </summary>
    public List<PriceInventorySyncChannelType> TimelinessChannelTypes { get; set; } = new();

    /// <summary>
    /// 模糊查询-产品名称关键字
    /// </summary>
    public string TicketNameKeyWord { get; set; }

    /// <summary>
    ///  模糊查询-id关键字
    /// </summary>
    public string TicketIdKeyWord { get; set; }

    /// <summary>
    /// 时段id
    /// </summary>
    public List<long> TimeSlotIds { get; set; } = new();

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    public bool? B2bSellingStatus { get; set; }
    
    /// <summary>
    /// 是否支持订单补差调整
    /// <value>[手工发货] - 支持订单补差</value>
    /// </summary>
    public bool? IsCompensation { get; set; }
}