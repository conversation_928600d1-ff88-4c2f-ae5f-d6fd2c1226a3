using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Scenic.DTOs.Ticket;

/// <summary>
/// 门票信息更新
/// </summary>
public class UpdateTicketInfo
{
    #region 基本信息

    /// <summary>
    /// 景区Id
    /// </summary>
    public long ScenicSpotId { get; set; }

    /// <summary>
    /// 门票类型
    /// </summary>
    public ScenicTicketsType TicketsType { get; set; }

    /// <summary>
    /// 凭证来源
    /// </summary>
    public CredentialSourceType? CredentialSourceType { get; set; }
    
    /// <summary>
    /// 门票名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 英文门票名称
    /// </summary>
    public string EnName { get; set; }

    /// <summary>
    /// 游客信息类型
    /// </summary>
    public TouristInfoType TouristInfoType { get; set; }

    /// <summary>
    /// 游客姓名必填
    /// </summary>
    public bool TouristNameRequired { get; set; }

    /// <summary>
    /// 游客身份证必填
    /// </summary>
    public bool TouristIDRequired { get; set; }

    /// <summary>
    /// 游客手机号必填
    /// </summary>
    public bool TouristPhoneRequired { get; set; }

    /// <summary>
    /// 费用包含
    /// </summary>
    public string FeeNote { get; set; }

    /// <summary>
    /// 费用不含
    /// </summary>
    public string FeeNotNote { get; set; }

    /// <summary>
    /// 预定须知
    /// </summary>
    public string OtherNote { get; set; }
    
    /// <summary>
    /// 亮点
    /// </summary>
    public string? Highlights { get; set; }

    /// <summary>
    /// 营业时间
    /// </summary>
    public string? BusinessHours { get; set; }

    /// <summary>
    /// 景区公告
    /// </summary>
    public string? ScenicNotice { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 产品详情
    /// </summary>
    public string? ProductDetails { get; set; }

    /// <summary>
    /// 有效期描述
    /// </summary>
    public string? ValidityDescription { get; set; }

    /// <summary>
    /// 注意事项
    /// </summary>
    public string? Precautions { get; set; }

    /// <summary>
    /// 使用方式
    /// </summary>
    public string? UsageInstructions { get; set; }

    /// <summary>
    /// 其他说明
    /// </summary>
    public string? OtherInstructions { get; set; }

    /// <summary>
    /// 取消政策
    /// </summary>
    public string? CancellationPolicy { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public List<string> Pictures { get; set; } = new();
    
    #endregion

    #region 运营信息

    /// <summary>
    /// 产品人id，产品开发者
    /// </summary>
    public long? DevelopUserId { get; set; }
    
    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }
    
    /// <summary>
    /// 平台运营人
    /// </summary>
    public List<ProductOperatorUserDto> ProductOperatorUser { get; set; }

    #endregion

    #region 购买须知

    #region 提前购买

    /// <summary>
    /// 需提前购买
    /// </summary>
    public bool NeedToBuyInAdvance { get; set; }

    /// <summary>
    /// 提前购买天数
    /// </summary>
    public int BuyDaysInAdvance { get; set; }

    /// <summary>
    /// 提前购买时分
    /// </summary>
    public TimeSpan BuyTimeInAdvance { get; set; }

    /// <summary>
    /// 提前退款时分
    /// </summary>
    public TimeSpan RefundTimeInAdvance { get; set; }

    #endregion

    #region 换票

    /// <summary>
    /// 是否需要换票
    /// </summary>
    public bool NeedToExchange { get; set; }

    /// <summary>
    /// 换票地点
    /// </summary>
    public string ExchangeLocation { get; set; }

    /// <summary>
    /// 换票凭证
    /// </summary>
    public List<ExchangeProofType> ExchangeProofs { get; set; } = new List<ExchangeProofType>();

    /// <summary>
    /// 换票说明
    /// </summary>
    public string ExchangeNote { get; set; }

    #endregion

    #region 退款政策

    /// <summary>
    /// 是否支持退款
    /// </summary>
    public bool IsSupportRefund { get; set; }

    /// <summary>
    /// 需提前几天退款
    /// </summary>
    public int RefundDaysInAdvance { get; set; }

    /// <summary>
    /// 退款比例1-100
    /// </summary>
    public int RefundRate { get; set; }

    /// <summary>
    /// 是否过期自动退款
    /// </summary>
    public bool AutoRefundAfterExpiration { get; set; }

    /// <summary>
    /// 过期自动退款比例1-100
    /// </summary>
    public int AutoRefundRate { get; set; }

    #endregion

    #endregion
    
    #region 币种
    
    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    #endregion
}