namespace Contracts.Common.Scenic.DTOs.Ticket;

//通用库存查询
public class GenericScenicInventoryQueryInput
{
    /// <summary>
    /// 景点id
    /// </summary>
    public long ScenicSpotId { get; set; }

    /// <summary>
    /// 门票id
    /// <value>字段目前必须传</value>
    /// </summary>
    public List<long> TicketIds { get; set; } = new();
    
    /// <summary>
    /// 时段id
    /// </summary>
    public long? TimeSlotId { get; set; }
    
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }
}