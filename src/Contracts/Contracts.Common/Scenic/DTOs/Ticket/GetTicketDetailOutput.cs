using Contracts.Common.Order.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Scenic.DTOs.Ticket;

public class GetTicketDetailOutput : TicketInfo
{
    /// <summary>
    /// 门票Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long TenantId { get; set; }

    /// <summary>
    /// 上下架状态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 成本价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 售价
    /// </summary>
    public decimal? SellingPrice { get; set; }

    /// <summary>
    /// 划线价
    /// </summary>
    public decimal? LinePrice { get; set; }

    /// <summary>
    /// 飞猪渠道价
    /// </summary>
    public decimal? FeiZhuChannelPrice { get; set; }

    /// <summary>
    /// 景区运营模式
    /// </summary>
    public Common.Hotel.Enums.OperatingModel OperatingModel { get; set; }
    
    /// <summary>
    /// 价格库存来源
    /// </summary>
    public PriceInventorySource? PriceInventorySource { get; set; }
    
    /// <summary>
    /// 供应商是否销售
    /// </summary>
    public bool SupplierIsSale { get; set; }
    
    /// <summary>
    /// 忽略价格信息
    /// </summary>
    public void IgnorePrice()
    {
        CostPrice = null;
        SellingPrice = null;
        LinePrice = null;
        FeiZhuChannelPrice = null;
    }
}