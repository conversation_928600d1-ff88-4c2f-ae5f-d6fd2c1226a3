using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Scenic.DTOs.Ticket;

public class SearchTicketsOutput
{
    /// <summary>
    /// 门票Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 景区Id
    /// </summary>
    public long ScenicSpotId { get; set; }

    /// <summary>
    /// 门票类型
    /// </summary>
    public ScenicTicketsType TicketsType { get; set; }

    /// <summary>
    /// 门票名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 售价
    /// </summary>
    public decimal? SellingPrice { get; set; }

    /// <summary>
    /// 忽略价格信息
    /// </summary>
    public void IgnorePrice() => SellingPrice = null;

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }
    
    /// <summary>
    /// 采购币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 凭证来源（期票时必填）
    /// </summary>
    public CredentialSourceType? CredentialSourceType { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    public string SupplierName { get; set; }

    /// <summary>
    /// 有效期类型
    /// </summary>
    public ProductValidityType ValidityType { get; set; }

    /// <summary>
    /// 有效期 - 起
    /// </summary>
    public DateTime? ValidityBegin { get; set; }

    /// <summary>
    /// 有效期 - 止
    /// </summary>
    public DateTime? ValidityEnd { get; set; }

    /// <summary>
    /// 门票价库类型
    /// </summary>
    public PriceInventoryType? PriceInventoryType { get; set; }

    /// <summary>
    /// 购买之后x天有效
    /// </summary>
    public int AfterPurchaseDays { get; set; }

    /// <summary>
    /// 门票时段信息
    /// </summary>
    public List<TicketTimeSlotInfo> TimeSlotInfos { get; set; } = new();

    /// <summary>
    /// 最晚可预订日期
    /// </summary>
    public string? LastCanReservationDate { get; set; }

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    public B2bSellingStatusType B2bSellingStatusType { get; set; }
    
            
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>如15表示为15%的折扣</remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
    
    /// <summary>
    /// 供应商是否销售
    /// </summary>
    public bool? SupplierIsSale { get; set; }

    /// <summary>
    /// 是否支持订单补差调整
    /// <value>[手工发货] - 支持订单补差</value>
    /// </summary>
    public bool IsCompensation { get; set; }
}