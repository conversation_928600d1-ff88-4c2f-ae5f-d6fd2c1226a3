using Contracts.Common.Product.Enums;

namespace Contracts.Common.Scenic.DTOs.ScenicSpot;

public class SearchScenicSpotsOutput
{
    /// <summary>
    /// 景区Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 运营模式
    /// </summary>
    public Common.Hotel.Enums.OperatingModel OperatingModel { get; set; }

    public int CountryCode { get; set; }
    /// <summary>
    /// 国家名称
    /// </summary>
    public string CountryName { get; set; }

    public int ProvinceCode { get; set; }
    public string ProvinceName { get; set; }

    public int CityCode { get; set; }
    /// <summary>
    /// 城市名称
    /// </summary>
    public string CityName { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 营业时间
    /// </summary>
    public string OpeningTime { get; set; }

    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    public string ENName { get; set; }

}