namespace Contracts.Common.Scenic.DTOs.ScenicSpot;

public class SearchScenicSpotsInput : PagingInput
{
    /// <summary>
    /// 景点关键词
    /// </summary>
    public string KeyWord { get; set; }

    /// <summary>
    /// 景点Id列表
    /// </summary>
    public List<long> ScenicSpotIds { get; set; } = new();

    /// <summary>
    /// 是否上架
    /// </summary>
    public bool? Enabled { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public int? CountryCode { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public int? CityCode { get; set; }

    /// <summary>
    /// 运营模式
    /// </summary>
    public Hotel.Enums.OperatingModel? OperatingModel { get; set; }

    /// <summary>
    /// 是否包含佣金
    /// </summary>
    public bool? HasCommission { get; set; }

    /// <summary>
    /// 是否参与限时抢购
    /// </summary>
    public bool IsFlashSale { get; set; }

    /// <summary>
    /// 是否只查有效门票的景区数据
    /// </summary>
    public bool IsValidity { get; set; }

    /// <summary>
    /// 门票id列表
    /// </summary>
    public IEnumerable<long> TicketIds { get; set; } = Enumerable.Empty<long>();

    /// <summary>
    /// 景区门票产品名称或景点门票Id关键字
    /// </summary>
    public string IdOrKeyWordByTicket { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long? SupplierId { get; set; }
}