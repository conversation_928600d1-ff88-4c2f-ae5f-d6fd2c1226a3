using Contracts.Common.Resource.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Scenic.DTOs.ScenicSpot;

public class SyncScenicSpotInput
{
    /// <summary>
    /// 景点资源Id
    /// </summary>
    public long ScenicSpotResourceId { get; set; }

    /// <summary>
    /// 简介
    /// </summary>
    public string Intro { get; set; }

    /// <summary>
    /// 营业时间
    /// </summary>
    public string OpeningTime { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 星级 1-5, 0表示其它
    /// </summary>
    public int Star { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string ContactNumber { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double Latitude { get; set; }

    /// <summary>
    /// 坐标系默认BD09
    /// </summary>
    public CoordinateType CoordinateType { get; set; } = CoordinateType.BD09;

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public int DistrictCode { get; set; }

    public string DistrictName { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    public string ENName { get; set; }
}
