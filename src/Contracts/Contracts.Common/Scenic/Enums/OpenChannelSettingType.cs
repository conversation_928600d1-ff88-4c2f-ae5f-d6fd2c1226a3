namespace Contracts.Common.Scenic.Enums;

/// <summary>
/// 渠道配置类型
/// </summary>
public enum OpenChannelSettingType
{
    /// <summary>
    /// 渠道价库同步配置
    /// </summary>
    PriceInventorySync = 1,
    
    /// <summary>
    /// 渠道时效配置
    /// </summary>
    TimelinessSync = 2
}


/// <summary>
/// 渠道时效触发时机类型
/// </summary>
public enum OpenChannelTimelinessTriggerType
{
    /// <summary>
    /// saas创建的订单触发渠道时效
    /// </summary>
    SaasCreatedOrder = 1,
    
    /// <summary>
    /// 供应商采购下单成功触发渠道时效
    /// </summary>
    SupplierCreatedOrder = 2
}