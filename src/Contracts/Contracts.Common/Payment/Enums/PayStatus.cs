using System.ComponentModel;

namespace Contracts.Common.Payment.Enums
{
    /// <summary>
    /// 支付状态
    /// </summary>
    public enum PayStatus
    {
        /// <summary>
        /// 待支付
        /// </summary>
        [Description("待支付")]
        Unpaid = 0,

        /// <summary>
        /// 支付中
        /// </summary>
        [Description("支付中")]
        Processing = 1,

        /// <summary>
        /// 订单支付成功
        /// </summary>
        [Description("订单支付成功")]
        Paid = 2,

        /// <summary>
        /// 订单已过期
        /// </summary>
        [Description("订单已过期")]
        Timeout = 3,

        /// <summary>
        /// 支付失败
        /// </summary>
        [Description("支付失败")]
        Fail = 4,

        /// <summary>
        /// 支付关闭
        /// </summary>
        [Description("支付关闭")]
        Closed = 5
    }
}
