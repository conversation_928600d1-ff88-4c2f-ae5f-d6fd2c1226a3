namespace Contracts.Common.Payment.Enums
{
    public enum OfflineReceiptOrderStatus
    {
        /// <summary>
        /// 待支付
        /// </summary>
        WaitingForPay = 1,

        /// <summary>
        /// 已收款
        /// </summary>
        Received = 2,

        /// <summary>
        /// 退款中
        /// </summary>
        Refunding = 3,

        /// <summary>
        /// 已退款
        /// </summary>
        Refunded = 4,

        /// <summary>
        /// 提现中
        /// </summary>
        Withdrawing = 5,

        /// <summary>
        /// 已提现
        /// </summary>
        Withdrawed = 6,

        /// <summary>
        /// 已取消
        /// </summary>
        Canceled = 7,
    }
}
