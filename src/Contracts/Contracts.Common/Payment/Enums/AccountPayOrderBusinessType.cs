namespace Contracts.Common.Payment.Enums
{
    public enum AccountPayOrderBusinessType
    {
        /// <summary>
        /// 达人提现
        /// </summary>
        DarenWithdrawal = 1,

        /// <summary>
        /// 供应商线下收款提现
        /// </summary>
        OfflineReceiptOrderWithdrawal = 2,

        /// <summary>
        /// 商户支付账户提现
        /// </summary>
        TenantWithdrawal = 3,

        /// <summary>
        /// 供应商结算单付款
        /// </summary>
        SupplierSettlementPayOrder = 4,
    }
}
