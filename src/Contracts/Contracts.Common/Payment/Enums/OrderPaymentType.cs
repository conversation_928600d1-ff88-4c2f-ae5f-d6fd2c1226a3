namespace Contracts.Common.Payment.Enums
{
    /// <summary>
    /// 支付单类型
    /// </summary>
    public enum OrderPaymentType
    {
        /// <summary>
        /// 订单支付
        /// </summary>
        OrderPay = 1,

        /// <summary>
        /// 预约单支付
        /// </summary>
        ReservationOrderPay = 2,

        /// <summary>
        /// 线下收款支付
        /// </summary>
        OfflineReceiptOrderPay = 3,

        /// <summary>
        /// 分销商额度充值
        /// </summary>
        AgencyCreditCharge = 4,

        /// <summary>
        /// 团房单 首尾款
        /// </summary>
        GroupBookingOrder = 5,
    }
}
