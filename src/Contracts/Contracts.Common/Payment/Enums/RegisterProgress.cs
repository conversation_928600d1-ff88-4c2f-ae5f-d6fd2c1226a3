using System.ComponentModel;

namespace Contracts.Common.Payment.Enums
{
    /// <summary>
    /// 商户支付号申请进度
    /// </summary>
    public enum RegisterProgress
    {
        None = 0,

        /// <summary>
        /// 提交资料
        /// </summary>
        [Description("提交资料")]
        SubmitData = 1,

        /// <summary>
        /// 真实性验证
        /// </summary>
        [Description("真实性验证")]
        TruthVerification = 2,

        /// <summary>
        /// 协议签署
        /// </summary>
        [Description("协议签署")]
        AgreementSign = 3,

        /// <summary>
        /// 平台审核
        /// </summary>
        [Description("平台审核")]
        PlatformCheck = 4,

        /// <summary>
        /// 易宝微信实名认证
        /// </summary>
        [Description("实名认证")]
        WechatAuth = 5,

        /// <summary>
        /// 易宝微信配置
        /// </summary>
        [Description("参数配置")]
        WechatConfig = 6,

        /// <summary>
        /// 配置已完成
        /// </summary>
        [Description("已完成")]
        Completed = 7
    }
}
