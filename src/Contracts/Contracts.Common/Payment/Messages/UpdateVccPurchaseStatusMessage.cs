using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.Messages;

/// <summary>
/// 更新虚拟卡采购状态
/// </summary>
public class UpdateVccPurchaseStatusMessage
{
    /// <summary>
    /// 主订单id
    /// </summary>
    public List<long> BaseOrderIds { get; set; }

    /// <summary>
    /// 结算单ID
    /// </summary>
    public long SettlementOrderId { get; set; }
    
    /// <summary>
    /// saas付款结算时间
    /// </summary>
    public DateTime? SettlementTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public VirtualCreditCardPurchaseStatus Status { get; set; }

    /// <summary>
    /// 租户ID
    /// </summary>
    public long TenantId { get; set; }
}