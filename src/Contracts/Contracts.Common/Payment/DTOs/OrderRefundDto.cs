using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs
{
    public class OrderRefundDto
    {
        /// <summary>
        /// 租户id
        /// </summary>
        public long TenantId { get; set; }

        public OrderRefundType OrderRefundType { get; set; }

        /// <summary>
        /// 退款单id
        /// </summary>
        public string OrderRefundId { get; set; }

        /// <summary>
        /// 支付单id 易宝原收款交易对应的商户收款请求号
        /// </summary>
        public long OrderPaymentId { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public OrderType OrderType { get; set; }

        /// <summary>
        /// 支付单类型
        /// </summary>
        public OrderPaymentType OrderPaymentType { get; set; }

        /// <summary>
        /// 外部单号 比如：易宝单号
        /// </summary>
        public string UniqueOrderNo { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal RefundAmount { get; set; }

        /// <summary>
        /// 退款原因
        /// </summary>
        public string Description { get; set; }
    }
}
