using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Payment.DTOs.OrderRefund;
public class SearchOrderRefundInput
{
    public PayType? PayType { get; set; }

    public OrderPaymentType[]? OrderPaymentTypes { get; set; }

    public DateTime? BeginDate { get; set; }
    public DateTime? EndDate { get; set; }
    public RefundStatus? RefundStatus { get; set; }

    /// <summary>
    /// 订单号
    /// </summary>
    public long[] OrderIds { get; set; }
}