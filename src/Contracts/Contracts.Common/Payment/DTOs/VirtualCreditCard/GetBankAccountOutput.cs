using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs.VirtualCreditCard;

public class GetBankAccountOutput
{
    /// <summary>
    /// saas银行账户id
    /// </summary>
    public long BankAccountId { get; set; }
    
    /// <summary>
    /// 开户名称
    /// </summary>
    public string AccountName { get; set; }
    
    /// <summary>
    /// Vcc供应商类型
    /// </summary>
    public VccSupplierType VccSupplierType { get; set; }

    /// <summary>
    /// Vcc供应商名称
    /// </summary>
    public string VccSupplierName { get; set; }
    
    /// <summary>
    /// 货币代码
    /// </summary>
    public string CurrencyCode { get; set; }
    
    /// <summary>
    /// 银行编号
    /// </summary>
    public string OrgBankId { get; set; }

    /// <summary>
    /// 公司编号
    /// </summary>
    public string OrgCompanyId { get; set; }
}