using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs
{
    public class RegisterQueryOutput
    {
        /// <summary>
        /// 申请单编号
        /// </summary>
        public string ApplicationNo { get; set; }

        /// <summary>
        /// 易宝商户编号
        /// </summary>
        public string MerchantNo { get; set; }

        /// <summary>
        /// 易宝审核意见
        /// </summary>
        public string AuditOpinion { get; set; }

        /// <summary>
        /// 入网进度说明
        /// </summary>
        public string ProgressDescription { get; set; }

        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime ApplicationTime { get; set; }

        /// <summary>
        /// 申请状态 0-待提交 1-申请审核中 2-真实性校验中 3-申请已驳回 4-协议待签署 5-业务开通中 6-申请已完成
        /// </summary>
        public ApplicationStatus ApplicationStatus { get; set; }

        /// <summary>
        /// 协议签署地址	string	当申请状态为“协议待签署”时，回调给商户该协议签署地址
        /// </summary>
        public string AgreementSignUrl { get; set; }
    }
}
