using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs
{
    public class OrderPayOutDto
    {
        /// <summary>
        /// 是否分账
        /// </summary>
        public FundProcessType FundProcessType { get; set; } = FundProcessType.NONE;

        /// <summary>
        /// 支付状态
        /// </summary>
        public PayStatus PayStatus { get; set; } = PayStatus.Unpaid;

        /// <summary>
        /// 外部订单号 如：易宝收款订单号
        /// </summary>
        public string UniqueOrderNo { get; set; }

        /// <summary>
        /// 渠道侧商户请求号
        /// <para>支付机构在微信侧的外部商户订单号，用于服务商用于点金计划商户小票功能</para>
        /// </summary>
        public string BankOrderId { get; set; }

        /// <summary>
        /// 支付标识信息
        /// </summary>
        public string PrePayTn { get; set; }

        /// <summary>
        /// 支付结果
        /// </summary>
        public string Message { get; set; }
    }
}
