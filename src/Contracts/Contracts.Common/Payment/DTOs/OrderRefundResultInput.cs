using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs
{
    public class OrderRefundResultInput
    {
        /// <summary>
        /// 订单退款单id
        /// </summary>
        public string RefundOrderId { get; set; }

        /// <summary>
        /// 外部退款id
        /// </summary>
        public string UniqueRefundNo { get; set; }

        /// <summary>
        /// 退款状态
        /// </summary>
        public RefundStatus RefundStatus { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string FailReason { get; set; }

        /// <summary>
        /// 商户手续费退款金额
        /// </summary>
        public decimal? ReturnMerchantFee { get; set; }
    }

}
