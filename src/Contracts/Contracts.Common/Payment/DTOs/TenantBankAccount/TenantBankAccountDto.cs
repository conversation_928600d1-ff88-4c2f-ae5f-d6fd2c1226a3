using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs.TenantBankAccount;
public class TenantBankAccountDto
{
    /// <summary>
    /// 商户银行账户类型
    /// </summary>
    public TenantBankAccountType TenantBankAccountType { get; set; } = TenantBankAccountType.Domestic;

    /// <summary>
    /// 开户名称
    /// <example>账户名称</example>
    /// </summary>
    public string AccountName { get; set; }

    /// <summary>
    /// 银行编号
    /// </summary>
    public string BankCode { get; set; }
    
    /// <summary>
    /// 银行分行编号
    /// </summary>
    public string? BranchCode { get; set; }
    
    /// <summary>
    /// 开户银行
    /// </summary>
    public string BankName { get; set; } = string.Empty;

    /// <summary>
    /// 分行名称
    /// </summary>
    public string? BranchName { get; set; }

    /// <summary>
    /// 账户类型 4-单位结算卡 5-对公卡
    /// </summary>
    public BankAccountType BankAccountType { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public string AccountNo { get; set; } = string.Empty;

    /// <summary>
    /// 地址
    /// <example>收款银行地址</example>
    /// </summary>
    public string Address { get; set; }
    
    /// <summary>
    /// 收款方地址
    /// </summary>
    public string? PayeeAddress { get; set; }
    
    /// <summary>
    /// 常驻的国家或地区
    /// </summary>
    public string? ResidentCountryOrRegion { get; set; }

    /// <summary>
    /// SwiftCode
    /// </summary>
    public string SwiftCode { get; set; }

    /// <summary>
    /// 货币代码 默认人民币
    /// </summary>
    public string? CurrencyCode { get; set; }

    /// <summary>
    /// Remark
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 启用/停用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 开户银行代码
    /// </summary>
    public string? OpeningBankCode { get; set; }

    /// <summary>
    /// Vcc设置
    /// </summary>
    public long? TenantBankAccountVccSettingId { get; set; }
    
    /// <summary>
    /// 第三方支付类型
    /// </summary>
    public ThirdPartyPaymentType? ThirdPartyPaymentType { get; set; }
}